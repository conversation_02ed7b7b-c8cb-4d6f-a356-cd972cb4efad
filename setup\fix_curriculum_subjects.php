<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>إصلاح جدول الأقسام الدراسية</h2>";
    
    // حذف الجدول الحالي إذا كان موجوداً
    echo "<p>🗑️ حذف الجدول الحالي...</p>";
    $db->exec("DROP TABLE IF EXISTS curriculum_subjects");
    
    // إنشاء الجدول الجديد
    echo "<p>🔨 إنشاء الجدول الجديد...</p>";
    $createTableSQL = "
    CREATE TABLE curriculum_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT 'اسم القسم/المادة',
        name_en VARCHAR(100) NULL COMMENT 'اسم القسم بالإنجليزية',
        description TEXT NULL COMMENT 'وصف القسم',
        education_level ENUM('primary', 'preparatory', 'secondary') NOT NULL COMMENT 'المرحلة التعليمية',
        education_type ENUM('azhari', 'general') NOT NULL COMMENT 'نوع التعليم',
        grade VARCHAR(10) NOT NULL COMMENT 'الصف',
        specialization ENUM('scientific', 'literary', 'all') DEFAULT 'all' COMMENT 'التخصص للثانوي',
        icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة القسم',
        color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون القسم',
        sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
        is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة القسم',
        created_by INT NULL COMMENT 'منشئ القسم',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_education (education_level, education_type, grade),
        INDEX idx_specialization (specialization),
        INDEX idx_active (is_active),
        INDEX idx_sort_order (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($createTableSQL);
    echo "<p>✅ تم إنشاء الجدول بنجاح</p>";
    
    // إدخال البيانات
    echo "<p>📝 إدخال البيانات...</p>";
    
    $subjects = [
        // الابتدائي الأزهري
        ['نحو', 'Grammar', 'primary', 'azhari', '4', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'azhari', '4', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'azhari', '4', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'azhari', '4', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'azhari', '4', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'azhari', '4', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'azhari', '5', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'azhari', '5', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'azhari', '5', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'azhari', '5', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'azhari', '5', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'azhari', '5', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'azhari', '6', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'azhari', '6', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'azhari', '6', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'azhari', '6', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'azhari', '6', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'azhari', '6', 'all', '💭', '#20B2AA', 6],
        
        // الابتدائي العام
        ['نحو', 'Grammar', 'primary', 'general', '1', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'general', '1', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'general', '1', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'general', '1', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'general', '1', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'general', '1', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'general', '2', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'general', '2', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'general', '2', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'general', '2', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'general', '2', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'general', '2', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'general', '3', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'general', '3', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'general', '3', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'general', '3', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'general', '3', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'general', '3', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'general', '4', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'general', '4', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'general', '4', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'general', '4', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'general', '4', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'general', '4', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'general', '5', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'general', '5', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'general', '5', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'general', '5', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'general', '5', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'general', '5', 'all', '💭', '#20B2AA', 6],
        
        ['نحو', 'Grammar', 'primary', 'general', '6', 'all', '📝', '#2E8B57', 1],
        ['نصوص', 'Texts', 'primary', 'general', '6', 'all', '📖', '#4682B4', 2],
        ['قراءة', 'Reading', 'primary', 'general', '6', 'all', '📚', '#FF6347', 3],
        ['إملاء', 'Dictation', 'primary', 'general', '6', 'all', '✍️', '#9370DB', 4],
        ['خط', 'Calligraphy', 'primary', 'general', '6', 'all', '🖋️', '#DAA520', 5],
        ['تعبير', 'Expression', 'primary', 'general', '6', 'all', '💭', '#20B2AA', 6],
        
        // الإعدادي الأزهري
        ['نحو', 'Grammar', 'preparatory', 'azhari', '1', 'all', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'preparatory', 'azhari', '1', 'all', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'preparatory', 'azhari', '1', 'all', '📖', '#4682B4', 3],
        ['إملاء', 'Dictation', 'preparatory', 'azhari', '1', 'all', '✍️', '#9370DB', 4],
        ['إنشاء', 'Composition', 'preparatory', 'azhari', '1', 'all', '📄', '#FF8C00', 5],
        ['خط', 'Calligraphy', 'preparatory', 'azhari', '1', 'all', '🖋️', '#DAA520', 6],
        
        // الثاني والثالث الإعدادي الأزهري (مع الصرف)
        ['نحو', 'Grammar', 'preparatory', 'azhari', '2', 'all', '📝', '#2E8B57', 1],
        ['صرف', 'Morphology', 'preparatory', 'azhari', '2', 'all', '🔤', '#8B4513', 2],
        ['مطالعة', 'Reading', 'preparatory', 'azhari', '2', 'all', '📚', '#FF6347', 3],
        ['نصوص', 'Texts', 'preparatory', 'azhari', '2', 'all', '📖', '#4682B4', 4],
        ['إملاء', 'Dictation', 'preparatory', 'azhari', '2', 'all', '✍️', '#9370DB', 5],
        ['إنشاء', 'Composition', 'preparatory', 'azhari', '2', 'all', '📄', '#FF8C00', 6],
        ['خط', 'Calligraphy', 'preparatory', 'azhari', '2', 'all', '🖋️', '#DAA520', 7],
        
        ['نحو', 'Grammar', 'preparatory', 'azhari', '3', 'all', '📝', '#2E8B57', 1],
        ['صرف', 'Morphology', 'preparatory', 'azhari', '3', 'all', '🔤', '#8B4513', 2],
        ['مطالعة', 'Reading', 'preparatory', 'azhari', '3', 'all', '📚', '#FF6347', 3],
        ['نصوص', 'Texts', 'preparatory', 'azhari', '3', 'all', '📖', '#4682B4', 4],
        ['إملاء', 'Dictation', 'preparatory', 'azhari', '3', 'all', '✍️', '#9370DB', 5],
        ['إنشاء', 'Composition', 'preparatory', 'azhari', '3', 'all', '📄', '#FF8C00', 6],
        ['خط', 'Calligraphy', 'preparatory', 'azhari', '3', 'all', '🖋️', '#DAA520', 7],
        
        // الإعدادي العام
        ['نحو', 'Grammar', 'preparatory', 'general', '1', 'all', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'preparatory', 'general', '1', 'all', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'preparatory', 'general', '1', 'all', '📖', '#4682B4', 3],
        ['إملاء', 'Dictation', 'preparatory', 'general', '1', 'all', '✍️', '#9370DB', 4],
        ['إنشاء', 'Composition', 'preparatory', 'general', '1', 'all', '📄', '#FF8C00', 5],
        ['خط', 'Calligraphy', 'preparatory', 'general', '1', 'all', '🖋️', '#DAA520', 6],
        
        ['نحو', 'Grammar', 'preparatory', 'general', '2', 'all', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'preparatory', 'general', '2', 'all', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'preparatory', 'general', '2', 'all', '📖', '#4682B4', 3],
        ['إملاء', 'Dictation', 'preparatory', 'general', '2', 'all', '✍️', '#9370DB', 4],
        ['إنشاء', 'Composition', 'preparatory', 'general', '2', 'all', '📄', '#FF8C00', 5],
        ['خط', 'Calligraphy', 'preparatory', 'general', '2', 'all', '🖋️', '#DAA520', 6],
        
        ['نحو', 'Grammar', 'preparatory', 'general', '3', 'all', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'preparatory', 'general', '3', 'all', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'preparatory', 'general', '3', 'all', '📖', '#4682B4', 3],
        ['إملاء', 'Dictation', 'preparatory', 'general', '3', 'all', '✍️', '#9370DB', 4],
        ['إنشاء', 'Composition', 'preparatory', 'general', '3', 'all', '📄', '#FF8C00', 5],
        ['خط', 'Calligraphy', 'preparatory', 'general', '3', 'all', '🖋️', '#DAA520', 6],
        
        // الثانوي العام - علمي
        ['نحو', 'Grammar', 'secondary', 'general', '1', 'scientific', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'secondary', 'general', '1', 'scientific', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'secondary', 'general', '1', 'scientific', '📖', '#4682B4', 3],
        ['إنشاء', 'Composition', 'secondary', 'general', '1', 'scientific', '📄', '#FF8C00', 4],
        ['قصة', 'Story', 'secondary', 'general', '1', 'scientific', '📚', '#8A2BE2', 5],
        
        ['نحو', 'Grammar', 'secondary', 'general', '2', 'scientific', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'secondary', 'general', '2', 'scientific', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'secondary', 'general', '2', 'scientific', '📖', '#4682B4', 3],
        ['إنشاء', 'Composition', 'secondary', 'general', '2', 'scientific', '📄', '#FF8C00', 4],
        ['قصة', 'Story', 'secondary', 'general', '2', 'scientific', '📚', '#8A2BE2', 5],
        
        ['نحو', 'Grammar', 'secondary', 'general', '3', 'scientific', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'secondary', 'general', '3', 'scientific', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'secondary', 'general', '3', 'scientific', '📖', '#4682B4', 3],
        ['إنشاء', 'Composition', 'secondary', 'general', '3', 'scientific', '📄', '#FF8C00', 4],
        ['قصة', 'Story', 'secondary', 'general', '3', 'scientific', '📚', '#8A2BE2', 5],
        
        // الثانوي العام - أدبي
        ['نحو', 'Grammar', 'secondary', 'general', '1', 'literary', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'secondary', 'general', '1', 'literary', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'secondary', 'general', '1', 'literary', '📖', '#4682B4', 3],
        ['إنشاء', 'Composition', 'secondary', 'general', '1', 'literary', '📄', '#FF8C00', 4],
        ['قصة', 'Story', 'secondary', 'general', '1', 'literary', '📚', '#8A2BE2', 5],
        
        ['نحو', 'Grammar', 'secondary', 'general', '2', 'literary', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'secondary', 'general', '2', 'literary', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'secondary', 'general', '2', 'literary', '📖', '#4682B4', 3],
        ['إنشاء', 'Composition', 'secondary', 'general', '2', 'literary', '📄', '#FF8C00', 4],
        ['قصة', 'Story', 'secondary', 'general', '2', 'literary', '📚', '#8A2BE2', 5],
        
        ['نحو', 'Grammar', 'secondary', 'general', '3', 'literary', '📝', '#2E8B57', 1],
        ['مطالعة', 'Reading', 'secondary', 'general', '3', 'literary', '📚', '#FF6347', 2],
        ['نصوص', 'Texts', 'secondary', 'general', '3', 'literary', '📖', '#4682B4', 3],
        ['إنشاء', 'Composition', 'secondary', 'general', '3', 'literary', '📄', '#FF8C00', 4],
        ['قصة', 'Story', 'secondary', 'general', '3', 'literary', '📚', '#8A2BE2', 5],
        
        // الثانوي الأزهري
        ['نحو', 'Grammar', 'secondary', 'azhari', '1', 'all', '📝', '#2E8B57', 1],
        ['صرف', 'Morphology', 'secondary', 'azhari', '1', 'all', '🔤', '#8B4513', 2],
        ['مطالعة', 'Reading', 'secondary', 'azhari', '1', 'all', '📚', '#FF6347', 3],
        ['بلاغة', 'Rhetoric', 'secondary', 'azhari', '1', 'all', '🎭', '#DC143C', 4],
        ['أدب', 'Literature', 'secondary', 'azhari', '1', 'all', '📜', '#4B0082', 5],
        ['نصوص', 'Texts', 'secondary', 'azhari', '1', 'all', '📖', '#4682B4', 6],
        ['إنشاء', 'Composition', 'secondary', 'azhari', '1', 'all', '📄', '#FF8C00', 7],
        
        // الثاني الثانوي أزهري (مع عروض وقافية)
        ['نحو', 'Grammar', 'secondary', 'azhari', '2', 'all', '📝', '#2E8B57', 1],
        ['صرف', 'Morphology', 'secondary', 'azhari', '2', 'all', '🔤', '#8B4513', 2],
        ['مطالعة', 'Reading', 'secondary', 'azhari', '2', 'all', '📚', '#FF6347', 3],
        ['بلاغة', 'Rhetoric', 'secondary', 'azhari', '2', 'all', '🎭', '#DC143C', 4],
        ['أدب', 'Literature', 'secondary', 'azhari', '2', 'all', '📜', '#4B0082', 5],
        ['نصوص', 'Texts', 'secondary', 'azhari', '2', 'all', '📖', '#4682B4', 6],
        ['إنشاء', 'Composition', 'secondary', 'azhari', '2', 'all', '📄', '#FF8C00', 7],
        ['عروض', 'Prosody', 'secondary', 'azhari', '2', 'all', '🎵', '#228B22', 8],
        ['قافية', 'Rhyme', 'secondary', 'azhari', '2', 'all', '🎶', '#32CD32', 9],
        
        ['نحو', 'Grammar', 'secondary', 'azhari', '3', 'all', '📝', '#2E8B57', 1],
        ['صرف', 'Morphology', 'secondary', 'azhari', '3', 'all', '🔤', '#8B4513', 2],
        ['مطالعة', 'Reading', 'secondary', 'azhari', '3', 'all', '📚', '#FF6347', 3],
        ['بلاغة', 'Rhetoric', 'secondary', 'azhari', '3', 'all', '🎭', '#DC143C', 4],
        ['أدب', 'Literature', 'secondary', 'azhari', '3', 'all', '📜', '#4B0082', 5],
        ['نصوص', 'Texts', 'secondary', 'azhari', '3', 'all', '📖', '#4682B4', 6],
        ['إنشاء', 'Composition', 'secondary', 'azhari', '3', 'all', '📄', '#FF8C00', 7],
    ];
    
    $stmt = $db->prepare("INSERT INTO curriculum_subjects (name, name_en, education_level, education_type, grade, specialization, icon, color, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $count = 0;
    foreach ($subjects as $subject) {
        $stmt->execute($subject);
        $count++;
    }
    
    echo "<p>✅ تم إدخال {$count} قسم بنجاح</p>";
    
    // عرض الإحصائيات
    $stmt = $db->query("SELECT COUNT(*) as total FROM curriculum_subjects");
    $total = $stmt->fetchColumn();
    
    echo "<h3>النتائج النهائية:</h3>";
    echo "<p>✅ إجمالي الأقسام: {$total}</p>";
    
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 تم إصلاح النظام بنجاح!</p>";
    
    echo "<hr>";
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='../test_curriculum_system.php'>اختبار النظام</a></li>";
    echo "<li><a href='../page/curriculum.php'>صفحة المنهج للطلاب</a></li>";
    echo "<li><a href='../admin/curriculum_subjects.php'>إدارة الأقسام</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: #f5f5f5;
        direction: rtl;
    }
    
    h2, h3 {
        color: #333;
    }
    
    p {
        margin: 10px 0;
    }
    
    a {
        color: #4682B4;
        text-decoration: none;
        font-weight: bold;
    }
    
    a:hover {
        text-decoration: underline;
    }
    
    ul {
        background: white;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    li {
        margin: 10px 0;
    }
</style>
