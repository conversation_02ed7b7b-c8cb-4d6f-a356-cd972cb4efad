<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/ExamManager.php';
require_once __DIR__ . '/../includes/SecurityHelper.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Check admin permissions
if (!SecurityHelper::checkAdminPermission('admin')) {
    header('Location: index.php?error=insufficient_permissions');
    exit;
}

$examManager = new ExamManager();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                // Sanitize input data
                $data = SecurityHelper::sanitizeInput([
                    'exam_name' => $_POST['exam_name'] ?? '',
                    'subject' => $_POST['subject'] ?? '',
                    'exam_date' => $_POST['exam_date'] ?? '',
                    'exam_time' => $_POST['exam_time'] ?? '',
                    'duration_minutes' => $_POST['duration_minutes'] ?? 120,
                    'location' => $_POST['location'] ?? '',
                    'instructions' => $_POST['instructions'] ?? '',
                    'education_level' => $_POST['education_level'] ?? '',
                    'education_type' => $_POST['education_type'] ?? '',
                    'grade' => $_POST['grade'] ?? '',
                    'specialization' => isset($_POST['specialization']) ? $_POST['specialization'] : null,
                    'created_by' => $_SESSION['admin_id']
                ]);

                // Validate data
                $validationErrors = SecurityHelper::validateExamData($data);
                if (!empty($validationErrors)) {
                    $message = implode('<br>', $validationErrors);
                    $messageType = 'error';
                } elseif ($examManager->addExam($data)) {
                    $message = 'تم إضافة الامتحان بنجاح';
                    $messageType = 'success';
                    // SecurityHelper::logAdminActivity('add_exam', ['exam_name' => $data['exam_name']]);
                } else {
                    $message = 'حدث خطأ أثناء إضافة الامتحان';
                    $messageType = 'error';
                }
                break;
                
            case 'edit':
                $data = SecurityHelper::sanitizeInput([
                    'exam_name' => $_POST['exam_name'] ?? '',
                    'subject' => $_POST['subject'] ?? '',
                    'exam_date' => $_POST['exam_date'] ?? '',
                    'exam_time' => $_POST['exam_time'] ?? '',
                    'duration_minutes' => $_POST['duration_minutes'] ?? 120,
                    'location' => $_POST['location'] ?? '',
                    'instructions' => $_POST['instructions'] ?? '',
                    'education_level' => $_POST['education_level'] ?? '',
                    'education_type' => $_POST['education_type'] ?? '',
                    'grade' => $_POST['grade'] ?? '',
                    'specialization' => isset($_POST['specialization']) ? $_POST['specialization'] : null
                ]);

                $validationErrors = SecurityHelper::validateExamData($data);
                if (!empty($validationErrors)) {
                    $message = implode('<br>', $validationErrors);
                    $messageType = 'error';
                } elseif ($examManager->updateExam($_POST['exam_id'], $data)) {
                    $message = 'تم تحديث الامتحان بنجاح';
                    $messageType = 'success';
                    // SecurityHelper::logAdminActivity('update_exam', ['exam_id' => $_POST['exam_id']]);
                } else {
                    $message = 'حدث خطأ أثناء تحديث الامتحان';
                    $messageType = 'error';
                }
                break;
                
            case 'delete':
                if ($examManager->deleteExam($_POST['exam_id'])) {
                    $message = 'تم حذف الامتحان بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء حذف الامتحان';
                    $messageType = 'error';
                }
                break;
                
            case 'toggle_status':
                if ($examManager->toggleExamStatus($_POST['exam_id'])) {
                    $message = 'تم تغيير حالة الامتحان بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تغيير حالة الامتحان';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get filters
$filters = [];
if (!empty($_GET['education_level'])) $filters['education_level'] = $_GET['education_level'];
if (!empty($_GET['education_type'])) $filters['education_type'] = $_GET['education_type'];
if (!empty($_GET['grade'])) $filters['grade'] = $_GET['grade'];
if (!empty($_GET['date_from'])) $filters['date_from'] = $_GET['date_from'];
if (!empty($_GET['date_to'])) $filters['date_to'] = $_GET['date_to'];

$exams = $examManager->getAllExams($filters);
$totalExams = $examManager->getTotalExamsCount();

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الامتحانات - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <style>
        .exam-form {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(70, 130, 180, 0.1);
        }
        
        .exam-form h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(70, 130, 180, 0.2);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #87CEEB;
            box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
        
        .exams-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(70, 130, 180, 0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .filters-section {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid rgba(70, 130, 180, 0.1);
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-toggle {
            background: #ffc107;
            color: #212529;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .exam-form {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Add Exam Form -->
                <div class="exam-form">
                    <h3>إضافة امتحان جديد</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="add">
                        <input type="hidden" name="csrf_token" value="<?php echo SecurityHelper::generateCSRFToken(); ?>">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="exam_name">اسم الامتحان *</label>
                                <input type="text" id="exam_name" name="exam_name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">المادة *</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="exam_date">تاريخ الامتحان *</label>
                                <input type="date" id="exam_date" name="exam_date" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="exam_time">وقت الامتحان *</label>
                                <input type="time" id="exam_time" name="exam_time" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="duration_minutes">مدة الامتحان (بالدقائق) *</label>
                                <input type="number" id="duration_minutes" name="duration_minutes" value="120" min="30" max="300" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="location">المكان</label>
                                <input type="text" id="location" name="location">
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="education_level">المرحلة التعليمية *</label>
                                <select id="education_level" name="education_level" required>
                                    <option value="">اختر المرحلة</option>
                                    <option value="primary">ابتدائي</option>
                                    <option value="preparatory">إعدادي</option>
                                    <option value="secondary">ثانوي</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="education_type">نوع التعليم *</label>
                                <select id="education_type" name="education_type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="general">عام</option>
                                    <option value="azhari">أزهري</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="grade">الصف *</label>
                                <select id="grade" name="grade" required>
                                    <option value="">اختر الصف</option>
                                </select>
                            </div>

                            <div id="specialization_container" class="form-group" style="display: none;">
                                <label for="specialization">التخصص *</label>
                                <select id="specialization" name="specialization">
                                    <option value="">اختر التخصص</option>
                                    <option value="scientific">علمي</option>
                                    <option value="literary">أدبي</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="instructions">تعليمات الامتحان</label>
                            <textarea id="instructions" name="instructions" rows="4" placeholder="أدخل تعليمات الامتحان هنا..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn-primary">إضافة الامتحان</button>
                    </form>
                </div>

                <!-- Exams List -->
                <div class="exams-table">
                    <div class="table-header">
                        قائمة الامتحانات (<?php echo count($exams); ?> امتحان)
                    </div>

                    <!-- Filters -->
                    <div class="filters-section">
                        <form method="GET">
                            <div class="filters-grid">
                                <div class="form-group">
                                    <label for="filter_education_level">المرحلة التعليمية</label>
                                    <select id="filter_education_level" name="education_level">
                                        <option value="">جميع المراحل</option>
                                        <option value="primary" <?php echo ($_GET['education_level'] ?? '') === 'primary' ? 'selected' : ''; ?>>ابتدائي</option>
                                        <option value="preparatory" <?php echo ($_GET['education_level'] ?? '') === 'preparatory' ? 'selected' : ''; ?>>إعدادي</option>
                                        <option value="secondary" <?php echo ($_GET['education_level'] ?? '') === 'secondary' ? 'selected' : ''; ?>>ثانوي</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_education_type">نوع التعليم</label>
                                    <select id="filter_education_type" name="education_type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="general" <?php echo ($_GET['education_type'] ?? '') === 'general' ? 'selected' : ''; ?>>عام</option>
                                        <option value="azhari" <?php echo ($_GET['education_type'] ?? '') === 'azhari' ? 'selected' : ''; ?>>أزهري</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="filter_grade">الصف</label>
                                    <input type="text" id="filter_grade" name="grade" value="<?php echo htmlspecialchars($_GET['grade'] ?? ''); ?>" placeholder="الصف">
                                </div>

                                <div class="form-group">
                                    <label for="filter_date_from">من تاريخ</label>
                                    <input type="date" id="filter_date_from" name="date_from" value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="filter_date_to">إلى تاريخ</label>
                                    <input type="date" id="filter_date_to" name="date_to" value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn-primary">تصفية</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>اسم الامتحان</th>
                                    <th>المادة</th>
                                    <th>التاريخ والوقت</th>
                                    <th>المدة</th>
                                    <th>المرحلة/النوع/الصف</th>
                                    <th>المكان</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($exams)): ?>
                                    <tr>
                                        <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                                            لا توجد امتحانات مطابقة للمعايير المحددة
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($exams as $exam): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($exam['exam_name']); ?></strong>
                                                <?php if ($exam['instructions']): ?>
                                                    <br><small style="color: #6c757d;"><?php echo htmlspecialchars(substr($exam['instructions'], 0, 50)) . (strlen($exam['instructions']) > 50 ? '...' : ''); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($exam['subject']); ?></td>
                                            <td>
                                                <?php echo date('Y/m/d', strtotime($exam['exam_date'])); ?><br>
                                                <small><?php echo date('H:i', strtotime($exam['exam_time'])); ?></small>
                                            </td>
                                            <td><?php echo $exam['duration_minutes']; ?> دقيقة</td>
                                            <td>
                                                <?php
                                                $educationLevels = ['primary' => 'ابتدائي', 'preparatory' => 'إعدادي', 'secondary' => 'ثانوي'];
                                                $educationTypes = ['general' => 'عام', 'azhari' => 'أزهري'];
                                                echo $educationLevels[$exam['education_level']] . ' / ' . $educationTypes[$exam['education_type']] . ' / ' . $exam['grade'];
                                                if ($exam['specialization']) {
                                                    $specializations = ['scientific' => 'علمي', 'literary' => 'أدبي'];
                                                    echo ' (' . $specializations[$exam['specialization']] . ')';
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($exam['location'] ?: 'غير محدد'); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo $exam['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                                    <?php echo $exam['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-sm btn-edit" onclick="editExam(<?php echo $exam['id']; ?>)">تعديل</button>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من تغيير حالة هذا الامتحان؟')">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="exam_id" value="<?php echo $exam['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-toggle">
                                                            <?php echo $exam['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>
                                                        </button>
                                                    </form>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الامتحان؟')">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="exam_id" value="<?php echo $exam['id']; ?>">
                                                        <button type="submit" class="btn-sm btn-delete">حذف</button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Exam Modal -->
    <div id="editExamModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل الامتحان</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editExamForm" method="POST">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="exam_id" id="edit_exam_id">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit_exam_name">اسم الامتحان *</label>
                            <input type="text" id="edit_exam_name" name="exam_name" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_subject">المادة *</label>
                            <input type="text" id="edit_subject" name="subject" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_exam_date">تاريخ الامتحان *</label>
                            <input type="date" id="edit_exam_date" name="exam_date" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_exam_time">وقت الامتحان *</label>
                            <input type="time" id="edit_exam_time" name="exam_time" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_duration_minutes">مدة الامتحان (بالدقائق) *</label>
                            <input type="number" id="edit_duration_minutes" name="duration_minutes" min="30" max="300" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_location">المكان</label>
                            <input type="text" id="edit_location" name="location">
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit_education_level">المرحلة التعليمية *</label>
                            <select id="edit_education_level" name="education_level" required>
                                <option value="">اختر المرحلة</option>
                                <option value="primary">ابتدائي</option>
                                <option value="preparatory">إعدادي</option>
                                <option value="secondary">ثانوي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_education_type">نوع التعليم *</label>
                            <select id="edit_education_type" name="education_type" required>
                                <option value="">اختر النوع</option>
                                <option value="general">عام</option>
                                <option value="azhari">أزهري</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_grade">الصف *</label>
                            <select id="edit_grade" name="grade" required>
                                <option value="">اختر الصف</option>
                            </select>
                        </div>

                        <div id="edit_specialization_container" class="form-group" style="display: none;">
                            <label for="edit_specialization">التخصص *</label>
                            <select id="edit_specialization" name="specialization">
                                <option value="">اختر التخصص</option>
                                <option value="scientific">علمي</option>
                                <option value="literary">أدبي</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_instructions">تعليمات الامتحان</label>
                        <textarea id="edit_instructions" name="instructions" rows="4"></textarea>
                    </div>

                    <div class="modal-buttons">
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                        <button type="button" class="btn-secondary" onclick="closeEditModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/education-selector.js"></script>
    <script>
        function editExam(examId) {
            // Fetch exam data via AJAX
            fetch(`get_exam.php?id=${examId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const exam = data.exam;
                        document.getElementById('edit_exam_id').value = exam.id;
                        document.getElementById('edit_exam_name').value = exam.exam_name;
                        document.getElementById('edit_subject').value = exam.subject;
                        document.getElementById('edit_exam_date').value = exam.exam_date;
                        document.getElementById('edit_exam_time').value = exam.exam_time;
                        document.getElementById('edit_duration_minutes').value = exam.duration_minutes;
                        document.getElementById('edit_location').value = exam.location || '';
                        document.getElementById('edit_instructions').value = exam.instructions || '';

                        // Set education values using the education selector
                        EducationSelector.setValues('edit_', exam.education_level, exam.education_type, exam.grade, exam.specialization);

                        document.getElementById('editExamModal').style.display = 'block';
                    } else {
                        alert('حدث خطأ في تحميل بيانات الامتحان');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في تحميل بيانات الامتحان');
                });
        }

        function closeEditModal() {
            document.getElementById('editExamModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editExamModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
    <script src="js/admin-modern.js"></script>
</body>
</html>
