<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "يجب تسجيل دخول الإدارة أولاً<br>";
    echo "<a href=\"login.php\">تسجيل دخول الإدارة</a>";
    exit;
}

$adminId = $_SESSION['admin_id'];
$userManager = new UserManager();
$notificationManager = new NotificationManager();

echo "<h2>اختبار نظام الإشعارات للإدارة</h2>";
echo "معرف الإدارة: " . $adminId . "<br><br>";

try {
    // 1. Test getting all users
    echo "<h3>1. اختبار الحصول على المستخدمين:</h3>";
    $allUsers = $userManager->getAllActiveUsers();
    echo "عدد المستخدمين النشطين: " . count($allUsers) . "<br>";
    
    if (!empty($allUsers)) {
        echo "أول 3 مستخدمين:<br>";
        foreach (array_slice($allUsers, 0, 3) as $user) {
            $displayName = trim($user['first_name'] . ' ' . $user['second_name']) ?: $user['username'];
            echo "- ID: {$user['id']}, الاسم: {$displayName}, البريد: {$user['email']}<br>";
        }
    }
    
    // 2. Test creating notification for first user
    if (!empty($allUsers)) {
        echo "<h3>2. اختبار إنشاء إشعار:</h3>";
        $firstUser = $allUsers[0];
        $testTitle = "إشعار تجريبي من الإدارة " . date('H:i:s');
        $testMessage = "هذا إشعار تجريبي تم إرساله من لوحة التحكم";
        
        $result = $notificationManager->createNotification(
            $firstUser['id'],
            $testTitle,
            $testMessage,
            'info'
        );
        
        if ($result) {
            echo "✅ تم إنشاء الإشعار للمستخدم: " . $firstUser['username'] . "<br>";
        } else {
            echo "❌ فشل في إنشاء الإشعار<br>";
        }
    }
    
    // 3. Test bulk notification creation
    if (count($allUsers) >= 2) {
        echo "<h3>3. اختبار الإشعارات المجمعة:</h3>";
        $userIds = array_slice(array_column($allUsers, 'id'), 0, 2);
        
        $bulkResult = $notificationManager->createBulkNotifications(
            $userIds,
            "إشعار جماعي تجريبي",
            "هذا إشعار تم إرساله لعدة مستخدمين",
            'warning'
        );
        
        echo "تم إرسال الإشعار إلى {$bulkResult} مستخدم<br>";
    }
    
    // 4. Test search users API
    echo "<h3>4. اختبار API البحث:</h3>";
    $searchQuery = "test";
    $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/../api/search_users.php?q=' . urlencode($searchQuery);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Cookie: ' . $_SERVER['HTTP_COOKIE']
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "✅ API البحث يعمل - وجد " . count($data['data']) . " نتيجة<br>";
        } else {
            echo "❌ API البحث لا يعمل: " . $response . "<br>";
        }
    } else {
        echo "❌ فشل في الاتصال بـ API البحث<br>";
    }
    
    // 5. Test notification statistics
    echo "<h3>5. إحصائيات الإشعارات:</h3>";
    $db = Database::getInstance()->getConnection();
    $statsStmt = $db->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
            SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success,
            SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as error,
            SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warning,
            SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info
        FROM notifications
    ");
    $statsStmt->execute();
    $stats = $statsStmt->fetch();
    
    echo "إجمالي الإشعارات: " . $stats['total'] . "<br>";
    echo "غير مقروءة: " . $stats['unread'] . "<br>";
    echo "نجاح: " . $stats['success'] . "<br>";
    echo "خطأ: " . $stats['error'] . "<br>";
    echo "تحذير: " . $stats['warning'] . "<br>";
    echo "معلومات: " . $stats['info'] . "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<br><br>";
echo "<a href='manage_notifications.php'>إدارة الإشعارات</a><br>";
echo "<a href='send_notifications.php'>إرسال إشعار جديد</a><br>";
echo "<a href=\"index.php\">العودة إلى الرئيسية</a>";
?>
