<?php
session_start();
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json');

if (!isset($_GET['q']) || empty(trim($_GET['q']))) {
    echo json_encode(['success' => false, 'message' => 'استعلام البحث مطلوب']);
    exit;
}

try {
    $query = trim($_GET['q']);
    $db = Database::getInstance()->getConnection();
    
    $results = [];
    
    // Search in users
    $userStmt = $db->prepare("
        SELECT 'user' as type, id, CONCAT(first_name, ' ', second_name) as title, username as subtitle, 'users.php' as url
        FROM users 
        WHERE first_name LIKE :query OR second_name LIKE :query OR username LIKE :query OR email LIKE :query
        LIMIT 5
    ");
    $searchQuery = "%{$query}%";
    $userStmt->bindParam(':query', $searchQuery);
    $userStmt->execute();
    $results = array_merge($results, $userStmt->fetchAll(PDO::FETCH_ASSOC));
    
    // Search in courses
    $courseStmt = $db->prepare("
        SELECT 'course' as type, id, title, description as subtitle, 'courses.php' as url
        FROM courses 
        WHERE title LIKE :query OR description LIKE :query
        LIMIT 5
    ");
    $courseStmt->bindParam(':query', $searchQuery);
    $courseStmt->execute();
    $results = array_merge($results, $courseStmt->fetchAll(PDO::FETCH_ASSOC));
    
    // Search in notifications
    $notificationStmt = $db->prepare("
        SELECT 'notification' as type, id, title, message as subtitle, 'manage_notifications.php' as url
        FROM notifications 
        WHERE title LIKE :query OR message LIKE :query
        LIMIT 5
    ");
    $notificationStmt->bindParam(':query', $searchQuery);
    $notificationStmt->execute();
    $results = array_merge($results, $notificationStmt->fetchAll(PDO::FETCH_ASSOC));
    
    echo json_encode([
        'success' => true,
        'results' => $results,
        'query' => $query
    ]);
    
} catch (Exception $e) {
    error_log("Error in search.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في البحث'
    ]);
}
?>
