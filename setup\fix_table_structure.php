<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 إصلاح هيكل الجداول</h2>";
    
    $success = 0;
    $errors = 0;
    
    // 1. إصلاح جدول activation_codes
    echo "<h3>🔧 إصلاح جدول activation_codes</h3>";
    
    // Check if is_used column exists
    $stmt = $db->query("SHOW COLUMNS FROM activation_codes LIKE 'is_used'");
    if ($stmt->rowCount() == 0) {
        echo "<p>➕ إضافة عمود is_used...</p>";
        $db->exec("ALTER TABLE activation_codes ADD COLUMN is_used BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود'");
        echo "<p style='color: green;'>✅ تم إضافة عمود is_used</p>";
        $success++;
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود is_used موجود بالفعل</p>";
    }
    
    // Check if used_by column exists
    $stmt = $db->query("SHOW COLUMNS FROM activation_codes LIKE 'used_by'");
    if ($stmt->rowCount() == 0) {
        echo "<p>➕ إضافة عمود used_by...</p>";
        $db->exec("ALTER TABLE activation_codes ADD COLUMN used_by INT NULL COMMENT 'المستخدم الذي استخدم الكود'");
        $db->exec("ALTER TABLE activation_codes ADD FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود used_by</p>";
        $success++;
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود used_by موجود بالفعل</p>";
    }
    
    // Check if used_at column exists
    $stmt = $db->query("SHOW COLUMNS FROM activation_codes LIKE 'used_at'");
    if ($stmt->rowCount() == 0) {
        echo "<p>➕ إضافة عمود used_at...</p>";
        $db->exec("ALTER TABLE activation_codes ADD COLUMN used_at TIMESTAMP NULL COMMENT 'تاريخ الاستخدام'");
        echo "<p style='color: green;'>✅ تم إضافة عمود used_at</p>";
        $success++;
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود used_at موجود بالفعل</p>";
    }
    
    // 2. إصلاح جدول subscription_plans
    echo "<h3>🔧 إصلاح جدول subscription_plans</h3>";
    
    // Check if name column exists
    $stmt = $db->query("SHOW COLUMNS FROM subscription_plans LIKE 'name'");
    if ($stmt->rowCount() == 0) {
        echo "<p>➕ إضافة عمود name...</p>";
        $db->exec("ALTER TABLE subscription_plans ADD COLUMN name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة'");
        echo "<p style='color: green;'>✅ تم إضافة عمود name</p>";
        $success++;
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود name موجود بالفعل</p>";
    }
    
    // Check if other required columns exist
    $required_columns = [
        'name_en' => "VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية'",
        'description' => "TEXT NULL COMMENT 'وصف الخطة'",
        'price' => "DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر الأصلي'",
        'discount_percentage' => "INT DEFAULT 0 COMMENT 'نسبة الخصم'",
        'discounted_price' => "DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر بعد الخصم'",
        'duration_days' => "INT NOT NULL DEFAULT 30 COMMENT 'مدة الاشتراك بالأيام'",
        'features' => "JSON NULL COMMENT 'مميزات الخطة'",
        'icon' => "VARCHAR(10) DEFAULT '📚' COMMENT 'أيقونة الخطة'",
        'color' => "VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة'",
        'is_popular' => "BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة شائعة'",
        'is_active' => "BOOLEAN DEFAULT TRUE COMMENT 'هل الخطة نشطة'",
        'sort_order' => "INT DEFAULT 0 COMMENT 'ترتيب العرض'",
        'created_by' => "INT NOT NULL DEFAULT 1 COMMENT 'منشئ الخطة'"
    ];
    
    foreach ($required_columns as $column => $definition) {
        $stmt = $db->query("SHOW COLUMNS FROM subscription_plans LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            echo "<p>➕ إضافة عمود $column...</p>";
            $db->exec("ALTER TABLE subscription_plans ADD COLUMN $column $definition");
            echo "<p style='color: green;'>✅ تم إضافة عمود $column</p>";
            $success++;
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود $column موجود بالفعل</p>";
        }
    }
    
    // 3. إصلاح جدول user_subscriptions
    echo "<h3>🔧 إصلاح جدول user_subscriptions</h3>";
    
    $user_sub_columns = [
        'user_id' => "INT NOT NULL COMMENT 'معرف المستخدم'",
        'plan_id' => "INT NOT NULL COMMENT 'معرف الخطة'",
        'activation_code' => "VARCHAR(50) NULL COMMENT 'كود التفعيل المستخدم'",
        'payment_method' => "ENUM('code', 'fawry', 'visa', 'wallet') NOT NULL DEFAULT 'code' COMMENT 'طريقة الدفع'",
        'payment_status' => "ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed' COMMENT 'حالة الدفع'",
        'amount_paid' => "DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ المدفوع'",
        'start_date' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ بداية الاشتراك'",
        'end_date' => "TIMESTAMP NOT NULL COMMENT 'تاريخ انتهاء الاشتراك'",
        'cancelled_at' => "TIMESTAMP NULL COMMENT 'تاريخ الإلغاء'",
        'cancellation_reason' => "TEXT NULL COMMENT 'سبب الإلغاء'"
    ];
    
    foreach ($user_sub_columns as $column => $definition) {
        $stmt = $db->query("SHOW COLUMNS FROM user_subscriptions LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            echo "<p>➕ إضافة عمود $column...</p>";
            $db->exec("ALTER TABLE user_subscriptions ADD COLUMN $column $definition");
            echo "<p style='color: green;'>✅ تم إضافة عمود $column</p>";
            $success++;
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود $column موجود بالفعل</p>";
        }
    }
    
    // 4. إصلاح جدول course_activation_codes
    echo "<h3>🔧 إصلاح جدول course_activation_codes</h3>";
    
    $course_code_columns = [
        'code' => "VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود التفعيل'",
        'course_id' => "INT NOT NULL COMMENT 'معرف الكورس'",
        'is_used' => "BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود'",
        'used_by' => "INT NULL COMMENT 'المستخدم الذي استخدم الكود'",
        'used_at' => "TIMESTAMP NULL COMMENT 'تاريخ الاستخدام'",
        'expires_at' => "TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الكود'",
        'created_by' => "INT NOT NULL COMMENT 'منشئ الكود'",
        'notes' => "TEXT NULL COMMENT 'ملاحظات'"
    ];
    
    foreach ($course_code_columns as $column => $definition) {
        $stmt = $db->query("SHOW COLUMNS FROM course_activation_codes LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            echo "<p>➕ إضافة عمود $column...</p>";
            $db->exec("ALTER TABLE course_activation_codes ADD COLUMN $column $definition");
            echo "<p style='color: green;'>✅ تم إضافة عمود $column</p>";
            $success++;
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود $column موجود بالفعل</p>";
        }
    }
    
    // 5. إضافة Foreign Keys إذا لم تكن موجودة
    echo "<h3>🔗 إضافة Foreign Keys</h3>";
    
    try {
        // Add foreign keys for activation_codes
        $db->exec("ALTER TABLE activation_codes ADD CONSTRAINT fk_activation_plan FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE");
        echo "<p style='color: green;'>✅ تم إضافة foreign key لجدول activation_codes</p>";
        $success++;
    } catch (Exception $e) {
        echo "<p style='color: blue;'>ℹ️ Foreign key موجود بالفعل أو خطأ: " . $e->getMessage() . "</p>";
    }
    
    try {
        // Add foreign keys for user_subscriptions
        $db->exec("ALTER TABLE user_subscriptions ADD CONSTRAINT fk_user_sub_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE");
        $db->exec("ALTER TABLE user_subscriptions ADD CONSTRAINT fk_user_sub_plan FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE");
        echo "<p style='color: green;'>✅ تم إضافة foreign keys لجدول user_subscriptions</p>";
        $success++;
    } catch (Exception $e) {
        echo "<p style='color: blue;'>ℹ️ Foreign keys موجودة بالفعل أو خطأ: " . $e->getMessage() . "</p>";
    }
    
    // 6. إضافة Indexes
    echo "<h3>📊 إضافة Indexes</h3>";
    
    $indexes = [
        "CREATE INDEX idx_activation_code ON activation_codes(code)",
        "CREATE INDEX idx_activation_used ON activation_codes(is_used)",
        "CREATE INDEX idx_plan_active ON subscription_plans(is_active)",
        "CREATE INDEX idx_user_subscription ON user_subscriptions(user_id, end_date)"
    ];
    
    foreach ($indexes as $index_sql) {
        try {
            $db->exec($index_sql);
            echo "<p style='color: green;'>✅ تم إضافة index</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: blue;'>ℹ️ Index موجود بالفعل</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إصلاح هيكل الجداول!</h3>";
    echo "<p>✅ العمليات الناجحة: {$success}</p>";
    echo "<p>❌ العمليات الفاشلة: {$errors}</p>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='test_subscription_system.php' style='color: #4682B4; font-weight: bold;'>إعادة تشغيل الاختبار</a></li>";
    echo "<li><a href='reset_subscription_system.php' style='color: #4682B4; font-weight: bold;'>إعادة تعيين البيانات</a></li>";
    echo "<li><a href='../admin/subscription_plans.php' style='color: #4682B4; font-weight: bold;'>إدارة الخطط</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        direction: rtl;
        min-height: 100vh;
    }
    
    h2, h3 {
        background: rgba(255, 255, 255, 0.9);
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin: 20px 0;
    }
    
    p {
        background: rgba(255, 255, 255, 0.8);
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #4682B4;
    }
    
    ul {
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        list-style-type: none;
    }
    
    li {
        margin: 15px 0;
        padding: 10px;
        background: rgba(70, 130, 180, 0.1);
        border-radius: 5px;
        border-right: 4px solid #4682B4;
    }
    
    a {
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    a:hover {
        text-decoration: underline;
        transform: translateX(-5px);
    }
    
    hr {
        margin: 30px 0;
        border: none;
        border-top: 2px solid rgba(255, 255, 255, 0.3);
    }
</style>
