<?php
require_once __DIR__ . '/../config/config.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<title>الحصول على Integration IDs من Paymob</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n";
echo ".success { color: green; }\n";
echo ".error { color: red; }\n";
echo ".info { color: blue; }\n";
echo ".integration { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>الحصول على Integration IDs من Paymob</h1>\n";

try {
    $config = PAYMENT_GATEWAYS['paymob'];
    $apiKey = $config['api_key'];
    
    echo "<div class='info'>جاري الحصول على Auth Token...</div><br>\n";
    
    // Get auth token
    $authData = ['api_key' => $apiKey];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://accept.paymob.com/api/auth/tokens');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($authData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception("HTTP Error: $httpCode");
    }
    
    $authResponse = json_decode($response, true);
    
    if (!$authResponse || !isset($authResponse['token'])) {
        throw new Exception("Failed to get auth token: " . $response);
    }
    
    $authToken = $authResponse['token'];
    echo "<div class='success'>✓ تم الحصول على Auth Token بنجاح</div><br>\n";
    
    // Get integrations
    echo "<div class='info'>جاري الحصول على Integration IDs...</div><br>\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://accept.paymob.com/api/ecommerce/integrations');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $authToken,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception("HTTP Error getting integrations: $httpCode");
    }
    
    $integrations = json_decode($response, true);
    
    if (!$integrations) {
        throw new Exception("Failed to get integrations: " . $response);
    }
    
    echo "<div class='success'>✓ تم الحصول على Integration IDs بنجاح</div><br>\n";
    echo "<h2>Integration IDs المتاحة:</h2>\n";
    
    foreach ($integrations as $integration) {
        echo "<div class='integration'>\n";
        echo "<strong>ID:</strong> " . $integration['id'] . "<br>\n";
        echo "<strong>Name:</strong> " . ($integration['name'] ?? 'N/A') . "<br>\n";
        echo "<strong>Type:</strong> " . ($integration['type'] ?? 'N/A') . "<br>\n";
        echo "<strong>Currency:</strong> " . ($integration['currency'] ?? 'N/A') . "<br>\n";
        echo "<strong>Status:</strong> " . ($integration['is_live'] ? 'Live' : 'Test') . "<br>\n";
        echo "</div>\n";
    }
    
    // Find card integration
    $cardIntegration = null;
    foreach ($integrations as $integration) {
        if (stripos($integration['name'] ?? '', 'card') !== false || 
            stripos($integration['type'] ?? '', 'card') !== false ||
            $integration['id'] == 4835055) { // Common card integration ID
            $cardIntegration = $integration;
            break;
        }
    }
    
    if ($cardIntegration) {
        echo "<br><div class='success'><h3>🎯 Integration ID الموصى به للبطاقات:</h3>\n";
        echo "<strong>ID: " . $cardIntegration['id'] . "</strong><br>\n";
        echo "Name: " . ($cardIntegration['name'] ?? 'N/A') . "<br>\n";
        echo "Type: " . ($cardIntegration['type'] ?? 'N/A') . "</div>\n";
        
        echo "<br><div class='info'><h3>📝 تحديث الإعدادات:</h3>\n";
        echo "قم بتحديث integration_id في ملف config/config.php إلى: <strong>" . $cardIntegration['id'] . "</strong></div>\n";
    } else {
        echo "<br><div class='error'>⚠ لم يتم العثور على integration للبطاقات. استخدم أول ID من القائمة أعلاه.</div>\n";
    }
    
} catch (Exception $e) {
    echo "<div class='error'><h2>✗ خطأ: " . $e->getMessage() . "</h2></div>\n";
}

echo "<br><a href='../page/dashboard.php'>العودة للوحة التحكم</a>\n";
echo "</body>\n";
echo "</html>\n";
?>
