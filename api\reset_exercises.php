<?php
header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['week_number']) || !isset($input['course_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    $courseManager = new CourseManager();
    $userId = $_SESSION['user_id'];
    $weekNumber = $input['week_number'];
    $courseId = $input['course_id'];
    
    // Check if user has access to this course
    if (!$courseManager->userHasAccess($userId, $courseId)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول لهذا الكورس']);
        exit;
    }
    
    // Get all exercises for this week
    $stmt = $db->prepare("
        SELECT id FROM course_exercises
        WHERE course_id = ? AND week_number = ?
    ");
    $stmt->execute([$courseId, $weekNumber]);
    $exercises = $stmt->fetchAll();
    
    if (empty($exercises)) {
        echo json_encode([
            'success' => true,
            'message' => 'لا توجد تمارين لهذا الأسبوع أو تم إعادة التعيين بالفعل',
            'exercises_reset' => 0
        ]);
        exit;
    }
    
    // Ensure the exercise attempts table exists
    try {
        $db->query("SELECT 1 FROM user_exercise_attempts LIMIT 1");
    } catch (Exception $e) {
        // Create the table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS user_exercise_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                exercise_id INT NOT NULL,
                user_answer TEXT NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                score DECIMAL(5,2) DEFAULT 0,
                attempt_number INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_exercise (user_id, exercise_id),
                INDEX idx_attempt (user_id, exercise_id, attempt_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        error_log("Created user_exercise_attempts table");
    }

    $db->beginTransaction();

    // Delete all previous answers for these exercises
    $exerciseIds = array_column($exercises, 'id');
    $placeholders = str_repeat('?,', count($exerciseIds) - 1) . '?';

    // Delete from user_exercise_attempts table (the correct table)
    $stmt = $db->prepare("
        DELETE FROM user_exercise_attempts
        WHERE user_id = ? AND exercise_id IN ($placeholders)
    ");
    $deletedRows = $stmt->execute(array_merge([$userId], $exerciseIds));
    $deletedCount = $stmt->rowCount();

    $db->commit();

    error_log("Reset exercises for user $userId, week $weekNumber: deleted $deletedCount rows");
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إعادة تعيين التمارين بنجاح',
        'exercises_reset' => count($exercises),
        'deleted_rows' => $deletedCount
    ]);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error resetting exercises: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
