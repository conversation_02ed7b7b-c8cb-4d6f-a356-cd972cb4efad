<?php
// Get current page name
$currentPage = basename($_SERVER['PHP_SELF']);

// Get admin information
if (isset($_SESSION['admin_id'])) {
    try {
        $adminManager = new AdminManager();
        $adminData = $adminManager->getAdminById($_SESSION['admin_id']);
        $adminName = $adminData['full_name'] ?? 'المدير';
        $adminRole = $adminData['role'] ?? 'مدير';
    } catch (Exception $e) {
        $adminName = $_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'المدير';
        $adminRole = 'مدير';
    }
} else {
    $adminName = 'غير محدد';
    $adminRole = 'غير محدد';
}

// Get notification count
try {
    $db = Database::getInstance()->getConnection();
    $notificationStmt = $db->prepare("
        SELECT COUNT(*) as unread_count
        FROM notifications
        WHERE is_read = 0
    ");
    $notificationStmt->execute();
    $unreadNotifications = $notificationStmt->fetch()['unread_count'] ?? 0;
} catch (Exception $e) {
    $unreadNotifications = 0;
}
?>

<header class="admin-header">
    <div class="header-container">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <span></span>
            <span></span>
            <span></span>
        </button>

        <!-- Logo and Site Title -->
        <div class="header-brand">
            <img src="../img/logo.png" alt="<?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?>" class="header-logo">
            <div class="brand-text">
                <h1><?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></h1>
                <span>لوحة التحكم</span>
            </div>
        </div>

        <!-- Header Actions -->
        <div class="header-actions">
            <!-- Search -->
            <div class="search-container">
                <input type="text" placeholder="البحث..." class="search-input" id="headerSearch">
                <button class="search-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>

            <!-- Notifications -->
            <div class="notification-dropdown">
                <button class="notification-btn" id="notificationBtn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2"/>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <?php if ($unreadNotifications > 0): ?>
                        <span class="notification-badge"><?php echo $unreadNotifications; ?></span>
                    <?php endif; ?>
                </button>
                
                <div class="notification-dropdown-menu" id="notificationDropdown">
                    <div class="notification-header">
                        <h3>الإشعارات</h3>
                        <button class="mark-all-read" onclick="markAllNotificationsRead()">تحديد الكل كمقروء</button>
                    </div>
                    <div class="notification-list" id="notificationList">
                        <div class="loading">جاري التحميل...</div>
                    </div>
                    <div class="notification-footer">
                        <a href="manage_notifications.php">عرض جميع الإشعارات</a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="quick-action-btn" title="إضافة مستخدم جديد" onclick="openModal('addUserModal')">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        <line x1="20" y1="8" x2="20" y2="14" stroke="currentColor" stroke-width="2"/>
                        <line x1="23" y1="11" x2="17" y2="11" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
                
                <button class="quick-action-btn" title="إرسال إشعار" onclick="openModal('sendNotificationModal')">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2"/>
                        <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>

            <!-- Admin Profile -->
            <div class="admin-profile-dropdown">
                <button class="admin-profile-btn" id="adminProfileBtn">
                    <div class="admin-avatar">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="admin-info">
                        <span class="admin-name"><?php echo htmlspecialchars($adminName); ?></span>
                        <span class="admin-role"><?php echo htmlspecialchars($adminRole); ?></span>
                    </div>
                    <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
                
                <div class="admin-profile-dropdown-menu" id="adminProfileDropdown">
                    <a href="#" class="dropdown-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        الملف الشخصي
                    </a>
                    <a href="#" class="dropdown-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        الإعدادات
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="logout.php" class="dropdown-item logout">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" stroke-width="2"/>
                            <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2"/>
                            <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>
