<?php
session_start();
require_once '../includes/database.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance()->getConnection();
    
    // Check if 'passed' column exists in user_weekly_test_attempts
    $stmt = $db->query("SHOW COLUMNS FROM user_weekly_test_attempts LIKE 'passed'");
    $columnExists = $stmt->fetch();
    
    if (!$columnExists) {
        // Add the 'passed' column
        $db->exec("
            ALTER TABLE user_weekly_test_attempts 
            ADD COLUMN passed TINYINT(1) DEFAULT 0 AFTER percentage
        ");
        
        echo json_encode([
            'success' => true,
            'message' => 'Added passed column to user_weekly_test_attempts table'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'passed column already exists'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
