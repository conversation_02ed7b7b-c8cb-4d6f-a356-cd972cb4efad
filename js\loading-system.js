/**
 * Simple Loading System
 * Provides simple loading screen for all pages
 */

class LoadingSystem {
    constructor() {
        this.loadingScreen = null;
        this.loadingDuration = 2000; // 2 seconds

        this.init();
    }

    init() {
        this.hidePageContent();
        this.createLoadingScreen();
        this.startLoading();
    }

    hidePageContent() {
        document.body.classList.add('loading');
    }

    showPageContent() {
        document.body.classList.remove('loading');
    }
    

    
    createLoadingScreen() {
        // إنشاء شاشة التحميل البسيطة
        this.loadingScreen = document.createElement('div');
        this.loadingScreen.className = 'loading-screen';
        this.loadingScreen.id = 'loadingScreen';

        this.loadingScreen.innerHTML = `
            <div class="loading-spinner"></div>
        `;

        document.body.appendChild(this.loadingScreen);
    }
    
    startLoading() {
        // إخفاء شاشة التحميل بعد المدة المحددة
        setTimeout(() => {
            this.hideLoadingScreen();
        }, this.loadingDuration);
    }
    
    hideLoadingScreen() {
        if (this.loadingScreen) {
            this.loadingScreen.classList.add('fade-out');

            setTimeout(() => {
                this.loadingScreen.style.display = 'none';
                this.showPageContent();
                this.showWelcomeMessage();
            }, 600);
        }
    }
    
    showWelcomeMessage() {
        // رسالة بسيطة "تم التحميل"
        const message = 'تم التحميل بنجاح';
        const icon = '✅';

        this.createWelcomeMessage(message, icon, 'success');
    }

    showPageLoadMessage() {
        // لا نحتاج رسالة إضافية
        return;
    }
    
    getPageIcon() {
        const icons = {
            'الصفحة الرئيسية': '🏠',
            'تسجيل الدخول': '🔐',
            'إنشاء حساب جديد': '📝',
            'لوحة التحكم': '📊',
            'الكورسات': '📚',
            'الدروس': '📖',
            'التمارين': '✏️',
            'الامتحانات': '📋',
            'الملف الشخصي': '👤',
            'الإعدادات': '⚙️'
        };
        
        return icons[this.pageName] || '📄';
    }
    
    createWelcomeMessage(message, icon, type = 'success', additionalClass = '') {
        const welcomeMsg = document.createElement('div');
        welcomeMsg.className = `welcome-message ${type} ${additionalClass}`;
        
        welcomeMsg.innerHTML = `
            <button class="welcome-message-close" onclick="this.parentElement.classList.add('hide')">×</button>
            <div class="welcome-message-header">
                <span class="welcome-message-icon">${icon}</span>
                <h4 class="welcome-message-title">مرحباً</h4>
            </div>
            <p class="welcome-message-text">${message}</p>
        `;
        
        document.body.appendChild(welcomeMsg);
        
        // إظهار الرسالة
        setTimeout(() => {
            welcomeMsg.classList.add('show');
        }, 100);
        
        // إخفاء الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            welcomeMsg.classList.add('hide');
            setTimeout(() => {
                welcomeMsg.remove();
            }, 500);
        }, 5000);
        
        return welcomeMsg;
    }
    
    // دالة لإظهار رسالة مخصصة
    static showMessage(message, icon = '💬', type = 'info', duration = 4000) {
        const welcomeMsg = document.createElement('div');
        welcomeMsg.className = `welcome-message ${type}`;
        
        welcomeMsg.innerHTML = `
            <button class="welcome-message-close" onclick="this.parentElement.classList.add('hide')">×</button>
            <div class="welcome-message-header">
                <span class="welcome-message-icon">${icon}</span>
                <h4 class="welcome-message-title">إشعار</h4>
            </div>
            <p class="welcome-message-text">${message}</p>
        `;
        
        document.body.appendChild(welcomeMsg);
        
        setTimeout(() => {
            welcomeMsg.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            welcomeMsg.classList.add('hide');
            setTimeout(() => {
                welcomeMsg.remove();
            }, 500);
        }, duration);
        
        return welcomeMsg;
    }
    
    // دالة لإظهار رسالة نجاح
    static showSuccess(message, duration = 4000) {
        return LoadingSystem.showMessage(message, '✅', 'success', duration);
    }
    
    // دالة لإظهار رسالة خطأ
    static showError(message, duration = 5000) {
        return LoadingSystem.showMessage(message, '❌', 'error', duration);
    }
    
    // دالة لإظهار رسالة تحذير
    static showWarning(message, duration = 4000) {
        return LoadingSystem.showMessage(message, '⚠️', 'warning', duration);
    }
    
    // دالة لإظهار رسالة معلومات
    static showInfo(message, duration = 4000) {
        return LoadingSystem.showMessage(message, 'ℹ️', 'info', duration);
    }
}

// تهيئة نظام التحميل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق شاشة التحميل على جميع الصفحات
    window.loadingSystem = new LoadingSystem();
});

// تصدير الكلاس للاستخدام العام
window.LoadingSystem = LoadingSystem;
