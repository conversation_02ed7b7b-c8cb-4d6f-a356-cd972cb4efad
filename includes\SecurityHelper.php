<?php
class SecurityHelper {
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email format
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate date format (Y-m-d)
     */
    public static function validateDate($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
    
    /**
     * Validate time format (H:i)
     */
    public static function validateTime($time) {
        $t = DateTime::createFromFormat('H:i', $time);
        return $t && $t->format('H:i') === $time;
    }
    
    /**
     * Validate numeric input
     */
    public static function validateNumeric($value, $min = null, $max = null) {
        if (!is_numeric($value)) {
            return false;
        }
        
        $num = (float)$value;
        
        if ($min !== null && $num < $min) {
            return false;
        }
        
        if ($max !== null && $num > $max) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate string length
     */
    public static function validateLength($string, $min = 0, $max = null) {
        $length = mb_strlen($string, 'UTF-8');
        
        if ($length < $min) {
            return false;
        }
        
        if ($max !== null && $length > $max) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate enum values
     */
    public static function validateEnum($value, $allowedValues) {
        return in_array($value, $allowedValues, true);
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Check admin permissions
     */
    public static function checkAdminPermission($requiredRole = 'admin') {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }
        
        // Get admin role from database
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT role FROM admins WHERE id = ? AND is_active = 1");
            $stmt->execute([$_SESSION['admin_id']]);
            $admin = $stmt->fetch();
            
            if (!$admin) {
                return false;
            }
            
            $roleHierarchy = [
                'moderator' => 1,
                'admin' => 2,
                'super_admin' => 3
            ];
            
            $userLevel = $roleHierarchy[$admin['role']] ?? 0;
            $requiredLevel = $roleHierarchy[$requiredRole] ?? 0;
            
            return $userLevel >= $requiredLevel;
            
        } catch (Exception $e) {
            error_log("Permission check error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log admin activity
     */
    public static function logAdminActivity($action, $details = null) {
        if (!isset($_SESSION['admin_id'])) {
            return;
        }
        
        try {
            $db = getDB();
            $stmt = $db->prepare("
                INSERT INTO admin_activity_log (admin_id, action, details, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $_SESSION['admin_id'],
                $action,
                $details ? json_encode($details) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
        } catch (Exception $e) {
            error_log("Activity logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Rate limiting check
     */
    public static function checkRateLimit($action, $maxAttempts = 5, $timeWindow = 300) {
        $key = $action . '_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        
        if (!isset($_SESSION['rate_limits'])) {
            $_SESSION['rate_limits'] = [];
        }
        
        $now = time();
        
        // Clean old entries
        if (isset($_SESSION['rate_limits'][$key])) {
            $_SESSION['rate_limits'][$key] = array_filter(
                $_SESSION['rate_limits'][$key],
                function($timestamp) use ($now, $timeWindow) {
                    return ($now - $timestamp) < $timeWindow;
                }
            );
        }
        
        // Check current attempts
        $attempts = count($_SESSION['rate_limits'][$key] ?? []);
        
        if ($attempts >= $maxAttempts) {
            return false;
        }
        
        // Record this attempt
        $_SESSION['rate_limits'][$key][] = $now;
        
        return true;
    }
    
    /**
     * Validate exam data
     */
    public static function validateExamData($data) {
        $errors = [];
        
        // Required fields
        $requiredFields = ['exam_name', 'subject', 'exam_date', 'exam_time', 'duration_minutes', 'education_level', 'education_type', 'grade'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "حقل {$field} مطلوب";
            }
        }
        
        // Validate date
        if (!empty($data['exam_date']) && !self::validateDate($data['exam_date'])) {
            $errors[] = "تاريخ الامتحان غير صحيح";
        }
        
        // Validate time
        if (!empty($data['exam_time']) && !self::validateTime($data['exam_time'])) {
            $errors[] = "وقت الامتحان غير صحيح";
        }
        
        // Validate duration
        if (!empty($data['duration_minutes']) && !self::validateNumeric($data['duration_minutes'], 30, 300)) {
            $errors[] = "مدة الامتحان يجب أن تكون بين 30 و 300 دقيقة";
        }
        
        // Validate education level
        if (!empty($data['education_level']) && !self::validateEnum($data['education_level'], ['primary', 'preparatory', 'secondary'])) {
            $errors[] = "المرحلة التعليمية غير صحيحة";
        }
        
        // Validate education type
        if (!empty($data['education_type']) && !self::validateEnum($data['education_type'], ['general', 'azhari'])) {
            $errors[] = "نوع التعليم غير صحيح";
        }
        
        // Validate specialization if provided
        if (!empty($data['specialization']) && !self::validateEnum($data['specialization'], ['scientific', 'literary'])) {
            $errors[] = "التخصص غير صحيح";
        }
        
        // Validate string lengths
        if (!empty($data['exam_name']) && !self::validateLength($data['exam_name'], 1, 255)) {
            $errors[] = "اسم الامتحان يجب أن يكون بين 1 و 255 حرف";
        }
        
        if (!empty($data['subject']) && !self::validateLength($data['subject'], 1, 100)) {
            $errors[] = "المادة يجب أن تكون بين 1 و 100 حرف";
        }
        
        if (!empty($data['location']) && !self::validateLength($data['location'], 0, 255)) {
            $errors[] = "المكان يجب أن يكون أقل من 255 حرف";
        }
        
        return $errors;
    }
    
    /**
     * Validate news data
     */
    public static function validateNewsData($data) {
        $errors = [];
        
        // Required fields
        $requiredFields = ['title', 'content', 'publication_date'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "حقل {$field} مطلوب";
            }
        }
        
        // Validate date
        if (!empty($data['publication_date']) && !self::validateDate($data['publication_date'])) {
            $errors[] = "تاريخ النشر غير صحيح";
        }
        
        // Validate education level
        if (!empty($data['education_level']) && !self::validateEnum($data['education_level'], ['primary', 'preparatory', 'secondary', 'all'])) {
            $errors[] = "المرحلة التعليمية غير صحيحة";
        }
        
        // Validate education type
        if (!empty($data['education_type']) && !self::validateEnum($data['education_type'], ['general', 'azhari', 'all'])) {
            $errors[] = "نوع التعليم غير صحيح";
        }
        
        // Validate specialization if provided
        if (!empty($data['specialization']) && !self::validateEnum($data['specialization'], ['scientific', 'literary', 'all'])) {
            $errors[] = "التخصص غير صحيح";
        }
        
        // Validate string lengths
        if (!empty($data['title']) && !self::validateLength($data['title'], 1, 255)) {
            $errors[] = "العنوان يجب أن يكون بين 1 و 255 حرف";
        }
        
        if (!empty($data['content']) && !self::validateLength($data['content'], 1, 65535)) {
            $errors[] = "المحتوى يجب أن يكون بين 1 و 65535 حرف";
        }
        
        if (!empty($data['summary']) && !self::validateLength($data['summary'], 0, 500)) {
            $errors[] = "الملخص يجب أن يكون أقل من 500 حرف";
        }
        
        if (!empty($data['category']) && !self::validateLength($data['category'], 1, 50)) {
            $errors[] = "التصنيف يجب أن يكون بين 1 و 50 حرف";
        }
        
        return $errors;
    }
    
    /**
     * Validate honor board data
     */
    public static function validateHonorBoardData($data) {
        $errors = [];
        
        // Required fields
        $requiredFields = ['student_name', 'grade_score', 'subject', 'ranking_position', 'achievement_type', 'achievement_date', 'education_level', 'education_type', 'grade'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "حقل {$field} مطلوب";
            }
        }
        
        // Validate date
        if (!empty($data['achievement_date']) && !self::validateDate($data['achievement_date'])) {
            $errors[] = "تاريخ الإنجاز غير صحيح";
        }
        
        // Validate grade score
        if (!empty($data['grade_score']) && !self::validateNumeric($data['grade_score'], 0, 100)) {
            $errors[] = "الدرجة يجب أن تكون بين 0 و 100";
        }
        
        // Validate ranking position
        if (!empty($data['ranking_position']) && !self::validateNumeric($data['ranking_position'], 1, 100)) {
            $errors[] = "المركز يجب أن يكون بين 1 و 100";
        }
        
        // Validate achievement type
        if (!empty($data['achievement_type']) && !self::validateEnum($data['achievement_type'], ['monthly', 'semester', 'yearly', 'special'])) {
            $errors[] = "نوع الإنجاز غير صحيح";
        }
        
        // Validate education level
        if (!empty($data['education_level']) && !self::validateEnum($data['education_level'], ['primary', 'preparatory', 'secondary'])) {
            $errors[] = "المرحلة التعليمية غير صحيحة";
        }
        
        // Validate education type
        if (!empty($data['education_type']) && !self::validateEnum($data['education_type'], ['general', 'azhari'])) {
            $errors[] = "نوع التعليم غير صحيح";
        }
        
        // Validate specialization if provided
        if (!empty($data['specialization']) && !self::validateEnum($data['specialization'], ['scientific', 'literary'])) {
            $errors[] = "التخصص غير صحيح";
        }
        
        // Validate string lengths
        if (!empty($data['student_name']) && !self::validateLength($data['student_name'], 1, 100)) {
            $errors[] = "اسم الطالب يجب أن يكون بين 1 و 100 حرف";
        }
        
        if (!empty($data['subject']) && !self::validateLength($data['subject'], 1, 100)) {
            $errors[] = "المادة يجب أن تكون بين 1 و 100 حرف";
        }
        
        if (!empty($data['additional_notes']) && !self::validateLength($data['additional_notes'], 0, 1000)) {
            $errors[] = "الملاحظات الإضافية يجب أن تكون أقل من 1000 حرف";
        }
        
        return $errors;
    }
}
?>
