<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';
require_once '../includes/PaymobGateway.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$paymobGateway = new PaymobGateway();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get user information
$stmt = $db->prepare("SELECT username, email, full_name, phone FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login.php');
    exit;
}

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Check if user already has access
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
if ($userCourseStatus && $userCourseStatus['activation_status'] === 'active') {
    header('Location: ' . SITE_URL . '/page/course_content.php?id=' . $courseId);
    exit;
}

$message = '';
$messageType = '';
$paymentData = null;

// Calculate final price
$finalPrice = $course['discount_percentage'] > 0 ? $course['discounted_price'] : $course['price'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_payment'])) {
        // Prepare customer info
        $customerInfo = [
            'name' => $user['full_name'] ?? $user['username'],
            'email' => $user['email'],
            'mobile' => $user['phone'] ?? '01000000000', // Default if no phone
            'course_title' => $course['title']
        ];
        
        // Create Paymob payment request
        $paymentResult = $paymobGateway->createPaymentRequest($userId, $courseId, $finalPrice, $customerInfo);
        
        if ($paymentResult['success']) {
            // Redirect to Paymob payment page
            header('Location: ' . $paymentResult['payment_url']);
            exit;
        } else {
            $message = $paymentResult['error'];
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدفع بالفيزا - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="../page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="../page/courses.php">الكورسات</a>
                    <span>/</span>
                    <a href="../page/course_details.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a>
                    <span>/</span>
                    <span>الدفع بالفيزا</span>
                </div>

                <div class="payment-container">
                    <!-- Course Info Card -->
                    <div class="course-info-card">
                        <div class="course-image">
                            <?php if ($course['main_image']): ?>
                                <img src="../uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-details">
                            <h3><?php echo htmlspecialchars($course['title']); ?></h3>
                            <p><?php echo htmlspecialchars($course['subject']); ?></p>
                            <div class="course-price">
                                <?php if ($course['discount_percentage'] > 0): ?>
                                    <span class="original-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                    <span class="discounted-price"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <div class="payment-form-card">
                        <div class="form-header">
                            <div class="payment-icon">💳</div>
                            <h1>الدفع بالفيزا</h1>
                            <p>ادفع بأمان باستخدام بطاقة الفيزا أو الماستركارد</p>
                        </div>

                        <!-- Paymob Info -->
                        <div class="paymob-info">
                            <h3>💳 مميزات الدفع بالفيزا</h3>
                            <div class="features-grid">
                                <div class="feature">
                                    <div class="feature-icon">⚡</div>
                                    <h4>تفعيل فوري</h4>
                                    <p>يتم تفعيل الكورس فور إتمام الدفع</p>
                                </div>
                                <div class="feature">
                                    <div class="feature-icon">🔒</div>
                                    <h4>دفع آمن</h4>
                                    <p>تشفير عالي المستوى لحماية بياناتك</p>
                                </div>
                                <div class="feature">
                                    <div class="feature-icon">💳</div>
                                    <h4>جميع البطاقات</h4>
                                    <p>يقبل فيزا وماستركارد ومدى</p>
                                </div>
                                <div class="feature">
                                    <div class="feature-icon">🌍</div>
                                    <h4>دولي ومحلي</h4>
                                    <p>يقبل البطاقات المحلية والدولية</p>
                                </div>
                            </div>
                        </div>

                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" class="payment-form">
                            <div class="payment-summary">
                                <h3>ملخص الدفع</h3>
                                <div class="summary-row">
                                    <span>الكورس:</span>
                                    <span><?php echo htmlspecialchars($course['title']); ?></span>
                                </div>
                                <div class="summary-row">
                                    <span>المبلغ:</span>
                                    <span class="amount"><?php echo number_format($finalPrice, 0); ?> جنيه</span>
                                </div>
                            </div>

                            <div class="security-notice">
                                <div class="security-icon">🔐</div>
                                <div class="security-content">
                                    <h4>دفع آمن ومضمون</h4>
                                    <p>جميع المعاملات محمية بتشفير SSL وتتم معالجتها عبر بوابة دفع معتمدة من البنك المركزي المصري</p>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="confirm_payment" class="btn btn-primary btn-large">
                                    <span class="btn-icon">💳</span>
                                    متابعة الدفع بالفيزا
                                </button>
                                <a href="../page/course_register.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">↩️</span>
                                    العودة لخيارات التفعيل
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 30px;
        }

        .course-info-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .course-image {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 40px;
            color: white;
        }

        .course-details h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .course-details p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .course-price {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 14px;
        }

        .discounted-price {
            color: #dc3545;
            font-size: 18px;
            font-weight: 700;
        }

        .current-price {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 700;
        }

        .payment-form-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .payment-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #6c757d;
            font-size: 18px;
        }

        .paymob-info {
            background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
            border: 2px solid #17a2b8;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .paymob-info h3 {
            color: #0c5460;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
        }

        .feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .feature h4 {
            color: #0c5460;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature p {
            color: #0c5460;
            font-size: 14px;
            margin: 0;
        }

        .payment-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .payment-summary h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .summary-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
        }

        .amount {
            color: #28a745;
            font-weight: bold;
        }

        .security-notice {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #6c757d;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .security-icon {
            font-size: 40px;
            flex-shrink: 0;
        }

        .security-content h4 {
            color: #495057;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .security-content p {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .breadcrumb {
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb a {
            color: #4682B4;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #6c757d;
            margin: 0 10px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .payment-container {
                padding: 15px;
            }

            .course-info-card {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .payment-form-card {
                padding: 25px;
            }

            .form-header h1 {
                font-size: 24px;
            }

            .payment-icon {
                font-size: 60px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .security-notice {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
