# دليل حل المشاكل - نظام التواصل بين الطلاب والمعلمين

## المشاكل الشائعة والحلول

### 1. خطأ "Column not found: sort_order"

**المشكلة:**
```
SQL Error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sort_order' in 'field list'
```

**السبب:**
- الجداول تم إنشاؤها بشكل جزئي
- هناك تضارب في هيكل الجداول

**الحل:**
1. شغل سكريبت إعادة التعيين:
   ```
   http://localhost/manash/setup/reset_communication_system.php
   ```
2. أو احذف الجداول يدوياً:
   ```sql
   DROP TABLE IF EXISTS message_notifications;
   DROP TABLE IF EXISTS admin_replies;
   DROP TABLE IF EXISTS message_templates;
   DROP TABLE IF EXISTS student_messages;
   DROP TABLE IF EXISTS message_categories;
   ```
3. ثم شغل سكريبت الإعداد:
   ```
   http://localhost/manash/setup/setup_communication_system.php
   ```

### 2. خطأ "Table already exists"

**المشكلة:**
```
Table 'message_categories' already exists
```

**السبب:**
- الجداول موجودة لكن غير مكتملة

**الحل:**
1. فحص حالة النظام:
   ```
   http://localhost/manash/setup/check_system_status.php
   ```
2. إعادة تعيين النظام:
   ```
   http://localhost/manash/setup/reset_communication_system.php
   ```

### 3. خطأ "Foreign key constraint fails"

**المشكلة:**
```
Cannot add or update a child row: a foreign key constraint fails
```

**السبب:**
- ترتيب إنشاء الجداول غير صحيح
- بيانات مرجعية مفقودة

**الحل:**
1. إعادة تعيين النظام بالترتيب الصحيح
2. التأكد من وجود جدول `admins` و `users`

### 4. الصفحات لا تعمل

**المشكلة:**
- صفحة "اسأل معلم" تظهر خطأ
- صفحة إدارة الرسائل لا تعمل

**الحل:**
1. التأكد من تشغيل سكريبت الإعداد
2. فحص ملفات السجل (error logs)
3. التأكد من صلاحيات قاعدة البيانات

### 5. الإشعارات لا تعمل

**المشكلة:**
- عدادات الرسائل لا تظهر
- الإشعارات لا تصل

**الحل:**
1. التأكد من وجود جدول `message_notifications`
2. فحص الجافا سكريبت في المتصفح
3. التأكد من تشغيل AJAX

## أوامر SQL مفيدة

### فحص الجداول الموجودة:
```sql
SHOW TABLES LIKE 'message_%';
```

### فحص هيكل جدول:
```sql
DESCRIBE message_categories;
```

### حذف جميع جداول النظام:
```sql
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS message_notifications;
DROP TABLE IF EXISTS admin_replies;
DROP TABLE IF EXISTS message_templates;
DROP TABLE IF EXISTS student_messages;
DROP TABLE IF EXISTS message_categories;
SET FOREIGN_KEY_CHECKS = 1;
```

### فحص البيانات:
```sql
SELECT COUNT(*) as categories FROM message_categories;
SELECT COUNT(*) as messages FROM student_messages;
SELECT COUNT(*) as replies FROM admin_replies;
```

## خطوات التشخيص

### 1. فحص الاتصال بقاعدة البيانات:
```php
try {
    $db = Database::getInstance()->getConnection();
    echo "الاتصال ناجح";
} catch (Exception $e) {
    echo "خطأ في الاتصال: " . $e->getMessage();
}
```

### 2. فحص الجداول:
```
http://localhost/manash/setup/check_system_status.php
```

### 3. فحص الأخطاء:
- تحقق من ملف error.log في Apache
- تحقق من أخطاء PHP في المتصفح
- استخدم Developer Tools في المتصفح

## الملفات المهمة

### ملفات الإعداد:
- `setup/setup_communication_system.php` - الإعداد الأساسي
- `setup/reset_communication_system.php` - إعادة التعيين
- `setup/check_system_status.php` - فحص الحالة

### ملفات النظام:
- `sql/student_teacher_communication.sql` - هيكل قاعدة البيانات
- `includes/MessageManager.php` - كلاس إدارة الرسائل
- `api/message_operations.php` - API العمليات

### ملفات الواجهة:
- `page/ask_teacher.php` - صفحة إرسال الرسائل
- `page/my_messages.php` - صفحة رسائل الطالب
- `admin/student_messages.php` - إدارة الرسائل

## نصائح مهمة

1. **دائماً اعمل نسخة احتياطية** قبل تشغيل سكريبت إعادة التعيين
2. **تأكد من صلاحيات قاعدة البيانات** قبل التثبيت
3. **استخدم فحص الحالة** للتشخيص قبل الإصلاح
4. **اقرأ رسائل الخطأ بعناية** لفهم المشكلة
5. **اختبر النظام** بعد كل إصلاح

## الحصول على المساعدة

إذا استمرت المشاكل:
1. شغل فحص الحالة وارسل النتائج
2. تحقق من ملفات السجل
3. تأكد من إعدادات قاعدة البيانات
4. جرب إعادة تعيين النظام

## معلومات الإصدار

- الإصدار: 1.0
- تاريخ آخر تحديث: 2025-01-21
- متطلبات النظام: PHP 7.4+, MySQL 5.7+
