<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مسموح']);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_id']) || !isset($input['days'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات مفقودة']);
    exit;
}

$user_id = intval($input['user_id']);
$days = intval($input['days']);

if ($user_id <= 0 || $days <= 0) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user's current subscription
    $stmt = $db->prepare("SELECT u.*, us.id as subscription_id, us.end_date as current_end_date
                         FROM users u
                         LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.end_date = u.subscription_end_date
                         WHERE u.id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
        exit;
    }
    
    if ($user['subscription_status'] !== 'active') {
        echo json_encode(['success' => false, 'message' => 'المستخدم ليس لديه اشتراك نشط']);
        exit;
    }
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Calculate new end date
        $current_end = $user['current_end_date'] ?: date('Y-m-d H:i:s');
        $new_end_date = date('Y-m-d H:i:s', strtotime($current_end . ' +' . $days . ' days'));
        
        // Update user subscription end date
        $stmt = $db->prepare("UPDATE users SET subscription_end_date = ? WHERE id = ?");
        $stmt->execute([$new_end_date, $user_id]);
        
        // Update user_subscriptions table if exists
        if ($user['subscription_id']) {
            $stmt = $db->prepare("UPDATE user_subscriptions SET end_date = ? WHERE id = ?");
            $stmt->execute([$new_end_date, $user['subscription_id']]);
        }
        
        // Log the extension
        $stmt = $db->prepare("INSERT INTO subscription_extensions (user_id, extended_by_admin, days_added, old_end_date, new_end_date, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        
        // Create table if it doesn't exist
        $db->exec("CREATE TABLE IF NOT EXISTS subscription_extensions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            extended_by_admin INT NOT NULL,
            days_added INT NOT NULL,
            old_end_date TIMESTAMP NOT NULL,
            new_end_date TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (extended_by_admin) REFERENCES admins(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        $stmt->execute([$user_id, $_SESSION['admin_id'], $days, $current_end, $new_end_date]);
        
        // Commit transaction
        $db->commit();
        
        echo json_encode([
            'success' => true, 
            'message' => "تم تمديد الاشتراك بنجاح لـ {$days} يوم",
            'new_end_date' => $new_end_date,
            'days_added' => $days
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تمديد الاشتراك: ' . $e->getMessage()]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
}
?>
