/* Import Modern Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap');

/* ===== GLOBAL LINK STYLES - REMOVE ALL UNDERLINES ===== */
a, a:link, a:visited, a:hover, a:active, a:focus {
    text-decoration: none !important;
    border-bottom: none !important;
    outline: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure no underlines on any link state */
a::before, a::after {
    text-decoration: none !important;
}

/* Remove underlines from all interactive elements */
button, input[type="submit"], input[type="button"], .btn {
    text-decoration: none !important;
}

/* Remove underlines from navigation links */
.nav a, .navbar a, .menu a, .sidebar a {
    text-decoration: none !important;
    border-bottom: none !important;
}

/* Remove underlines from card links */
.card a, .course-card a, .lesson-card a {
    text-decoration: none !important;
}

/* ===== CUSTOM SCROLLBAR DESIGN ===== */
/* Main scrollbar styling */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

/* Scrollbar track */
::-webkit-scrollbar-track {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Scrollbar thumb */
::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #5F9EA0 100%);
    border-radius: 10px;
    border: 2px solid #f8f9fa;
    box-shadow: 0 2px 8px rgba(70, 130, 180, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scrollbar thumb on hover */
::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #4682B4 0%, #87CEEB 50%, #20B2AA 100%);
    box-shadow: 0 4px 12px rgba(70, 130, 180, 0.5);
    transform: scale(1.05);
}

/* Scrollbar thumb when active */
::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #5F9EA0 0%, #4682B4 100%);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Scrollbar corner */
::-webkit-scrollbar-corner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

/* Firefox scrollbar styling */
html {
    scrollbar-width: thin;
    scrollbar-color: #87CEEB #f8f9fa;
}

/* Thin scrollbar for smaller elements */
.thin-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 6px;
    border: 1px solid #f8f9fa;
}

/* ===== CUSTOM CURSOR STYLES ===== */
/* Default cursor */
* {
    cursor: default;
}

/* Pointer cursors for interactive elements */
a, button, input[type="submit"], input[type="button"], input[type="reset"],
.btn, .clickable, .card-clickable, .nav-item, .menu-item,
.course-card, .lesson-card, .exercise-card, .exam-card,
.notification-item, .dropdown-item, .tab-item, .accordion-header,
[role="button"], [onclick], .pointer {
    cursor: pointer !important;
}

/* Text cursor for input fields */
input[type="text"], input[type="email"], input[type="password"],
input[type="search"], input[type="tel"], input[type="url"],
input[type="number"], textarea, select, .form-control,
[contenteditable="true"], .text-input {
    cursor: text !important;
}

/* Help cursor for tooltips and help elements */
.tooltip, .help-icon, .info-icon, [title], .help-text,
.question-mark, .hint, .tooltip-trigger {
    cursor: help !important;
}

/* Move cursor for draggable elements */
.draggable, .sortable-item, .drag-handle, [draggable="true"] {
    cursor: move !important;
}

/* Resize cursors */
.resize-horizontal { cursor: ew-resize !important; }
.resize-vertical { cursor: ns-resize !important; }
.resize-both { cursor: nwse-resize !important; }

/* Not-allowed cursor for disabled elements */
.disabled, [disabled], .btn:disabled, input:disabled,
textarea:disabled, select:disabled, .not-allowed {
    cursor: not-allowed !important;
    opacity: 0.6;
}

/* Wait cursor for loading states */
.loading, .processing, .wait {
    cursor: wait !important;
}

/* Grab cursor for scrollable areas */
.scrollable, .carousel, .slider, .grab {
    cursor: grab !important;
}

.scrollable:active, .carousel:active, .slider:active, .grab:active {
    cursor: grabbing !important;
}

/* Enhanced Focus States */
button:focus, input:focus, textarea:focus, select:focus,
.btn:focus, .form-control:focus, a:focus {
    outline: 3px solid #4682B4 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 4px rgba(70, 130, 180, 0.25) !important;
    border-color: #87CEEB !important;
}

/* Modern CSS Variables */
:root {
    /* Primary Colors */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #c82333 100%);

    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --medium-gray: #6c757d;
    --dark-gray: #2c3e50;
    --border-color: #dee2e6;

    /* Typography */
    --font-primary: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-xxl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--dark-gray);
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    font-weight: var(--font-weight-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--dark-gray);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

a {
    color: #667eea;
    text-decoration: none;
    transition: var(--transition-normal);
}

a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-weight: var(--font-weight-semibold);
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: var(--transition-normal);
}

.btn:hover::before {
    transform: translateX(0);
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--warning-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background: var(--danger-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: var(--primary-gradient);
    color: var(--white);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1rem;
}

.btn-small {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.75rem;
}

/* Modern Card Styles */
.card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition-normal);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    padding: var(--spacing-xl);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--light-gray);
    border-top: 1px solid var(--border-color);
}

/* Modern Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-gray);
}

.form-control {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-size: 1rem;
    background: var(--white);
    transition: var(--transition-normal);
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control::placeholder {
    color: var(--medium-gray);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* Enhanced Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: var(--transition-slow);
}

.loading-screen.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loading-logo {
    width: 120px;
    height: 120px;
    margin-bottom: var(--spacing-xl);
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: var(--shadow-xl);
}

.loading-text {
    color: var(--white);
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Modern Animations */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Advanced Timer System Styles */
.exam-timer-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: linear-gradient(135deg, var(--white) 0%, var(--light-gray) 100%);
    border-radius: 25px;
    padding: 15px 25px;
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.2);
    border: 2px solid rgba(135, 206, 235, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.exam-timer-container.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    animation: timerWarning 1s ease-in-out infinite alternate;
}

.exam-timer-container.critical {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
    animation: timerCritical 0.5s ease-in-out infinite alternate;
}

.timer-display {
    display: flex;
    align-items: center;
    gap: 15px;
    font-family: 'Cairo', sans-serif;
}

.timer-icon {
    font-size: 24px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.timer-container.warning .timer-icon {
    color: #ffc107;
    animation: pulse 1s infinite;
}

.timer-container.critical .timer-icon {
    color: #dc3545;
    animation: shake 0.5s infinite;
}

.timer-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-gray);
    margin: 0 10px 0 0;
}

.timer-time {
    font-size: 24px;
    font-weight: 800;
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    min-width: 80px;
    text-align: center;
}

.timer-container.warning .timer-time {
    color: #856404;
}

.timer-container.critical .timer-time {
    color: #721c24;
}

.timer-progress {
    width: 200px;
    height: 8px;
    background: rgba(135, 206, 235, 0.2);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.timer-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-gradient));
    border-radius: 10px;
    transition: width 1s ease, background 0.3s ease;
    position: relative;
}

.timer-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

.timer-container.warning .timer-progress-bar {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.timer-container.critical .timer-progress-bar {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

/* Timer Warning Animations */
@keyframes timerWarning {
    0% { box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2); }
    100% { box-shadow: 0 15px 40px rgba(255, 193, 7, 0.4); }
}

@keyframes timerCritical {
    0% { box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3); }
    100% { box-shadow: 0 15px 40px rgba(220, 53, 69, 0.6); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Timer Warning Modal */
.timer-warning-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.timer-warning-content {
    background: linear-gradient(135deg, var(--white) 0%, var(--light-gray) 100%);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 3px solid;
    animation: bounceIn 0.5s ease;
}

.timer-warning-content.warning {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.timer-warning-content.critical {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.warning-icon {
    font-size: 60px;
    margin-bottom: 20px;
    animation: pulse 1s infinite;
}

.warning-icon.warning {
    color: #ffc107;
}

.warning-icon.critical {
    color: #dc3545;
}

.warning-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--dark-gray);
}

.warning-message {
    font-size: 16px;
    margin-bottom: 25px;
    color: var(--medium-gray);
    line-height: 1.5;
}

.warning-time {
    font-size: 32px;
    font-weight: 800;
    font-family: 'Courier New', monospace;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.warning-time.warning {
    color: #856404;
}

.warning-time.critical {
    color: #721c24;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Modern Glass Morphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Modern Gradient Text */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Hover Effects */
.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Modern Progress Bars */
.progress {
    width: 100%;
    height: 8px;
    background: var(--light-gray);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

/* Modern Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-lg);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-gradient);
    color: var(--white);
}

.badge-success {
    background: var(--success-gradient);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-gradient);
    color: var(--white);
}

.badge-danger {
    background: var(--danger-gradient);
    color: var(--white);
}

/* Modern Tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--dark-gray);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* Modern Focus States */
*:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Modern Selection */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: var(--dark-gray);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.25rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-xxl: 2rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }

    .btn-large {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
    }
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header Styles */
.header {
    background: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.logo-text h2 {
    color: #333;
    font-size: 24px;
    font-weight: bold;
}

.nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
}

.nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav a:hover {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    transform: translateY(-2px);
}

/* Split Screen Layout */
.split-container {
    min-height: 100vh;
    display: flex;
}

.split-left, .split-right {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}


/* Registration Steps Section */
.registration-steps {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.steps-title {
    color: white;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.steps-list {
    list-style: none;
    text-align: right;
}

.steps-list li {
    padding: 15px 20px;
    font-size: 17px;
    position: relative;
    padding-right: 45px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 8px;
}

.steps-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.steps-list li:hover {
    background: rgba(255,255,255,0.15);
    border-radius: 8px;
    padding-right: 45px;
    transform: translateX(-5px);
}

.steps-list li:before {
    content: attr(data-step);
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.steps-list li.active {
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
    font-weight: bold;
}

.steps-list li.active:before {
    background: #FFD700;
    color: #4682B4;
    box-shadow: 0 0 15px rgba(255,215,0,0.5);
    transform: translateY(-50%) scale(1.1);
}

.split-right {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0 12px 40px rgba(70,130,180,0.18);
    position: relative;
    overflow: hidden;
}

.split-right:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.18) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.18) 0%, transparent 60%),
        linear-gradient(120deg, rgba(255,255,255,0.10) 0%, transparent 100%);
    pointer-events: none;
    z-index: 1;
}

.split-left {
    background: #fff;
    padding: 80px 60px;
    border-radius: 0 22px 22px 0;
    box-shadow: 0 12px 40px rgba(70,130,180,0.10);
}

.platform-features {
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.platform-logo {
    width: 140px;
    height: 140px;
    margin: 0 auto 36px;
    border-radius: 50%;
    border: 5px solid rgba(255,255,255,0.6);
    box-shadow: 0 16px 48px rgba(0,0,0,0.22);
    transition: transform 0.3s cubic-bezier(.4,2,.6,1), box-shadow 0.3s;
    background: rgba(255,255,255,0.16);
    display: flex;
    align-items: center;
    justify-content: center;
}

.platform-logo:hover {
    transform: scale(1.10) rotate(-3deg);
    box-shadow: 0 20px 56px rgba(70,130,180,0.22);
}

.platform-title {
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 22px;
    text-shadow: 2px 6px 18px rgba(0,0,0,0.22);
    background: linear-gradient(45deg, #fff, #e0f7fa 60%, #b0e0e6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1.5px;
}

.platform-subtitle {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.99;
    line-height: 1.8;
    color: #f0f8ff;
    text-shadow: 0 3px 12px rgba(70,130,180,0.22);
}

.features-section {
    background: rgba(255,255,255,0.16);
    border-radius: 22px;
    padding: 32px 26px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.22);
    box-shadow: 0 10px 36px rgba(70,130,180,0.13);
    margin-bottom: 14px;
}

.features-title {
    color: #fff;
    margin-bottom: 22px;
    font-size: 22px;
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 14px;
    letter-spacing: 0.7px;
    text-shadow: 0 3px 12px rgba(0,0,0,0.18);
}

.features-title:before {
    content: "⭐";
    font-size: 26px;
    filter: drop-shadow(0 3px 6px rgba(255,215,0,0.28));
}

.features-list {
    list-style: none;
    text-align: right;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 12px 0 12px 0;
    font-size: 16px;
    position: relative;
    padding-right: 38px;
    line-height: 1.7;
    color: #f8f9fa;
    transition: background 0.2s, color 0.2s;
    border-radius: 10px;
}

.features-list li:before {
    content: "✓";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #FFD700;
    font-weight: bold;
    font-size: 20px;
    text-shadow: 0 0 10px rgba(255,215,0,0.28);
}

.features-list li:hover {
    background: rgba(255,255,255,0.12);
    color: #fff;
}

/* Form Styles */
.form-container {
    width: 100%;
    max-width: 450px;
}

.form-title {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
}

.form-subtitle {
    color: #666;
    margin-bottom: 40px;
    text-align: center;
    font-size: 16px;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    direction: rtl;
}

.form-input:focus {
    outline: none;
    border-color: #87CEEB;
    background: white;
    box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
}

.form-input.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.form-input.success {
    border-color: #28a745;
    background: #f8fff8;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

.success-message {
    color: #28a745;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.success-message.show {
    display: block;
}

/* Button Styles */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(135, 206, 235, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-full {
    width: 100%;
    margin-bottom: 15px;
}

/* Multi-step Form Styles */
.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
    gap: 20px;
}

.step {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #e1e5e9;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    transition: all 0.4s ease;
    border: 3px solid transparent;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 2;
}

.step.active {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border-color: white;
    box-shadow: 0 4px 15px rgba(135, 206, 235, 0.4);
    transform: scale(1.1);
}

.step.completed {
    background: #4CAF50;
    color: white;
    border-color: #4CAF50;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.step:not(:last-child):before {
    content: '';
    position: absolute;
    left: calc(100% + 7px);
    top: 50%;
    transform: translateY(-50%);
    width: 15px;
    height: 3px;
    background: #e1e5e9;
    border-radius: 2px;
    z-index: 1;
    transition: all 0.3s ease;
}

.step.completed:not(:last-child):before {
    background: linear-gradient(90deg, #4CAF50, #87CEEB);
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    gap: 15px;
}

.btn-nav {
    flex: 1;
    max-width: 150px;
}

/* Radio and Checkbox Styles */
.radio-group, .checkbox-group {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.radio-item, .checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.radio-item:hover, .checkbox-item:hover {
    border-color: #87CEEB;
    background: white;
}

.radio-item.selected, .checkbox-item.selected {
    border-color: #87CEEB;
    background: rgba(135, 206, 235, 0.1);
}

.radio-item input, .checkbox-item input {
    margin: 0;
}

/* Select Dropdown Styles */
.form-select {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    direction: rtl;
}

.form-select:focus {
    outline: none;
    border-color: #87CEEB;
    background: white;
    box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
}

/* Preview Styles */
.preview-section {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.preview-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
}

.preview-content {
    color: #666;
    line-height: 1.6;
}

.preview-item {
    margin-bottom: 8px;
}

.preview-label {
    font-weight: 500;
    color: #333;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

.modal-text {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 10px;
}

.strength-bar {
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-weak .strength-fill {
    width: 33%;
    background: #dc3545;
}

.strength-medium .strength-fill {
    width: 66%;
    background: #ffc107;
}

.strength-strong .strength-fill {
    width: 100%;
    background: #28a745;
}

.strength-text {
    font-size: 12px;
    font-weight: 500;
}

.strength-weak .strength-text {
    color: #dc3545;
}

.strength-medium .strength-text {
    color: #ffc107;
}

.strength-strong .strength-text {
    color: #28a745;
}

/* Alert Styles */
.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.hidden { display: none; }
.visible { display: block; }

.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */

/* Welcome Section */
.welcome-section {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 25px;
    margin: 30px 0;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    text-align: center;
}

.welcome-title {
    font-size: 22px;
    font-weight: bold;
    color: white;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.welcome-text {
    font-size: 16px;
    color: rgba(255,255,255,0.9);
    line-height: 1.5;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 25px 0;
}

.stat-item {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 20px 15px;
    text-align: center;
    -webkit-backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255,255,255,0.15);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.stat-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.stat-number {
    font-size: 20px;
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 12px;
    color: rgba(255,255,255,0.8);
    font-weight: 500;
}

/* Features Highlights */
.features-highlights {
    margin-top: 25px;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255,255,255,0.08);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 12px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.feature-highlight:hover {
    background: rgba(255,255,255,0.12);
    transform: translateX(-5px);
}

.feature-icon {
    font-size: 28px;
    min-width: 40px;
    text-align: center;
}

.feature-content h4 {
    color: white;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.feature-content p {
    color: rgba(255,255,255,0.8);
    font-size: 13px;
    line-height: 1.4;
    margin: 0;
}

/* Teacher Introduction Styles */
.teacher-intro {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.teacher-image {
    margin-bottom: 20px;
}

.teacher-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid #87CEEB;
    box-shadow: 0 8px 25px rgba(135, 206, 235, 0.3);
    transition: transform 0.3s ease;
}

.teacher-photo:hover {
    transform: scale(1.05);
}

.teacher-name {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.teacher-title {
    font-size: 20px;
    color: #4682B4;
    margin-bottom: 20px;
    font-weight: 600;
}

.teacher-description {
    font-size: 16px;
    color: #666;
    line-height: 1.8;
    max-width: 500px;
    margin: 0 auto;
}

/* Experience Stats */
.experience-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px 15px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(135, 206, 235, 0.2);
    border-color: #87CEEB;
}

.stat-card .stat-icon {
    font-size: 30px;
    margin-bottom: 10px;
    display: block;
}

.stat-card .stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #4682B4;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Teaching Levels */
.teaching-levels {
    margin-bottom: 40px;
}

.teaching-levels h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 25px;
    text-align: center;
    font-weight: bold;
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.level-card {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    padding: 25px 20px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
}

.level-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(70, 130, 180, 0.4);
}

.level-card h4 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.level-card p {
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.5;
}

/* Action Buttons */
.action-buttons {
    margin-top: 30px;
}

/* Footer Styles */
.footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #87CEEB;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 10px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #87CEEB;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 20px;
    text-align: center;
    color: #999;
}

/* Print Styles */
@media print {
    .split-right,
    .form-navigation,
    .btn,
    .step-indicator,
    .footer {
        display: none;
    }

    .split-container {
        flex-direction: column;
    }

    .split-left {
        padding: 20px;
    }
}
@media (max-width: 768px) {
    .split-container {
        flex-direction: column;
    }

    .split-right {
        order: -1;
        min-height: 300px;
        padding: 30px 20px;
    }

    .split-left {
        padding: 40px 20px;
    }

    .platform-logo {
        width: 100px;
        height: 100px;
        margin-bottom: 20px;
    }

    .platform-title {
        font-size: 28px;
        margin-bottom: 15px;
    }

    .platform-subtitle {
        font-size: 16px;
        margin-bottom: 30px;
    }

    .features-list {
        display: none;
    }

    .form-title {
        font-size: 24px;
    }

    .form-container {
        max-width: 100%;
    }

    .step-indicator {
        gap: 10px;
        margin-bottom: 30px;
    }

    .step {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .step:not(:last-child):after {
        left: -20px;
        width: 10px;
    }

    .radio-group, .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }

    .form-navigation {
        flex-direction: column;
        gap: 10px;
    }

    .btn-nav {
        max-width: 100%;
    }

    .modal-content {
        padding: 30px 20px;
        margin: 20px;
    }

    .modal-buttons {
        flex-direction: column;
    }

    .registration-steps{
        display: none
    }

    .stats-grid{
        display: none;
    }

    /* Teacher Introduction Mobile */
    .teacher-intro {
        padding: 20px;
        margin-bottom: 30px;
    }

    .teacher-photo {
        width: 120px;
        height: 120px;
    }

    .teacher-name {
        font-size: 24px;
    }

    .teacher-title {
        font-size: 16px;
    }

    .teacher-description {
        font-size: 14px;
    }

    /* Experience Stats Mobile */
    .experience-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-bottom: 30px;
    }

    .stat-card {
        padding: 20px 10px;
    }

    .stat-card .stat-icon {
        font-size: 24px;
    }

    .stat-card .stat-number {
        font-size: 20px;
    }

    .stat-card .stat-label {
        font-size: 12px;
    }

    /* Teaching Levels Mobile */
    .levels-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .level-card {
        padding: 20px 15px;
    }

    .level-card h4 {
        font-size: 16px;
    }

    .level-card p {
        font-size: 13px;
    }

    /* Footer Mobile */
    .footer {
        padding: 30px 0 15px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .header .container {
        flex-direction: column;
        gap: 15px;
    }

    .nav ul {
        gap: 10px;
    }

    .nav a {
        padding: 8px 15px;
        font-size: 14px;
    }

    .split-left, .split-right {
        padding: 20px 15px;
    }

    .form-input, .form-select {
        padding: 12px;
        font-size: 14px;
    }

    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .platform-title {
        font-size: 24px;
    }

    .form-title {
        font-size: 20px;
    }

    .registration-steps{
        display: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
        margin: 20px 0;
        display: none;
    }

    .welcome-section {
        padding: 20px 15px;
        margin: 20px 0;
    }

    .welcome-title {
        font-size: 18px;
    }

    .welcome-text {
        font-size: 14px;
    }

    .feature-highlight {
        display: block;
    }

    /* Teacher Introduction Small Mobile */
    .teacher-intro {
        padding: 15px;
        margin-bottom: 25px;
    }

    .teacher-photo {
        width: 100px;
        height: 100px;
    }

    .teacher-name {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .teacher-title {
        font-size: 14px;
        margin-bottom: 15px;
    }

    .teacher-description {
        font-size: 13px;
        line-height: 1.6;
    }

    /* Experience Stats Small Mobile */
    .experience-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 25px;
    }

    .stat-card {
        padding: 15px 8px;
    }

    .stat-card .stat-icon {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .stat-card .stat-number {
        font-size: 18px;
        margin-bottom: 3px;
    }

    .stat-card .stat-label {
        font-size: 11px;
    }

    /* Teaching Levels Small Mobile */
    .teaching-levels h3 {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .level-card {
        padding: 18px 12px;
    }

    .level-card h4 {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .level-card p {
        font-size: 12px;
    }

    /* Footer Small Mobile */
    .footer {
        padding: 25px 0 10px;
    }

    .footer-section h4 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .footer-section p {
        font-size: 13px;
        margin-bottom: 8px;
    }

    .footer-section ul li {
        margin-bottom: 6px;
    }

    .footer-section ul li a {
        font-size: 13px;
    }

    .footer-bottom {
        padding-top: 15px;
        font-size: 12px;
    }
}

/* Course Management Responsive Styles */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.course-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(70, 130, 180, 0.2);
    border-color: #87CEEB;
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-icon {
    font-size: 60px;
    color: white;
}

.discount-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #dc3545;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.course-content {
    padding: 25px;
}

.course-header {
    margin-bottom: 15px;
}

.course-title {
    color: #2c3e50;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
}

.course-subject {
    color: #4682B4;
    font-size: 14px;
    font-weight: 600;
    background: rgba(135, 206, 235, 0.1);
    padding: 4px 12px;
    border-radius: 15px;
    display: inline-block;
}

.course-description {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.course-price {
    margin-bottom: 20px;
}

.original-price {
    color: #6c757d;
    text-decoration: line-through;
    font-size: 14px;
    margin-left: 10px;
}

.discounted-price {
    color: #dc3545;
    font-size: 20px;
    font-weight: 700;
}

.current-price {
    color: #2c3e50;
    font-size: 20px;
    font-weight: 700;
}

.course-actions {
    margin-top: 20px;
}

.btn-full {
    width: 100%;
}

.btn-icon {
    font-size: 18px;
}

.empty-state {
    text-align: center;
    padding: 80px 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 80px;
    margin-bottom: 20px;
}

.empty-state h2 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 30px;
    font-size: 16px;
}

/* Course Mobile Responsive */
@media (max-width: 768px) {
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .course-card {
        margin: 0 10px;
    }

    .course-content {
        padding: 20px;
    }

    .course-title {
        font-size: 18px;
    }

    .empty-state {
        padding: 60px 20px;
    }

    .empty-icon {
        font-size: 60px;
    }

    .empty-state h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .courses-grid {
        gap: 15px;
    }

    .course-card {
        margin: 0 5px;
    }

    .course-content {
        padding: 15px;
    }

    .course-title {
        font-size: 16px;
    }

    .course-subject {
        font-size: 12px;
        padding: 3px 8px;
    }

    .course-description {
        font-size: 13px;
    }

    .discounted-price, .current-price {
        font-size: 18px;
    }

    .empty-state {
        padding: 40px 15px;
    }

    .empty-icon {
        font-size: 50px;
    }

    .empty-state h2 {
        font-size: 20px;
    }

    .empty-state p {
        font-size: 14px;
    }
}