<?php
session_start();
require_once '../includes/database.php';

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = Database::getInstance()->getConnection();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['user_id'];

// Get POST data
$videoId = isset($_POST['video_id']) ? intval($_POST['video_id']) : 0;
$progressPercentage = isset($_POST['progress_percentage']) ? floatval($_POST['progress_percentage']) : 0;
$lastPosition = isset($_POST['last_position']) ? intval($_POST['last_position']) : 0;
$completed = isset($_POST['completed']) ? (bool)$_POST['completed'] : false;

if ($videoId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid video ID']);
    exit;
}

try {
    // Check if progress record exists
    $stmt = $db->prepare("SELECT id FROM user_video_progress WHERE user_id = ? AND video_id = ?");
    $stmt->execute([$userId, $videoId]);
    $existingRecord = $stmt->fetch();
    
    if ($existingRecord) {
        // Update existing record
        $stmt = $db->prepare("
            UPDATE user_video_progress 
            SET progress_percentage = ?, 
                last_position_seconds = ?, 
                completed = ?,
                completed_at = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND video_id = ?
        ");
        $completedAt = $completed ? date('Y-m-d H:i:s') : null;
        $stmt->execute([$progressPercentage, $lastPosition, $completed, $completedAt, $userId, $videoId]);
    } else {
        // Insert new record
        $stmt = $db->prepare("
            INSERT INTO user_video_progress 
            (user_id, video_id, progress_percentage, last_position_seconds, completed, completed_at) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $completedAt = $completed ? date('Y-m-d H:i:s') : null;
        $stmt->execute([$userId, $videoId, $progressPercentage, $lastPosition, $completed, $completedAt]);
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'Video progress updated successfully',
        'completed' => $completed,
        'progress_percentage' => $progressPercentage
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
