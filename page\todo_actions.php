<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('معطيات غير صحيحة');
    }
    
    $userManager = new UserManager();
    $userId = $_SESSION['user_id'];
    $action = $input['action'];
    
    switch ($action) {
        case 'create':
            $title = trim($input['title'] ?? '');
            $description = trim($input['description'] ?? '');
            $priority = $input['priority'] ?? 'medium';
            $dueDate = !empty($input['due_date']) ? $input['due_date'] : null;
            
            if (empty($title)) {
                throw new Exception('عنوان المهمة مطلوب');
            }
            
            if (!in_array($priority, ['low', 'medium', 'high'])) {
                $priority = 'medium';
            }
            
            $result = $userManager->createTodo($userId, $title, $description, $priority, $dueDate);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة المهمة بنجاح']);
            } else {
                throw new Exception('فشل في إضافة المهمة');
            }
            break;
            
        case 'update':
            $todoId = $input['todo_id'] ?? 0;
            $title = trim($input['title'] ?? '');
            $description = trim($input['description'] ?? '');
            $priority = $input['priority'] ?? 'medium';
            $dueDate = !empty($input['due_date']) ? $input['due_date'] : null;
            
            if (empty($title)) {
                throw new Exception('عنوان المهمة مطلوب');
            }
            
            if (!in_array($priority, ['low', 'medium', 'high'])) {
                $priority = 'medium';
            }
            
            $result = $userManager->updateTodo($todoId, $userId, $title, $description, $priority, $dueDate);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث المهمة بنجاح']);
            } else {
                throw new Exception('فشل في تحديث المهمة');
            }
            break;
            
        case 'toggle':
            $todoId = $input['todo_id'] ?? 0;
            
            if (!$todoId) {
                throw new Exception('معرف المهمة مطلوب');
            }
            
            $result = $userManager->toggleTodoComplete($todoId, $userId);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث حالة المهمة']);
            } else {
                throw new Exception('فشل في تحديث حالة المهمة');
            }
            break;
            
        case 'delete':
            $todoId = $input['todo_id'] ?? 0;
            
            if (!$todoId) {
                throw new Exception('معرف المهمة مطلوب');
            }
            
            $result = $userManager->deleteTodo($todoId, $userId);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم حذف المهمة بنجاح']);
            } else {
                throw new Exception('فشل في حذف المهمة');
            }
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
