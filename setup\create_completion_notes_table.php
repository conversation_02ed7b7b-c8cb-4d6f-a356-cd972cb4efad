<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    // Create course_completion_notes table
    $sql = "
        CREATE TABLE IF NOT EXISTS course_completion_notes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            completion_date DATETIME NOT NULL,
            note TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_completion (user_id, course_id),
            INDEX idx_user_id (user_id),
            INDEX idx_course_id (course_id),
            INDEX idx_completion_date (completion_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql);
    
    echo "✅ تم إنشاء جدول course_completion_notes بنجاح!\n";
    
    // Check if table was created successfully
    $stmt = $db->query("SHOW TABLES LIKE 'course_completion_notes'");
    if ($stmt->rowCount() > 0) {
        echo "✅ تم التحقق من وجود الجدول بنجاح!\n";
        
        // Show table structure
        $stmt = $db->query("DESCRIBE course_completion_notes");
        echo "\n📋 هيكل الجدول:\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        echo "| Field           | Type             | Null | Key | Default | Extra          |\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            printf("| %-15s | %-16s | %-4s | %-3s | %-7s | %-14s |\n",
                $row['Field'],
                $row['Type'],
                $row['Null'],
                $row['Key'],
                $row['Default'] ?? 'NULL',
                $row['Extra']
            );
        }
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        
    } else {
        echo "❌ فشل في إنشاء الجدول!\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 تم الانتهاء من إعداد جدول ملحوظات إكمال الكورسات!\n";
?>
