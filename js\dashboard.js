// Dashboard JavaScript Functions
// Get site URL from PHP - use relative path
const siteUrl = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');

// Global variables
let currentEditingTodo = null;
let currentEditingNote = null;

// Todo functions
function showAddTodoModal() {
    console.log('showAddTodoModal called');
    const modal = document.getElementById('todoModal');
    console.log('Todo modal found:', modal);
    if (modal) {
        const title = document.getElementById('todoModalTitle');
        const form = document.getElementById('todoForm');
        const todoId = document.getElementById('todoId');

        console.log('Elements found:', { title, form, todoId });

        if (title) title.textContent = 'إضافة مهمة جديدة';
        if (form) form.reset();
        if (todoId) todoId.value = '';

        currentEditingTodo = null;

        // Force display with multiple methods
        modal.style.setProperty('display', 'flex', 'important');
        modal.style.setProperty('visibility', 'visible', 'important');
        modal.style.setProperty('opacity', '1', 'important');
        modal.classList.add('modal-open');

        // Ensure modal is on top
        modal.style.zIndex = '999999';

        console.log('Todo modal displayed, style:', modal.style.display);
        console.log('Modal classes:', modal.className);

        // Focus on first input
        setTimeout(() => {
            const firstInput = modal.querySelector('input[type="text"]');
            if (firstInput) firstInput.focus();
        }, 100);
    } else {
        console.error('Todo modal not found!');
    }
}

function closeTodoModal() {
    const modal = document.getElementById('todoModal');
    if (modal) {
        modal.style.setProperty('display', 'none', 'important');
        modal.style.setProperty('visibility', 'hidden', 'important');
        modal.style.setProperty('opacity', '0', 'important');
        modal.classList.remove('modal-open');
        currentEditingTodo = null;
    }
}

function editTodo(todoId) {
    const todoItem = document.querySelector(`[data-id="${todoId}"]`);
    if (!todoItem) return;
    
    const title = todoItem.querySelector('.todo-title').textContent;
    const description = todoItem.querySelector('.todo-description')?.textContent || '';
    
    document.getElementById('todoModalTitle').textContent = 'تعديل المهمة';
    document.getElementById('todoId').value = todoId;
    document.getElementById('todoTitle').value = title;
    document.getElementById('todoDescription').value = description;
    currentEditingTodo = todoId;
    document.getElementById('todoModal').style.display = 'flex';
}

function showTodoTab(tab) {
    document.querySelectorAll('.todo-tab').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    document.querySelectorAll('.todo-list').forEach(list => list.classList.remove('active'));
    document.getElementById(`${tab}-todos`).classList.add('active');
}

function toggleTodo(todoId) {
    fetch('/manash/page/todo_actions.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'toggle', todo_id: todoId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (window.notifications) {
                window.notifications.success('تم تحديث المهمة بنجاح');
            }
            // Reload page after successful operation
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            if (window.notifications) {
                window.notifications.error('حدث خطأ: ' + data.message);
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (window.notifications) {
            window.notifications.error('حدث خطأ في الاتصال');
        } else {
            alert('حدث خطأ في الاتصال');
        }
    });
}

function deleteTodo(todoId) {
    const confirmDelete = async () => {
        if (window.notifications) {
            return await window.notifications.confirm('هل تريد حذف هذه المهمة؟', 'تأكيد الحذف');
        } else {
            return confirm('هل تريد حذف هذه المهمة؟');
        }
    };

    confirmDelete().then(confirmed => {
        if (confirmed) {
            fetch('/manash/page/todo_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'delete', todo_id: todoId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (window.notifications) {
                        window.notifications.success('تم حذف المهمة بنجاح');
                    }
                    // Reload page after successful operation
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    if (window.notifications) {
                        window.notifications.error('حدث خطأ: ' + data.message);
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (window.notifications) {
                    window.notifications.error('حدث خطأ في الاتصال');
                } else {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
    });
}

// Note functions
function showAddNoteModal() {
    console.log('showAddNoteModal called');
    const modal = document.getElementById('noteModal');
    console.log('Note modal found:', modal);
    if (modal) {
        const title = document.getElementById('noteModalTitle');
        const form = document.getElementById('noteForm');
        const noteId = document.getElementById('noteId');

        console.log('Elements found:', { title, form, noteId });

        if (title) title.textContent = 'إضافة ملاحظة جديدة';
        if (form) form.reset();
        if (noteId) noteId.value = '';

        currentEditingNote = null;

        // Force display with multiple methods
        modal.style.setProperty('display', 'flex', 'important');
        modal.style.setProperty('visibility', 'visible', 'important');
        modal.style.setProperty('opacity', '1', 'important');
        modal.classList.add('modal-open');

        // Ensure modal is on top
        modal.style.zIndex = '999999';

        console.log('Note modal displayed, style:', modal.style.display);
        console.log('Modal classes:', modal.className);

        // Focus on first input
        setTimeout(() => {
            const firstInput = modal.querySelector('input[type="text"]');
            if (firstInput) firstInput.focus();
        }, 100);
    } else {
        console.error('Note modal not found!');
    }
}

function closeNoteModal() {
    const modal = document.getElementById('noteModal');
    if (modal) {
        modal.style.setProperty('display', 'none', 'important');
        modal.style.setProperty('visibility', 'hidden', 'important');
        modal.style.setProperty('opacity', '0', 'important');
        modal.classList.remove('modal-open');
        currentEditingNote = null;
    }
}

function editNote(noteId) {
    const noteCard = document.querySelector(`[data-id="${noteId}"]`);
    if (!noteCard) return;
    
    const title = noteCard.querySelector('.note-title').textContent;
    const content = noteCard.querySelector('.note-content').textContent.replace('...', '');
    
    document.getElementById('noteModalTitle').textContent = 'تعديل الملاحظة';
    document.getElementById('noteId').value = noteId;
    document.getElementById('noteTitle').value = title;
    document.getElementById('noteContent').value = content;
    currentEditingNote = noteId;
    document.getElementById('noteModal').style.display = 'flex';
}

function toggleNotePin(noteId) {
    fetch('/manash/page/note_actions.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'toggle_pin', note_id: noteId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (window.notifications) {
                window.notifications.success('تم تحديث حالة التثبيت');
            }
            // Reload page after successful operation
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            if (window.notifications) {
                window.notifications.error('حدث خطأ: ' + data.message);
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (window.notifications) {
            window.notifications.error('حدث خطأ في الاتصال');
        } else {
            alert('حدث خطأ في الاتصال');
        }
    });
}

function deleteNote(noteId) {
    const confirmDelete = async () => {
        if (window.notifications) {
            return await window.notifications.confirm('هل تريد حذف هذه الملاحظة؟', 'تأكيد الحذف');
        } else {
            return confirm('هل تريد حذف هذه الملاحظة؟');
        }
    };

    confirmDelete().then(confirmed => {
        if (confirmed) {
            fetch('/manash/page/note_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'delete', note_id: noteId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (window.notifications) {
                        window.notifications.success('تم حذف الملاحظة بنجاح');
                    }
                    // Reload page after successful operation
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    if (window.notifications) {
                        window.notifications.error('حدث خطأ: ' + data.message);
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (window.notifications) {
                    window.notifications.error('حدث خطأ في الاتصال');
                } else {
                    alert('حدث خطأ في الاتصال');
                }
            });
        }
    });
}

// Sidebar toggle function
function toggleSidebar() {
    const sidebar = document.getElementById('dashboardSidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    if (sidebar) sidebar.classList.toggle('show');
    if (overlay) overlay.classList.toggle('show');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up dashboard');

    // Add click outside modal to close
    document.addEventListener('click', function(e) {
        const todoModal = document.getElementById('todoModal');
        const noteModal = document.getElementById('noteModal');

        if (e.target === todoModal) {
            closeTodoModal();
        }
        if (e.target === noteModal) {
            closeNoteModal();
        }
    });

    // Add escape key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeTodoModal();
            closeNoteModal();
        }
    });

    // Setup form handlers
    const todoForm = document.getElementById('todoForm');
    if (todoForm) {
        console.log('Todo form found, adding event listener');
        todoForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Todo form submitted');
            
            const formData = new FormData(this);
            const data = {
                action: currentEditingTodo ? 'update' : 'create',
                todo_id: formData.get('todo_id'),
                title: formData.get('title'),
                description: formData.get('description'),
                priority: formData.get('priority'),
                due_date: formData.get('due_date')
            };
            
            console.log('Sending todo data:', data);
            
            fetch('/manash/page/todo_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Todo response:', data);
                if (data.success) {
                    closeTodoModal();
                    if (window.notifications) {
                        window.notifications.success(currentEditingTodo ? 'تم تحديث المهمة بنجاح' : 'تم إضافة المهمة بنجاح');
                    }
                    // Reload page after successful operation
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    if (window.notifications) {
                        window.notifications.error('حدث خطأ: ' + data.message);
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Todo error:', error);
                alert('حدث خطأ في الاتصال');
            });
        });
    } else {
        console.log('Todo form not found');
    }

    const noteForm = document.getElementById('noteForm');
    if (noteForm) {
        console.log('Note form found, adding event listener');
        noteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Note form submitted');
            
            const formData = new FormData(this);
            const data = {
                action: currentEditingNote ? 'update' : 'create',
                note_id: formData.get('note_id'),
                title: formData.get('title'),
                content: formData.get('content'),
                category: formData.get('category')
            };
            
            console.log('Sending note data:', data);
            
            fetch('/manash/page/note_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Note response:', data);
                if (data.success) {
                    closeNoteModal();
                    if (window.notifications) {
                        window.notifications.success(currentEditingNote ? 'تم تحديث الملاحظة بنجاح' : 'تم إضافة الملاحظة بنجاح');
                    }
                    // Reload page after successful operation
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    if (window.notifications) {
                        window.notifications.error('حدث خطأ: ' + data.message);
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                }
            })
            .catch(error => {
                console.error('Note error:', error);
                alert('حدث خطأ في الاتصال');
            });
        });
    } else {
        console.log('Note form not found');
    }

    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        const todoModal = document.getElementById('todoModal');
        const noteModal = document.getElementById('noteModal');
        
        if (e.target === todoModal) {
            closeTodoModal();
        }
        if (e.target === noteModal) {
            closeNoteModal();
        }
    });

    // Close sidebar when clicking outside
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('dashboardSidebar');
        const overlay = document.getElementById('sidebarOverlay');
        const toggleBtn = document.querySelector('.sidebar-toggle');
        
        if (sidebar && overlay && toggleBtn) {
            if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            }
        }
    });

    // Add hover effects to quick action cards
    document.querySelectorAll('[style*="transition: transform"]').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    console.log('Dashboard initialization complete');
});

// Helper functions for dynamic updates
function updateTodoLists() {
    // Reload todo widget content
    fetch('/manash/includes/todo_widget.php')
        .then(response => response.text())
        .then(html => {
            const todoWidget = document.querySelector('.todo-widget .widget-content');
            if (todoWidget) {
                todoWidget.innerHTML = html;
                // Re-attach event listeners if needed
                initializeTodoEvents();
            }
        })
        .catch(error => {
            console.error('Error updating todo lists:', error);
            if (window.notifications) {
                window.notifications.error('حدث خطأ في تحديث المهام');
            }
        });
}

function updateNotesDisplay() {
    // Reload notes widget content
    fetch('../includes/notes_widget.php')
        .then(response => response.text())
        .then(html => {
            const notesWidget = document.querySelector('.notes-widget .widget-content');
            if (notesWidget) {
                notesWidget.innerHTML = html;
                // Re-attach event listeners if needed
                initializeNotesEvents();
            }
        })
        .catch(error => {
            console.error('Error updating notes display:', error);
            if (window.notifications) {
                window.notifications.error('حدث خطأ في تحديث الملاحظات');
            }
        });
}

// Initialize event listeners for todos
function initializeTodoEvents() {
    // Add any specific event listeners for todo items if needed
    console.log('Todo events initialized');
}

// Initialize event listeners for notes
function initializeNotesEvents() {
    // Add any specific event listeners for note items if needed
    console.log('Notes events initialized');
}

// Add fade out animation CSS if not exists
if (!document.querySelector('#fadeOutAnimation')) {
    const style = document.createElement('style');
    style.id = 'fadeOutAnimation';
    style.textContent = `
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
    `;
    document.head.appendChild(style);
}
