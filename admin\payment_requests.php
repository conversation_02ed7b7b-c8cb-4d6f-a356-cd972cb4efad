<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'approve':
                try {
                    $db->beginTransaction();

                    // Update payment request status
                    $stmt = $db->prepare("
                        UPDATE payment_requests
                        SET status = 'approved', reviewed_by = ?, reviewed_at = NOW(), admin_notes = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$_SESSION['admin_id'], $_POST['admin_notes'] ?? '', $_POST['request_id']]);

                    // Get payment request details
                    $stmt = $db->prepare("SELECT user_id, course_id FROM payment_requests WHERE id = ?");
                    $stmt->execute([$_POST['request_id']]);
                    $request = $stmt->fetch();

                    if ($request) {
                        // Activate course subscription (don't handle transaction since we're already in one)
                        $activationResult = $courseManager->activateSubscription(
                            $request['user_id'],
                            $request['course_id'],
                            'payment',
                            ['payment_request_id' => $_POST['request_id']],
                            false // Don't handle transaction
                        );

                        if (!$activationResult) {
                            throw new Exception('Failed to activate subscription');
                        }
                    }

                    $db->commit();
                    $message = 'تم قبول طلب الدفع وتفعيل الكورس بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    // Only rollback if transaction is active
                    if ($db->inTransaction()) {
                        $db->rollBack();
                    }
                    $message = 'خطأ في معالجة طلب الدفع';
                    $messageType = 'error';
                }
                break;

            case 'reject':
                try {
                    $stmt = $db->prepare("
                        UPDATE payment_requests 
                        SET status = 'rejected', reviewed_by = ?, reviewed_at = NOW(), admin_notes = ?
                        WHERE id = ?
                    ");
                    if ($stmt->execute([$_SESSION['admin_id'], $_POST['admin_notes'] ?? '', $_POST['request_id']])) {
                        $message = 'تم رفض طلب الدفع';
                        $messageType = 'success';
                    } else {
                        $message = 'فشل في رفض طلب الدفع';
                        $messageType = 'error';
                    }
                } catch (Exception $e) {
                    $message = 'خطأ في معالجة طلب الدفع';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get all payment requests
$stmt = $db->prepare("
    SELECT pr.*, c.title as course_title, c.price, c.discounted_price,
           u.username, u.first_name, u.second_name,
           a.full_name as reviewed_by_name
    FROM payment_requests pr
    JOIN courses c ON pr.course_id = c.id
    JOIN users u ON pr.user_id = u.id
    LEFT JOIN admins a ON pr.reviewed_by = a.id
    ORDER BY pr.created_at DESC
");
$stmt->execute();
$paymentRequests = $stmt->fetchAll();

// Get statistics
$stats = [
    'pending' => 0,
    'approved' => 0,
    'rejected' => 0,
    'total' => count($paymentRequests)
];

foreach ($paymentRequests as $request) {
    $stats[$request['status']]++;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات الدفع - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>طلبات الدفع</h1>
                <p>مراجعة وإدارة طلبات تفعيل الكورسات عبر الدفع</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['pending']; ?></div>
                    <div class="stat-label">طلبات معلقة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['approved']; ?></div>
                    <div class="stat-label">طلبات مقبولة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['rejected']; ?></div>
                    <div class="stat-label">طلبات مرفوضة</div>
                </div>
            </div>

            <!-- Payment Requests List -->
            <div class="content-card">
                <h2>طلبات الدفع</h2>
                <?php if (!empty($paymentRequests)): ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>الكورس</th>
                                    <th>السعر</th>
                                    <th>رقم الهاتف</th>
                                    <th>طريقة التحويل</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($paymentRequests as $request): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($request['first_name'] . ' ' . $request['second_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($request['username']); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($request['course_title']); ?></td>
                                        <td>
                                            <?php 
                                            $price = $request['discounted_price'] > 0 ? $request['discounted_price'] : $request['price'];
                                            echo number_format($price, 2) . ' جنيه';
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($request['mobile_number']); ?></td>
                                        <td>
                                            <?php
                                            $methods = [
                                                'vodafone_cash' => 'فودافون كاش',
                                                'etisalat_cash' => 'اتصالات كاش',
                                                'we_cash' => 'وي كاش',
                                                'orange_cash' => 'أورانج كاش'
                                            ];
                                            echo $methods[$request['transfer_method']] ?? $request['transfer_method'];
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClasses = [
                                                'pending' => 'status-pending',
                                                'approved' => 'status-approved',
                                                'rejected' => 'status-rejected'
                                            ];
                                            $statusTexts = [
                                                'pending' => 'معلق',
                                                'approved' => 'مقبول',
                                                'rejected' => 'مرفوض'
                                            ];
                                            ?>
                                            <span class="status-badge <?php echo $statusClasses[$request['status']]; ?>">
                                                <?php echo $statusTexts[$request['status']]; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button onclick="viewReceipt('<?php echo $request['receipt_image']; ?>')" class="btn btn-sm btn-info">
                                                    عرض الإيصال
                                                </button>
                                                <?php if ($request['status'] === 'pending'): ?>
                                                    <button onclick="showApprovalModal(<?php echo $request['id']; ?>)" class="btn btn-sm btn-success">
                                                        قبول
                                                    </button>
                                                    <button onclick="showRejectionModal(<?php echo $request['id']; ?>)" class="btn btn-sm btn-danger">
                                                        رفض
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p style="text-align: center; color: #666; padding: 40px;">لا توجد طلبات دفع حالياً</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Receipt Modal -->
    <div id="receiptModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إيصال الدفع</h3>
                <button class="modal-close" onclick="closeModal('receiptModal')">&times;</button>
            </div>
            <div class="modal-body">
                <img id="receiptImage" src="" alt="إيصال الدفع" style="max-width: 100%; height: auto;">
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div id="approvalModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>قبول طلب الدفع</h3>
                <button class="modal-close" onclick="closeModal('approvalModal')">&times;</button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="approve">
                    <input type="hidden" name="request_id" id="approveRequestId">
                    
                    <div class="form-group">
                        <label for="approveNotes">ملاحظات (اختياري)</label>
                        <textarea name="admin_notes" id="approveNotes" rows="3" placeholder="أضف أي ملاحظات..."></textarea>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-success">تأكيد القبول</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('approvalModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>رفض طلب الدفع</h3>
                <button class="modal-close" onclick="closeModal('rejectionModal')">&times;</button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="request_id" id="rejectRequestId">
                    
                    <div class="form-group">
                        <label for="rejectNotes">سبب الرفض *</label>
                        <textarea name="admin_notes" id="rejectNotes" rows="3" required placeholder="اكتب سبب رفض الطلب..."></textarea>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-danger">تأكيد الرفض</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('rejectionModal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <style>
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .text-muted {
            color: #6c757d;
            font-size: 0.9em;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-actions {
            padding: 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6c757d;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 8px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>

    <script>
        function viewReceipt(imageName) {
            const modal = document.getElementById('receiptModal');
            const image = document.getElementById('receiptImage');
            image.src = '../uploads/receipts/' + imageName;
            modal.style.display = 'flex';
        }

        function showApprovalModal(requestId) {
            document.getElementById('approveRequestId').value = requestId;
            document.getElementById('approvalModal').style.display = 'flex';
        }

        function showRejectionModal(requestId) {
            document.getElementById('rejectRequestId').value = requestId;
            document.getElementById('rejectionModal').style.display = 'flex';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });
        });
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
