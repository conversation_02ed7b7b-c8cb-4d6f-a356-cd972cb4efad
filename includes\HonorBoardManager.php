<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

class HonorBoardManager {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Add a new honor board entry
     */
    public function addHonorEntry($data) {
        try {
            $sql = "INSERT INTO honor_board (
                student_name, grade_score, subject, ranking_position, 
                achievement_type, achievement_date, education_level, 
                education_type, grade, specialization, additional_notes, created_by
            ) VALUES (
                :student_name, :grade_score, :subject, :ranking_position,
                :achievement_type, :achievement_date, :education_level,
                :education_type, :grade, :specialization, :additional_notes, :created_by
            )";
            
            $stmt = $this->db->prepare($sql);

            // Handle specialization - only allow valid ENUM values or NULL
            $specialization = null;
            if (!empty($data['specialization']) && in_array($data['specialization'], ['scientific', 'literary'])) {
                $specialization = $data['specialization'];
            }

            return $stmt->execute([
                ':student_name' => $data['student_name'],
                ':grade_score' => $data['grade_score'],
                ':subject' => $data['subject'],
                ':ranking_position' => $data['ranking_position'],
                ':achievement_type' => $data['achievement_type'],
                ':achievement_date' => $data['achievement_date'],
                ':education_level' => $data['education_level'],
                ':education_type' => $data['education_type'],
                ':grade' => $data['grade'],
                ':specialization' => $specialization,
                ':additional_notes' => $data['additional_notes'],
                ':created_by' => $data['created_by']
            ]);
        } catch (PDOException $e) {
            error_log("Error adding honor entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an existing honor board entry
     */
    public function updateHonorEntry($id, $data) {
        try {
            $sql = "UPDATE honor_board SET 
                student_name = :student_name,
                grade_score = :grade_score,
                subject = :subject,
                ranking_position = :ranking_position,
                achievement_type = :achievement_type,
                achievement_date = :achievement_date,
                education_level = :education_level,
                education_type = :education_type,
                grade = :grade,
                specialization = :specialization,
                additional_notes = :additional_notes,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :id";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                ':id' => $id,
                ':student_name' => $data['student_name'],
                ':grade_score' => $data['grade_score'],
                ':subject' => $data['subject'],
                ':ranking_position' => $data['ranking_position'],
                ':achievement_type' => $data['achievement_type'],
                ':achievement_date' => $data['achievement_date'],
                ':education_level' => $data['education_level'],
                ':education_type' => $data['education_type'],
                ':grade' => $data['grade'],
                ':specialization' => $data['specialization'],
                ':additional_notes' => $data['additional_notes']
            ]);
        } catch (PDOException $e) {
            error_log("Error updating honor entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a honor board entry
     */
    public function deleteHonorEntry($id) {
        try {
            $sql = "DELETE FROM honor_board WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error deleting honor entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get honor entry by ID
     */
    public function getHonorEntryById($id) {
        try {
            $sql = "SELECT * FROM honor_board WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting honor entry: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all honor board entries with optional filters
     */
    public function getAllHonorEntries($filters = []) {
        try {
            $sql = "SELECT h.*, a.full_name as created_by_name 
                    FROM honor_board h 
                    LEFT JOIN admins a ON h.created_by = a.id 
                    WHERE h.is_active = 1";
            
            $params = [];
            
            if (!empty($filters['education_level'])) {
                $sql .= " AND h.education_level = :education_level";
                $params[':education_level'] = $filters['education_level'];
            }
            
            if (!empty($filters['education_type'])) {
                $sql .= " AND h.education_type = :education_type";
                $params[':education_type'] = $filters['education_type'];
            }
            
            if (!empty($filters['grade'])) {
                $sql .= " AND h.grade = :grade";
                $params[':grade'] = $filters['grade'];
            }
            
            if (!empty($filters['subject'])) {
                $sql .= " AND h.subject = :subject";
                $params[':subject'] = $filters['subject'];
            }
            
            if (!empty($filters['achievement_type'])) {
                $sql .= " AND h.achievement_type = :achievement_type";
                $params[':achievement_type'] = $filters['achievement_type'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND h.achievement_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND h.achievement_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }
            
            $sql .= " ORDER BY h.ranking_position ASC, h.achievement_date DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting honor entries: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get top honor board entries for dashboard widget
     */
    public function getTopHonorEntries($limit = 10, $userEducation = null) {
        try {
            $sql = "SELECT * FROM honor_board
                    WHERE is_active = 1";

            $params = [];

            if ($userEducation) {
                // More flexible filtering - show entries that match user's education
                $sql .= " AND education_level = :education_level
                         AND education_type = :education_type
                         AND grade = :grade";
                $params[':education_level'] = $userEducation['education_level'];
                $params[':education_type'] = $userEducation['education_type'];
                $params[':grade'] = $userEducation['grade'];
            }

            $sql .= " ORDER BY ranking_position ASC, achievement_date DESC LIMIT :limit";

            $stmt = $this->db->prepare($sql);

            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);

            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting top honor entries: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get total honor entries count
     */
    public function getTotalHonorEntriesCount() {
        try {
            $sql = "SELECT COUNT(*) FROM honor_board WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Error getting total honor entries count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Toggle honor entry active status
     */
    public function toggleHonorEntryStatus($id) {
        try {
            $sql = "UPDATE honor_board SET is_active = NOT is_active WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error toggling honor entry status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get subjects list
     */
    public function getSubjects() {
        try {
            $sql = "SELECT DISTINCT subject FROM honor_board WHERE subject IS NOT NULL ORDER BY subject";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Error getting subjects: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if ranking position is available
     */
    public function isRankingPositionAvailable($position, $subject, $achievement_type, $achievement_date, $education_level, $education_type, $grade, $excludeId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM honor_board 
                    WHERE ranking_position = :position 
                    AND subject = :subject 
                    AND achievement_type = :achievement_type 
                    AND achievement_date = :achievement_date 
                    AND education_level = :education_level 
                    AND education_type = :education_type 
                    AND grade = :grade 
                    AND is_active = 1";
            
            $params = [
                ':position' => $position,
                ':subject' => $subject,
                ':achievement_type' => $achievement_type,
                ':achievement_date' => $achievement_date,
                ':education_level' => $education_level,
                ':education_type' => $education_type,
                ':grade' => $grade
            ];
            
            if ($excludeId) {
                $sql .= " AND id != :exclude_id";
                $params[':exclude_id'] = $excludeId;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn() == 0;
        } catch (PDOException $e) {
            error_log("Error checking ranking position: " . $e->getMessage());
            return false;
        }
    }
}
?>
