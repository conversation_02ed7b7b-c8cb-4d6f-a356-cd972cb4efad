<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';
require_once __DIR__ . '/../includes/PaymobGateway.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$paymobGateway = new PaymobGateway();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get user information
$stmt = $db->prepare("SELECT username, email, full_name, phone FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login.php');
    exit;
}

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Check if user already has access
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
if ($userCourseStatus && $userCourseStatus['activation_status'] === 'active') {
    header('Location: ' . SITE_URL . '/page/course_content.php?id=' . $courseId);
    exit;
}

$message = '';
$messageType = '';
$selectedWallet = $_POST['wallet_type'] ?? '';

// Calculate final price
$finalPrice = $course['discount_percentage'] > 0 ? $course['discounted_price'] : $course['price'];

// Get available wallets
$walletConfig = PAYMENT_GATEWAYS['paymob']['wallet_integrations'];
$availableWallets = [
    'vodafone_cash' => [
        'name' => 'فودافون كاش',
        'icon' => '📱',
        'color' => '#e60000',
        'enabled' => !empty($walletConfig['vodafone_cash']) && $walletConfig['vodafone_cash'] !== 'YOUR_VODAFONE_INTEGRATION_ID'
    ],
    'etisalat_cash' => [
        'name' => 'اتصالات كاش',
        'icon' => '💚',
        'color' => '#00b04f',
        'enabled' => !empty($walletConfig['etisalat_cash']) && $walletConfig['etisalat_cash'] !== 'YOUR_ETISALAT_INTEGRATION_ID'
    ],
    'we_cash' => [
        'name' => 'وي كاش',
        'icon' => '💜',
        'color' => '#662d91',
        'enabled' => !empty($walletConfig['we_cash']) && $walletConfig['we_cash'] !== 'YOUR_WE_INTEGRATION_ID'
    ],
    'orange_cash' => [
        'name' => 'أورانج كاش',
        'icon' => '🧡',
        'color' => '#ff6600',
        'enabled' => !empty($walletConfig['orange_cash']) && $walletConfig['orange_cash'] !== 'YOUR_ORANGE_INTEGRATION_ID'
    ]
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['wallet_type']) && !empty($_POST['wallet_type'])) {
        $walletType = $_POST['wallet_type'];
        
        if (!isset($availableWallets[$walletType]) || !$availableWallets[$walletType]['enabled']) {
            $message = 'نوع المحفظة المختار غير متاح';
            $messageType = 'error';
        } else {
            // Prepare customer info
            $customerInfo = [
                'name' => $user['full_name'] ?? $user['username'],
                'email' => $user['email'],
                'mobile' => $user['phone'] ?? '01000000000',
                'course_title' => $course['title']
            ];
            
            // Create wallet payment request using Paymob with specific integration
            $integrationId = $walletConfig[$walletType];
            
            // Temporarily override integration_id for this wallet
            $originalConfig = PAYMENT_GATEWAYS['paymob'];
            $tempConfig = $originalConfig;
            $tempConfig['integration_id'] = $integrationId;
            
            // Update config temporarily
            $GLOBALS['PAYMENT_GATEWAYS']['paymob'] = $tempConfig;
            
            $paymentResult = $paymobGateway->createPaymentRequest($userId, $courseId, $finalPrice, $customerInfo);
            
            // Restore original config
            $GLOBALS['PAYMENT_GATEWAYS']['paymob'] = $originalConfig;
            
            if ($paymentResult['success']) {
                // Redirect to Paymob payment page
                header('Location: ' . $paymentResult['payment_url']);
                exit;
            } else {
                $message = $paymentResult['error'];
                $messageType = 'error';
            }
        }
    } else {
        $message = 'يرجى اختيار نوع المحفظة';
        $messageType = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدفع بالمحافظ الإلكترونية - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="../page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="../page/courses.php">الكورسات</a>
                    <span>/</span>
                    <a href="../page/course_details.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a>
                    <span>/</span>
                    <span>المحافظ الإلكترونية</span>
                </div>

                <div class="payment-container">
                    <!-- Course Info Card -->
                    <div class="course-info-card">
                        <div class="course-image">
                            <?php if ($course['main_image']): ?>
                                <img src="../uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-details">
                            <h3><?php echo htmlspecialchars($course['title']); ?></h3>
                            <p><?php echo htmlspecialchars($course['subject']); ?></p>
                            <div class="course-price">
                                <?php if ($course['discount_percentage'] > 0): ?>
                                    <span class="original-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                    <span class="discounted-price"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <div class="payment-form-card">
                        <div class="form-header">
                            <div class="payment-icon">📱</div>
                            <h1>المحافظ الإلكترونية</h1>
                            <p>اختر المحفظة الإلكترونية المناسبة لك</p>
                        </div>

                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" class="wallet-form">
                            <div class="wallet-selection">
                                <h3>اختر المحفظة الإلكترونية:</h3>
                                <div class="wallets-grid">
                                    <?php foreach ($availableWallets as $walletKey => $wallet): ?>
                                        <?php if ($wallet['enabled']): ?>
                                            <div class="wallet-option" onclick="selectWallet('<?php echo $walletKey; ?>')">
                                                <input type="radio" name="wallet_type" value="<?php echo $walletKey; ?>" 
                                                       id="wallet_<?php echo $walletKey; ?>" 
                                                       <?php echo $selectedWallet === $walletKey ? 'checked' : ''; ?>>
                                                <label for="wallet_<?php echo $walletKey; ?>" class="wallet-card" 
                                                       style="border-color: <?php echo $wallet['color']; ?>">
                                                    <div class="wallet-icon" style="color: <?php echo $wallet['color']; ?>">
                                                        <?php echo $wallet['icon']; ?>
                                                    </div>
                                                    <div class="wallet-name"><?php echo $wallet['name']; ?></div>
                                                </label>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div class="payment-summary">
                                <h3>ملخص الدفع</h3>
                                <div class="summary-row">
                                    <span>الكورس:</span>
                                    <span><?php echo htmlspecialchars($course['title']); ?></span>
                                </div>
                                <div class="summary-row">
                                    <span>المبلغ:</span>
                                    <span class="amount"><?php echo number_format($finalPrice, 0); ?> جنيه</span>
                                </div>
                            </div>

                            <div class="wallet-features">
                                <h3>مميزات الدفع بالمحافظ الإلكترونية</h3>
                                <div class="features-list">
                                    <div class="feature">⚡ تفعيل فوري للكورس</div>
                                    <div class="feature">🔒 دفع آمن ومضمون</div>
                                    <div class="feature">📱 سهولة في الاستخدام</div>
                                    <div class="feature">💨 معالجة سريعة</div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large" id="payButton" disabled>
                                    <span class="btn-icon">📱</span>
                                    متابعة الدفع
                                </button>
                                <a href="../page/course_register.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">↩️</span>
                                    العودة لخيارات التفعيل
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 30px;
        }

        .course-info-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .course-image {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 40px;
            color: white;
        }

        .payment-form-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .payment-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .wallet-selection {
            margin-bottom: 30px;
        }

        .wallet-selection h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .wallets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .wallet-option {
            cursor: pointer;
        }

        .wallet-option input[type="radio"] {
            display: none;
        }

        .wallet-card {
            display: block;
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .wallet-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .wallet-option input[type="radio"]:checked + .wallet-card {
            border-width: 3px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }

        .wallet-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .wallet-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .payment-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .wallet-features {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .wallet-features h3 {
            color: #155724;
            margin-bottom: 20px;
            text-align: center;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .feature {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
            color: #155724;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-large:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .payment-container {
                padding: 15px;
            }

            .course-info-card {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .payment-form-card {
                padding: 25px;
            }

            .wallets-grid {
                grid-template-columns: 1fr;
            }

            .features-list {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        function selectWallet(walletType) {
            // Enable pay button when wallet is selected
            document.getElementById('payButton').disabled = false;
            
            // Update button text with selected wallet
            const walletNames = {
                'vodafone_cash': 'فودافون كاش',
                'etisalat_cash': 'اتصالات كاش',
                'we_cash': 'وي كاش',
                'orange_cash': 'أورانج كاش'
            };
            
            const button = document.getElementById('payButton');
            button.innerHTML = '<span class="btn-icon">📱</span>الدفع عبر ' + walletNames[walletType];
        }

        // Check if any wallet is already selected
        document.addEventListener('DOMContentLoaded', function() {
            const selectedWallet = document.querySelector('input[name="wallet_type"]:checked');
            if (selectedWallet) {
                selectWallet(selectedWallet.value);
            }
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
