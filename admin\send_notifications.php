<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Handle notification sending
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $type = $_POST['type'] ?? 'info';
    $recipients = $_POST['recipients'] ?? 'all';
    $specificUsersInput = $_POST['specific_users'] ?? '';
    // Handle both string and array inputs
    if (is_array($specificUsersInput)) {
        $specificUsers = $specificUsersInput;
    } else {
        $specificUsers = !empty($specificUsersInput) ? explode(',', $specificUsersInput) : [];
    }
    $educationLevel = $_POST['education_level'] ?? '';
    $educationType = $_POST['education_type'] ?? '';
    $grade = $_POST['grade'] ?? '';
    
    if (empty($title) || empty($content)) {
        $message = 'يرجى إدخال عنوان ومحتوى الإشعار';
        $messageType = 'error';
    } else {
        try {
            $userManager = new UserManager();
            $notificationManager = new NotificationManager();
            
            // Get target users based on selection
            $targetUsers = [];

            // Debug logging
            error_log("Recipients type: " . $recipients);
            error_log("Specific users input: " . print_r($specificUsersInput, true));
            error_log("Specific users array: " . print_r($specificUsers, true));

            if ($recipients === 'all') {
                $targetUsers = $userManager->getAllActiveUsers();
                error_log("All users count: " . count($targetUsers));
            } elseif ($recipients === 'specific') {
                if (!empty($specificUsers)) {
                    // Clean and convert to integers
                    $specificUsers = array_map('intval', array_filter($specificUsers));
                    $specificUsers = array_filter($specificUsers, function($id) { return $id > 0; });
                    error_log("Cleaned specific users: " . print_r($specificUsers, true));

                    if (!empty($specificUsers)) {
                        $targetUsers = $userManager->getUsersByIds($specificUsers);
                        error_log("Found specific users count: " . count($targetUsers));
                    } else {
                        error_log("No valid user IDs found");
                    }
                } else {
                    error_log("No specific users provided");
                }
            } elseif ($recipients === 'filtered') {
                $filters = [];
                if (!empty($educationLevel)) $filters['education_level'] = $educationLevel;
                if (!empty($educationType)) $filters['education_type'] = $educationType;
                if (!empty($grade)) $filters['grade'] = $grade;

                error_log("Filters: " . print_r($filters, true));
                $targetUsers = $userManager->getUsersByFilters($filters);
                error_log("Filtered users count: " . count($targetUsers));
            }
            
            if (empty($targetUsers)) {
                $message = 'لم يتم العثور على مستخدمين لإرسال الإشعار إليهم';
                $messageType = 'error';
            } else {
                // Send notifications
                $successCount = 0;
                foreach ($targetUsers as $user) {
                    $result = $notificationManager->createNotification(
                        $user['id'],
                        $title,
                        $content,
                        $type
                    );
                    if ($result) $successCount++;
                }
                
                $message = "تم إرسال الإشعار بنجاح إلى {$successCount} مستخدم";
                $messageType = 'success';
                
                // Log admin activity
                error_log("Admin {$_SESSION['admin_id']} sent notification to {$successCount} users: {$title}");
            }
            
        } catch (Exception $e) {
            $message = 'حدث خطأ أثناء إرسال الإشعار: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال إشعارات - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">

    <style>
        .notification-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .form-section h3 {
            margin-top: 0;
            color: #4682B4;
            border-bottom: 2px solid #87CEEB;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #4682B4;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .radio-item input[type="radio"] {
            width: auto;
        }
        
        .filter-section {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .filter-section.active {
            display: block;
        }
        
        .user-search {
            display: none;
            margin-top: 20px;
        }
        
        .user-search.active {
            display: block;
        }
        
        .selected-users {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            min-height: 50px;
        }
        
        .user-tag {
            display: inline-block;
            background: #4682B4;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 3px;
            font-size: 14px;
        }
        
        .user-tag .remove {
            margin-right: 8px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
        
        .message {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .preview-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .notification-preview {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .notification-preview .title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .notification-preview .content {
            color: #666;
            line-height: 1.5;
        }
        
        .notification-preview .type-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .type-badge.info { background: #d1ecf1; color: #0c5460; }
        .type-badge.success { background: #d4edda; color: #155724; }
        .type-badge.warning { background: #fff3cd; color: #856404; }
        .type-badge.error { background: #f8d7da; color: #721c24; }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="page-header">
                <h1>📢 إرسال إشعارات</h1>
                <p>إدارة وتحكم في النظام</p>
            </div>
                
                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" class="notification-form" id="notificationForm">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3>📝 معلومات الإشعار</h3>
                        
                        <div class="form-group">
                            <label for="title" class="form-label">عنوان الإشعار *</label>
                            <input type="text" id="title" name="title" class="form-input" required 
                                   placeholder="أدخل عنوان الإشعار" value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="content" class="form-label">محتوى الإشعار *</label>
                            <textarea id="content" name="content" class="form-textarea" required 
                                      placeholder="أدخل محتوى الإشعار"><?php echo htmlspecialchars($_POST['content'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">نوع الإشعار</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="type_info" name="type" value="info" 
                                           <?php echo ($_POST['type'] ?? 'info') === 'info' ? 'checked' : ''; ?>>
                                    <label for="type_info">معلومات</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="type_success" name="type" value="success"
                                           <?php echo ($_POST['type'] ?? '') === 'success' ? 'checked' : ''; ?>>
                                    <label for="type_success">نجاح</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="type_warning" name="type" value="warning"
                                           <?php echo ($_POST['type'] ?? '') === 'warning' ? 'checked' : ''; ?>>
                                    <label for="type_warning">تحذير</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="type_error" name="type" value="error"
                                           <?php echo ($_POST['type'] ?? '') === 'error' ? 'checked' : ''; ?>>
                                    <label for="type_error">خطأ</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recipients Selection -->
                    <div class="form-section">
                        <h3>👥 المستقبلين</h3>
                        
                        <div class="form-group">
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="recipients_all" name="recipients" value="all" 
                                           <?php echo ($_POST['recipients'] ?? 'all') === 'all' ? 'checked' : ''; ?>>
                                    <label for="recipients_all">جميع المستخدمين</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="recipients_filtered" name="recipients" value="filtered"
                                           <?php echo ($_POST['recipients'] ?? '') === 'filtered' ? 'checked' : ''; ?>>
                                    <label for="recipients_filtered">حسب الفلتر</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="recipients_specific" name="recipients" value="specific"
                                           <?php echo ($_POST['recipients'] ?? '') === 'specific' ? 'checked' : ''; ?>>
                                    <label for="recipients_specific">مستخدمين محددين</label>
                                </div>
                            </div>
                        </div>

                        <!-- Filter Section -->
                        <div id="filterSection" class="filter-section">
                            <h4>🔍 فلتر المستخدمين</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div class="form-group">
                                    <label for="education_level" class="form-label">المرحلة التعليمية</label>
                                    <select id="education_level" name="education_level" class="form-select">
                                        <option value="">جميع المراحل</option>
                                        <option value="primary">ابتدائي</option>
                                        <option value="preparatory">إعدادي</option>
                                        <option value="secondary">ثانوي</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="education_type" class="form-label">نوع التعليم</label>
                                    <select id="education_type" name="education_type" class="form-select">
                                        <option value="">جميع الأنواع</option>
                                        <option value="general">عام</option>
                                        <option value="azhari">أزهري</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="grade" class="form-label">الصف</label>
                                    <select id="grade" name="grade" class="form-select">
                                        <option value="">جميع الصفوف</option>
                                        <option value="1">الأول</option>
                                        <option value="2">الثاني</option>
                                        <option value="3">الثالث</option>
                                        <option value="4">الرابع</option>
                                        <option value="5">الخامس</option>
                                        <option value="6">السادس</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Specific Users Section -->
                        <div id="specificSection" class="user-search">
                            <h4>🔍 البحث عن مستخدمين</h4>
                            <input type="text" id="userSearch" placeholder="ابحث بالاسم أو اسم المستخدم أو البريد الإلكتروني" 
                                   class="form-input" style="margin-bottom: 15px;">
                            <div id="searchResults" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 5px;"></div>
                            <div class="selected-users" id="selectedUsers">
                                <p style="margin: 0; color: #666;">لم يتم اختيار مستخدمين بعد</p>
                            </div>
                            <input type="hidden" name="specific_users" id="specificUsersInput">
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="preview-section">
                        <h3>👁️ معاينة الإشعار</h3>
                        <div class="notification-preview" id="notificationPreview">
                            <div class="type-badge info" id="previewTypeBadge">معلومات</div>
                            <div class="title" id="previewTitle">عنوان الإشعار</div>
                            <div class="content" id="previewContent">محتوى الإشعار</div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn-primary">📤 إرسال الإشعار</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Handle recipient selection
        document.querySelectorAll('input[name="recipients"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const filterSection = document.getElementById('filterSection');
                const specificSection = document.getElementById('specificSection');
                
                filterSection.classList.remove('active');
                specificSection.classList.remove('active');
                
                if (this.value === 'filtered') {
                    filterSection.classList.add('active');
                } else if (this.value === 'specific') {
                    specificSection.classList.add('active');
                }
            });
        });

        // Live preview
        function updatePreview() {
            const title = document.getElementById('title').value || 'عنوان الإشعار';
            const content = document.getElementById('content').value || 'محتوى الإشعار';
            const type = document.querySelector('input[name="type"]:checked').value;
            
            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewContent').textContent = content;
            
            const badge = document.getElementById('previewTypeBadge');
            badge.className = `type-badge ${type}`;
            
            const typeLabels = {
                'info': 'معلومات',
                'success': 'نجاح', 
                'warning': 'تحذير',
                'error': 'خطأ'
            };
            badge.textContent = typeLabels[type];
        }

        // Add event listeners for live preview
        document.getElementById('title').addEventListener('input', updatePreview);
        document.getElementById('content').addEventListener('input', updatePreview);
        document.querySelectorAll('input[name="type"]').forEach(radio => {
            radio.addEventListener('change', updatePreview);
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state
            const checkedRecipient = document.querySelector('input[name="recipients"]:checked');
            if (checkedRecipient) {
                checkedRecipient.dispatchEvent(new Event('change'));
            }
            
            updatePreview();
        });

        // User search functionality
        let selectedUsers = [];
        let searchTimeout;

        document.getElementById('userSearch').addEventListener('input', function() {
            const query = this.value.trim();
            const resultsDiv = document.getElementById('searchResults');

            if (query.length < 2) {
                resultsDiv.innerHTML = '';
                return;
            }

            // Clear previous timeout
            clearTimeout(searchTimeout);

            // Show loading
            resultsDiv.innerHTML = '<p style="padding: 10px; color: #666;">جاري البحث...</p>';

            // Debounce search
            searchTimeout = setTimeout(() => {
                console.log('Searching for:', query);
                fetch(`../api/search_users.php?q=${encodeURIComponent(query)}&limit=10`)
                    .then(response => {
                        console.log('Search response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Search data:', data);
                        if (data.success && data.data.length > 0) {
                            resultsDiv.innerHTML = data.data.map(user => `
                                <div class="search-result-item" onclick="selectUser(${user.id}, '${user.display_text}')"
                                     style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; transition: background 0.2s;">
                                    <div style="font-weight: 500;">${user.display_text}</div>
                                    <div style="font-size: 12px; color: #666;">${user.secondary_text}</div>
                                </div>
                            `).join('');

                            // Add hover effects
                            document.querySelectorAll('.search-result-item').forEach(item => {
                                item.addEventListener('mouseenter', function() {
                                    this.style.background = '#f8f9fa';
                                });
                                item.addEventListener('mouseleave', function() {
                                    this.style.background = 'white';
                                });
                            });
                        } else {
                            resultsDiv.innerHTML = '<p style="padding: 10px; color: #666;">لم يتم العثور على نتائج</p>';
                        }
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        resultsDiv.innerHTML = '<p style="padding: 10px; color: #dc3545;">حدث خطأ في البحث</p>';
                    });
            }, 300);
        });

        function selectUser(userId, displayText) {
            console.log('Selecting user:', userId, displayText);

            // Validate inputs
            if (!userId || !displayText) {
                console.error('Invalid user data:', userId, displayText);
                return;
            }

            // Convert to integer
            userId = parseInt(userId);
            if (isNaN(userId) || userId <= 0) {
                console.error('Invalid user ID:', userId);
                return;
            }

            // Check if user already selected
            if (selectedUsers.find(u => u.id === userId)) {
                console.log('User already selected');
                if (window.notifications) {
                    window.notifications.warning('هذا المستخدم محدد بالفعل');
                }
                return;
            }

            // Add to selected users
            selectedUsers.push({ id: userId, name: displayText });
            console.log('Selected users:', selectedUsers);
            updateSelectedUsersDisplay();

            // Clear search
            document.getElementById('userSearch').value = '';
            document.getElementById('searchResults').innerHTML = '';

            // Show success message
            if (window.notifications) {
                window.notifications.success(`تم إضافة ${displayText}`);
            }
        }

        function removeUser(userId) {
            const userToRemove = selectedUsers.find(u => u.id === userId);
            selectedUsers = selectedUsers.filter(u => u.id !== userId);
            updateSelectedUsersDisplay();

            if (userToRemove && window.notifications) {
                window.notifications.info(`تم إزالة ${userToRemove.name}`);
            }

            console.log('Removed user:', userId, 'Remaining users:', selectedUsers);
        }

        function updateSelectedUsersDisplay() {
            const container = document.getElementById('selectedUsers');
            const input = document.getElementById('specificUsersInput');

            if (selectedUsers.length === 0) {
                container.innerHTML = '<p style="margin: 0; color: #666;">لم يتم اختيار مستخدمين بعد</p>';
                input.value = '';
            } else {
                container.innerHTML = selectedUsers.map(user => `
                    <span class="user-tag">
                        ${user.name}
                        <span class="remove" onclick="removeUser(${user.id})">×</span>
                    </span>
                `).join('');

                // Update hidden input
                input.value = selectedUsers.map(u => u.id).join(',');
            }
        }
    </script>

    <script src="../js/notifications.js"></script>
    <script>
        // Show success/error messages using the new notification system
        document.addEventListener('DOMContentLoaded', function() {
            <?php if (!empty($message)): ?>
                <?php if ($messageType === 'success'): ?>
                    notifications.success('<?php echo addslashes($message); ?>');
                <?php elseif ($messageType === 'error'): ?>
                    notifications.error('<?php echo addslashes($message); ?>');
                <?php else: ?>
                    notifications.info('<?php echo addslashes($message); ?>');
                <?php endif; ?>
            <?php endif; ?>
        });

        // Enhanced form submission with loading state
        document.querySelector('.notification-form').addEventListener('submit', function(e) {
            const recipients = document.querySelector('input[name="recipients"]:checked').value;
            console.log('Form submission - Recipients:', recipients);

            if (recipients === 'specific') {
                console.log('Selected users for submission:', selectedUsers);

                // Add selected user IDs to form
                const existingInput = document.querySelector('input[name="specific_users"]');
                if (existingInput) {
                    existingInput.remove();
                }

                const userIds = selectedUsers.map(u => u.id);
                console.log('User IDs to send:', userIds);

                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'specific_users';
                hiddenInput.value = userIds.join(',');
                this.appendChild(hiddenInput);

                if (userIds.length === 0) {
                    e.preventDefault();
                    if (window.notifications) {
                        window.notifications.error('يرجى اختيار مستخدم واحد على الأقل');
                    } else {
                        alert('يرجى اختيار مستخدم واحد على الأقل');
                    }
                    return;
                }
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            const loading = notifications.showLoading('جاري إرسال الإشعار...');
            submitBtn.disabled = true;
            submitBtn.textContent = 'جاري الإرسال...';

            // The form will submit normally, but we show loading feedback
            setTimeout(() => {
                loading.close();
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }, 1000);
        });
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
