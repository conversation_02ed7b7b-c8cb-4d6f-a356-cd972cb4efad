/* ===== ADMIN PANEL FIXES - إصلاحات التصميم ===== */

/* Reset any conflicting styles */
* {
    box-sizing: border-box;
}

/* Ensure proper layout structure */
body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    direction: rtl;
    background: #f8fafc;
}

/* Fix header positioning */
.admin-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 70px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid #e2e8f0 !important;
    z-index: 1000 !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

/* Fix sidebar positioning */
.modern-sidebar {
    position: fixed !important;
    top: 70px !important;
    right: 0 !important;
    width: 280px !important;
    height: calc(100vh - 70px) !important;
    background: rgba(255, 255, 255, 0.98) !important;
    border-left: 1px solid #e2e8f0 !important;
    overflow-y: auto !important;
    z-index: 999 !important;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
}

/* Fix main content positioning */
.main-content {
    margin-top: 70px !important;
    margin-right: 280px !important;
    padding: 2rem !important;
    min-height: calc(100vh - 70px) !important;
    background: #f8fafc !important;
    transition: margin-right 0.3s ease !important;
}

/* Fix admin container */
.admin-container {
    display: block !important;
    min-height: 100vh !important;
    background: #f8fafc !important;
}

/* Header container fixes */
.header-container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    height: 100% !important;
    padding: 0 1.5rem !important;
    max-width: 100% !important;
}

/* Header brand fixes */
.header-brand {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.header-logo {
    width: 40px !important;
    height: 40px !important;
    border-radius: 0.75rem !important;
    object-fit: cover !important;
}

.brand-text h1 {
    font-size: 1.25rem !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 !important;
}

.brand-text span {
    font-size: 0.875rem !important;
    color: #64748b !important;
}

/* Header actions fixes */
.header-actions {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

/* Search container fixes */
.search-container {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
}

.search-input {
    width: 300px !important;
    padding: 0.5rem 1rem !important;
    padding-left: 40px !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 0.75rem !important;
    background: #f1f5f9 !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
}

.search-input:focus {
    outline: none !important;
    border-color: #4f46e5 !important;
    background: #ffffff !important;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
}

/* Notification button fixes */
.notification-btn {
    position: relative !important;
    background: none !important;
    border: none !important;
    padding: 0.5rem !important;
    border-radius: 0.75rem !important;
    cursor: pointer !important;
    color: #475569 !important;
    transition: all 0.3s ease !important;
}

.notification-btn:hover {
    background: #f1f5f9 !important;
    color: #4f46e5 !important;
}

.notification-badge {
    position: absolute !important;
    top: 2px !important;
    right: 2px !important;
    background: #ef4444 !important;
    color: white !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    padding: 2px 6px !important;
    border-radius: 10px !important;
    min-width: 18px !important;
    text-align: center !important;
}

/* Admin profile button fixes */
.admin-profile-btn {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    background: none !important;
    border: none !important;
    padding: 0.5rem !important;
    border-radius: 0.75rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.admin-profile-btn:hover {
    background: #f1f5f9 !important;
}

.admin-avatar {
    width: 36px !important;
    height: 36px !important;
    background: linear-gradient(135deg, #4f46e5, #6366f1) !important;
    border-radius: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
}

/* Sidebar brand fixes */
.sidebar-brand {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.brand-logo {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.logo-img {
    width: 32px !important;
    height: 32px !important;
    border-radius: 0.5rem !important;
}

.brand-info h3 {
    font-size: 1rem !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 !important;
}

.brand-info span {
    font-size: 0.75rem !important;
    color: #64748b !important;
}

/* Sidebar profile fixes */
.sidebar-profile {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e2e8f0 !important;
    background: linear-gradient(135deg, #f1f5f9 0%, rgba(241, 245, 249, 0.5) 100%) !important;
}

.profile-avatar {
    width: 48px !important;
    height: 48px !important;
    background: linear-gradient(135deg, #4f46e5, #6366f1) !important;
    border-radius: 1rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    flex-shrink: 0 !important;
}

/* Navigation fixes */
.sidebar-navigation {
    padding: 1rem 0 !important;
}

.nav-section {
    margin-bottom: 1.5rem !important;
}

.section-title {
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    color: #64748b !important;
    padding: 0 1.5rem 0.5rem !important;
    margin: 0 !important;
    letter-spacing: 0.5px !important;
}

.nav-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.nav-item {
    margin: 0 1rem 0.25rem !important;
}

.nav-link {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    color: #475569 !important;
    text-decoration: none !important;
    border-radius: 0.75rem !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.nav-link:hover {
    color: #4f46e5 !important;
    transform: translateX(-2px) !important;
    background: rgba(79, 70, 229, 0.1) !important;
}

.nav-item.active .nav-link {
    color: #4f46e5 !important;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(99, 102, 241, 0.05)) !important;
    font-weight: 600 !important;
}

.nav-item.active .nav-link::after {
    content: '' !important;
    position: absolute !important;
    right: 0 !important;
    top: 20% !important;
    bottom: 20% !important;
    width: 3px !important;
    background: linear-gradient(135deg, #4f46e5, #6366f1) !important;
    border-radius: 2px 0 0 2px !important;
}

.nav-icon {
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
}

.nav-text {
    flex: 1 !important;
    font-size: 0.875rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Sidebar footer fixes */
.sidebar-footer {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding: 1.5rem !important;
    border-top: 1px solid #e2e8f0 !important;
    background: #ffffff !important;
}

.logout-link {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    padding: 1rem !important;
    color: #ef4444 !important;
    text-decoration: none !important;
    border-radius: 0.75rem !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.logout-link:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    border-color: #ef4444 !important;
}

/* Mobile responsive fixes */
@media (max-width: 1920) {
    .modern-sidebar {
        transform: translateX(100%) !important;
        transition: transform 0.3s ease !important;
    }
    
    .modern-sidebar.open {
        transform: translateX(0) !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .mobile-menu-toggle {
        display: flex !important;
    }
}

@media (max-width: 1920px) {
    .search-container {
        display: none !important;
    }
    
    .header-container {
        padding: 0 1rem !important;
    }
    
    .main-content {
        padding: 1rem !important;
    }
}

/* Modal Fixes - إصلاح النوافذ المنبثقة */
.modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px) !important;
    z-index: 2000 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.modal-overlay.show {
    opacity: 1 !important;
    visibility: visible !important;
}

.modal {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) scale(0.9) !important;
    background: #ffffff !important;
    border-radius: 1rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    z-index: 2001 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    min-width: 500px !important;
}

.modal.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translate(-50%, -50%) scale(1) !important;
}

.modal-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e2e8f0 !important;
    background: #f8fafc !important;
    border-radius: 1rem 1rem 0 0 !important;
}

.modal-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #1e293b !important;
    margin: 0 !important;
}

.modal-close {
    background: none !important;
    border: none !important;
    font-size: 1.5rem !important;
    color: #64748b !important;
    cursor: pointer !important;
    padding: 0.25rem !important;
    border-radius: 0.375rem !important;
    transition: all 0.3s ease !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.modal-close:hover {
    background: #f1f5f9 !important;
    color: #1e293b !important;
}

.modal-body {
    padding: 1.5rem !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.modal-footer {
    display: flex !important;
    justify-content: flex-end !important;
    gap: 1rem !important;
    padding: 1.5rem !important;
    border-top: 1px solid #e2e8f0 !important;
    background: #f8fafc !important;
    border-radius: 0 0 1rem 1rem !important;
}

/* Form Elements in Modals */
.modal .form-group {
    margin-bottom: 1rem !important;
}

.modal .form-label {
    display: block !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #1e293b !important;
    margin-bottom: 0.5rem !important;
}

.modal .form-input,
.modal .form-select,
.modal .form-textarea {
    width: 100% !important;
    padding: 0.75rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
    color: #1e293b !important;
}

.modal .form-input:focus,
.modal .form-select:focus,
.modal .form-textarea:focus {
    outline: none !important;
    border-color: #4f46e5 !important;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
}

.modal .form-textarea {
    resize: vertical !important;
    min-height: 100px !important;
}

/* Grid layouts in modals */
.modal .grid-2 {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1rem !important;
}

.modal .grid-3 {
    display: grid !important;
    grid-template-columns: 1fr 1fr 1fr !important;
    gap: 1rem !important;
}

/* Buttons in modals */
.modal .btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    border: none !important;
    border-radius: 0.5rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.modal .btn-primary {
    background: linear-gradient(135deg, #4f46e5, #6366f1) !important;
    color: white !important;
}

.modal .btn-primary:hover {
    background: linear-gradient(135deg, #3730a3, #4f46e5) !important;
    transform: translateY(-1px) !important;
}

.modal .btn-secondary {
    background: #f1f5f9 !important;
    color: #475569 !important;
    border: 1px solid #d1d5db !important;
}

.modal .btn-secondary:hover {
    background: #e2e8f0 !important;
    color: #1e293b !important;
}

/* Ensure proper z-index stacking */
.toast {
    z-index: 3000 !important;
}

/* Mobile responsive for modals */
@media (max-width: 768px) {
    .modal {
        min-width: 95vw !important;
        max-width: 95vw !important;
        margin: 1rem !important;
    }

    .modal .grid-2,
    .modal .grid-3 {
        grid-template-columns: 1fr !important;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem !important;
    }
}

/* Fix any layout issues */
.page-header h1 {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 0 0.5rem 0 !important;
}

.content-card {
    background: #ffffff !important;
    border-radius: 1rem !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    transition: all 0.3s ease !important;
}

.content-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
}
