<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

class ExamManager {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Add a new exam
     */
    public function addExam($data) {
        try {
            $sql = "INSERT INTO upcoming_exams (
                exam_name, subject, exam_date, exam_time, duration_minutes, 
                location, instructions, education_level, education_type, 
                grade, specialization, created_by
            ) VALUES (
                :exam_name, :subject, :exam_date, :exam_time, :duration_minutes,
                :location, :instructions, :education_level, :education_type,
                :grade, :specialization, :created_by
            )";
            
            $stmt = $this->db->prepare($sql);

            // Handle specialization - only allow valid ENUM values or NULL
            $specialization = null;
            if (!empty($data['specialization']) && in_array($data['specialization'], ['scientific', 'literary'])) {
                $specialization = $data['specialization'];
            }

            return $stmt->execute([
                ':exam_name' => $data['exam_name'],
                ':subject' => $data['subject'],
                ':exam_date' => $data['exam_date'],
                ':exam_time' => $data['exam_time'],
                ':duration_minutes' => $data['duration_minutes'],
                ':location' => $data['location'],
                ':instructions' => $data['instructions'],
                ':education_level' => $data['education_level'],
                ':education_type' => $data['education_type'],
                ':grade' => $data['grade'],
                ':specialization' => $specialization,
                ':created_by' => $data['created_by']
            ]);
        } catch (PDOException $e) {
            error_log("Error adding exam: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an existing exam
     */
    public function updateExam($id, $data) {
        try {
            $sql = "UPDATE upcoming_exams SET 
                exam_name = :exam_name,
                subject = :subject,
                exam_date = :exam_date,
                exam_time = :exam_time,
                duration_minutes = :duration_minutes,
                location = :location,
                instructions = :instructions,
                education_level = :education_level,
                education_type = :education_type,
                grade = :grade,
                specialization = :specialization,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :id";
            
            $stmt = $this->db->prepare($sql);

            // Handle specialization - only allow valid ENUM values or NULL
            $specialization = null;
            if (!empty($data['specialization']) && in_array($data['specialization'], ['scientific', 'literary'])) {
                $specialization = $data['specialization'];
            }

            return $stmt->execute([
                ':id' => $id,
                ':exam_name' => $data['exam_name'],
                ':subject' => $data['subject'],
                ':exam_date' => $data['exam_date'],
                ':exam_time' => $data['exam_time'],
                ':duration_minutes' => $data['duration_minutes'],
                ':location' => $data['location'],
                ':instructions' => $data['instructions'],
                ':education_level' => $data['education_level'],
                ':education_type' => $data['education_type'],
                ':grade' => $data['grade'],
                ':specialization' => $specialization
            ]);
        } catch (PDOException $e) {
            error_log("Error updating exam: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete an exam
     */
    public function deleteExam($id) {
        try {
            $sql = "DELETE FROM upcoming_exams WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error deleting exam: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get exam by ID
     */
    public function getExamById($id) {
        try {
            $sql = "SELECT * FROM upcoming_exams WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting exam: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all exams with optional filters
     */
    public function getAllExams($filters = []) {
        try {
            $sql = "SELECT e.*, a.full_name as created_by_name 
                    FROM upcoming_exams e 
                    LEFT JOIN admins a ON e.created_by = a.id 
                    WHERE e.is_active = 1";
            
            $params = [];
            
            if (!empty($filters['education_level'])) {
                $sql .= " AND e.education_level = :education_level";
                $params[':education_level'] = $filters['education_level'];
            }
            
            if (!empty($filters['education_type'])) {
                $sql .= " AND e.education_type = :education_type";
                $params[':education_type'] = $filters['education_type'];
            }
            
            if (!empty($filters['grade'])) {
                $sql .= " AND e.grade = :grade";
                $params[':grade'] = $filters['grade'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND e.exam_date >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND e.exam_date <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }
            
            $sql .= " ORDER BY e.exam_date ASC, e.exam_time ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting exams: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get upcoming exams for dashboard widget
     */
    public function getUpcomingExams($limit = 5, $userEducation = null) {
        try {
            $sql = "SELECT * FROM upcoming_exams
                    WHERE is_active = 1 AND exam_date >= CURDATE()";

            $params = [];

            if ($userEducation) {
                // More flexible filtering - show exams that match user's education or are for all
                $sql .= " AND (education_level = :education_level OR education_level = 'all')
                         AND (education_type = :education_type OR education_type = 'all')
                         AND (grade = :grade OR grade = 'all')";
                $params[':education_level'] = $userEducation['education_level'];
                $params[':education_type'] = $userEducation['education_type'];
                $params[':grade'] = $userEducation['grade'];
            }

            $sql .= " ORDER BY exam_date ASC, exam_time ASC LIMIT :limit";

            $stmt = $this->db->prepare($sql);

            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);

            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting upcoming exams: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get total exams count
     */
    public function getTotalExamsCount() {
        try {
            $sql = "SELECT COUNT(*) FROM upcoming_exams WHERE is_active = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Error getting total exams count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Toggle exam active status
     */
    public function toggleExamStatus($id) {
        try {
            $sql = "UPDATE upcoming_exams SET is_active = NOT is_active WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error toggling exam status: " . $e->getMessage());
            return false;
        }
    }
}
?>
