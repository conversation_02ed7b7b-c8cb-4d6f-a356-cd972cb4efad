/**
 * Enhanced UI JavaScript
 * Provides advanced interactions and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== ENHANCED LINK INTERACTIONS =====
    
    // Remove any remaining underlines and add smooth transitions
    function enhanceLinks() {
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            // Ensure no underlines
            link.style.textDecoration = 'none';
            link.style.textDecorationLine = 'none';
            link.style.borderBottom = 'none';
            
            // Add smooth hover effects
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }
    
    // ===== ENHANCED SCROLLBAR INTERACTIONS =====
    
    // Add smooth scrolling behavior
    function enhanceScrolling() {
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Add scroll progress indicator
        const scrollProgress = document.createElement('div');
        scrollProgress.className = 'scroll-progress';
        scrollProgress.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, #87CEEB 0%, #4682B4 50%, #5F9EA0 100%);
            z-index: 9999;
            transition: width 0.1s ease;
            box-shadow: 0 2px 8px rgba(70, 130, 180, 0.3);
        `;
        document.body.appendChild(scrollProgress);
        
        // Update scroll progress
        window.addEventListener('scroll', function() {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            scrollProgress.style.width = scrolled + '%';
        });
    }
    
    // ===== ENHANCED CURSOR INTERACTIONS =====

    // Enhanced cursor styles for different elements
    function enhanceCursors() {
        // Enhanced cursor for interactive elements
        const interactiveElements = document.querySelectorAll('a, button, .btn, .clickable, .course-card, .lesson-card');
        interactiveElements.forEach(element => {
            element.style.cursor = 'pointer';

            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Text cursor for input fields
        const inputElements = document.querySelectorAll('input, textarea, select');
        inputElements.forEach(element => {
            element.style.cursor = 'text';
        });

        // Help cursor for tooltips
        const helpElements = document.querySelectorAll('.tooltip, .help-icon, [title]');
        helpElements.forEach(element => {
            element.style.cursor = 'help';
        });

        // Not-allowed cursor for disabled elements
        const disabledElements = document.querySelectorAll('.disabled, [disabled]');
        disabledElements.forEach(element => {
            element.style.cursor = 'not-allowed';
        });
    }
    
    // ===== ENHANCED CARD INTERACTIONS =====
    
    function enhanceCards() {
        const cards = document.querySelectorAll('.card, .course-card, .lesson-card, .exercise-card, .exam-card');
        
        cards.forEach(card => {
            // Add ripple effect
            card.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: radial-gradient(circle, rgba(135, 206, 235, 0.3) 0%, transparent 70%);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                    z-index: 1;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
            
            // Add tilt effect
            card.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-5px)`;
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
            });
        });
    }
    
    // ===== ENHANCED FORM INTERACTIONS =====
    
    function enhanceForms() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            // Add floating label effect
            const label = input.previousElementSibling;
            if (label && label.tagName === 'LABEL') {
                input.addEventListener('focus', function() {
                    label.style.transform = 'translateY(-25px) scale(0.8)';
                    label.style.color = '#4682B4';
                });
                
                input.addEventListener('blur', function() {
                    if (!this.value) {
                        label.style.transform = 'translateY(0) scale(1)';
                        label.style.color = '#6c757d';
                    }
                });
            }
            
            // Add input validation styling
            input.addEventListener('input', function() {
                if (this.checkValidity()) {
                    this.style.borderColor = '#28a745';
                    this.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';
                } else {
                    this.style.borderColor = '#dc3545';
                    this.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';
                }
            });
        });
    }
    
    // ===== ENHANCED LOADING ANIMATIONS =====
    
    function enhanceLoading() {
        // Add loading overlay for AJAX requests
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        const spinner = document.createElement('div');
        spinner.style.cssText = `
            width: 60px;
            height: 60px;
            border: 4px solid rgba(135, 206, 235, 0.3);
            border-top: 4px solid #4682B4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        `;
        
        loadingOverlay.appendChild(spinner);
        document.body.appendChild(loadingOverlay);
        
        // Show loading for forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                loadingOverlay.style.display = 'flex';
            });
        });
    }
    
    // ===== ENHANCED NOTIFICATIONS =====
    
    function enhanceNotifications() {
        // Create notification container
        const notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(notificationContainer);
        
        // Function to show notifications
        window.showNotification = function(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                background: white;
                border-radius: 12px;
                padding: 16px 20px;
                margin-bottom: 10px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                border-left: 4px solid ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#4682B4'};
                transform: translateX(100%);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                cursor: pointer;
            `;
            
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#4682B4'}; font-size: 20px;">
                        ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}
                    </div>
                    <div style="flex: 1; color: #333;">${message}</div>
                    <div style="color: #999; cursor: pointer; font-size: 18px;">×</div>
                </div>
            `;
            
            notificationContainer.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // Auto remove
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, duration);
            
            // Click to remove
            notification.addEventListener('click', function() {
                this.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    this.remove();
                }, 300);
            });
        };
    }
    
    // ===== ADD RIPPLE ANIMATION CSS =====
    
    const rippleCSS = document.createElement('style');
    rippleCSS.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(rippleCSS);
    
    // ===== INITIALIZE ALL ENHANCEMENTS =====
    
    enhanceLinks();
    enhanceScrolling();
    enhanceCursors();
    enhanceCards();
    enhanceForms();
    enhanceLoading();
    enhanceNotifications();
    
    console.log('Enhanced UI initialized successfully!');
});
