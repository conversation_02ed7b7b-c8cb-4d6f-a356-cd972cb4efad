<?php
require_once __DIR__ . '/../config/config.php';
require_once 'database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('معطيات غير صحيحة');
    }
    
    $userManager = new UserManager();
    $userId = $_SESSION['user_id'];
    $action = $input['action'];
    
    switch ($action) {
        case 'create':
            $title = trim($input['title'] ?? '');
            $content = trim($input['content'] ?? '');
            $category = trim($input['category'] ?? 'general');
            
            if (empty($title)) {
                throw new Exception('عنوان الملاحظة مطلوب');
            }
            
            if (empty($content)) {
                throw new Exception('محتوى الملاحظة مطلوب');
            }
            
            $result = $userManager->createNote($userId, $title, $content, $category);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة الملاحظة بنجاح']);
            } else {
                throw new Exception('فشل في إضافة الملاحظة');
            }
            break;
            
        case 'update':
            $noteId = $input['note_id'] ?? 0;
            $title = trim($input['title'] ?? '');
            $content = trim($input['content'] ?? '');
            $category = trim($input['category'] ?? 'general');
            
            if (empty($title)) {
                throw new Exception('عنوان الملاحظة مطلوب');
            }
            
            if (empty($content)) {
                throw new Exception('محتوى الملاحظة مطلوب');
            }
            
            $result = $userManager->updateNote($noteId, $userId, $title, $content, $category);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث الملاحظة بنجاح']);
            } else {
                throw new Exception('فشل في تحديث الملاحظة');
            }
            break;
            
        case 'toggle_pin':
            $noteId = $input['note_id'] ?? 0;
            
            if (!$noteId) {
                throw new Exception('معرف الملاحظة مطلوب');
            }
            
            $result = $userManager->toggleNotePin($noteId, $userId);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث حالة التثبيت']);
            } else {
                throw new Exception('فشل في تحديث حالة التثبيت');
            }
            break;
            
        case 'delete':
            $noteId = $input['note_id'] ?? 0;
            
            if (!$noteId) {
                throw new Exception('معرف الملاحظة مطلوب');
            }
            
            $result = $userManager->deleteNote($noteId, $userId);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'تم حذف الملاحظة بنجاح']);
            } else {
                throw new Exception('فشل في حذف الملاحظة');
            }
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
