<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id']) || !isset($_GET['method'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$courseId = $_GET['id'];
$method = $_GET['method'];

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

// Validate method
if (!in_array($method, ['code', 'payment'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم التفعيل بنجاح - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <div class="success-container">
                    <div class="success-card">
                        <!-- Success Animation -->
                        <div class="success-animation">
                            <div class="success-icon">🎉</div>
                            <div class="checkmark">
                                <svg viewBox="0 0 52 52" class="checkmark-svg">
                                    <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>
                                    <path class="checkmark-check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                                </svg>
                            </div>
                        </div>

                        <!-- Success Content -->
                        <div class="success-content">
                            <?php if ($method === 'code'): ?>
                                <h1>🎓 تم تفعيل الكورس بنجاح!</h1>
                                <p class="success-message">
                                    تهانينا! تم تفعيل كورس <strong><?php echo htmlspecialchars($course['title']); ?></strong> بنجاح.
                                    يمكنك الآن الوصول إلى جميع محتويات الكورس والبدء في التعلم.
                                </p>
                            <?php else: ?>
                                <h1>📤 تم إرسال طلب التفعيل!</h1>
                                <p class="success-message">
                                    تم إرسال طلب تفعيل كورس <strong><?php echo htmlspecialchars($course['title']); ?></strong> بنجاح.
                                    سيتم مراجعة طلبك وتفعيل الكورس خلال 1-24 ساعة.
                                </p>
                            <?php endif; ?>
                        </div>

                        <!-- Course Info -->
                        <div class="course-info-section">
                            <div class="course-image">
                                <?php if ($course['main_image']): ?>
                                    <img src="../uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                         alt="<?php echo htmlspecialchars($course['title']); ?>">
                                <?php else: ?>
                                    <div class="course-placeholder">
                                        <span class="course-icon">📚</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="course-details">
                                <h3><?php echo htmlspecialchars($course['title']); ?></h3>
                                <p><?php echo htmlspecialchars($course['subject']); ?></p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <?php if ($method === 'code'): ?>
                                <a href="../page/course_content.php?id=<?php echo $courseId; ?>" class="btn btn-primary btn-large">
                                    <span class="btn-icon">🚀</span>
                                    ابدأ التعلم الآن
                                </a>
                                <a href="../page/courses.php" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">📚</span>
                                    تصفح المزيد من الكورسات
                                </a>
                            <?php else: ?>
                                <a href="../page/dashboard.php" class="btn btn-primary btn-large">
                                    <span class="btn-icon">🏠</span>
                                    العودة للرئيسية
                                </a>
                                <a href="../page/courses.php" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">📚</span>
                                    تصفح الكورسات
                                </a>
                            <?php endif; ?>
                        </div>

                        <!-- Additional Info -->
                        <?php if ($method === 'payment'): ?>
                            <div class="additional-info">
                                <div class="info-card">
                                    <div class="info-icon">⏰</div>
                                    <div class="info-content">
                                        <h4>وقت المراجعة</h4>
                                        <p>سيتم مراجعة طلبك خلال 1-24 ساعة من الآن</p>
                                    </div>
                                </div>
                                
                                <div class="info-card">
                                    <div class="info-icon">📧</div>
                                    <div class="info-content">
                                        <h4>إشعار التفعيل</h4>
                                        <p>ستصلك رسالة تأكيد عند تفعيل الكورس</p>
                                    </div>
                                </div>
                                
                                <div class="info-card">
                                    <div class="info-icon">💬</div>
                                    <div class="info-content">
                                        <h4>تحتاج مساعدة؟</h4>
                                        <p>تواصل معنا: <a href="https://wa.me/201128031228" target="_blank">01128031228</a></p>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="additional-info">
                                <div class="info-card">
                                    <div class="info-icon">🎯</div>
                                    <div class="info-content">
                                        <h4>ابدأ رحلتك التعليمية</h4>
                                        <p>استكشف محتوى الكورس وابدأ في حل التمارين</p>
                                    </div>
                                </div>
                                
                                <div class="info-card">
                                    <div class="info-icon">📱</div>
                                    <div class="info-content">
                                        <h4>تعلم في أي وقت</h4>
                                        <p>يمكنك الوصول للكورس من أي جهاز في أي وقت</p>
                                    </div>
                                </div>
                                
                                <div class="info-card">
                                    <div class="info-icon">🏆</div>
                                    <div class="info-content">
                                        <h4>حقق أهدافك</h4>
                                        <p>اكمل الكورس واحصل على شهادة إتمام</p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .success-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .success-card {
            background: white;
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        .success-animation {
            position: relative;
            margin-bottom: 40px;
        }

        .success-icon {
            font-size: 100px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .checkmark {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
        }

        .checkmark-svg {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #28a745;
            stroke-miterlimit: 10;
            box-shadow: inset 0px 0px 0px #28a745;
            animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
        }

        .checkmark-circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 2;
            stroke-miterlimit: 10;
            stroke: #28a745;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .checkmark-check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        .success-content h1 {
            color: #28a745;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .success-message {
            color: #495057;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .course-info-section {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            gap: 20px;
            justify-content: center;
        }

        .course-image {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 40px;
            color: white;
        }

        .course-details h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .course-details p {
            color: #6c757d;
            font-size: 16px;
            margin: 0;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 40px;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .additional-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            border-color: #87CEEB;
            transform: translateY(-3px);
        }

        .info-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .info-content h4 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .info-content p {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
            line-height: 1.4;
        }

        .info-content a {
            color: #25d366;
            text-decoration: none;
            font-weight: 600;
        }

        .info-content a:hover {
            text-decoration: underline;
        }

        /* Animations */
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -30px, 0);
            }
            70% {
                transform: translate3d(0, -15px, 0);
            }
            90% {
                transform: translate3d(0, -4px, 0);
            }
        }

        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {
            0%, 100% {
                transform: none;
            }
            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 30px #28a745;
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .success-card {
                padding: 30px 20px;
            }

            .success-content h1 {
                font-size: 28px;
            }

            .success-message {
                font-size: 16px;
            }

            .course-info-section {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }

            .additional-info {
                grid-template-columns: 1fr;
            }

            .success-icon {
                font-size: 80px;
            }

            .checkmark {
                width: 60px;
                height: 60px;
            }

            .checkmark-svg {
                width: 60px;
                height: 60px;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // Auto-redirect after 10 seconds for payment method
        <?php if ($method === 'payment'): ?>
        setTimeout(function() {
            window.location.href = 'dashboard.php';
        }, 10000);
        <?php endif; ?>
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
