<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>إصلاح الجداول المفقودة</h2>";
    
    $success = 0;
    $errors = 0;
    
    // Check and create payments table
    $stmt = $db->query("SHOW TABLES LIKE 'payments'");
    if ($stmt->rowCount() == 0) {
        echo "<p>🔨 إنشاء جدول payments...</p>";
        try {
            $sql = "CREATE TABLE payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                subscription_id INT NOT NULL COMMENT 'معرف الاشتراك',
                user_id INT NOT NULL COMMENT 'معرف المستخدم',
                payment_method ENUM('code', 'fawry', 'visa', 'wallet') NOT NULL COMMENT 'طريقة الدفع',
                payment_gateway VARCHAR(50) NULL COMMENT 'بوابة الدفع',
                transaction_id VARCHAR(100) NULL COMMENT 'معرف المعاملة',
                gateway_response JSON NULL COMMENT 'استجابة البوابة',
                amount DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
                currency VARCHAR(3) DEFAULT 'EGP' COMMENT 'العملة',
                status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending' COMMENT 'حالة الدفع',
                payment_date TIMESTAMP NULL COMMENT 'تاريخ الدفع',
                refund_date TIMESTAMP NULL COMMENT 'تاريخ الاسترداد',
                refund_amount DECIMAL(10,2) NULL COMMENT 'مبلغ الاسترداد',
                notes TEXT NULL COMMENT 'ملاحظات',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_payments (user_id),
                INDEX idx_status (status),
                INDEX idx_transaction (transaction_id),
                INDEX idx_payment_date (payment_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($sql);
            echo "<p style='color: green;'>✅ تم إنشاء جدول payments بنجاح</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول payments: " . $e->getMessage() . "</p>";
            $errors++;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول payments موجود بالفعل</p>";
    }
    
    // Check and create subscription_stats table
    $stmt = $db->query("SHOW TABLES LIKE 'subscription_stats'");
    if ($stmt->rowCount() == 0) {
        echo "<p>🔨 إنشاء جدول subscription_stats...</p>";
        try {
            $sql = "CREATE TABLE subscription_stats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE NOT NULL COMMENT 'التاريخ',
                total_subscriptions INT DEFAULT 0 COMMENT 'إجمالي الاشتراكات',
                new_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات الجديدة',
                cancelled_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات الملغاة',
                expired_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات المنتهية',
                total_revenue DECIMAL(10,2) DEFAULT 0 COMMENT 'إجمالي الإيرادات',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                UNIQUE KEY unique_date (date),
                INDEX idx_date (date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($sql);
            echo "<p style='color: green;'>✅ تم إنشاء جدول subscription_stats بنجاح</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول subscription_stats: " . $e->getMessage() . "</p>";
            $errors++;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول subscription_stats موجود بالفعل</p>";
    }
    
    // Check and create course_activation_codes table
    $stmt = $db->query("SHOW TABLES LIKE 'course_activation_codes'");
    if ($stmt->rowCount() == 0) {
        echo "<p>🔨 إنشاء جدول course_activation_codes...</p>";
        try {
            $sql = "CREATE TABLE course_activation_codes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود التفعيل',
                course_id INT NOT NULL COMMENT 'معرف الكورس',
                is_used BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود',
                used_by INT NULL COMMENT 'المستخدم الذي استخدم الكود',
                used_at TIMESTAMP NULL COMMENT 'تاريخ الاستخدام',
                expires_at TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
                created_by INT NOT NULL COMMENT 'منشئ الكود',
                notes TEXT NULL COMMENT 'ملاحظات',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
                INDEX idx_code (code),
                INDEX idx_course_unused (course_id, is_used),
                INDEX idx_expires (expires_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($sql);
            echo "<p style='color: green;'>✅ تم إنشاء جدول course_activation_codes بنجاح</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول course_activation_codes: " . $e->getMessage() . "</p>";
            $errors++;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول course_activation_codes موجود بالفعل</p>";
    }
    
    // Add subscription status to users table if not exists
    try {
        $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'subscription_status'");
        if ($stmt->rowCount() == 0) {
            echo "<p>🔨 إضافة عمود subscription_status لجدول users...</p>";
            $db->exec("ALTER TABLE users ADD COLUMN subscription_status ENUM('none', 'active', 'expired', 'cancelled') DEFAULT 'none' COMMENT 'حالة الاشتراك'");
            echo "<p style='color: green;'>✅ تم إضافة عمود subscription_status بنجاح</p>";
            $success++;
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود subscription_status موجود بالفعل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في إضافة عمود subscription_status: " . $e->getMessage() . "</p>";
        $errors++;
    }
    
    // Add current_plan_id to users table if not exists
    try {
        $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'current_plan_id'");
        if ($stmt->rowCount() == 0) {
            echo "<p>🔨 إضافة عمود current_plan_id لجدول users...</p>";
            $db->exec("ALTER TABLE users ADD COLUMN current_plan_id INT NULL COMMENT 'معرف الخطة الحالية'");
            $db->exec("ALTER TABLE users ADD FOREIGN KEY (current_plan_id) REFERENCES subscription_plans(id) ON DELETE SET NULL");
            echo "<p style='color: green;'>✅ تم إضافة عمود current_plan_id بنجاح</p>";
            $success++;
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود current_plan_id موجود بالفعل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في إضافة عمود current_plan_id: " . $e->getMessage() . "</p>";
        $errors++;
    }
    
    // Add subscription_end_date to users table if not exists
    try {
        $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'subscription_end_date'");
        if ($stmt->rowCount() == 0) {
            echo "<p>🔨 إضافة عمود subscription_end_date لجدول users...</p>";
            $db->exec("ALTER TABLE users ADD COLUMN subscription_end_date TIMESTAMP NULL COMMENT 'تاريخ انتهاء الاشتراك'");
            echo "<p style='color: green;'>✅ تم إضافة عمود subscription_end_date بنجاح</p>";
            $success++;
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود subscription_end_date موجود بالفعل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في إضافة عمود subscription_end_date: " . $e->getMessage() . "</p>";
        $errors++;
    }
    
    // Verify all tables exist
    $required_tables = ['subscription_plans', 'user_subscriptions', 'activation_codes', 'payments', 'subscription_stats', 'course_activation_codes'];
    $existing_tables = [];
    
    foreach ($required_tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[] = $table;
        }
    }
    
    echo "<hr>";
    echo "<h3>النتائج النهائية:</h3>";
    echo "<p>✅ العمليات الناجحة: $success</p>";
    echo "<p>❌ العمليات الفاشلة: $errors</p>";
    echo "<p>📊 الجداول الموجودة: " . count($existing_tables) . " من " . count($required_tables) . "</p>";
    
    if (count($existing_tables) === count($required_tables)) {
        echo "<h3 style='color: green;'>🎉 تم إصلاح جميع الجداول بنجاح!</h3>";
        
        // Insert sample data for today's stats
        try {
            $today = date('Y-m-d');
            $stmt = $db->prepare("INSERT IGNORE INTO subscription_stats (date, total_subscriptions, new_subscriptions) VALUES (?, 0, 0)");
            $stmt->execute([$today]);
            echo "<p>📊 تم إنشاء إحصائيات اليوم</p>";
        } catch (Exception $e) {
            // Ignore if already exists
        }
        
    } else {
        echo "<h3 style='color: red;'>❌ بعض الجداول ما زالت مفقودة</h3>";
        $missing = array_diff($required_tables, $existing_tables);
        echo "<p>الجداول المفقودة: " . implode(', ', $missing) . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='../admin/subscription_plans.php'>إدارة الخطط</a></li>";
    echo "<li><a href='../admin/activation_codes.php'>إدارة أكواد التفعيل</a></li>";
    echo "<li><a href='../page/subscriptions.php'>صفحة الاشتراكات للمستخدمين</a></li>";
    echo "<li><a href='install_subscription_system.php'>إعادة تشغيل التثبيت الكامل</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: #f5f5f5;
        direction: rtl;
    }
    
    h2, h3 {
        color: #333;
    }
    
    p {
        margin: 10px 0;
        padding: 8px;
        border-radius: 4px;
    }
    
    a {
        color: #4682B4;
        text-decoration: none;
        font-weight: bold;
    }
    
    a:hover {
        text-decoration: underline;
    }
    
    ul {
        background: white;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    li {
        margin: 10px 0;
    }
    
    hr {
        margin: 30px 0;
        border: none;
        border-top: 2px solid #ddd;
    }
</style>
