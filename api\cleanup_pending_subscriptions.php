<?php
/**
 * Cleanup script for old pending subscriptions
 * This script should be run periodically (e.g., via cron job) to clean up
 * pending subscriptions that are older than 24 hours
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Only allow this script to run from command line or specific admin access
if (php_sapi_name() !== 'cli' && !isset($_GET['admin_key'])) {
    http_response_code(403);
    die('Access denied');
}

// If running via web, check admin key
if (isset($_GET['admin_key']) && $_GET['admin_key'] !== 'cleanup_pending_2024') {
    http_response_code(403);
    die('Invalid admin key');
}

try {
    $courseManager = new CourseManager();
    
    // Clean up old pending subscriptions
    $result = $courseManager->cleanupPendingSubscriptions();
    
    if ($result) {
        $message = "Successfully cleaned up old pending subscriptions";
        error_log($message);
        
        if (php_sapi_name() !== 'cli') {
            echo json_encode(['success' => true, 'message' => $message]);
        } else {
            echo $message . "\n";
        }
    } else {
        $message = "Failed to clean up pending subscriptions";
        error_log($message);
        
        if (php_sapi_name() !== 'cli') {
            echo json_encode(['success' => false, 'message' => $message]);
        } else {
            echo $message . "\n";
        }
    }
    
} catch (Exception $e) {
    $message = "Error during cleanup: " . $e->getMessage();
    error_log($message);
    
    if (php_sapi_name() !== 'cli') {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => $message]);
    } else {
        echo $message . "\n";
    }
}
?>
