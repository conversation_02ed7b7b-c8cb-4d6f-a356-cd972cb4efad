<?php
require_once __DIR__ . '/HonorBoardManager.php';
require_once __DIR__ . '/database.php';

// Get user education info for filtering
$userEducation = null;
if (isset($_SESSION['user_id'])) {
    try {
        $userManager = new UserManager();
        $userData = $userManager->getUserById($_SESSION['user_id']);
        if ($userData) {
            $userEducation = [
                'education_level' => $userData['education_level'],
                'education_type' => $userData['education_type'],
                'grade' => $userData['grade']
            ];
        }
    } catch (Exception $e) {
        error_log("Error getting user data for widget: " . $e->getMessage());
    }
}

$honorBoardManager = new HonorBoardManager();
$topStudents = $honorBoardManager->getTopHonorEntries(10, $userEducation);
?>

<div class="honor-widget">
    <div class="honor-header">
        <h3>لوحة الشرف</h3>
        <div class="widget-icon">🏆</div>
    </div>

    <div class="honor-stats">
        <div class="honor-stat">
            <span class="stat-number"><?php echo count($topStudents); ?></span>
            <span class="stat-label">طلاب متفوقون</span>
        </div>
        <div class="honor-stat">
            <span class="stat-number">
                <?php 
                $topThree = array_filter($topStudents, function($student) {
                    return $student['ranking_position'] <= 3;
                });
                echo count($topThree);
                ?>
            </span>
            <span class="stat-label">المراكز الأولى</span>
        </div>
    </div>

    <div class="honor-content">
        <?php if (empty($topStudents)): ?>
            <div class="empty-state">
                <div class="empty-icon">🎖️</div>
                <p>لا توجد إنجازات مسجلة</p>
                <small>ستظهر إنجازات الطلاب المتفوقين هنا</small>
            </div>
        <?php else: ?>
            <div class="honor-list">
                <?php foreach ($topStudents as $index => $student): ?>
                    <div class="honor-item <?php echo $student['ranking_position'] <= 3 ? 'top-three' : ''; ?>">
                        <div class="honor-rank">
                            <div class="rank-number rank-<?php echo $student['ranking_position'] <= 3 ? $student['ranking_position'] : 'other'; ?>">
                                <?php echo $student['ranking_position']; ?>
                            </div>
                            <?php if ($student['ranking_position'] == 1): ?>
                                <div class="rank-medal">🥇</div>
                            <?php elseif ($student['ranking_position'] == 2): ?>
                                <div class="rank-medal">🥈</div>
                            <?php elseif ($student['ranking_position'] == 3): ?>
                                <div class="rank-medal">🥉</div>
                            <?php endif; ?>
                        </div>
                        <div class="honor-details">
                            <h5 class="student-name"><?php echo htmlspecialchars($student['student_name']); ?></h5>
                            <div class="honor-info">
                                <span class="honor-subject"><?php echo htmlspecialchars($student['subject']); ?></span>
                                <span class="honor-score"><?php echo number_format($student['grade_score'], 1); ?>%</span>
                            </div>
                            <div class="honor-meta">
                                <span class="achievement-type">
                                    <?php 
                                    $achievementTypes = [
                                        'monthly' => 'شهري',
                                        'semester' => 'فصل دراسي',
                                        'yearly' => 'سنوي',
                                        'special' => 'خاص'
                                    ];
                                    echo $achievementTypes[$student['achievement_type']];
                                    ?>
                                </span>
                                <span class="achievement-date">
                                    <?php 
                                    $achievementDate = strtotime($student['achievement_date']);
                                    $now = time();
                                    $diff = $now - $achievementDate;
                                    
                                    if ($diff < 2592000) { // Less than 30 days
                                        echo 'حديث';
                                    } elseif ($diff < 7776000) { // Less than 90 days
                                        echo 'هذا الفصل';
                                    } else {
                                        echo date('Y', $achievementDate);
                                    }
                                    ?>
                                </span>
                            </div>
                            <?php if ($student['additional_notes']): ?>
                                <div class="honor-notes">
                                    <?php echo htmlspecialchars(substr($student['additional_notes'], 0, 80)) . (strlen($student['additional_notes']) > 80 ? '...' : ''); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="honor-badge">
                            <?php if ($student['ranking_position'] == 1): ?>
                                <span class="badge-first">الأول</span>
                            <?php elseif ($student['ranking_position'] == 2): ?>
                                <span class="badge-second">الثاني</span>
                            <?php elseif ($student['ranking_position'] == 3): ?>
                                <span class="badge-third">الثالث</span>
                            <?php else: ?>
                                <span class="badge-other">متفوق</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.honor-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15);
    border: 2px solid rgba(70, 130, 180, 0.1);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.honor-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 20px 20px 0 0;
}

.honor-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.25);
    border-color: rgba(135, 206, 235, 0.3);
}

.honor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
}

.honor-header h3 {
    color: #2c3e50;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.widget-icon {
    font-size: 32px;
    opacity: 0.7;
    animation: pulse 2s infinite;
}

.honor-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.honor-stat {
    flex: 1;
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(70, 130, 180, 0.05) 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(135, 206, 235, 0.2);
    transition: all 0.3s ease;
}

.honor-stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(135, 206, 235, 0.2);
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.15) 0%, rgba(70, 130, 180, 0.1) 100%);
}

.honor-stat .stat-number {
    display: block;
    font-size: 28px;
    font-weight: 800;
    color: #4682B4;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(70, 130, 180, 0.1);
}

.honor-stat .stat-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.honor-content {
    max-height: 400px;
    overflow-y: auto;
}

.honor-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.honor-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.honor-item.top-three {
    border-color: rgba(255, 193, 7, 0.3);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.02) 0%, rgba(255, 193, 7, 0.01) 100%);
}

.honor-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.honor-item.top-three::before {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
}

.honor-item:hover::before {
    transform: scaleY(1);
}

.honor-item:hover {
    transform: translateX(8px);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.15);
    border-color: rgba(135, 206, 235, 0.3);
}

.honor-rank {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    min-width: 60px;
}

.rank-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.rank-1 { background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); }
.rank-2 { background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%); }
.rank-3 { background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%); }
.rank-other { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }

.rank-medal {
    font-size: 20px;
    animation: bounce 2s infinite;
}

.honor-details {
    flex: 1;
}

.student-name {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 10px 0;
    line-height: 1.3;
}

.honor-info {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.honor-subject {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.honor-score {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    color: #17a2b8;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 700;
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.honor-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.achievement-type,
.achievement-date {
    font-size: 12px;
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.honor-notes {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
    margin-top: 8px;
    padding: 8px;
    background: rgba(70, 130, 180, 0.05);
    border-radius: 8px;
    border-left: 3px solid #87CEEB;
}

.honor-badge {
    align-self: flex-start;
}

.honor-badge span {
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.badge-first {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #333;
}

.badge-second {
    background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
    color: #333;
}

.badge-third {
    background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
    color: white;
}

.badge-other {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.empty-state small {
    font-size: 14px;
    opacity: 0.8;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .honor-widget {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 15px;
    }

    .honor-header h3 {
        font-size: 18px;
    }

    .widget-icon {
        font-size: 24px;
    }

    .honor-stats {
        flex-direction: column;
        gap: 12px;
    }

    .honor-stat {
        padding: 15px;
    }

    .honor-stat .stat-number {
        font-size: 24px;
    }

    .honor-item {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
        text-align: center;
    }

    .honor-rank {
        align-self: center;
    }

    .student-name {
        font-size: 16px;
    }

    .honor-info,
    .honor-meta {
        justify-content: center;
        flex-wrap: wrap;
    }

    .honor-badge {
        align-self: center;
    }
}

@media (max-width: 480px) {
    .honor-widget {
        padding: 15px;
        border-radius: 12px;
    }

    .honor-header h3 {
        font-size: 16px;
    }

    .honor-item {
        padding: 12px;
        gap: 12px;
    }

    .rank-number {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .rank-medal {
        font-size: 16px;
    }

    .student-name {
        font-size: 15px;
    }

    .honor-info,
    .honor-meta {
        flex-direction: column;
        gap: 8px;
    }

    .empty-state {
        padding: 40px 15px;
    }

    .empty-icon {
        font-size: 48px;
    }
}
</style>
