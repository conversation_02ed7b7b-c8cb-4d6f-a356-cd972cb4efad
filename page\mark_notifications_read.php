<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $userManager = new UserManager();
    
    if (isset($input['mark_all']) && $input['mark_all']) {
        // Mark all notifications as read
        $result = $userManager->markAllNotificationsAsRead($_SESSION['user_id']);
    } elseif (isset($input['notification_id'])) {
        // Mark specific notification as read
        $result = $userManager->markNotificationAsRead($input['notification_id'], $_SESSION['user_id']);
    } else {
        throw new Exception('معطيات غير صحيحة');
    }
    
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث الإشعارات']);
    } else {
        throw new Exception('فشل في تحديث الإشعارات');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}
?>
