<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h1>🧪 اختبار نظام الاشتراكات الشامل</h1>";

$tests_passed = 0;
$tests_failed = 0;
$total_tests = 0;

function runTest($test_name, $test_function) {
    global $tests_passed, $tests_failed, $total_tests;
    $total_tests++;
    
    echo "<div class='test-item'>";
    echo "<h3>🔍 {$test_name}</h3>";
    
    try {
        $result = $test_function();
        if ($result['success']) {
            echo "<p class='success'>✅ {$result['message']}</p>";
            $tests_passed++;
        } else {
            echo "<p class='error'>❌ {$result['message']}</p>";
            $tests_failed++;
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ: {$e->getMessage()}</p>";
        $tests_failed++;
    }
    
    echo "</div>";
}

// Test 1: Database Connection
runTest("اتصال قاعدة البيانات", function() {
    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return ['success' => true, 'message' => 'تم الاتصال بقاعدة البيانات بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage()];
    }
});

// Test 2: Required Tables Exist
runTest("فحص الجداول المطلوبة", function() {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $required_tables = [
        'subscription_plans',
        'user_subscriptions', 
        'activation_codes',
        'course_activation_codes',
        'payments',
        'subscription_stats'
    ];
    
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missing_tables[] = $table;
        }
    }
    
    if (empty($missing_tables)) {
        return ['success' => true, 'message' => 'جميع الجداول المطلوبة موجودة (' . count($required_tables) . ' جداول)'];
    } else {
        return ['success' => false, 'message' => 'جداول مفقودة: ' . implode(', ', $missing_tables)];
    }
});

// Test 3: Subscription Plans Data
runTest("بيانات خطط الاشتراك", function() {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM subscription_plans WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $count = $result['count'];
    
    if ($count > 0) {
        return ['success' => true, 'message' => "يوجد {$count} خطة اشتراك نشطة"];
    } else {
        return ['success' => false, 'message' => 'لا توجد خطط اشتراك نشطة'];
    }
});

// Test 4: Activation Codes
runTest("أكواد التفعيل", function() {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->query("SELECT COUNT(*) as total, 
                       SUM(CASE WHEN is_used = 0 THEN 1 ELSE 0 END) as available
                       FROM activation_codes");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['total'] > 0) {
        return ['success' => true, 'message' => "يوجد {$result['total']} كود تفعيل، منها {$result['available']} متاح"];
    } else {
        return ['success' => false, 'message' => 'لا توجد أكواد تفعيل'];
    }
});

// Test 5: Admin Pages Accessibility
runTest("صفحات الأدمن", function() {
    $admin_pages = [
        'subscription_plans.php',
        'activation_codes.php', 
        'subscribers.php'
    ];
    
    $accessible_pages = [];
    $inaccessible_pages = [];
    
    foreach ($admin_pages as $page) {
        $file_path = __DIR__ . '/../admin/' . $page;
        if (file_exists($file_path)) {
            $accessible_pages[] = $page;
        } else {
            $inaccessible_pages[] = $page;
        }
    }
    
    if (empty($inaccessible_pages)) {
        return ['success' => true, 'message' => 'جميع صفحات الأدمن موجودة (' . count($accessible_pages) . ' صفحات)'];
    } else {
        return ['success' => false, 'message' => 'صفحات مفقودة: ' . implode(', ', $inaccessible_pages)];
    }
});

// Test 6: User Pages Accessibility
runTest("صفحات المستخدمين", function() {
    $user_pages = [
        'subscriptions.php',
        'payment_methods.php',
        'activate_code.php',
        'subscription_success.php',
        'check_subscription.php'
    ];
    
    $accessible_pages = [];
    $inaccessible_pages = [];
    
    foreach ($user_pages as $page) {
        $file_path = __DIR__ . '/../page/' . $page;
        if (file_exists($file_path)) {
            $accessible_pages[] = $page;
        } else {
            $inaccessible_pages[] = $page;
        }
    }
    
    if (empty($inaccessible_pages)) {
        return ['success' => true, 'message' => 'جميع صفحات المستخدمين موجودة (' . count($accessible_pages) . ' صفحات)'];
    } else {
        return ['success' => false, 'message' => 'صفحات مفقودة: ' . implode(', ', $inaccessible_pages)];
    }
});

// Test 7: Database Columns Check
runTest("أعمدة قاعدة البيانات", function() {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check users table columns
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'subscription_status'");
    $has_subscription_status = $stmt->rowCount() > 0;
    
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'current_plan_id'");
    $has_current_plan_id = $stmt->rowCount() > 0;
    
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'subscription_end_date'");
    $has_subscription_end_date = $stmt->rowCount() > 0;
    
    $missing_columns = [];
    if (!$has_subscription_status) $missing_columns[] = 'subscription_status';
    if (!$has_current_plan_id) $missing_columns[] = 'current_plan_id';
    if (!$has_subscription_end_date) $missing_columns[] = 'subscription_end_date';
    
    if (empty($missing_columns)) {
        return ['success' => true, 'message' => 'جميع الأعمدة المطلوبة موجودة في جدول users'];
    } else {
        return ['success' => false, 'message' => 'أعمدة مفقودة في جدول users: ' . implode(', ', $missing_columns)];
    }
});

// Test 8: Sample Data Test
runTest("اختبار البيانات التجريبية", function() {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if we have sample plans
    $stmt = $db->query("SELECT COUNT(*) as count FROM subscription_plans WHERE name LIKE '%الخطة%'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $sample_plans = $result['count'];
    
    // Check if we have sample codes
    $stmt = $db->query("SELECT COUNT(*) as count FROM activation_codes WHERE code LIKE '%2024'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $sample_codes = $result['count'];
    
    if ($sample_plans > 0 && $sample_codes > 0) {
        return ['success' => true, 'message' => "يوجد {$sample_plans} خطة تجريبية و {$sample_codes} كود تجريبي"];
    } else {
        return ['success' => false, 'message' => 'البيانات التجريبية غير مكتملة'];
    }
});

// Test 9: Configuration Check
runTest("فحص الإعدادات", function() {
    $required_constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS', 'SITE_NAME', 'SITE_URL'];
    $missing_constants = [];
    
    foreach ($required_constants as $constant) {
        if (!defined($constant)) {
            $missing_constants[] = $constant;
        }
    }
    
    if (empty($missing_constants)) {
        return ['success' => true, 'message' => 'جميع الإعدادات المطلوبة موجودة'];
    } else {
        return ['success' => false, 'message' => 'إعدادات مفقودة: ' . implode(', ', $missing_constants)];
    }
});

// Test 10: File Permissions
runTest("صلاحيات الملفات", function() {
    $critical_files = [
        __DIR__ . '/../config/config.php',
        __DIR__ . '/../includes/database.php',
        __DIR__ . '/../admin/subscription_plans.php',
        __DIR__ . '/../page/subscriptions.php'
    ];
    
    $unreadable_files = [];
    
    foreach ($critical_files as $file) {
        if (!is_readable($file)) {
            $unreadable_files[] = basename($file);
        }
    }
    
    if (empty($unreadable_files)) {
        return ['success' => true, 'message' => 'جميع الملفات الأساسية قابلة للقراءة'];
    } else {
        return ['success' => false, 'message' => 'ملفات غير قابلة للقراءة: ' . implode(', ', $unreadable_files)];
    }
});

// Display Results
echo "<div class='results'>";
echo "<h2>📊 نتائج الاختبار</h2>";
echo "<div class='result-summary'>";
echo "<div class='result-item success'>";
echo "<h3>✅ اختبارات ناجحة</h3>";
echo "<span class='number'>{$tests_passed}</span>";
echo "</div>";
echo "<div class='result-item error'>";
echo "<h3>❌ اختبارات فاشلة</h3>";
echo "<span class='number'>{$tests_failed}</span>";
echo "</div>";
echo "<div class='result-item total'>";
echo "<h3>📊 إجمالي الاختبارات</h3>";
echo "<span class='number'>{$total_tests}</span>";
echo "</div>";
echo "</div>";

$success_rate = ($total_tests > 0) ? round(($tests_passed / $total_tests) * 100, 1) : 0;
echo "<div class='success-rate'>";
echo "<h3>معدل النجاح: {$success_rate}%</h3>";
if ($success_rate >= 90) {
    echo "<p class='success'>🎉 النظام يعمل بشكل ممتاز!</p>";
} elseif ($success_rate >= 70) {
    echo "<p class='warning'>⚠️ النظام يعمل بشكل جيد مع بعض المشاكل البسيطة</p>";
} else {
    echo "<p class='error'>🚨 النظام يحتاج إلى إصلاحات مهمة</p>";
}
echo "</div>";

echo "<div class='next-steps'>";
echo "<h3>🔗 الخطوات التالية:</h3>";
echo "<ul>";
if ($tests_failed == 0) {
    echo "<li><a href='../admin/subscription_plans.php'>إدارة خطط الاشتراك</a></li>";
    echo "<li><a href='../admin/activation_codes.php'>إدارة أكواد التفعيل</a></li>";
    echo "<li><a href='../admin/subscribers.php'>إدارة المشتركين</a></li>";
    echo "<li><a href='../page/subscriptions.php'>صفحة الاشتراكات</a></li>";
} else {
    echo "<li><a href='reset_subscription_system.php'>إعادة تعيين النظام</a></li>";
    echo "<li><a href='fix_missing_tables.php'>إصلاح الجداول المفقودة</a></li>";
}
echo "</ul>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    direction: rtl;
    min-height: 100vh;
}

h1 {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    color: #333;
}

.test-item {
    background: rgba(255, 255, 255, 0.9);
    margin: 15px 0;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-right: 5px solid #4682B4;
}

.test-item h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.success {
    color: #155724;
    background: #d4edda;
    padding: 10px;
    border-radius: 5px;
    border-right: 4px solid #28a745;
}

.error {
    color: #721c24;
    background: #f8d7da;
    padding: 10px;
    border-radius: 5px;
    border-right: 4px solid #dc3545;
}

.warning {
    color: #856404;
    background: #fff3cd;
    padding: 10px;
    border-radius: 5px;
    border-right: 4px solid #ffc107;
}

.results {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 30px;
}

.result-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.result-item {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.result-item.success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 2px solid #28a745;
}

.result-item.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 2px solid #dc3545;
}

.result-item.total {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 2px solid #2196f3;
}

.result-item h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.number {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
}

.success-rate {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border: 2px solid #6c757d;
}

.next-steps {
    margin-top: 30px;
}

.next-steps ul {
    list-style: none;
    padding: 0;
}

.next-steps li {
    margin: 10px 0;
    padding: 15px;
    background: rgba(70, 130, 180, 0.1);
    border-radius: 8px;
    border-right: 4px solid #4682B4;
}

.next-steps a {
    color: #4682B4;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
}

.next-steps a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .result-summary {
        grid-template-columns: 1fr;
    }
    
    body {
        margin: 10px;
    }
    
    h1 {
        padding: 20px;
    }
}
</style>
