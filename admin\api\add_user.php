<?php
session_start();
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    // Validate required fields
    $required_fields = ['first_name', 'second_name', 'username', 'email', 'password'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            echo json_encode(['success' => false, 'message' => "حقل {$field} مطلوب"]);
            exit;
        }
    }
    
    $firstName = trim($_POST['first_name']);
    $secondName = trim($_POST['second_name']);
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'صيغة البريد الإلكتروني غير صحيحة']);
        exit;
    }
    
    // Validate password length
    if (strlen($password) < 6) {
        echo json_encode(['success' => false, 'message' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل']);
        exit;
    }
    
    $db = Database::getInstance()->getConnection();
    
    // Check if username already exists
    $checkStmt = $db->prepare("SELECT id FROM users WHERE username = :username OR email = :email");
    $checkStmt->bindParam(':username', $username);
    $checkStmt->bindParam(':email', $email);
    $checkStmt->execute();
    
    if ($checkStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل']);
        exit;
    }
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert new user
    $insertStmt = $db->prepare("
        INSERT INTO users (first_name, second_name, username, email, password, created_at) 
        VALUES (:first_name, :second_name, :username, :email, :password, NOW())
    ");
    
    $insertStmt->bindParam(':first_name', $firstName);
    $insertStmt->bindParam(':second_name', $secondName);
    $insertStmt->bindParam(':username', $username);
    $insertStmt->bindParam(':email', $email);
    $insertStmt->bindParam(':password', $hashedPassword);
    
    if ($insertStmt->execute()) {
        $userId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة المستخدم بنجاح',
            'user_id' => $userId
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في إضافة المستخدم']);
    }
    
} catch (Exception $e) {
    error_log("Error in add_user.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في إضافة المستخدم'
    ]);
}
?>
