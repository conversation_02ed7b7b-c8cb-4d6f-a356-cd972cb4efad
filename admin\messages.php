<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/MessageManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$messageManager = new MessageManager();
$db = Database::getInstance()->getConnection();

// Handle reply submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_reply'])) {
    try {
        $messageId = (int)$_POST['message_id'];
        $replyText = trim($_POST['reply_text']);
        $newStatus = $_POST['new_status'] ?? 'replied';
        
        if (empty($replyText)) {
            throw new Exception('نص الرد مطلوب');
        }
        
        // Send reply
        $adminId = $_SESSION['admin_id'] ?? 1; // Use admin ID 1 as fallback
        $replyId = $messageManager->createReply($messageId, $adminId, $replyText, true);
        
        if ($replyId) {
            // Update message status
            $messageManager->updateMessageStatus($messageId, $newStatus);
            $success_message = 'تم إرسال الرد بنجاح';
        } else {
            throw new Exception('فشل في إرسال الرد');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    try {
        $messageId = (int)$_POST['message_id'];
        $newStatus = $_POST['status'];
        
        $messageManager->updateMessageStatus($messageId, $newStatus);
        $success_message = 'تم تحديث حالة الرسالة';
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$priority_filter = $_GET['priority'] ?? '';
$category_filter = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = 20;

// Build query conditions
$conditions = [];
$params = [];

if (!empty($status_filter)) {
    $conditions[] = "sm.status = ?";
    $params[] = $status_filter;
}

if (!empty($priority_filter)) {
    $conditions[] = "sm.priority = ?";
    $params[] = $priority_filter;
}

if (!empty($category_filter)) {
    $conditions[] = "sm.category_id = ?";
    $params[] = $category_filter;
}

if (!empty($search)) {
    $conditions[] = "(sm.subject LIKE ? OR sm.message LIKE ? OR u.full_name LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Get total count
$countSql = "
    SELECT COUNT(*) 
    FROM student_messages sm 
    LEFT JOIN users u ON sm.user_id = u.id 
    $whereClause
";
$countStmt = $db->prepare($countSql);
$countStmt->execute($params);
$totalMessages = $countStmt->fetchColumn();
$totalPages = ceil($totalMessages / $perPage);

// Get messages
$offset = ($page - 1) * $perPage;
$messagesSql = "
    SELECT sm.*,
           u.full_name as user_name, u.email as user_email,
           mc.name_ar as category_name, mc.icon as category_icon,
           (SELECT COUNT(*) FROM admin_replies ar WHERE ar.message_id = sm.id) as reply_count
    FROM student_messages sm
    LEFT JOIN users u ON sm.user_id = u.id
    LEFT JOIN message_categories mc ON sm.category_id = mc.id
    $whereClause
    ORDER BY sm.created_at DESC
    LIMIT $perPage OFFSET $offset
";

$messagesStmt = $db->prepare($messagesSql);
$messagesStmt->execute($params);
$messages = $messagesStmt->fetchAll();

// Get categories for filter
$categories = $messageManager->getCategories();

// Get selected message details if ID is provided
$selectedMessage = null;
$messageReplies = [];
if (isset($_GET['id'])) {
    $messageId = (int)$_GET['id'];
    foreach ($messages as $msg) {
        if ($msg['id'] == $messageId) {
            $selectedMessage = $msg;
            $messageReplies = $messageManager->getMessageReplies($messageId);
            break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرسائل - لوحة التحكم</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="admin-container">
                <div class="page-header">
                    <h1><i class="fas fa-envelope"></i> إدارة الرسائل</h1>
                    <p>عرض والرد على رسائل الطلاب</p>
                </div>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="stats-grid">
                    <?php
                    $stats = [
                        'pending' => $db->query("SELECT COUNT(*) FROM student_messages WHERE status = 'pending'")->fetchColumn(),
                        'read' => $db->query("SELECT COUNT(*) FROM student_messages WHERE status = 'read'")->fetchColumn(),
                        'replied' => $db->query("SELECT COUNT(*) FROM student_messages WHERE status = 'replied'")->fetchColumn(),
                        'total' => $db->query("SELECT COUNT(*) FROM student_messages")->fetchColumn()
                    ];
                    ?>
                    <div class="stat-card pending">
                        <div class="stat-icon"><i class="fas fa-clock"></i></div>
                        <div class="stat-info">
                            <h3><?php echo $stats['pending']; ?></h3>
                            <p>في الانتظار</p>
                        </div>
                    </div>
                    <div class="stat-card read">
                        <div class="stat-icon"><i class="fas fa-eye"></i></div>
                        <div class="stat-info">
                            <h3><?php echo $stats['read']; ?></h3>
                            <p>تم القراءة</p>
                        </div>
                    </div>
                    <div class="stat-card replied">
                        <div class="stat-icon"><i class="fas fa-reply"></i></div>
                        <div class="stat-info">
                            <h3><?php echo $stats['replied']; ?></h3>
                            <p>تم الرد</p>
                        </div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-icon"><i class="fas fa-envelope"></i></div>
                        <div class="stat-info">
                            <h3><?php echo $stats['total']; ?></h3>
                            <p>إجمالي الرسائل</p>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <form method="GET" class="filters-form">
                        <div class="filter-group">
                            <label for="status">الحالة:</label>
                            <select name="status" id="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>تم القراءة</option>
                                <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>تم الرد</option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>مغلقة</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="priority">الأولوية:</label>
                            <select name="priority" id="priority">
                                <option value="">جميع الأولويات</option>
                                <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>منخفضة</option>
                                <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                                <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>عالية</option>
                                <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>عاجلة</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="category">التصنيف:</label>
                            <select name="category" id="category">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name_ar']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="search">البحث:</label>
                            <input type="text" name="search" id="search" 
                                   placeholder="البحث في الموضوع أو النص أو اسم المستخدم"
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>

                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="?" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Messages List -->
                <div class="messages-container">
                    <div class="messages-list">
                        <div class="list-header">
                            <h3>الرسائل (<?php echo $totalMessages; ?>)</h3>
                        </div>

                        <?php if (empty($messages)): ?>
                            <div class="empty-state">
                                <i class="fas fa-inbox"></i>
                                <h3>لا توجد رسائل</h3>
                                <p>لا توجد رسائل تطابق معايير البحث</p>
                            </div>
                        <?php else: ?>
                            <div class="messages-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>المستخدم</th>
                                            <th>الموضوع</th>
                                            <th>التصنيف</th>
                                            <th>الأولوية</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                            <th>الردود</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($messages as $message): ?>
                                            <tr class="message-row <?php echo $selectedMessage && $selectedMessage['id'] == $message['id'] ? 'selected' : ''; ?>">
                                                <td>
                                                    <div class="user-info">
                                                        <strong><?php echo htmlspecialchars($message['user_name']); ?></strong>
                                                        <small><?php echo htmlspecialchars($message['user_email']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="message-subject">
                                                        <?php echo htmlspecialchars($message['subject']); ?>
                                                    </div>
                                                    <div class="message-preview">
                                                        <?php echo htmlspecialchars(mb_substr($message['message'], 0, 100)) . '...'; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($message['category_name']): ?>
                                                        <span class="category-badge">
                                                            <?php echo $message['category_icon']; ?>
                                                            <?php echo htmlspecialchars($message['category_name']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="category-badge">📝 عام</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="priority-badge priority-<?php echo $message['priority']; ?>">
                                                        <?php 
                                                        $priorityLabels = [
                                                            'low' => 'منخفضة',
                                                            'medium' => 'متوسطة',
                                                            'high' => 'عالية',
                                                            'urgent' => 'عاجلة'
                                                        ];
                                                        echo $priorityLabels[$message['priority']] ?? $message['priority'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $message['status']; ?>">
                                                        <?php 
                                                        $statusLabels = [
                                                            'pending' => 'في الانتظار',
                                                            'read' => 'تم القراءة',
                                                            'replied' => 'تم الرد',
                                                            'closed' => 'مغلقة'
                                                        ];
                                                        echo $statusLabels[$message['status']] ?? $message['status'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="message-date">
                                                        <?php echo date('Y/m/d', strtotime($message['created_at'])); ?>
                                                        <small><?php echo date('H:i', strtotime($message['created_at'])); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="reply-count">
                                                        <?php echo $message['reply_count']; ?> رد
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="?id=<?php echo $message['id']; ?>" 
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <div class="pagination">
                                    <?php
                                    $queryParams = $_GET;
                                    unset($queryParams['page']);
                                    $baseQuery = http_build_query($queryParams);
                                    $baseUrl = '?' . ($baseQuery ? $baseQuery . '&' : '');
                                    ?>
                                    
                                    <?php if ($page > 1): ?>
                                        <a href="<?php echo $baseUrl; ?>page=<?php echo $page - 1; ?>" class="pagination-btn">
                                            <i class="fas fa-chevron-right"></i> السابق
                                        </a>
                                    <?php endif; ?>
                                    
                                    <span class="pagination-info">
                                        صفحة <?php echo $page; ?> من <?php echo $totalPages; ?>
                                    </span>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <a href="<?php echo $baseUrl; ?>page=<?php echo $page + 1; ?>" class="pagination-btn">
                                            التالي <i class="fas fa-chevron-left"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Message Details Modal -->
                    <?php if ($selectedMessage): ?>
                        <div class="message-details-section">
                            <div class="message-details-header">
                                <h3>تفاصيل الرسالة</h3>
                                <a href="?" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إغلاق
                                </a>
                            </div>

                            <div class="message-details-content">
                                <div class="message-info">
                                    <div class="info-row">
                                        <strong>المرسل:</strong>
                                        <?php echo htmlspecialchars($selectedMessage['user_name']); ?>
                                        (<?php echo htmlspecialchars($selectedMessage['user_email']); ?>)
                                    </div>
                                    <div class="info-row">
                                        <strong>الموضوع:</strong>
                                        <?php echo htmlspecialchars($selectedMessage['subject']); ?>
                                    </div>
                                    <div class="info-row">
                                        <strong>التاريخ:</strong>
                                        <?php echo date('Y/m/d H:i', strtotime($selectedMessage['created_at'])); ?>
                                    </div>
                                    <div class="info-row">
                                        <strong>الحالة:</strong>
                                        <span class="status-badge status-<?php echo $selectedMessage['status']; ?>">
                                            <?php
                                            $statusLabels = [
                                                'pending' => 'في الانتظار',
                                                'read' => 'تم القراءة',
                                                'replied' => 'تم الرد',
                                                'closed' => 'مغلقة'
                                            ];
                                            echo $statusLabels[$selectedMessage['status']] ?? $selectedMessage['status'];
                                            ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="original-message">
                                    <h4>نص الرسالة:</h4>
                                    <div class="message-text">
                                        <?php echo nl2br(htmlspecialchars($selectedMessage['message'])); ?>
                                    </div>
                                </div>

                                <!-- Replies Section -->
                                <?php if (!empty($messageReplies)): ?>
                                    <div class="replies-section">
                                        <h4>الردود السابقة:</h4>
                                        <?php foreach ($messageReplies as $reply): ?>
                                            <div class="reply-item">
                                                <div class="reply-header">
                                                    <span class="reply-author">
                                                        <i class="fas fa-user-tie"></i>
                                                        <?php echo htmlspecialchars($reply['admin_name'] ?? 'المدير'); ?>
                                                    </span>
                                                    <span class="reply-date">
                                                        <?php echo date('Y/m/d H:i', strtotime($reply['created_at'])); ?>
                                                    </span>
                                                </div>
                                                <div class="reply-text">
                                                    <?php echo nl2br(htmlspecialchars($reply['reply_text'])); ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Reply Form -->
                                <div class="reply-form-section">
                                    <h4>إرسال رد:</h4>
                                    <form method="POST" class="reply-form">
                                        <input type="hidden" name="message_id" value="<?php echo $selectedMessage['id']; ?>">

                                        <div class="form-group">
                                            <label for="reply_text">نص الرد:</label>
                                            <textarea name="reply_text" id="reply_text"
                                                      class="form-control" rows="6"
                                                      placeholder="اكتب ردك هنا..." required></textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="new_status">تحديث حالة الرسالة:</label>
                                            <select name="new_status" id="new_status" class="form-control">
                                                <option value="replied" selected>تم الرد</option>
                                                <option value="read">تم القراءة</option>
                                                <option value="closed">مغلقة</option>
                                            </select>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" name="send_reply" class="btn btn-primary" onclick="openModal('addModal')">
                                                <i class="fas fa-paper-plane"></i>
                                                إرسال الرد
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- Quick Status Update -->
                                <div class="quick-actions">
                                    <h4>إجراءات سريعة:</h4>
                                    <form method="POST" class="status-form">
                                        <input type="hidden" name="message_id" value="<?php echo $selectedMessage['id']; ?>">
                                        <div class="status-buttons">
                                            <button type="submit" name="update_status" value="read"
                                                    class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> تم القراءة
                                            </button>
                                            <button type="submit" name="update_status" value="closed"
                                                    class="btn btn-sm btn-secondary">
                                                <i class="fas fa-times"></i> إغلاق
                                            </button>
                                        </div>
                                        <input type="hidden" name="status" value="">
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            border-radius: 20px;
            color: white;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.3);
        }
        
        .page-header h1 {
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .page-header p {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .stat-card.pending .stat-icon { background: #ffc107; }
        .stat-card.read .stat-icon { background: #17a2b8; }
        .stat-card.replied .stat-icon { background: #28a745; }
        .stat-card.total .stat-icon { background: #4682B4; }
        
        .stat-info h3 {
            margin: 0 0 5px 0;
            font-size: 2em;
            color: #333;
            font-weight: 700;
        }
        
        .stat-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .filters-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .filter-group input,
        .filter-group select {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }
        
        .filter-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .messages-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .list-header {
            padding: 20px 25px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .list-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.3em;
        }
        
        .messages-table {
            overflow-x: auto;
        }
        
        .messages-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .messages-table th,
        .messages-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }
        
        .messages-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .message-row:hover {
            background: #f8f9fa;
        }
        
        .message-row.selected {
            background: #e3f2fd;
        }
        
        .user-info strong {
            display: block;
            color: #333;
        }
        
        .user-info small {
            color: #666;
            font-size: 12px;
        }
        
        .message-subject {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .message-preview {
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .category-badge,
        .priority-badge,
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }
        
        .priority-badge.priority-low { background: #d4edda; color: #155724; }
        .priority-badge.priority-medium { background: #fff3cd; color: #856404; }
        .priority-badge.priority-high { background: #f8d7da; color: #721c24; }
        .priority-badge.priority-urgent { background: #f5c6cb; color: #721c24; }
        
        .status-badge.status-pending { background: #fff3cd; color: #856404; }
        .status-badge.status-read { background: #d1ecf1; color: #0c5460; }
        .status-badge.status-replied { background: #d4edda; color: #155724; }
        .status-badge.status-closed { background: #d6d8db; color: #383d41; }
        
        .message-date {
            color: #333;
            font-weight: 500;
        }
        
        .message-date small {
            display: block;
            color: #666;
            font-size: 11px;
        }
        
        .reply-count {
            background: #4682B4;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 64px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .pagination-btn {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .pagination-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }
        
        .pagination-info {
            color: #666;
            font-weight: 600;
        }

        /* Message Details Section */
        .message-details-section {
            background: white;
            border-radius: 15px;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .message-details-header {
            background: #f8f9fa;
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .message-details-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.3em;
        }

        .message-details-content {
            padding: 25px;
        }

        .message-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .info-row {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-row strong {
            min-width: 80px;
            color: #333;
        }

        .original-message {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-right: 4px solid #4682B4;
        }

        .original-message h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .message-text {
            color: #555;
            line-height: 1.6;
            font-size: 15px;
        }

        .replies-section {
            margin-bottom: 25px;
        }

        .replies-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .reply-item {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #28a745;
        }

        .reply-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .reply-author {
            font-weight: 600;
            color: #28a745;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reply-date {
            color: #666;
            font-size: 14px;
        }

        .reply-text {
            color: #333;
            line-height: 1.6;
            font-size: 14px;
        }

        .reply-form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .reply-form-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
            line-height: 1.6;
        }

        .form-actions {
            text-align: center;
        }

        .quick-actions {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ffeaa7;
        }

        .quick-actions h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .status-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .admin-container {
                padding: 15px;
            }

            .filters-form {
                grid-template-columns: 1fr;
            }

            .filter-actions {
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .messages-table {
                font-size: 14px;
            }

            .messages-table th,
            .messages-table td {
                padding: 10px 8px;
            }

            .message-details-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .status-buttons {
                flex-direction: column;
            }

            .info-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>

    <script>
        // Auto-hide alerts
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });

            // Handle quick status update
            const statusButtons = document.querySelectorAll('.status-buttons button');
            statusButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const form = this.closest('form');
                    const statusInput = form.querySelector('input[name="status"]');
                    statusInput.value = this.value;
                });
            });

            // Auto-scroll to message details if selected
            const messageDetails = document.querySelector('.message-details-section');
            if (messageDetails) {
                messageDetails.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    </script>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
