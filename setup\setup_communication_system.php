<?php
/**
 * Student-Teacher Communication System Setup Script
 * This script automatically creates the necessary database tables and initializes the system
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

class CommunicationSystemSetup {
    private $db;
    private $errors = [];
    private $success = [];
    
    public function __construct() {
        try {
            $this->db = Database::getInstance()->getConnection();
        } catch (Exception $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function install() {
        echo "<h2>Setting up Student-Teacher Communication System...</h2>\n";
        
        // Check if tables already exist
        if ($this->tablesExist()) {
            echo "<p style='color: orange;'>⚠️ Communication system tables already exist. Skipping creation...</p>\n";
            return true;
        }
        
        // Create tables
        $this->createTables();
        
        // Insert default data
        $this->insertDefaultData();
        
        // Display results
        $this->displayResults();
        
        return empty($this->errors);
    }
    
    private function tablesExist() {
        try {
            $tables = ['student_messages', 'admin_replies', 'message_notifications', 'message_categories', 'message_templates'];
            $existingTables = $this->db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

            $allExist = true;
            foreach ($tables as $table) {
                if (!in_array($table, $existingTables)) {
                    $allExist = false;
                    break;
                }
            }

            // If some tables exist but not all, suggest reset
            $someExist = false;
            foreach ($tables as $table) {
                if (in_array($table, $existingTables)) {
                    $someExist = true;
                    break;
                }
            }

            if ($someExist && !$allExist) {
                echo "<div style='color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
                echo "<h4>⚠️ تحذير: جداول غير مكتملة</h4>";
                echo "<p>يبدو أن بعض جداول النظام موجودة ولكن ليس جميعها. هذا قد يسبب أخطاء.</p>";
                echo "<p><strong>الحلول المقترحة:</strong></p>";
                echo "<ul>";
                echo "<li><a href='reset_communication_system.php' style='color: #dc3545; font-weight: bold;'>إعادة تعيين النظام (حذف وإعادة إنشاء الجداول)</a></li>";
                echo "<li>أو تجاهل هذا التحذير والمتابعة</li>";
                echo "</ul>";
                echo "</div>";
            }

            return $allExist;
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function createTables() {
        $sqlFile = __DIR__ . '/../sql/student_teacher_communication.sql';
        
        if (!file_exists($sqlFile)) {
            $this->errors[] = "SQL file not found: {$sqlFile}";
            return;
        }
        
        $sql = file_get_contents($sqlFile);
        
        // Remove comments and split into statements
        $statements = $this->parseSqlStatements($sql);
        
        foreach ($statements as $statement) {
            if (trim($statement)) {
                try {
                    $this->db->exec($statement);
                    $this->success[] = "Executed: " . substr(trim($statement), 0, 50) . "...";
                } catch (PDOException $e) {
                    // Skip ALTER TABLE errors if column already exists
                    if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                        $this->errors[] = "SQL Error: " . $e->getMessage() . " - Statement: " . substr($statement, 0, 100);
                    }
                }
            }
        }
    }
    
    private function parseSqlStatements($sql) {
        // Remove comments
        $lines = explode("\n", $sql);
        $cleanedLines = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && !preg_match('/^\s*--/', $line)) {
                $cleanedLines[] = $line;
            }
        }
        
        $cleanedSql = implode("\n", $cleanedLines);
        
        // Split by semicolon but be careful with semicolons inside strings
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';
        
        for ($i = 0; $i < strlen($cleanedSql); $i++) {
            $char = $cleanedSql[$i];
            
            if (!$inString && ($char === '"' || $char === "'")) {
                $inString = true;
                $stringChar = $char;
            } elseif ($inString && $char === $stringChar) {
                $inString = false;
                $stringChar = '';
            } elseif (!$inString && $char === ';') {
                $statements[] = trim($current);
                $current = '';
                continue;
            }
            
            $current .= $char;
        }
        
        if (trim($current)) {
            $statements[] = trim($current);
        }
        
        return array_filter($statements, function($stmt) {
            return !empty(trim($stmt));
        });
    }
    
    private function insertDefaultData() {
        // Check if default data already exists
        try {
            $count = $this->db->query("SELECT COUNT(*) FROM message_categories")->fetchColumn();
            if ($count > 0) {
                $this->success[] = "Default categories already exist, skipping insertion.";
                return;
            }
        } catch (Exception $e) {
            $this->errors[] = "Error checking existing data: " . $e->getMessage();
            return;
        }
        
        // The default data is already included in the SQL file
        $this->success[] = "Default message categories and templates inserted successfully.";
    }
    
    private function displayResults() {
        echo "<h3>Setup Results:</h3>\n";
        
        if (!empty($this->success)) {
            echo "<div style='color: green; background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>✅ Success:</h4>\n";
            foreach ($this->success as $message) {
                echo "<p>• {$message}</p>\n";
            }
            echo "</div>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<div style='color: red; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>❌ Errors:</h4>\n";
            foreach ($this->errors as $error) {
                echo "<p>• {$error}</p>\n";
            }
            echo "</div>\n";
        }
        
        if (empty($this->errors)) {
            echo "<div style='color: blue; background: #cce7ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>🎉 Communication System Setup Complete!</h4>\n";
            echo "<p>The student-teacher communication system has been successfully installed.</p>\n";
            echo "<p><strong>Next steps:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Students can now send messages via the 'Ask Teacher' page</li>\n";
            echo "<li>Admins can view and reply to messages in the admin panel</li>\n";
            echo "<li>Notification system is active for new messages</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getSuccess() {
        return $this->success;
    }
}

// Run the setup if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'setup_communication_system.php') {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إعداد نظام التواصل - <?php echo SITE_NAME; ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .btn { background: #4682B4; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
            .btn:hover { background: #357abd; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>إعداد نظام التواصل بين الطلاب والمعلمين</h1>
            
            <?php
            $setup = new CommunicationSystemSetup();
            $success = $setup->install();
            
            if ($success) {
                echo "<p style='text-align: center; margin-top: 20px;'>";
                echo "<a href='../admin/student_messages.php' class='btn'>الذهاب إلى إدارة الرسائل</a> ";
                echo "<a href='../page/ask_teacher.php' class='btn'>اختبار صفحة اسأل معلم</a>";
                echo "</p>";
            }
            ?>
        </div>
    </body>
    </html>
    <?php
}
?>
