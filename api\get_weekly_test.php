<?php
// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(0);

// Set the correct path
$basePath = dirname(__DIR__);
require_once $basePath . '/config/config.php';
require_once $basePath . '/includes/database.php';

// Get database connection
try {
    $db = Database::getInstance()->getConnection();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Simple authentication check
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit;
}

// Get test ID
$testId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$testId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الاختبار مطلوب']);
    exit;
}

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        // For testing, create a dummy session
        $_SESSION['user_id'] = 1;
    }

    $userId = $_SESSION['user_id'];

    // Check if tables exist and create if needed
    try {
        $db->query("SELECT 1 FROM course_weekly_tests LIMIT 1");
    } catch (Exception $e) {
        // Create tables if they don't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS course_weekly_tests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                duration_minutes INT DEFAULT 30,
                total_marks DECIMAL(5,2) DEFAULT 0,
                passing_marks DECIMAL(5,2) DEFAULT 0,
                week_number INT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_course_id (course_id),
                INDEX idx_week_number (week_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS course_weekly_test_questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                test_id INT NOT NULL,
                question_text TEXT NOT NULL,
                question_type ENUM('true_false', 'multiple_choice') NOT NULL,
                options JSON,
                correct_answer TEXT NOT NULL,
                explanation TEXT,
                points DECIMAL(5,2) DEFAULT 1.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_test_id (test_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS user_weekly_test_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                test_id INT NOT NULL,
                total_score DECIMAL(5,2) DEFAULT 0.00,
                max_score DECIMAL(5,2) DEFAULT 0.00,
                percentage DECIMAL(5,2) DEFAULT 0.00,
                time_taken_minutes INT DEFAULT 0,
                attempt_number INT DEFAULT 1,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP NULL,
                INDEX idx_user_test (user_id, test_id),
                INDEX idx_completed_at (completed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_course (user_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                subject VARCHAR(100),
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }
    
    // Get weekly test details
    $stmt = $db->prepare("
        SELECT wt.*, c.title as course_title, c.subject
        FROM course_weekly_tests wt
        JOIN courses c ON wt.course_id = c.id
        WHERE wt.id = ? AND wt.is_active = 1
    ");
    $stmt->execute([$testId]);
    $test = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$test) {
        // Create sample data if no tests exist
        $stmt = $db->query("SELECT COUNT(*) FROM course_weekly_tests");
        $testCount = $stmt->fetchColumn();

        if ($testCount == 0) {
            // Create a sample course first
            $stmt = $db->query("SELECT COUNT(*) FROM courses");
            $courseCount = $stmt->fetchColumn();

            if ($courseCount == 0) {
                $stmt = $db->prepare("
                    INSERT INTO courses (title, subject, description, is_active)
                    VALUES ('كورس تجريبي', 'البرمجة', 'كورس تجريبي للاختبار', 1)
                ");
                $stmt->execute();
                $courseId = $db->lastInsertId();

                // Create enrollment for the user
                $stmt = $db->prepare("
                    INSERT INTO course_enrollments (user_id, course_id, status)
                    VALUES (?, ?, 'active')
                ");
                $stmt->execute([$userId, $courseId]);
            } else {
                $stmt = $db->query("SELECT id FROM courses LIMIT 1");
                $courseId = $stmt->fetchColumn();
            }

            // Create sample weekly tests
            for ($i = 1; $i <= 10; $i++) {
                $stmt = $db->prepare("
                    INSERT INTO course_weekly_tests (course_id, title, description, duration_minutes, total_marks, passing_marks, week_number, is_active, created_by)
                    VALUES (?, ?, ?, 20, 5, 3, 1, 1, 1)
                ");
                $stmt->execute([$courseId, "اختبار أسبوعي تجريبي $i", "اختبار أسبوعي تجريبي رقم $i للاختبار"]);
                $newTestId = $db->lastInsertId();

                // Add sample questions
                $questions = [
                    ['هل CSS تستخدم للتنسيق؟', 'true_false', 'true', 1],
                    ['هل MySQL قاعدة بيانات؟', 'true_false', 'true', 2],
                    ['هل JavaScript لغة برمجة؟', 'true_false', 'true', 2]
                ];

                foreach ($questions as $q) {
                    $stmt = $db->prepare("
                        INSERT INTO course_weekly_test_questions (test_id, question_text, question_type, correct_answer, points)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$newTestId, $q[0], $q[1], $q[2], $q[3]]);
                }
            }

            // Now try to get the requested test again
            $stmt = $db->prepare("
                SELECT wt.*, c.title as course_title, c.subject
                FROM course_weekly_tests wt
                JOIN courses c ON wt.course_id = c.id
                WHERE wt.id = ? AND wt.is_active = 1
            ");
            $stmt->execute([$testId]);
            $test = $stmt->fetch(PDO::FETCH_ASSOC);
        }

        if (!$test) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'الاختبار غير موجود']);
            exit;
        }
    }
    
    // Check if user is enrolled in the course
    $stmt = $db->prepare("
        SELECT id FROM course_enrollments 
        WHERE user_id = ? AND course_id = ? AND status = 'active'
    ");
    $stmt->execute([$userId, $test['course_id']]);
    
    if (!$stmt->fetch()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'غير مسجل في هذا الكورس']);
        exit;
    }
    
    // Check if user has already completed this test
    $stmt = $db->prepare("
        SELECT id, total_score, max_score, percentage, attempt_number, completed_at
        FROM user_weekly_test_attempts
        WHERE user_id = ? AND test_id = ? AND completed_at IS NOT NULL
        ORDER BY attempt_number DESC
        LIMIT 1
    ");
    $stmt->execute([$userId, $testId]);
    $lastSubmission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get test questions
    $stmt = $db->prepare("
        SELECT id, question_text, question_type, options, correct_answer, points
        FROM course_weekly_test_questions
        WHERE test_id = ?
        ORDER BY id ASC
    ");
    $stmt->execute([$testId]);
    $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process questions (hide correct answers from client)
    $processedQuestions = [];
    foreach ($questions as $question) {
        $processedQuestion = [
            'id' => $question['id'],
            'question_text' => $question['question_text'],
            'question_type' => $question['question_type'],
            'points' => $question['points']
        ];
        
        // Add options for multiple choice questions
        if ($question['question_type'] === 'multiple_choice' && $question['options']) {
            $processedQuestion['options'] = json_decode($question['options'], true);
        }
        
        $processedQuestions[] = $processedQuestion;
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'test' => [
            'id' => $test['id'],
            'title' => $test['title'],
            'description' => $test['description'],
            'course_title' => $test['course_title'],
            'subject' => $test['subject'],
            'week_number' => $test['week_number'],
            'total_marks' => $test['total_marks'],
            'passing_marks' => $test['passing_marks'],
            'duration_minutes' => $test['duration_minutes'],
            'instructions' => $test['instructions'],
            'questions' => $processedQuestions
        ]
    ];
    
    // Add previous attempt info if exists
    if ($lastSubmission) {
        $response['last_attempt'] = [
            'total_score' => $lastSubmission['total_score'],
            'max_score' => $lastSubmission['max_score'],
            'percentage' => $lastSubmission['percentage'],
            'attempt_number' => $lastSubmission['attempt_number'],
            'completed_at' => $lastSubmission['completed_at']
        ];
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Error in get_weekly_test.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في الخادم']);
}
?>
