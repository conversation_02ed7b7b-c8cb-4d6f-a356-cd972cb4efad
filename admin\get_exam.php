<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/ExamManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Check if exam ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الامتحان مطلوب']);
    exit;
}

$examManager = new ExamManager();
$exam = $examManager->getExamById($_GET['id']);

if ($exam) {
    echo json_encode(['success' => true, 'exam' => $exam]);
} else {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
}
?>
