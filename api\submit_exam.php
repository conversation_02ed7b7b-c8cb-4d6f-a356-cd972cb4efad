<?php
require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');


// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// For testing, allow access without login
// if (!isset($_SESSION['user_id'])) {
//     http_response_code(401);
//     echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
//     exit;
// }

// Use a test user ID if not logged in
$userId = $_SESSION['user_id'] ?? 1;

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();

    $examId = $_POST['exam_id'] ?? null;
    $answers = $_POST['answers'] ?? [];

    // Debug logging
    error_log("Exam submission - Exam ID: $examId, Answers: " . json_encode($answers));

    if (!$examId || empty($answers)) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }

    if (!is_numeric($examId)) {
        echo json_encode(['success' => false, 'message' => 'معرف الامتحان غير صالح']);
        exit;
    }
    
    // Get exam details
    $stmt = $db->prepare("
        SELECT id, title, total_marks, passing_marks
        FROM course_exams 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$examId]);
    $exam = $stmt->fetch();
    
    if (!$exam) {
        echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
        exit;
    }
    
    // Get all questions for this exam
    $stmt = $db->prepare("
        SELECT * FROM course_exam_questions 
        WHERE exam_id = ? 
        ORDER BY id ASC
    ");
    $stmt->execute([$examId]);
    $questions = $stmt->fetchAll();
    
    if (empty($questions)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد أسئلة لهذا الامتحان']);
        exit;
    }
    
    // Debug: Log received answers
    error_log("Received answers: " . json_encode($answers));
    error_log("Questions count: " . count($questions));

    // Validate that all questions are answered
    $unansweredQuestions = [];
    foreach ($questions as $index => $question) {
        // Check both index-based and question-id-based answers
        $hasAnswer = false;
        if (isset($answers[$index]) && trim($answers[$index]) !== '') {
            $hasAnswer = true;
        } elseif (isset($answers[$question['id']]) && trim($answers[$question['id']]) !== '') {
            $hasAnswer = true;
        }

        if (!$hasAnswer) {
            $unansweredQuestions[] = $index + 1;
        }
    }

    if (!empty($unansweredQuestions)) {
        echo json_encode([
            'success' => false,
            'message' => 'يرجى الإجابة على جميع الأسئلة',
            'unanswered_questions' => $unansweredQuestions
        ]);
        exit;
    }
    
    // Get current attempt number
    $questionIds = array_column($questions, 'id');
    if (empty($questionIds)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد أسئلة صالحة']);
        exit;
    }

    $stmt = $db->prepare("
        SELECT COALESCE(MAX(attempt_number), 0) + 1 as next_attempt
        FROM user_exam_answers_simple
        WHERE user_id = ? AND question_id IN (" . implode(',', $questionIds) . ")
    ");
    $stmt->execute([$userId]);
    $attemptNumber = $stmt->fetchColumn();
    
    $db->beginTransaction();
    
    $results = [];
    $totalScore = 0;
    $maxScore = 0;
    
    // Process each answer
    foreach ($questions as $index => $question) {
        // Get user answer - try both index-based and question-id-based
        $userAnswer = '';
        if (isset($answers[$index]) && trim($answers[$index]) !== '') {
            $userAnswer = trim($answers[$index]);
        } elseif (isset($answers[$question['id']]) && trim($answers[$question['id']]) !== '') {
            $userAnswer = trim($answers[$question['id']]);
        }

        $correctAnswer = trim($question['correct_answer']);
        $questionPoints = floatval($question['points']);
        $maxScore += $questionPoints;

        // Debug logging for each question
        error_log("Question {$question['id']}: User answer = '$userAnswer', Correct answer = '$correctAnswer', Type = {$question['question_type']}");

        // If correct answer is empty, mark as incorrect but log the issue
        if (empty($correctAnswer)) {
            error_log("WARNING: Question {$question['id']} has empty correct answer!");
        }
        
        // Normalize answers for true/false questions
        if ($question['question_type'] === 'true_false') {
            // Normalize user answer
            if (in_array(strtolower($userAnswer), ['true', 'صح', '1', 'نعم'])) {
                $userAnswer = 'true';
            } elseif (in_array(strtolower($userAnswer), ['false', 'خطأ', '0', 'لا'])) {
                $userAnswer = 'false';
            }
            
            // Normalize correct answer
            if (in_array(strtolower($correctAnswer), ['true', 'صح', '1', 'نعم'])) {
                $correctAnswer = 'true';
            } elseif (in_array(strtolower($correctAnswer), ['false', 'خطأ', '0', 'لا'])) {
                $correctAnswer = 'false';
            }
        }
        
        $isCorrect = (strcasecmp($userAnswer, $correctAnswer) === 0);
        $isCorrectInt = $isCorrect ? 1 : 0; // Convert boolean to integer
        $score = $isCorrect ? $questionPoints : 0;
        $totalScore += $score;
        
        // Format correct answer for display
        $displayCorrectAnswer = $question['correct_answer'] ?? 'غير محدد';
        if ($question['question_type'] === 'true_false') {
            if ($correctAnswer === 'true') {
                $displayCorrectAnswer = 'صح';
            } elseif ($correctAnswer === 'false') {
                $displayCorrectAnswer = 'خطأ';
            }
        }
        
        // Format user answer for display
        $displayUserAnswer = $answers[$index];
        if ($question['question_type'] === 'true_false') {
            if ($userAnswer === 'true') {
                $displayUserAnswer = 'صح';
            } elseif ($userAnswer === 'false') {
                $displayUserAnswer = 'خطأ';
            }
        }
        
        // Save answer to database
        $stmt = $db->prepare("
            INSERT INTO user_exam_answers_simple (user_id, question_id, user_answer, is_correct, score, attempt_number)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $question['id'], $answers[$index], $isCorrectInt, $score, $attemptNumber]);
        
        $results[] = [
            'question_number' => $index + 1,
            'question_text' => $question['question_text'],
            'user_answer' => $displayUserAnswer,
            'correct_answer' => $displayCorrectAnswer,
            'is_correct' => $isCorrect,
            'score' => $score,
            'max_score' => $questionPoints,
            'explanation' => $question['explanation'] ?? '',
            'question_type' => $question['question_type']
        ];
    }
    
    // Calculate percentage and pass status
    $percentage = $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 1) : 0;
    $passed = $percentage >= 60; // Use percentage-based passing (60%)

    // Save exam attempt result
    $stmt = $db->prepare("
        INSERT INTO user_exam_attempts (
            user_id, exam_id, attempt_number, total_score, max_score,
            percentage, passed, completed_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([
        $userId, $examId, $attemptNumber, $totalScore, $maxScore,
        $percentage, $passed ? 1 : 0
    ]);

    $db->commit();

    echo json_encode([
        'success' => true,
        'results' => $results,
        'total_questions' => count($questions),
        'total_score' => $totalScore,
        'max_score' => $maxScore,
        'percentage' => $percentage,
        'passing_marks' => $exam['passing_marks'],
        'passed' => $passed,
        'attempt_number' => $attemptNumber,
        'exam_title' => $exam['title']
    ]);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error submitting exam: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
