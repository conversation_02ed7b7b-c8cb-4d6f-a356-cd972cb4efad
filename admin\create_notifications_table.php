<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إنشاء جدول الإشعارات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
</style>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create user_notifications table
    $createNotificationsSQL = "CREATE TABLE IF NOT EXISTS user_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        notification_type ENUM('subscription_expired', 'subscription_expiring', 'subscription_renewed', 'subscription_cancelled') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        is_dismissed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        metadata JSON NULL,
        
        INDEX idx_user_id (user_id),
        INDEX idx_type (notification_type),
        INDEX idx_read (is_read),
        INDEX idx_dismissed (is_dismissed),
        INDEX idx_created (created_at),
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($createNotificationsSQL);
    echo "<div class='success'>✅ تم إنشاء جدول user_notifications بنجاح</div>";
    
    // Create notification_settings table
    $createSettingsSQL = "CREATE TABLE IF NOT EXISTS notification_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        notification_type VARCHAR(50) NOT NULL,
        is_enabled BOOLEAN DEFAULT TRUE,
        last_shown TIMESTAMP NULL,
        show_count INT DEFAULT 0,
        max_show_count INT DEFAULT 1,
        
        UNIQUE KEY unique_user_type (user_id, notification_type),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($createSettingsSQL);
    echo "<div class='success'>✅ تم إنشاء جدول notification_settings بنجاح</div>";
    
    // Test data
    echo "<div class='info'>📊 إحصائيات الجداول:</div>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM user_notifications");
    $notif_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>عدد الإشعارات: $notif_count</div>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM notification_settings");
    $settings_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>عدد إعدادات الإشعارات: $settings_count</div>";
    
    echo "<div class='success'>🎉 تم إعداد نظام الإشعارات بنجاح!</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div class='error'>الملف: " . $e->getFile() . "</div>";
    echo "<div class='error'>السطر: " . $e->getLine() . "</div>";
}
?>

<p><a href="../page/dashboard.php">الذهاب للداشبورد</a></p>
