<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Create table if it doesn't exist
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_completion_notes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            completion_date DATETIME NOT NULL,
            note TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_completion (user_id, course_id),
            INDEX idx_user_id (user_id),
            INDEX idx_course_id (course_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Get all completion notes with user and course details
    $stmt = $db->prepare("
        SELECT 
            ccn.*,
            u.first_name,
            u.last_name,
            u.email,
            c.title as course_title,
            c.subject as course_subject
        FROM course_completion_notes ccn
        JOIN users u ON ccn.user_id = u.id
        JOIN courses c ON ccn.course_id = c.id
        ORDER BY ccn.completion_date DESC
    ");
    $stmt->execute();
    $completionNotes = $stmt->fetchAll();
    
    // Get statistics
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_completions,
            COUNT(DISTINCT user_id) as unique_students,
            COUNT(DISTINCT course_id) as completed_courses
        FROM course_completion_notes
    ");
    $stmt->execute();
    $stats = $stmt->fetch();
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملحوظات الطلاب على الكورسات - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="admin-main">
            <div class="admin-header">
                <div class="header-content">
                    <h1><i class="fas fa-sticky-note"></i> ملحوظات الطلاب على الكورسات</h1>
                    <p>عرض وإدارة ملحوظات إكمال الطلاب للكورسات</p>
                </div>
            </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['total_completions'] ?? 0; ?></div>
                            <div class="stat-label">إجمالي الإكمالات</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['unique_students'] ?? 0; ?></div>
                            <div class="stat-label">طلاب مكملين</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['completed_courses'] ?? 0; ?></div>
                            <div class="stat-label">كورسات مكتملة</div>
                        </div>
                    </div>
                </div>

                <!-- Completion Notes Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-list"></i> ملحوظات الإكمال</h3>
                        <div class="table-actions">
                            <button class="btn btn-primary" onclick="openModal('addModal')" onclick="exportNotes()">
                                <i class="fas fa-download"></i>
                                تصدير البيانات
                            </button>
                        </div>
                    </div>
                    
                    <?php if (empty($completionNotes)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-sticky-note"></i>
                            </div>
                            <h3>لا توجد ملحوظات إكمال</h3>
                            <p>لم يكمل أي طالب أي كورس بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>الطالب</th>
                                        <th>الكورس</th>
                                        <th>تاريخ الإكمال</th>
                                        <th>الملحوظة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($completionNotes as $note): ?>
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <div class="user-name">
                                                        <?php echo htmlspecialchars($note['first_name'] . ' ' . $note['last_name']); ?>
                                                    </div>
                                                    <div class="user-email">
                                                        <?php echo htmlspecialchars($note['email']); ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="course-info">
                                                    <div class="course-title">
                                                        <?php echo htmlspecialchars($note['course_title']); ?>
                                                    </div>
                                                    <div class="course-subject">
                                                        <?php echo htmlspecialchars($note['course_subject']); ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="date-info">
                                                    <div class="completion-date">
                                                        <?php echo date('Y-m-d', strtotime($note['completion_date'])); ?>
                                                    </div>
                                                    <div class="completion-time">
                                                        <?php echo date('H:i', strtotime($note['completion_date'])); ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="note-content">
                                                    <?php echo htmlspecialchars($note['note']); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-sm btn-info" onclick="viewDetails(<?php echo $note['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                        عرض
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="deleteNote(<?php echo $note['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                        حذف
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function viewDetails(noteId) {
            // يمكن إضافة نافذة منبثقة لعرض تفاصيل أكثر
            alert('عرض تفاصيل الملحوظة رقم: ' + noteId);
        }

        function deleteNote(noteId) {
            if (confirm('هل أنت متأكد من حذف هذه الملحوظة؟')) {
                // إضافة كود الحذف هنا
                alert('سيتم حذف الملحوظة رقم: ' + noteId);
            }
        }

        function exportNotes() {
            // إضافة كود التصدير هنا
            alert('سيتم تصدير البيانات قريباً');
        }
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
