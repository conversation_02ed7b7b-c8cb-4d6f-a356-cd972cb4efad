<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔄 إعادة تعيين نظام الاشتراكات</h2>";
    echo "<p style='color: red; font-weight: bold;'>⚠️ تحذير: سيتم حذف جميع بيانات الاشتراكات!</p>";
    
    $success = 0;
    $errors = 0;
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // 1. مسح بيانات جدول المدفوعات
        echo "<p>🗑️ مسح بيانات جدول payments...</p>";
        $result = $db->exec("DELETE FROM payments");
        echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول payments</p>";
        $success++;
        
        // 2. مسح بيانات جدول اشتراكات المستخدمين
        echo "<p>🗑️ مسح بيانات جدول user_subscriptions...</p>";
        $result = $db->exec("DELETE FROM user_subscriptions");
        echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول user_subscriptions</p>";
        $success++;
        
        // 3. مسح بيانات جدول أكواد التفعيل للاشتراكات
        echo "<p>🗑️ مسح بيانات جدول activation_codes...</p>";
        $result = $db->exec("DELETE FROM activation_codes");
        echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول activation_codes</p>";
        $success++;
        
        // 4. مسح بيانات جدول أكواد تفعيل الكورسات
        echo "<p>🗑️ مسح بيانات جدول course_activation_codes...</p>";
        $result = $db->exec("DELETE FROM course_activation_codes");
        echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول course_activation_codes</p>";
        $success++;
        
        // 5. مسح بيانات جدول إحصائيات الاشتراكات
        echo "<p>🗑️ مسح بيانات جدول subscription_stats...</p>";
        $result = $db->exec("DELETE FROM subscription_stats");
        echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول subscription_stats</p>";
        $success++;
        
        // 6. مسح بيانات جدول تمديد الاشتراكات (إذا كان موجود)
        echo "<p>🗑️ مسح بيانات جدول subscription_extensions...</p>";
        try {
            $result = $db->exec("DELETE FROM subscription_extensions");
            echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول subscription_extensions</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ جدول subscription_extensions غير موجود</p>";
        }
        
        // 7. مسح بيانات جدول خطط الاشتراك
        echo "<p>🗑️ مسح بيانات جدول subscription_plans...</p>";
        $result = $db->exec("DELETE FROM subscription_plans");
        echo "<p style='color: green;'>✅ تم حذف {$result} سجل من جدول subscription_plans</p>";
        $success++;
        
        // 8. إعادة تعيين معلومات الاشتراك في جدول المستخدمين
        echo "<p>🔄 إعادة تعيين معلومات الاشتراك في جدول users...</p>";
        $result = $db->exec("UPDATE users SET 
                            subscription_status = 'none', 
                            current_plan_id = NULL, 
                            subscription_end_date = NULL");
        echo "<p style='color: green;'>✅ تم تحديث {$result} مستخدم في جدول users</p>";
        $success++;
        
        // 9. إعادة تعيين AUTO_INCREMENT للجداول
        echo "<p>🔄 إعادة تعيين AUTO_INCREMENT للجداول...</p>";
        $tables = ['subscription_plans', 'user_subscriptions', 'activation_codes', 'course_activation_codes', 'payments', 'subscription_stats'];
        
        foreach ($tables as $table) {
            try {
                $db->exec("ALTER TABLE {$table} AUTO_INCREMENT = 1");
                echo "<p style='color: blue;'>🔄 تم إعادة تعيين AUTO_INCREMENT لجدول {$table}</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ لم يتم إعادة تعيين AUTO_INCREMENT لجدول {$table}</p>";
            }
        }
        
        // Commit transaction
        $db->commit();

        echo "<hr>";
        echo "<h3 style='color: green;'>🎉 تم إعادة تعيين نظام الاشتراكات بنجاح!</h3>";
        echo "<p>✅ العمليات الناجحة: {$success}</p>";
        echo "<p>❌ العمليات الفاشلة: {$errors}</p>";

    } catch (Exception $e) {
        $db->rollback();
        echo "<p style='color: red;'>❌ خطأ في العملية: " . $e->getMessage() . "</p>";
        $errors++;
    }

    // إدراج خطط افتراضية جديدة (خارج المعاملة)
    try {
        echo "<h3>📝 إدراج خطط افتراضية جديدة...</h3>";
        
        $defaultPlans = [
            [
                'name' => 'الخطة الأساسية',
                'name_en' => 'Basic Plan',
                'description' => 'خطة مناسبة للمبتدئين',
                'price' => 99.00,
                'discount_percentage' => 0,
                'discounted_price' => 99.00,
                'duration_days' => 30,
                'features' => '["الوصول لجميع الأقسام", "دعم فني أساسي", "تحديثات مجانية"]',
                'icon' => '📚',
                'color' => '#4682B4',
                'is_popular' => 0,
                'sort_order' => 1
            ],
            [
                'name' => 'الخطة المتقدمة',
                'name_en' => 'Advanced Plan',
                'description' => 'خطة شاملة للطلاب الجادين',
                'price' => 199.00,
                'discount_percentage' => 20,
                'discounted_price' => 159.20,
                'duration_days' => 60,
                'features' => '["الوصول لجميع الأقسام", "دعم فني متقدم", "تحديثات مجانية", "اختبارات إضافية", "تقارير مفصلة"]',
                'icon' => '🎓',
                'color' => '#28a745',
                'is_popular' => 1,
                'sort_order' => 2
            ],
            [
                'name' => 'الخطة المميزة',
                'name_en' => 'Premium Plan',
                'description' => 'خطة متكاملة مع جميع المميزات',
                'price' => 299.00,
                'discount_percentage' => 25,
                'discounted_price' => 224.25,
                'duration_days' => 90,
                'features' => '["الوصول لجميع الأقسام", "دعم فني مميز", "تحديثات مجانية", "اختبارات إضافية", "تقارير مفصلة", "جلسات فردية", "محتوى حصري"]',
                'icon' => '👑',
                'color' => '#ffc107',
                'is_popular' => 0,
                'sort_order' => 3
            ]
        ];
        
        $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, is_active, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, 1)");
        
        foreach ($defaultPlans as $plan) {
            $stmt->execute([
                $plan['name'],
                $plan['name_en'],
                $plan['description'],
                $plan['price'],
                $plan['discount_percentage'],
                $plan['discounted_price'],
                $plan['duration_days'],
                $plan['features'],
                $plan['icon'],
                $plan['color'],
                $plan['is_popular'],
                $plan['sort_order']
            ]);
            echo "<p style='color: green;'>✅ تم إضافة خطة: {$plan['name']}</p>";
        }
        
        // إدراج أكواد تفعيل تجريبية
        echo "<h3>🎫 إدراج أكواد تفعيل تجريبية...</h3>";
        
        $testCodes = [
            ['code' => 'BASIC2024', 'plan_id' => 1, 'notes' => 'كود تجريبي للخطة الأساسية'],
            ['code' => 'ADVANCED2024', 'plan_id' => 2, 'notes' => 'كود تجريبي للخطة المتقدمة'],
            ['code' => 'PREMIUM2024', 'plan_id' => 3, 'notes' => 'كود تجريبي للخطة المميزة']
        ];
        
        $stmt = $db->prepare("INSERT INTO activation_codes (code, plan_id, expires_at, created_by, notes) VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY), 1, ?)");
        
        foreach ($testCodes as $code) {
            $stmt->execute([$code['code'], $code['plan_id'], $code['notes']]);
            echo "<p style='color: green;'>✅ تم إضافة كود: {$code['code']}</p>";
        }
        
        // إدراج إحصائيات اليوم
        echo "<h3>📊 إدراج إحصائيات اليوم...</h3>";
        $today = date('Y-m-d');
        $stmt = $db->prepare("INSERT INTO subscription_stats (date, total_subscriptions, new_subscriptions, cancelled_subscriptions, expired_subscriptions, total_revenue) VALUES (?, 0, 0, 0, 0, 0.00)");
        $stmt->execute([$today]);
        echo "<p style='color: green;'>✅ تم إضافة إحصائيات اليوم</p>";

        echo "<hr>";
        echo "<h3 style='color: green;'>🎉 تم إعداد النظام بالكامل بنجاح!</h3>";

    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في إدراج البيانات الافتراضية: " . $e->getMessage() . "</p>";
        $errors++;
    }
    
    echo "<hr>";
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='../admin/subscription_plans.php' style='color: #4682B4; font-weight: bold;'>إدارة الخطط</a></li>";
    echo "<li><a href='../admin/activation_codes.php' style='color: #4682B4; font-weight: bold;'>إدارة أكواد التفعيل</a></li>";
    echo "<li><a href='../admin/subscribers.php' style='color: #4682B4; font-weight: bold;'>إدارة المشتركين</a></li>";
    echo "<li><a href='../page/subscriptions.php' style='color: #4682B4; font-weight: bold;'>صفحة الاشتراكات للمستخدمين</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        direction: rtl;
        min-height: 100vh;
    }
    
    h2, h3 {
        background: rgba(255, 255, 255, 0.9);
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin: 20px 0;
    }
    
    p {
        background: rgba(255, 255, 255, 0.8);
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #4682B4;
    }
    
    ul {
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        list-style-type: none;
    }
    
    li {
        margin: 15px 0;
        padding: 10px;
        background: rgba(70, 130, 180, 0.1);
        border-radius: 5px;
        border-right: 4px solid #4682B4;
    }
    
    a {
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    a:hover {
        text-decoration: underline;
        transform: translateX(-5px);
    }
    
    hr {
        margin: 30px 0;
        border: none;
        border-top: 2px solid rgba(255, 255, 255, 0.3);
    }
</style>
