<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

session_start();

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

try {
    // Check if classes exist
    if (!class_exists('UserManager')) {
        throw new Exception('UserManager class not found');
    }

    $userManager = new UserManager();
    $userId = $_SESSION['user_id'];

    error_log("Getting notifications for user: " . $userId);

    // Get notifications with error handling
    $notifications = $userManager->getUserNotifications($userId);

    if ($notifications === false) {
        throw new Exception('Failed to get notifications from database');
    }

    error_log("Found notifications count: " . count($notifications));

    // Format notifications for display
    $formattedNotifications = [];

    if (!empty($notifications)) {
        foreach ($notifications as $notification) {
            $formattedNotifications[] = [
                'id' => (int)$notification['id'],
                'title' => $notification['title'] ?? 'بدون عنوان',
                'message' => $notification['message'] ?? 'بدون رسالة',
                'type' => $notification['type'] ?? 'info',
                'is_read' => (bool)($notification['is_read'] ?? false),
                'created_at' => isset($notification['created_at']) ?
                    date('Y-m-d H:i', strtotime($notification['created_at'])) :
                    date('Y-m-d H:i')
            ];
        }
    }

    // Set proper headers
    header('Content-Type: application/json; charset=utf-8');

    echo json_encode([
        'success' => true,
        'notifications' => $formattedNotifications,
        'count' => count($formattedNotifications)
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    error_log("Error in get_notifications.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    header('Content-Type: application/json; charset=utf-8');
    http_response_code(500);

    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في تحميل الإشعارات',
        'error' => $e->getMessage(),
        'debug' => [
            'user_id' => $_SESSION['user_id'] ?? 'not set',
            'file' => __FILE__,
            'line' => __LINE__
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
