<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session if not started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set admin session for testing
$_SESSION['admin_id'] = 1;

echo "<h2>تشخيص مشكلة إضافة خطط الاشتراك</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.info { color: blue; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

echo "<div class='section'>";
echo "<h3>1. فحص الاتصال بقاعدة البيانات</h3>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    echo "<p class='info'>Host: " . DB_HOST . "</p>";
    echo "<p class='info'>Database: " . DB_NAME . "</p>";
    echo "<p class='info'>User: " . DB_USER . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}
echo "</div>";

echo "<div class='section'>";
echo "<h3>2. فحص وجود جدول subscription_plans</h3>";

try {
    $stmt = $db->query("SHOW TABLES LIKE 'subscription_plans'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ جدول subscription_plans موجود</p>";
        
        // Show table structure
        $stmt = $db->query("DESCRIBE subscription_plans");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h4>هيكل الجدول:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ جدول subscription_plans غير موجود</p>";
        echo "<p class='info'>سيتم إنشاء الجدول...</p>";
        
        $createTableSQL = "CREATE TABLE subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة',
            name_en VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية',
            description TEXT NULL COMMENT 'وصف الخطة',
            price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر الأصلي',
            discount_percentage INT DEFAULT 0 COMMENT 'نسبة الخصم',
            discounted_price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر بعد الخصم',
            duration_days INT NOT NULL DEFAULT 30 COMMENT 'مدة الاشتراك بالأيام',
            features JSON NULL COMMENT 'مميزات الخطة',
            icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة الخطة',
            color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة',
            is_popular BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة شائعة',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الخطة نشطة',
            sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
            created_by INT NOT NULL DEFAULT 1 COMMENT 'منشئ الخطة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_active (is_active),
            INDEX idx_sort (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($createTableSQL);
        echo "<p class='success'>✅ تم إنشاء جدول subscription_plans بنجاح</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في فحص/إنشاء الجدول: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h3>3. محاكاة إضافة خطة</h3>";

// Simulate POST data
$_POST = [
    'action' => 'add',
    'name' => 'خطة تجريبية للتشخيص',
    'name_en' => 'Debug Test Plan',
    'description' => 'هذه خطة تجريبية لتشخيص المشكلة',
    'price' => '150.00',
    'discount_percentage' => '15',
    'duration_days' => '45',
    'features' => "ميزة تجريبية 1\nميزة تجريبية 2\nميزة تجريبية 3",
    'icon' => '🔧',
    'color' => '#28a745',
    'sort_order' => '999',
    'is_active' => '1'
];

echo "<h4>البيانات المرسلة:</h4>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

try {
    // Calculate discounted price
    $price = floatval($_POST['price']);
    $discount = intval($_POST['discount_percentage']);
    $discounted_price = $price - ($price * $discount / 100);
    
    echo "<p class='info'>السعر الأصلي: $price</p>";
    echo "<p class='info'>نسبة الخصم: $discount%</p>";
    echo "<p class='info'>السعر بعد الخصم: $discounted_price</p>";
    
    // Process features
    $features = array_filter(array_map('trim', explode("\n", $_POST['features'])));
    $features_json = json_encode($features, JSON_UNESCAPED_UNICODE);
    
    echo "<p class='info'>المميزات JSON: $features_json</p>";
    
    $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, is_active, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $params = [
        $_POST['name'],
        $_POST['name_en'] ?? '',
        $_POST['description'] ?? '',
        $price,
        $discount,
        $discounted_price,
        $_POST['duration_days'],
        $features_json,
        $_POST['icon'] ?? '📚',
        $_POST['color'] ?? '#4682B4',
        isset($_POST['is_popular']) ? 1 : 0,
        isset($_POST['is_active']) ? 1 : 0,
        $_POST['sort_order'] ?? 0,
        $_SESSION['admin_id']
    ];
    
    echo "<h4>المعاملات المرسلة للاستعلام:</h4>";
    echo "<pre>" . print_r($params, true) . "</pre>";
    
    $result = $stmt->execute($params);
    
    if ($result) {
        $planId = $db->lastInsertId();
        echo "<p class='success'>✅ تم إضافة الخطة بنجاح - ID: $planId</p>";
        
        // Verify the plan was actually added
        $verifyStmt = $db->prepare("SELECT * FROM subscription_plans WHERE id = ?");
        $verifyStmt->execute([$planId]);
        $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($verifyResult) {
            echo "<h4>تفاصيل الخطة المضافة:</h4>";
            echo "<pre>" . print_r($verifyResult, true) . "</pre>";
            
            // Clean up - delete the test plan
            $deleteStmt = $db->prepare("DELETE FROM subscription_plans WHERE id = ?");
            $deleteStmt->execute([$planId]);
            echo "<p class='info'>🗑️ تم حذف الخطة التجريبية</p>";
        } else {
            echo "<p class='error'>❌ لم يتم العثور على الخطة المضافة!</p>";
        }
    } else {
        echo "<p class='error'>❌ فشل في إضافة الخطة</p>";
        echo "<p class='error'>معلومات الخطأ: " . print_r($stmt->errorInfo(), true) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في إضافة الخطة: " . $e->getMessage() . "</p>";
    echo "<p class='error'>الملف: " . $e->getFile() . "</p>";
    echo "<p class='error'>السطر: " . $e->getLine() . "</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h3>4. فحص الخطط الموجودة</h3>";

try {
    $stmt = $db->query("SELECT COUNT(*) as count FROM subscription_plans");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p class='info'>📊 عدد الخطط الحالية: $count</p>";
    
    if ($count > 0) {
        $stmt = $db->query("SELECT id, name, name_en, price, discounted_price, is_active FROM subscription_plans ORDER BY sort_order");
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>الخطط الموجودة:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الاسم الإنجليزي</th><th>السعر</th><th>السعر المخفض</th><th>نشط</th></tr>";
        foreach ($plans as $plan) {
            $status = $plan['is_active'] ? 'نعم' : 'لا';
            echo "<tr>";
            echo "<td>{$plan['id']}</td>";
            echo "<td>{$plan['name']}</td>";
            echo "<td>{$plan['name_en']}</td>";
            echo "<td>{$plan['price']}</td>";
            echo "<td>{$plan['discounted_price']}</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في فحص الخطط: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h3>5. معلومات الجلسة</h3>";
echo "<p class='info'>Admin ID: " . ($_SESSION['admin_id'] ?? 'غير محدد') . "</p>";
echo "<p class='info'>Session Status: " . session_status() . "</p>";
echo "</div>";
?>
