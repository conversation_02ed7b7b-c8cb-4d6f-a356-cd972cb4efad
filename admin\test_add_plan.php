<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>اختبار إضافة خطة اشتراك</h2>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // Check if subscription_plans table exists
    $stmt = $db->query("SHOW TABLES LIKE 'subscription_plans'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ جدول subscription_plans غير موجود. سيتم إنشاؤه...</p>";
        
        // Create subscription_plans table
        $createTableSQL = "CREATE TABLE subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة',
            name_en VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية',
            description TEXT NULL COMMENT 'وصف الخطة',
            price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر الأصلي',
            discount_percentage INT DEFAULT 0 COMMENT 'نسبة الخصم',
            discounted_price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر بعد الخصم',
            duration_days INT NOT NULL DEFAULT 30 COMMENT 'مدة الاشتراك بالأيام',
            features JSON NULL COMMENT 'مميزات الخطة',
            icon VARCHAR(10) DEFAULT '📚' COMMENT 'أيقونة الخطة',
            color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة',
            is_popular BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة شائعة',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الخطة نشطة',
            sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
            created_by INT NOT NULL DEFAULT 1 COMMENT 'منشئ الخطة',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_active (is_active),
            INDEX idx_sort (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($createTableSQL);
        echo "<p>✅ تم إنشاء جدول subscription_plans بنجاح</p>";
    } else {
        echo "<p>✅ جدول subscription_plans موجود</p>";
    }
    
    // Test adding a plan
    echo "<h3>اختبار إضافة خطة تجريبية</h3>";
    
    $testPlan = [
        'name' => 'خطة تجريبية',
        'name_en' => 'Test Plan',
        'description' => 'هذه خطة تجريبية للاختبار',
        'price' => 100.00,
        'discount_percentage' => 10,
        'discounted_price' => 90.00,
        'duration_days' => 30,
        'features' => '["ميزة 1", "ميزة 2", "ميزة 3"]',
        'icon' => '🧪',
        'color' => '#ff6b6b',
        'is_popular' => 0,
        'is_active' => 1,
        'sort_order' => 999,
        'created_by' => 1
    ];
    
    $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, is_active, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        $testPlan['name'],
        $testPlan['name_en'],
        $testPlan['description'],
        $testPlan['price'],
        $testPlan['discount_percentage'],
        $testPlan['discounted_price'],
        $testPlan['duration_days'],
        $testPlan['features'],
        $testPlan['icon'],
        $testPlan['color'],
        $testPlan['is_popular'],
        $testPlan['is_active'],
        $testPlan['sort_order'],
        $testPlan['created_by']
    ]);
    
    if ($result) {
        $planId = $db->lastInsertId();
        echo "<p>✅ تم إضافة الخطة التجريبية بنجاح - ID: $planId</p>";
        
        // Verify the plan was added
        $stmt = $db->prepare("SELECT * FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        $plan = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($plan) {
            echo "<h4>تفاصيل الخطة المضافة:</h4>";
            echo "<ul>";
            foreach ($plan as $key => $value) {
                echo "<li><strong>$key:</strong> $value</li>";
            }
            echo "</ul>";
            
            // Clean up - delete the test plan
            $stmt = $db->prepare("DELETE FROM subscription_plans WHERE id = ?");
            $stmt->execute([$planId]);
            echo "<p>🗑️ تم حذف الخطة التجريبية</p>";
        }
    } else {
        echo "<p>❌ فشل في إضافة الخطة التجريبية</p>";
    }
    
    // Show current plans count
    $stmt = $db->query("SELECT COUNT(*) as count FROM subscription_plans");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>📊 عدد الخطط الحالية في قاعدة البيانات: $count</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?>
