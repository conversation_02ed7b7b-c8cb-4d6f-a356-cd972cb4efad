<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set admin session for testing
$_SESSION['admin_id'] = 1;

echo "<h2>تشغيل فحص انتهاء الاشتراكات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
.warning { color: orange; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
.output { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; border: 1px solid #ddd; }
.btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; color: white; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; }
</style>";

echo "<div class='info'>⏰ الوقت الحالي: " . date('Y-m-d H:i:s') . "</div>";

if (isset($_POST['run_check'])) {
    echo "<div class='warning'>🔄 جاري تشغيل فحص انتهاء الاشتراكات...</div>";
    
    // Capture output
    ob_start();
    
    try {
        // Include the cron job file
        $cron_file = __DIR__ . '/../cron/check_expired_subscriptions.php';
        
        if (file_exists($cron_file)) {
            include $cron_file;
            $output = ob_get_clean();
            
            echo "<div class='success'>✅ تم تشغيل فحص انتهاء الاشتراكات بنجاح</div>";
            
            if (!empty($output)) {
                echo "<h3>مخرجات العملية:</h3>";
                echo "<div class='output'>$output</div>";
            }
            
        } else {
            ob_end_clean();
            echo "<div class='error'>❌ ملف فحص الاشتراكات غير موجود: $cron_file</div>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>❌ خطأ في تشغيل فحص الاشتراكات: " . $e->getMessage() . "</div>";
    }
    
    echo "<div class='info'>✅ انتهت عملية الفحص</div>";
}

// Show current subscription status
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get statistics
    $stats = [];
    
    // Active subscriptions
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE subscription_status = 'active'");
    $stats['active'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Expired subscriptions that need to be updated
    $current_time = date('Y-m-d H:i:s');
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users 
                         WHERE subscription_status = 'active' 
                         AND subscription_end_date <= ?");
    $stmt->execute([$current_time]);
    $stats['needs_expiry'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Already expired
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE subscription_status = 'expired'");
    $stats['expired'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<h3>حالة الاشتراكات الحالية:</h3>";
    echo "<ul>";
    echo "<li><strong>اشتراكات نشطة:</strong> {$stats['active']}</li>";
    echo "<li><strong>اشتراكات تحتاج انتهاء:</strong> <span style='color: red; font-weight: bold;'>{$stats['needs_expiry']}</span></li>";
    echo "<li><strong>اشتراكات منتهية بالفعل:</strong> {$stats['expired']}</li>";
    echo "</ul>";
    
    if ($stats['needs_expiry'] > 0) {
        echo "<div class='warning'>⚠️ يوجد {$stats['needs_expiry']} اشتراك يحتاج لانتهاء!</div>";
        
        // Show details of subscriptions that need expiry
        $stmt = $db->prepare("SELECT u.id, u.username, u.subscription_end_date, sp.name as plan_name
                             FROM users u
                             LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                             WHERE u.subscription_status = 'active' 
                             AND u.subscription_end_date <= ?
                             ORDER BY u.subscription_end_date");
        $stmt->execute([$current_time]);
        $expired_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>الاشتراكات التي تحتاج انتهاء:</h4>";
        echo "<ul>";
        foreach ($expired_users as $user) {
            $expired_since = time() - strtotime($user['subscription_end_date']);
            $expired_minutes = floor($expired_since / 60);
            echo "<li><strong>{$user['username']}</strong> (ID: {$user['id']}) - خطة: {$user['plan_name']} - انتهت منذ: $expired_minutes دقيقة</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='success'>✅ جميع الاشتراكات محدثة</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في قراءة البيانات: " . $e->getMessage() . "</div>";
}
?>

<form method="POST">
    <button type="submit" name="run_check" class="btn btn-danger">🔄 تشغيل فحص انتهاء الاشتراكات الآن</button>
</form>

<div class="info">
    <h4>📋 كيفية الاستخدام:</h4>
    <ol>
        <li>اضغط على زر "تشغيل فحص انتهاء الاشتراكات الآن"</li>
        <li>ستظهر لك النتائج والمخرجات</li>
        <li>سيتم تحديث حالة الاشتراكات المنتهية تلقائياً</li>
        <li>يمكنك تكرار العملية عدة مرات للتأكد</li>
    </ol>
    
    <h4>⚙️ إعداد المهمة التلقائية:</h4>
    <p>لتشغيل هذا الفحص تلقائياً كل ساعة، أضف هذا السطر إلى crontab:</p>
    <code style="background: #f0f0f0; padding: 5px; border-radius: 3px;">
        0 * * * * /usr/bin/php <?php echo realpath(__DIR__ . '/../cron/check_expired_subscriptions.php'); ?>
    </code>
</div>

<p>
    <a href="test_subscription_expiry.php" class="btn btn-primary">اختبار انتهاء الاشتراك</a>
    <a href="subscription_logs.php" class="btn btn-success">عرض السجلات</a>
    <a href="subscription_plans.php" class="btn btn-warning">إدارة الخطط</a>
</p>
