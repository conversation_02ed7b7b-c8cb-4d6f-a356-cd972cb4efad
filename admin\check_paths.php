<?php
/**
 * <PERSON><PERSON><PERSON> to check and verify all file paths in admin directory
 */

echo "=== Admin Panel Path Checker ===\n\n";

// Check CSS files
echo "1. Checking CSS Files:\n";
$cssFiles = [
    'css/admin-modern.css',
    'css/admin-fixes.css',
    'css/admin-styles.css',
    'css/enhanced-admin-styles.css',
    'css/course_content.css'
];

foreach ($cssFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "   ✓ $file (exists)\n";
    } else {
        echo "   ✗ $file (missing)\n";
    }
}

// Check JS files
echo "\n2. Checking JavaScript Files:\n";
$jsFiles = [
    'js/admin-modern.js',
    'js/admin-dashboard.js',
    'js/charts.js',
    'js/education-selector.js'
];

foreach ($jsFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "   ✓ $file (exists)\n";
    } else {
        echo "   ✗ $file (missing)\n";
    }
}

// Check API files
echo "\n3. Checking API Files:\n";
$apiFiles = [
    'api/get_notifications.php',
    'api/mark_notification_read.php',
    'api/mark_all_notifications_read.php',
    'api/search.php',
    'api/add_user.php',
    'api/send_notification.php'
];

foreach ($apiFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "   ✓ $file (exists)\n";
    } else {
        echo "   ✗ $file (missing)\n";
    }
}

// Check include files
echo "\n4. Checking Include Files:\n";
$includeFiles = [
    'includes/header.php',
    'includes/sidebar.php'
];

foreach ($includeFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "   ✓ $file (exists)\n";
    } else {
        echo "   ✗ $file (missing)\n";
    }
}

// Check external dependencies
echo "\n5. Checking External Dependencies:\n";
$externalFiles = [
    '../css/notifications.css',
    '../js/notifications.js',
    '../img/logo.png',
    '../config/config.php',
    '../includes/database.php'
];

foreach ($externalFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "   ✓ $file (exists)\n";
    } else {
        echo "   ✗ $file (missing)\n";
    }
}

// Check main admin files
echo "\n6. Checking Main Admin Files:\n";
$mainFiles = [
    'index.php',
    'login.php',
    'users.php',
    'courses.php',
    'news.php',
    'exams.php'
];

foreach ($mainFiles as $file) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        echo "   ✓ $file (exists)\n";
        
        // Check if file uses modern design
        $content = file_get_contents($path);
        if (strpos($content, 'admin-modern.css') !== false) {
            echo "     → Uses modern design ✓\n";
        } else {
            echo "     → Uses old design ⚠\n";
        }
        
        if (strpos($content, 'includes/header.php') !== false) {
            echo "     → Includes header ✓\n";
        } else {
            echo "     → Missing header ⚠\n";
        }
    } else {
        echo "   ✗ $file (missing)\n";
    }
}

// Summary
echo "\n=== Summary ===\n";
echo "Path check completed. Please review any missing files or warnings above.\n";
echo "Files marked with ✗ are missing and need to be created.\n";
echo "Files marked with ⚠ may need updates to use the modern design.\n";

// Recommendations
echo "\n=== Recommendations ===\n";
echo "1. Ensure all CSS and JS files are present\n";
echo "2. Update any files still using old design\n";
echo "3. Test all pages to ensure proper functionality\n";
echo "4. Check browser console for any 404 errors\n";
?>
