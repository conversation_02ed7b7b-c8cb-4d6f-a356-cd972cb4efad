<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$db = Database::getInstance()->getConnection();

try {
    // Get recent lessons with subject info
    $stmt = $db->prepare("
        SELECT l.id, l.title, l.lesson_number, l.is_free,
               cs.name as subject_name, cs.color as subject_color, cs.icon as subject_icon
        FROM lessons l
        JOIN curriculum_subjects cs ON l.subject_id = cs.id
        WHERE l.is_active = 1
        ORDER BY l.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $lessons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'lessons' => $lessons
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
