<?php

if (!class_exists('NotificationManager')) {
class NotificationManager {
    private $db;
    
    public function __construct() {
        try {
            $this->db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (Exception $e) {
            error_log("NotificationManager DB Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create a notification for a user
     */
    public function createNotification($user_id, $type, $title, $message, $metadata = null, $expires_hours = 24) {
        try {
            $expires_at = date('Y-m-d H:i:s', time() + ($expires_hours * 3600));
            
            $stmt = $this->db->prepare("INSERT INTO user_notifications 
                                       (user_id, notification_type, title, message, expires_at, metadata) 
                                       VALUES (?, ?, ?, ?, ?, ?)");
            
            $result = $stmt->execute([
                $user_id,
                $type,
                $title,
                $message,
                $expires_at,
                $metadata ? json_encode($metadata, JSON_UNESCAPED_UNICODE) : null
            ]);
            
            if ($result) {
                return $this->db->lastInsertId();
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Create notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get unread notifications for a user
     */
    public function getUserNotifications($user_id, $limit = 10) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM user_notifications 
                                       WHERE user_id = ? 
                                       AND is_dismissed = FALSE 
                                       AND (expires_at IS NULL OR expires_at > NOW())
                                       ORDER BY created_at DESC 
                                       LIMIT ?");
            $stmt->execute([$user_id, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get notifications error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if user should see subscription expired notification
     */
    public function shouldShowSubscriptionExpiredNotification($user_id) {
        try {
            // Check if user has expired subscription
            $stmt = $this->db->prepare("SELECT subscription_status, subscription_end_date 
                                       FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user || $user['subscription_status'] !== 'expired') {
                return false;
            }
            
            // Check if notification was already shown recently
            $stmt = $this->db->prepare("SELECT last_shown, show_count, max_show_count 
                                       FROM notification_settings 
                                       WHERE user_id = ? AND notification_type = 'subscription_expired'");
            $stmt->execute([$user_id]);
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($settings) {
                // If already shown max times, don't show again
                if ($settings['show_count'] >= $settings['max_show_count']) {
                    return false;
                }
                
                // If shown recently (within 1 hour), don't show again
                if ($settings['last_shown'] && 
                    strtotime($settings['last_shown']) > (time() - 3600)) {
                    return false;
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Should show notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark notification as shown
     */
    public function markNotificationShown($user_id, $notification_type) {
        try {
            // Insert or update notification settings
            $stmt = $this->db->prepare("INSERT INTO notification_settings 
                                       (user_id, notification_type, last_shown, show_count) 
                                       VALUES (?, ?, NOW(), 1)
                                       ON DUPLICATE KEY UPDATE 
                                       last_shown = NOW(), 
                                       show_count = show_count + 1");
            
            return $stmt->execute([$user_id, $notification_type]);
        } catch (Exception $e) {
            error_log("Mark notification shown error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Dismiss a notification
     */
    public function dismissNotification($notification_id, $user_id) {
        try {
            $stmt = $this->db->prepare("UPDATE user_notifications 
                                       SET is_dismissed = TRUE 
                                       WHERE id = ? AND user_id = ?");
            return $stmt->execute([$notification_id, $user_id]);
        } catch (Exception $e) {
            error_log("Dismiss notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create subscription expired notification
     */
    public function createSubscriptionExpiredNotification($user_id) {
        $title = "انتهى اشتراكك!";
        $message = "لقد انتهت صلاحية اشتراكك. قم بتجديد الاشتراك للاستمرار في الوصول لجميع المحتويات التعليمية.";
        
        $metadata = [
            'action_url' => '/page/subscriptions.php',
            'action_text' => 'تجديد الاشتراك',
            'priority' => 'high'
        ];
        
        return $this->createNotification($user_id, 'subscription_expired', $title, $message, $metadata, 72); // 3 days
    }
    
    /**
     * Get subscription expired notification for display
     */
    public function getSubscriptionExpiredNotification($user_id) {
        if (!$this->shouldShowSubscriptionExpiredNotification($user_id)) {
            return null;
        }
        
        // Mark as shown
        $this->markNotificationShown($user_id, 'subscription_expired');
        
        return [
            'type' => 'subscription_expired',
            'title' => 'انتهى اشتراكك! 🔔',
            'message' => 'لقد انتهت صلاحية اشتراكك. قم بتجديد الاشتراك للاستمرار في الوصول لجميع المحتويات التعليمية المتميزة.',
            'action_url' => SITE_URL . '/page/subscriptions.php',
            'action_text' => '🔄 تجديد الاشتراك الآن',
            'priority' => 'high',
            'icon' => '⚠️',
            'color' => '#dc3545'
        ];
    }
    
    /**
     * Clean up old notifications
     */
    public function cleanupOldNotifications() {
        try {
            // Delete expired notifications
            $stmt = $this->db->prepare("DELETE FROM user_notifications 
                                       WHERE expires_at IS NOT NULL AND expires_at < NOW()");
            $stmt->execute();
            
            // Delete old dismissed notifications (older than 30 days)
            $stmt = $this->db->prepare("DELETE FROM user_notifications 
                                       WHERE is_dismissed = TRUE 
                                       AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
            $stmt->execute();
            
            return true;
        } catch (Exception $e) {
            error_log("Cleanup notifications error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reset notification settings for a user (for testing)
     */
    public function resetUserNotificationSettings($user_id, $notification_type = null) {
        try {
            if ($notification_type) {
                $stmt = $this->db->prepare("DELETE FROM notification_settings 
                                           WHERE user_id = ? AND notification_type = ?");
                $stmt->execute([$user_id, $notification_type]);
            } else {
                $stmt = $this->db->prepare("DELETE FROM notification_settings WHERE user_id = ?");
                $stmt->execute([$user_id]);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Reset notification settings error: " . $e->getMessage());
            return false;
        }
    }
}
} // End of class_exists check
?>
