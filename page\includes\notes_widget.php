<?php
/**
 * Notes Widget Content
 * Returns HTML content for notes widget
 */

require_once __DIR__ . '/../../includes/database.php';

if (!isset($_SESSION['user_id'])) {
    echo '<div class="error">غير مصرح</div>';
    exit;
}

$userManager = new UserManager();
$notes = $userManager->getUserNotes($_SESSION['user_id']);

// Separate pinned and regular notes
$pinnedNotes = array_filter($notes, function($note) {
    return $note['is_pinned'];
});

$regularNotes = array_filter($notes, function($note) {
    return !$note['is_pinned'];
});
?>

<?php if (!empty($pinnedNotes)): ?>
<div class="notes-section pinned-section">
    <h4>الملاحظات المثبتة</h4>
    <div class="notes-grid">
        <?php foreach ($pinnedNotes as $note): ?>
            <div class="note-card pinned" data-id="<?php echo $note['id']; ?>">
                <div class="note-header">
                    <h5><?php echo htmlspecialchars($note['title']); ?></h5>
                    <div class="note-actions">
                        <button class="pin-btn active" onclick="toggleNotePin(<?php echo $note['id']; ?>)" title="إلغاء التثبيت">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 4v4l3 3-2 2-3-3-4 4-2-2 4-4-3-3 2-2 3 3V4h2z"/>
                            </svg>
                        </button>
                        <button class="edit-btn" onclick="editNote(<?php echo $note['id']; ?>)" title="تعديل">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <button class="delete-btn" onclick="deleteNote(<?php echo $note['id']; ?>)" title="حذف">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="note-content">
                    <p><?php echo nl2br(htmlspecialchars($note['content'])); ?></p>
                </div>
                <div class="note-footer">
                    <small><?php echo date('Y-m-d H:i', strtotime($note['created_at'])); ?></small>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<div class="notes-section regular-section">
    <h4>الملاحظات</h4>
    <?php if (empty($regularNotes)): ?>
        <div class="empty-state">
            <p>لا توجد ملاحظات</p>
        </div>
    <?php else: ?>
        <div class="notes-grid">
            <?php foreach ($regularNotes as $note): ?>
                <div class="note-card" data-id="<?php echo $note['id']; ?>">
                    <div class="note-header">
                        <h5><?php echo htmlspecialchars($note['title']); ?></h5>
                        <div class="note-actions">
                            <button class="pin-btn" onclick="toggleNotePin(<?php echo $note['id']; ?>)" title="تثبيت">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M16 4v4l3 3-2 2-3-3-4 4-2-2 4-4-3-3 2-2 3 3V4h2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="edit-btn" onclick="editNote(<?php echo $note['id']; ?>)" title="تعديل">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="delete-btn" onclick="deleteNote(<?php echo $note['id']; ?>)" title="حذف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="note-content">
                        <p><?php echo nl2br(htmlspecialchars($note['content'])); ?></p>
                    </div>
                    <div class="note-footer">
                        <small><?php echo date('Y-m-d H:i', strtotime($note['created_at'])); ?></small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<div class="add-note-section">
    <button class="add-note-btn" onclick="openNoteModal()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        إضافة ملاحظة جديدة
    </button>
</div>
