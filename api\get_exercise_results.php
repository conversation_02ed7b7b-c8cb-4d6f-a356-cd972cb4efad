<?php
require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    $userId = $_SESSION['user_id'];
    
    $weekNumber = $_GET['week_number'] ?? null;
    $courseId = $_GET['course_id'] ?? null;
    
    if (!$weekNumber || !$courseId) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }
    
    // Get course passing grade
    $stmt = $db->prepare("SELECT passing_grade FROM courses WHERE id = ?");
    $stmt->execute([$courseId]);
    $rawPassingGrade = $stmt->fetchColumn();
    $passingGrade = floatval($rawPassingGrade ?: 60);
    
    // Get all exercises for this week
    $stmt = $db->prepare("
        SELECT * FROM course_exercises 
        WHERE course_id = ? AND week_number = ? 
        ORDER BY id ASC
    ");
    $stmt->execute([$courseId, $weekNumber]);
    $exercises = $stmt->fetchAll();
    
    if (empty($exercises)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد تمارين لهذا الأسبوع']);
        exit;
    }
    
    // Get the latest attempt for each exercise
    $exerciseIds = array_column($exercises, 'id');
    $placeholders = str_repeat('?,', count($exerciseIds) - 1) . '?';
    
    $stmt = $db->prepare("
        SELECT uea.*, ce.question_text, ce.correct_answer, ce.question_type, ce.explanation
        FROM user_exercise_attempts uea
        JOIN course_exercises ce ON uea.exercise_id = ce.id
        WHERE uea.user_id = ? AND uea.exercise_id IN ($placeholders)
        AND uea.attempt_number = (
            SELECT MAX(attempt_number) 
            FROM user_exercise_attempts uea2 
            WHERE uea2.user_id = uea.user_id 
            AND uea2.exercise_id = uea.exercise_id
        )
        ORDER BY ce.id ASC
    ");
    $stmt->execute(array_merge([$userId], $exerciseIds));
    $attempts = $stmt->fetchAll();
    
    if (empty($attempts)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد محاولات سابقة لهذا الأسبوع']);
        exit;
    }
    
    // Process results
    $results = [];
    $totalQuestions = count($exercises);
    $correctAnswers = 0;
    
    foreach ($exercises as $index => $exercise) {
        // Find the attempt for this exercise
        $attempt = null;
        foreach ($attempts as $att) {
            if ($att['exercise_id'] == $exercise['id']) {
                $attempt = $att;
                break;
            }
        }
        
        if (!$attempt) {
            // No attempt found for this exercise
            $results[] = [
                'question_number' => $index + 1,
                'question_text' => $exercise['question_text'],
                'user_answer' => 'لم يتم الإجابة',
                'correct_answer' => $exercise['correct_answer'],
                'is_correct' => false,
                'explanation' => $exercise['explanation'] ?? '',
                'question_type' => $exercise['question_type']
            ];
            continue;
        }
        
        if ($attempt['is_correct']) {
            $correctAnswers++;
        }
        
        // Format answers for display
        $displayUserAnswer = $attempt['user_answer'];
        $displayCorrectAnswer = $exercise['correct_answer'];
        
        if ($exercise['question_type'] === 'true_false') {
            // Format true/false answers
            if (in_array(strtolower($attempt['user_answer']), ['true', '1'])) {
                $displayUserAnswer = 'صح';
            } elseif (in_array(strtolower($attempt['user_answer']), ['false', '0'])) {
                $displayUserAnswer = 'خطأ';
            }
            
            if (in_array(strtolower($exercise['correct_answer']), ['true', '1'])) {
                $displayCorrectAnswer = 'صح';
            } elseif (in_array(strtolower($exercise['correct_answer']), ['false', '0'])) {
                $displayCorrectAnswer = 'خطأ';
            }
        }
        
        $results[] = [
            'question_number' => $index + 1,
            'question_text' => $exercise['question_text'],
            'user_answer' => $displayUserAnswer,
            'correct_answer' => $displayCorrectAnswer,
            'is_correct' => (bool)$attempt['is_correct'],
            'explanation' => $exercise['explanation'] ?? '',
            'question_type' => $exercise['question_type'],
            'attempt_date' => $attempt['created_at']
        ];
    }
    
    // Calculate score and passing status
    $score = round(($correctAnswers / $totalQuestions) * 100, 1);
    $scoreFloat = floatval($score);
    $passed = $scoreFloat >= $passingGrade;

    // Debug logging
    error_log("=== Get Exercise Results Debug ===");
    error_log("Course ID: $courseId, Week: $weekNumber");
    error_log("Passing Grade: $passingGrade%");
    error_log("Correct Answers: $correctAnswers");
    error_log("Total Questions: $totalQuestions");
    error_log("Calculated Score: $score%");
    error_log("Passed Status: " . ($passed ? 'Yes' : 'No'));
    error_log("==================================");
    
    // Get attempt number
    $attemptNumber = $attempts[0]['attempt_number'] ?? 1;
    
    echo json_encode([
        'success' => true,
        'results' => $results,
        'total_questions' => $totalQuestions,
        'correct_answers' => $correctAnswers,
        'score' => $score,
        'passed' => $passed,
        'passing_grade' => $passingGrade,
        'attempt_number' => $attemptNumber,
        'week_number' => $weekNumber,
        'course_id' => $courseId
    ]);
    
} catch (Exception $e) {
    error_log("Error getting exercise results: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
