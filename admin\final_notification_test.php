<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار الإشعارات النهائي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .success { color: green; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; margin: 15px 0; }
        .error { color: red; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 15px 0; }
        .info { color: blue; padding: 15px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; margin: 15px 0; }
        .warning { color: orange; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; margin: 15px 0; }
        .btn { padding: 12px 25px; margin: 8px; border: none; border-radius: 8px; cursor: pointer; color: white; text-decoration: none; display: inline-block; font-weight: bold; }
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: black; }
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 10px; border-left: 5px solid #007bff; }
        .step h3 { margin-top: 0; color: #007bff; }
    </style>
</head>
<body>";

echo "<h1>🧪 اختبار نظام الإشعارات النهائي</h1>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // Get first user
    $stmt = $db->query("SELECT id, username FROM users ORDER BY id LIMIT 1");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<div class='error'>❌ لا يوجد مستخدمين في النظام</div>";
        exit;
    }
    
    $user_id = $user['id'];
    $username = $user['username'];
    
    echo "<div class='info'>👤 المستخدم للاختبار: <strong>$username</strong> (ID: $user_id)</div>";
    
    // Handle actions
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'setup':
                // 1. Create notification tables
                try {
                    $createSettingsSQL = "CREATE TABLE IF NOT EXISTS notification_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        notification_type VARCHAR(50) NOT NULL,
                        is_enabled BOOLEAN DEFAULT TRUE,
                        last_shown TIMESTAMP NULL,
                        show_count INT DEFAULT 0,
                        max_show_count INT DEFAULT 1,
                        UNIQUE KEY unique_user_type (user_id, notification_type)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                    
                    $db->exec($createSettingsSQL);
                    echo "<div class='success'>✅ تم إنشاء جدول notification_settings</div>";
                } catch (Exception $e) {
                    echo "<div class='info'>ℹ️ جدول notification_settings موجود بالفعل</div>";
                }
                
                // 2. Expire user subscription
                $stmt = $db->prepare("UPDATE users SET subscription_status = 'expired', current_plan_id = NULL WHERE id = ?");
                $result = $stmt->execute([$user_id]);
                
                if ($result) {
                    echo "<div class='success'>✅ تم تعيين اشتراك المستخدم كمنتهي</div>";
                } else {
                    echo "<div class='error'>❌ فشل في تعديل حالة الاشتراك</div>";
                }
                
                // 3. Reset notification settings
                $stmt = $db->prepare("DELETE FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
                $stmt->execute([$user_id]);
                echo "<div class='success'>✅ تم إعادة تعيين إعدادات الإشعارات</div>";
                
                echo "<div class='warning'>🎉 <strong>تم الإعداد بنجاح!</strong> الآن افتح الداشبورد لرؤية الإشعار</div>";
                break;
                
            case 'reset':
                $stmt = $db->prepare("DELETE FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
                $stmt->execute([$user_id]);
                echo "<div class='success'>✅ تم إعادة تعيين الإشعارات - يمكن إظهار الإشعار مرة أخرى</div>";
                break;
                
            case 'activate':
                $stmt = $db->prepare("UPDATE users SET subscription_status = 'active', current_plan_id = 1 WHERE id = ?");
                $result = $stmt->execute([$user_id]);
                echo "<div class='success'>✅ تم تفعيل اشتراك المستخدم</div>";
                break;
        }
    }
    
    // Show current status
    $stmt = $db->prepare("SELECT subscription_status, subscription_end_date FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $userStatus = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $statusText = $userStatus['subscription_status'] ?: 'غير محدد';
    $statusColor = '';
    switch ($statusText) {
        case 'active': $statusColor = 'color: green; font-weight: bold;'; break;
        case 'expired': $statusColor = 'color: red; font-weight: bold;'; break;
        case 'cancelled': $statusColor = 'color: orange; font-weight: bold;'; break;
        default: $statusColor = 'color: gray;'; break;
    }
    
    echo "<div class='info'>📊 حالة اشتراك المستخدم: <span style='$statusColor'>$statusText</span></div>";
    
    // Check notification settings
    try {
        $stmt = $db->prepare("SELECT * FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
        $stmt->execute([$user_id]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings) {
            $lastShown = $settings['last_shown'] ? date('Y-m-d H:i:s', strtotime($settings['last_shown'])) : 'لم يُعرض';
            echo "<div class='info'>⚙️ إعدادات الإشعار: عُرض <strong>{$settings['show_count']}</strong> مرة، آخر عرض: <strong>$lastShown</strong></div>";
            
            $shouldShow = ($settings['show_count'] < $settings['max_show_count']);
        } else {
            echo "<div class='info'>⚙️ لا توجد إعدادات إشعار - <strong>سيظهر الإشعار</strong></div>";
            $shouldShow = true;
        }
    } catch (Exception $e) {
        echo "<div class='warning'>⚠️ جدول الإعدادات غير موجود - سيتم إنشاؤه</div>";
        $shouldShow = false;
    }
    
    // Final check
    $canShow = ($userStatus['subscription_status'] === 'expired') && $shouldShow;
    $showText = $canShow ? '<span style="color: green; font-weight: bold;">نعم ✅</span>' : '<span style="color: red; font-weight: bold;">لا ❌</span>';
    echo "<div class='info'>🔔 <strong>يجب إظهار الإشعار:</strong> $showText</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "
<div class='step'>
    <h3>📋 خطوات الاختبار:</h3>
    
    <div style='margin: 20px 0;'>
        <h4>1️⃣ إعداد الاختبار:</h4>
        <form method='POST' style='display: inline;'>
            <button type='submit' name='action' value='setup' class='btn btn-primary'>إعداد الاختبار (إنشاء جداول + انتهاء اشتراك)</button>
        </form>
    </div>
    
    <div style='margin: 20px 0;'>
        <h4>2️⃣ فتح الداشبورد:</h4>
        <a href='../page/dashboard.php' class='btn btn-success' target='_blank'>🏠 فتح الداشبورد</a>
        <p style='margin: 10px 0; color: #666;'>يجب أن يظهر إشعار انتهاء الاشتراك في نافذة منبثقة</p>
    </div>
    
    <div style='margin: 20px 0;'>
        <h4>3️⃣ اختبار الصفحات الأخرى:</h4>
        <a href='../page/curriculum.php' class='btn btn-warning' target='_blank'>📚 صفحة المنهج</a>
        <a href='../page/subscriptions.php' class='btn btn-warning' target='_blank'>💳 صفحة الاشتراكات</a>
    </div>
    
    <div style='margin: 20px 0;'>
        <h4>4️⃣ إعادة تعيين الإشعارات:</h4>
        <form method='POST' style='display: inline;'>
            <button type='submit' name='action' value='reset' class='btn btn-danger'>🔄 إعادة تعيين (لإظهار الإشعار مرة أخرى)</button>
        </form>
    </div>
    
    <div style='margin: 20px 0;'>
        <h4>5️⃣ تفعيل الاشتراك (لإخفاء الإشعار):</h4>
        <form method='POST' style='display: inline;'>
            <button type='submit' name='action' value='activate' class='btn btn-success'>✅ تفعيل الاشتراك</button>
        </form>
    </div>
</div>

<div class='info'>
    <h4>📝 ملاحظات مهمة:</h4>
    <ul>
        <li><strong>الإشعار يظهر مرة واحدة فقط</strong> لكل مستخدم</li>
        <li>بعد إغلاق الإشعار، <strong>لن يظهر مرة أخرى</strong> حتى إعادة تعيين الإعدادات</li>
        <li>الإشعار يظهر في: <strong>الداشبورد، المنهج، الاشتراكات</strong></li>
        <li>يمكن إغلاق الإشعار بـ: <strong>النقر على X، خارج النافذة، مفتاح Escape</strong></li>
        <li>الإشعار يظهر فقط للمستخدمين ذوي الاشتراك <strong>المنتهي</strong></li>
    </ul>
</div>

<div class='warning'>
    <h4>🔧 استكشاف الأخطاء:</h4>
    <ul>
        <li>إذا لم يظهر الإشعار، تأكد من أن حالة الاشتراك <strong>'expired'</strong></li>
        <li>تأكد من وجود جدول <strong>notification_settings</strong></li>
        <li>تحقق من console المتصفح للأخطاء</li>
        <li>تأكد من أن المستخدم مسجل دخول</li>
    </ul>
</div>
";

echo "</body></html>";
?>
