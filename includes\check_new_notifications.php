<?php
/**
 * Check for New Notifications
 * Returns new notifications since last check
 */

require_once __DIR__ . '/../config/config.php';
require_once 'database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

try {
    $userManager = new UserManager();
    $userId = $_SESSION['user_id'];
    
    // Get last check timestamp from request
    $lastCheck = $_GET['last_check'] ?? 0;
    
    // Get new notifications since last check
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("
        SELECT id, title, message, type, is_read, created_at, UNIX_TIMESTAMP(created_at) as timestamp
        FROM notifications
        WHERE (user_id = ? OR is_global = 1) 
        AND UNIX_TIMESTAMP(created_at) > ?
        OR<PERSON>R BY created_at DESC
        LIMIT 10
    ");
    
    $stmt->execute([$userId, $lastCheck]);
    $newNotifications = $stmt->fetchAll();
    
    // Get current unread count
    $unreadCount = $userManager->getUnreadNotificationCount($userId);
    
    // Format notifications
    $formattedNotifications = array_map(function($notification) {
        return [
            'id' => $notification['id'],
            'title' => $notification['title'],
            'message' => $notification['message'],
            'type' => $notification['type'],
            'is_read' => (bool)$notification['is_read'],
            'created_at' => date('Y-m-d H:i', strtotime($notification['created_at'])),
            'timestamp' => $notification['timestamp']
        ];
    }, $newNotifications);
    
    echo json_encode([
        'success' => true,
        'has_new' => !empty($newNotifications),
        'notifications' => $formattedNotifications,
        'unread_count' => $unreadCount,
        'current_timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم'
    ]);
    error_log("Check notifications error: " . $e->getMessage());
}
?>
