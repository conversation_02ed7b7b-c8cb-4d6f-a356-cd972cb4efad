<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$adminManager = new AdminManager();
$userManager = new UserManager();
$notificationManager = new NotificationManager();

// Handle actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'delete_notification':
            $notificationId = (int)$_POST['notification_id'];
            if ($notificationManager->deleteNotification($notificationId)) {
                $message = 'تم حذف الإشعار بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في حذف الإشعار';
                $messageType = 'error';
            }
            break;
            
        case 'mark_as_read':
            $notificationId = (int)$_POST['notification_id'];
            if ($notificationManager->markAsRead($notificationId)) {
                $message = 'تم تحديد الإشعار كمقروء';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث الإشعار';
                $messageType = 'error';
            }
            break;
            
        case 'mark_as_unread':
            $notificationId = (int)$_POST['notification_id'];
            if ($notificationManager->markAsUnread($notificationId)) {
                $message = 'تم تحديد الإشعار كغير مقروء';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث الإشعار';
                $messageType = 'error';
            }
            break;
            
        case 'bulk_delete':
            $notificationIds = $_POST['notification_ids'] ?? [];
            if (!empty($notificationIds)) {
                $deletedCount = $notificationManager->bulkDeleteNotifications($notificationIds);
                $message = "تم حذف {$deletedCount} إشعار";
                $messageType = 'success';
            }
            break;
    }
}

// Get filters
$filterUser = $_GET['user_id'] ?? '';
$filterType = $_GET['type'] ?? '';
$filterStatus = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$whereConditions = [];
$params = [];

if ($filterUser) {
    $whereConditions[] = "n.user_id = ?";
    $params[] = $filterUser;
}

if ($filterType) {
    $whereConditions[] = "n.type = ?";
    $params[] = $filterType;
}

if ($filterStatus === 'read') {
    $whereConditions[] = "n.is_read = 1";
} elseif ($filterStatus === 'unread') {
    $whereConditions[] = "n.is_read = 0";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get notifications
$db = Database::getInstance()->getConnection();
$stmt = $db->prepare("
    SELECT n.*, u.username, u.first_name, u.second_name, a.username as admin_username
    FROM notifications n
    LEFT JOIN users u ON n.user_id = u.id
    LEFT JOIN admins a ON n.admin_id = a.id
    {$whereClause}
    ORDER BY n.created_at DESC
    LIMIT {$limit} OFFSET {$offset}
");
$stmt->execute($params);
$notifications = $stmt->fetchAll();

// Get total count
$countStmt = $db->prepare("
    SELECT COUNT(*) as total
    FROM notifications n
    LEFT JOIN users u ON n.user_id = u.id
    {$whereClause}
");
$countStmt->execute($params);
$totalNotifications = $countStmt->fetch()['total'];
$totalPages = ceil($totalNotifications / $limit);

// Get statistics
$statsStmt = $db->prepare("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
        SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success,
        SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as error,
        SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warning,
        SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info
    FROM notifications
");
$statsStmt->execute();
$stats = $statsStmt->fetch();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإشعارات - لوحة التحكم</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">

    <style>
        .notifications-management {
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid rgba(70, 130, 180, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4682B4;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.1);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .notifications-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .bulk-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .notification-row {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: grid;
            grid-template-columns: 40px 1fr 120px 100px 150px 120px;
            gap: 15px;
            align-items: center;
        }
        
        .notification-row:hover {
            background: #f8f9fa;
        }
        
        .notification-row.unread {
            background: linear-gradient(135deg, #fff5f5 0%, #fef5e7 100%);
            border-left: 4px solid #4682B4;
        }
        
        .notification-content {
            min-width: 0;
        }
        
        .notification-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .notification-message {
            color: #666;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .notification-user {
            font-size: 13px;
            color: #666;
        }
        
        .notification-type {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }
        
        .type-success { background: #d4edda; color: #155724; }
        .type-error { background: #f8d7da; color: #721c24; }
        .type-warning { background: #fff3cd; color: #856404; }
        .type-info { background: #d1ecf1; color: #0c5460; }
        
        .notification-actions {
            display: flex;
            gap: 5px;
        }
        
        .action-btn {
            padding: 5px 8px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .btn-read { background: #28a745; color: white; }
        .btn-unread { background: #6c757d; color: white; }
        .btn-delete { background: #dc3545; color: white; }
        
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #4682B4;
        }
        
        .pagination .current {
            background: #4682B4;
            color: white;
        }
        
        @media (max-width: 768px) {
            .notification-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="notifications-management">
                <h1>إدارة الإشعارات</h1>
                
                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">إجمالي الإشعارات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['unread']; ?></div>
                        <div class="stat-label">غير مقروءة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['success']; ?></div>
                        <div class="stat-label">نجاح</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['error']; ?></div>
                        <div class="stat-label">خطأ</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['warning']; ?></div>
                        <div class="stat-label">تحذير</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['info']; ?></div>
                        <div class="stat-label">معلومات</div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div style="margin-bottom: 20px;">
                    <a href="send_notifications.php" class="btn btn-primary" onclick="openModal('addModal')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إرسال إشعار جديد
                    </a>
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <h3>تصفية الإشعارات</h3>
                    <form method="GET" class="filters-grid">
                        <div class="form-group">
                            <label>المستخدم</label>
                            <select name="user_id" class="form-control">
                                <option value="">جميع المستخدمين</option>
                                <?php
                                $usersStmt = $db->prepare("SELECT DISTINCT u.id, u.username, u.first_name, u.second_name FROM users u INNER JOIN notifications n ON u.id = n.user_id ORDER BY u.first_name");
                                $usersStmt->execute();
                                $users = $usersStmt->fetchAll();
                                foreach ($users as $user) {
                                    $selected = $filterUser == $user['id'] ? 'selected' : '';
                                    $displayName = trim($user['first_name'] . ' ' . $user['second_name']) ?: $user['username'];
                                    echo "<option value='{$user['id']}' {$selected}>{$displayName} ({$user['username']})</option>";
                                }
                                ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>نوع الإشعار</label>
                            <select name="type" class="form-control">
                                <option value="">جميع الأنواع</option>
                                <option value="success" <?php echo $filterType === 'success' ? 'selected' : ''; ?>>نجاح</option>
                                <option value="error" <?php echo $filterType === 'error' ? 'selected' : ''; ?>>خطأ</option>
                                <option value="warning" <?php echo $filterType === 'warning' ? 'selected' : ''; ?>>تحذير</option>
                                <option value="info" <?php echo $filterType === 'info' ? 'selected' : ''; ?>>معلومات</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>حالة القراءة</label>
                            <select name="status" class="form-control">
                                <option value="">جميع الحالات</option>
                                <option value="unread" <?php echo $filterStatus === 'unread' ? 'selected' : ''; ?>>غير مقروء</option>
                                <option value="read" <?php echo $filterStatus === 'read' ? 'selected' : ''; ?>>مقروء</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">تصفية</button>
                            <a href="manage_notifications.php" class="btn btn-secondary">إعادة تعيين</a>
                        </div>
                    </form>
                </div>

                <!-- Notifications Table -->
                <div class="notifications-table">
                    <div class="table-header">
                        <h3>الإشعارات (<?php echo $totalNotifications; ?>)</h3>
                        <div class="bulk-actions">
                            <button type="button" id="selectAll" class="btn btn-secondary">تحديد الكل</button>
                            <button type="button" id="bulkDelete" class="btn btn-danger">حذف المحدد</button>
                        </div>
                    </div>

                    <?php if (empty($notifications)): ?>
                        <div style="padding: 40px; text-align: center; color: #666;">
                            لا توجد إشعارات تطابق المعايير المحددة
                        </div>
                    <?php else: ?>
                        <form id="bulkForm" method="POST">
                            <input type="hidden" name="action" value="bulk_delete">

                            <!-- Table Header -->
                            <div class="notification-row" style="background: #f8f9fa; font-weight: 600;">
                                <div></div>
                                <div>المحتوى</div>
                                <div>المستخدم</div>
                                <div>النوع</div>
                                <div>التاريخ</div>
                                <div>الإجراءات</div>
                            </div>

                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-row <?php echo !$notification['is_read'] ? 'unread' : ''; ?>">
                                    <div>
                                        <input type="checkbox" name="notification_ids[]" value="<?php echo $notification['id']; ?>" class="notification-checkbox">
                                    </div>

                                    <div class="notification-content">
                                        <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                    </div>

                                    <div class="notification-user">
                                        <?php
                                        $displayName = trim($notification['first_name'] . ' ' . $notification['second_name']) ?: $notification['username'];
                                        echo htmlspecialchars($displayName);
                                        ?>
                                        <br><small><?php echo htmlspecialchars($notification['username']); ?></small>
                                    </div>

                                    <div>
                                        <span class="notification-type type-<?php echo $notification['type']; ?>">
                                            <?php
                                            $typeLabels = [
                                                'success' => 'نجاح',
                                                'error' => 'خطأ',
                                                'warning' => 'تحذير',
                                                'info' => 'معلومات'
                                            ];
                                            echo $typeLabels[$notification['type']] ?? $notification['type'];
                                            ?>
                                        </span>
                                    </div>

                                    <div>
                                        <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                                        <br><small>بواسطة: <?php echo htmlspecialchars($notification['admin_username'] ?? 'النظام'); ?></small>
                                    </div>

                                    <div class="notification-actions">
                                        <?php if (!$notification['is_read']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_as_read">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="action-btn btn-read" title="تحديد كمقروء">✓</button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_as_unread">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="action-btn btn-unread" title="تحديد كغير مقروء">↻</button>
                                            </form>
                                        <?php endif; ?>

                                        <form method="POST" style="display: inline;" onsubmit="return confirm('هل تريد حذف هذا الإشعار؟')">
                                            <input type="hidden" name="action" value="delete_notification">
                                            <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                            <button type="submit" class="action-btn btn-delete" title="حذف">🗑</button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </form>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">السابق</a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">التالي</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="../js/notifications.js"></script>
    <script src="../js/custom-confirm.js"></script>
    <script>
        // Show success/error messages
        <?php if (!empty($message)): ?>
            document.addEventListener('DOMContentLoaded', function() {
                <?php if ($messageType === 'success'): ?>
                    notifications.success('<?php echo addslashes($message); ?>');
                <?php else: ?>
                    notifications.error('<?php echo addslashes($message); ?>');
                <?php endif; ?>
            });
        <?php endif; ?>

        // Select all functionality
        document.getElementById('selectAll').addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.notification-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });

            this.textContent = allChecked ? 'تحديد الكل' : 'إلغاء التحديد';
        });

        // Bulk delete functionality
        document.getElementById('bulkDelete').addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.notification-checkbox:checked');

            if (checkedBoxes.length === 0) {
                alert('يرجى تحديد إشعار واحد على الأقل للحذف');
                return;
            }

            if (confirm(`هل تريد حذف ${checkedBoxes.length} إشعار؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
                document.getElementById('bulkForm').submit();
            }
        });
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
