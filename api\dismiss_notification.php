<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/NotificationManager.php';

// Set content type to JSON
header('Content-Type: application/json; charset=utf-8');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول'], JSON_UNESCAPED_UNICODE);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $notification_type = $input['notification_type'] ?? '';
    
    if (empty($notification_type)) {
        echo json_encode(['success' => false, 'message' => 'نوع الإشعار مطلوب'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $notificationManager = new NotificationManager();
    $user_id = $_SESSION['user_id'];
    
    // Mark notification as shown (which prevents it from showing again)
    $result = $notificationManager->markNotificationShown($user_id, $notification_type);
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => 'تم إغلاق الإشعار بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'فشل في إغلاق الإشعار'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    error_log("Dismiss notification error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في إغلاق الإشعار'
    ], JSON_UNESCAPED_UNICODE);
}
?>
