<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $db->prepare("INSERT INTO curriculum_subjects (name, name_en, description, education_level, education_type, grade, specialization, icon, color, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['name_en'],
                        $_POST['description'],
                        $_POST['education_level'],
                        $_POST['education_type'],
                        $_POST['grade'],
                        $_POST['specialization'],
                        $_POST['icon'],
                        $_POST['color'],
                        $_POST['sort_order'],
                        $_SESSION['admin_id']
                    ]);
                    $message = 'تم إضافة القسم بنجاح';
                    break;
                    
                case 'edit':
                    $stmt = $db->prepare("UPDATE curriculum_subjects SET name = ?, name_en = ?, description = ?, education_level = ?, education_type = ?, grade = ?, specialization = ?, icon = ?, color = ?, sort_order = ? WHERE id = ?");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['name_en'],
                        $_POST['description'],
                        $_POST['education_level'],
                        $_POST['education_type'],
                        $_POST['grade'],
                        $_POST['specialization'],
                        $_POST['icon'],
                        $_POST['color'],
                        $_POST['sort_order'],
                        $_POST['id']
                    ]);
                    $message = 'تم تحديث القسم بنجاح';
                    break;
                    
                case 'delete':
                    $stmt = $db->prepare("DELETE FROM curriculum_subjects WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم حذف القسم بنجاح';
                    break;
                    
                case 'toggle_status':
                    $stmt = $db->prepare("UPDATE curriculum_subjects SET is_active = !is_active WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم تغيير حالة القسم بنجاح';
                    break;
            }
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// Get all subjects
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->query("SELECT * FROM curriculum_subjects ORDER BY education_level, education_type, grade, sort_order");
    $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $subjects = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام الدراسية - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="admin-container">
                <div class="page-header">
                    <h1>إدارة الأقسام الدراسية</h1>
                    <button class="btn btn-primary" onclick="showAddModal()">إضافة قسم جديد</button>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>

                <!-- Subjects Table -->
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>المرحلة</th>
                                <th>النوع</th>
                                <th>الصف</th>
                                <th>التخصص</th>
                                <th>الأيقونة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subjects as $subject): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($subject['name']); ?></td>
                                    <td><?php echo EDUCATION_LEVELS[$subject['education_level']] ?? $subject['education_level']; ?></td>
                                    <td><?php echo EDUCATION_TYPES[$subject['education_type']] ?? $subject['education_type']; ?></td>
                                    <td><?php echo $subject['grade']; ?></td>
                                    <td><?php echo $subject['specialization'] === 'all' ? 'الكل' : ($subject['specialization'] === 'scientific' ? 'علمي' : 'أدبي'); ?></td>
                                    <td><?php echo $subject['icon']; ?></td>
                                    <td>
                                        <span class="status-badge <?php echo $subject['is_active'] ? 'active' : 'inactive'; ?>">
                                            <?php echo $subject['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editSubject(<?php echo htmlspecialchars(json_encode($subject)); ?>)">تعديل</button>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?php echo $subject['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">
                                                <?php echo $subject['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>
                                            </button>
                                        </form>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $subject['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">حذف</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div id="subjectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة قسم جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="subjectForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="id" id="subjectId">
                
                <div class="form-group">
                    <label for="name">اسم القسم</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="name_en">الاسم بالإنجليزية</label>
                    <input type="text" id="name_en" name="name_en">
                </div>
                
                <div class="form-group">
                    <label for="description">الوصف</label>
                    <textarea id="description" name="description"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="education_level">المرحلة التعليمية</label>
                    <select id="education_level" name="education_level" required onchange="updateGradeOptions()">
                        <option value="">اختر المرحلة</option>
                        <option value="primary">ابتدائي</option>
                        <option value="preparatory">إعدادي</option>
                        <option value="secondary">ثانوي</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="education_type">نوع التعليم</label>
                    <select id="education_type" name="education_type" required onchange="updateGradeOptions()">
                        <option value="">اختر النوع</option>
                        <option value="azhari">أزهري</option>
                        <option value="general">عام</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="grade">الصف</label>
                    <select id="grade" name="grade" required>
                        <option value="">اختر الصف</option>
                    </select>
                </div>
                
                <div class="form-group" id="specializationGroup" style="display: none;">
                    <label for="specialization">التخصص</label>
                    <select id="specialization" name="specialization">
                        <option value="all">الكل</option>
                        <option value="scientific">علمي</option>
                        <option value="literary">أدبي</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="icon">الأيقونة</label>
                    <input type="text" id="icon" name="icon" value="📚">
                </div>
                
                <div class="form-group">
                    <label for="color">اللون</label>
                    <input type="color" id="color" name="color" value="#4682B4">
                </div>
                
                <div class="form-group">
                    <label for="sort_order">ترتيب العرض</label>
                    <input type="number" id="sort_order" name="sort_order" value="0">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/education-selector.js"></script>
    <script>
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة قسم جديد';
            document.getElementById('formAction').value = 'add';
            document.getElementById('subjectForm').reset();
            document.getElementById('subjectModal').style.display = 'block';
        }

        function editSubject(subject) {
            document.getElementById('modalTitle').textContent = 'تعديل القسم';
            document.getElementById('formAction').value = 'edit';
            document.getElementById('subjectId').value = subject.id;
            document.getElementById('name').value = subject.name;
            document.getElementById('name_en').value = subject.name_en || '';
            document.getElementById('description').value = subject.description || '';
            document.getElementById('education_level').value = subject.education_level;
            document.getElementById('education_type').value = subject.education_type;
            document.getElementById('icon').value = subject.icon;
            document.getElementById('color').value = subject.color;
            document.getElementById('sort_order').value = subject.sort_order;
            
            // Update grade options and set value
            updateGradeOptions();
            setTimeout(() => {
                document.getElementById('grade').value = subject.grade;
                document.getElementById('specialization').value = subject.specialization;
            }, 100);
            
            document.getElementById('subjectModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('subjectModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('subjectModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>

    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
            padding: 0 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-actions {
            padding: 20px;
            border-top: 1px solid #ddd;
            text-align: left;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-badge.active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-badge.inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            margin: 0 2px;
        }
    </style>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
