<?php
/**
 * Cron Job: Check and expire subscriptions
 * Run this script every hour to check for expired subscriptions
 * 
 * To set up cron job, add this line to your crontab:
 * 0 * * * * /usr/bin/php /path/to/your/project/cron/check_expired_subscriptions.php
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/NotificationManager.php';

// Log file for cron job
$log_file = __DIR__ . '/logs/subscription_expiry.log';
$log_dir = dirname($log_file);

// Create logs directory if it doesn't exist
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

function writeLog($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running from command line
    if (php_sapi_name() === 'cli') {
        echo $log_message;
    }
}

try {
    writeLog("Starting subscription expiry check...");

    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Initialize notification manager
    $notificationManager = new NotificationManager();
    
    $current_time = date('Y-m-d H:i:s');
    writeLog("Current time: $current_time");
    
    // Find users with expired subscriptions
    $stmt = $db->prepare("SELECT id, username, email, subscription_end_date, current_plan_id,
                         sp.name as plan_name
                         FROM users u
                         LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                         WHERE u.subscription_status = 'active' 
                         AND u.subscription_end_date <= ?");
    $stmt->execute([$current_time]);
    $expired_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $expired_count = count($expired_users);
    writeLog("Found $expired_count expired subscriptions");
    
    if ($expired_count > 0) {
        // Begin transaction
        $db->beginTransaction();
        
        try {
            // Update expired subscriptions
            $stmt = $db->prepare("UPDATE users SET 
                                 subscription_status = 'expired',
                                 current_plan_id = NULL
                                 WHERE subscription_status = 'active' 
                                 AND subscription_end_date <= ?");
            $result = $stmt->execute([$current_time]);
            
            if ($result) {
                // Log each expired user and create notifications
                foreach ($expired_users as $user) {
                    writeLog("Expired subscription for user: {$user['username']} (ID: {$user['id']}) - Plan: {$user['plan_name']} - Expired: {$user['subscription_end_date']}");

                    // Create notification for expired subscription
                    $notificationManager->createSubscriptionExpiredNotification($user['id']);
                    writeLog("Created expiry notification for user: {$user['username']}");
                    
                    // Log to subscription history (create table if needed)
                    try {
                        $stmt = $db->prepare("INSERT INTO subscription_history 
                                             (user_id, plan_name, action, action_date, notes)
                                             VALUES (?, ?, 'expired', NOW(), ?)");
                        $stmt->execute([
                            $user['id'],
                            $user['plan_name'],
                            "Subscription expired automatically on $current_time"
                        ]);
                    } catch (Exception $e) {
                        // Create table if it doesn't exist
                        $createTableSQL = "CREATE TABLE IF NOT EXISTS subscription_history (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_id INT NOT NULL,
                            plan_name VARCHAR(100),
                            action ENUM('activated', 'expired', 'cancelled', 'renewed') NOT NULL,
                            action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            notes TEXT,
                            INDEX idx_user_id (user_id),
                            INDEX idx_action_date (action_date)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                        
                        $db->exec($createTableSQL);
                        
                        // Try again
                        $stmt = $db->prepare("INSERT INTO subscription_history 
                                             (user_id, plan_name, action, action_date, notes)
                                             VALUES (?, ?, 'expired', NOW(), ?)");
                        $stmt->execute([
                            $user['id'],
                            $user['plan_name'],
                            "Subscription expired automatically on $current_time"
                        ]);
                    }
                }
                
                // Update statistics
                try {
                    $stmt = $db->prepare("INSERT INTO subscription_stats (date, expired_subscriptions) 
                                         VALUES (CURDATE(), ?) 
                                         ON DUPLICATE KEY UPDATE expired_subscriptions = expired_subscriptions + ?");
                    $stmt->execute([$expired_count, $expired_count]);
                } catch (Exception $e) {
                    // Create stats table if it doesn't exist
                    $createStatsSQL = "CREATE TABLE IF NOT EXISTS subscription_stats (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        date DATE NOT NULL UNIQUE,
                        new_subscriptions INT DEFAULT 0,
                        expired_subscriptions INT DEFAULT 0,
                        cancelled_subscriptions INT DEFAULT 0,
                        renewed_subscriptions INT DEFAULT 0,
                        total_revenue DECIMAL(10,2) DEFAULT 0,
                        INDEX idx_date (date)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                    
                    $db->exec($createStatsSQL);
                    
                    // Try again
                    $stmt = $db->prepare("INSERT INTO subscription_stats (date, expired_subscriptions) 
                                         VALUES (CURDATE(), ?) 
                                         ON DUPLICATE KEY UPDATE expired_subscriptions = expired_subscriptions + ?");
                    $stmt->execute([$expired_count, $expired_count]);
                }
                
                // Commit transaction
                $db->commit();
                writeLog("Successfully expired $expired_count subscriptions");
                
                // Send notification emails (optional)
                foreach ($expired_users as $user) {
                    if (!empty($user['email'])) {
                        try {
                            // You can implement email notification here
                            writeLog("Email notification should be sent to: {$user['email']}");
                        } catch (Exception $e) {
                            writeLog("Failed to send email to {$user['email']}: " . $e->getMessage());
                        }
                    }
                }
                
            } else {
                throw new Exception("Failed to update expired subscriptions");
            }
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } else {
        writeLog("No expired subscriptions found");
    }
    
    // Check for subscriptions expiring soon (within 24 hours)
    $tomorrow = date('Y-m-d H:i:s', time() + (24 * 60 * 60));
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users 
                         WHERE subscription_status = 'active' 
                         AND subscription_end_date BETWEEN ? AND ?");
    $stmt->execute([$current_time, $tomorrow]);
    $expiring_soon = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($expiring_soon > 0) {
        writeLog("Warning: $expiring_soon subscriptions will expire within 24 hours");
    }
    
    // Get current active subscriptions count
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE subscription_status = 'active'");
    $stmt->execute();
    $active_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    writeLog("Current active subscriptions: $active_count");

    // Clean up old notifications
    $notificationManager->cleanupOldNotifications();
    writeLog("Cleaned up old notifications");

    writeLog("Subscription expiry check completed successfully");
    
} catch (Exception $e) {
    $error_message = "Error in subscription expiry check: " . $e->getMessage();
    writeLog($error_message);
    
    // Log to PHP error log as well
    error_log($error_message);
    
    // Exit with error code if running from command line
    if (php_sapi_name() === 'cli') {
        exit(1);
    }
}

// Clean up old log entries (keep last 30 days)
try {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    // Keep only last 1000 lines to prevent log file from growing too large
    if (count($lines) > 1000) {
        $lines = array_slice($lines, -1000);
        file_put_contents($log_file, implode("\n", $lines));
        writeLog("Log file trimmed to last 1000 entries");
    }
} catch (Exception $e) {
    writeLog("Warning: Could not trim log file: " . $e->getMessage());
}

writeLog("Script execution completed");
?>
