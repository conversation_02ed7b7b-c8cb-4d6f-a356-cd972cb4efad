/* ===== ENHANCED UI IMPROVEMENTS ===== */
/* Enhanced Link Styles - Absolutely No Underlines */

/* Global link reset - remove all possible underlines */
a, a:link, a:visited, a:hover, a:active, a:focus,
a::before, a::after, a > *, a * {
    text-decoration: none !important;
    text-decoration-line: none !important;
    text-decoration-style: none !important;
    text-decoration-color: transparent !important;
    border-bottom: none !important;
    border-bottom-style: none !important;
    text-underline-offset: 0 !important;
    text-decoration-thickness: 0 !important;
}

/* Remove underlines from specific link types */
.nav-link, .navbar-nav a, .menu-link, .sidebar-link,
.breadcrumb a, .pagination a, .tab-link, .dropdown-link,
.card-link, .btn-link, .footer-link, .header-link {
    text-decoration: none !important;
    border-bottom: none !important;
}

/* Enhanced hover effects without underlines */
a:hover {
    text-decoration: none !important;
    transform: translateY(-1px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover, .menu-link:hover {
    background: rgba(135, 206, 235, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    margin: -8px -12px;
}

/* ===== ADVANCED SCROLLBAR DESIGN ===== */

/* Main scrollbar with gradient and animations */
::-webkit-scrollbar {
    width: 14px;
    height: 14px;
}

::-webkit-scrollbar-track {
    background: linear-gradient(135deg, 
        rgba(248, 249, 250, 0.8) 0%, 
        rgba(233, 236, 239, 0.9) 50%, 
        rgba(206, 212, 218, 0.8) 100%);
    border-radius: 12px;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 -2px 4px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(135, 206, 235, 0.2);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
        #87CEEB 0%, 
        #4682B4 25%, 
        #5F9EA0 50%, 
        #20B2AA 75%, 
        #4682B4 100%);
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 
        0 2px 8px rgba(70, 130, 180, 0.4),
        inset 0 1px 2px rgba(255, 255, 255, 0.3),
        inset 0 -1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, 
        #4682B4 0%, 
        #87CEEB 25%, 
        #20B2AA 50%, 
        #5F9EA0 75%, 
        #87CEEB 100%);
    box-shadow: 
        0 4px 16px rgba(70, 130, 180, 0.6),
        inset 0 2px 4px rgba(255, 255, 255, 0.4),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.9);
}

::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, 
        #5F9EA0 0%, 
        #4682B4 50%, 
        #20B2AA 100%);
    box-shadow: 
        inset 0 2px 6px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(70, 130, 180, 0.5);
    transform: scale(0.95);
}

::-webkit-scrollbar-corner {
    background: linear-gradient(135deg, 
        rgba(248, 249, 250, 0.9) 0%, 
        rgba(233, 236, 239, 0.9) 100%);
    border-radius: 12px;
    border: 1px solid rgba(135, 206, 235, 0.2);
}

/* Animated scrollbar for special containers */
.animated-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
        #87CEEB 0%, 
        #4682B4 50%, 
        #87CEEB 100%);
    background-size: 200% 200%;
    animation: scrollbarGradient 3s ease infinite;
}

@keyframes scrollbarGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Thin scrollbar for modals and small containers */
.modal-content::-webkit-scrollbar,
.dropdown-menu::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.modal-content::-webkit-scrollbar-thumb,
.dropdown-menu::-webkit-scrollbar-thumb,
.sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* ===== ADVANCED CURSOR STYLES ===== */

/* Interactive elements with enhanced cursors */
.course-card, .lesson-item, .exercise-item, .exam-item {
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.course-card:hover, .lesson-item:hover, 
.exercise-item:hover, .exam-item:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
    cursor: pointer !important;
}

/* Video player controls */
.video-controls, .video-progress, .volume-slider {
    cursor: pointer !important;
}

.video-progress:hover {
    cursor: pointer !important;
    transform: scaleY(1.5);
}

/* Progress bars and sliders */
.progress-bar, .slider, .range-input {
    cursor: pointer !important;
}

.progress-bar:hover, .slider:hover {
    cursor: pointer !important;
    filter: brightness(1.1);
}

/* Notification and alert cursors */
.notification, .alert, .toast {
    cursor: default !important;
}

.notification.clickable, .alert.clickable {
    cursor: pointer !important;
}

.notification .close-btn, .alert .close-btn {
    cursor: pointer !important;
}

/* Form element enhanced cursors */
.form-group label {
    cursor: pointer !important;
}

.checkbox, .radio {
    cursor: pointer !important;
}

.file-upload-area {
    cursor: pointer !important;
}

.file-upload-area:hover {
    cursor: pointer !important;
    background: rgba(135, 206, 235, 0.1);
}

/* Loading and disabled states */
.btn.loading, .form-control.loading {
    cursor: wait !important;
    position: relative;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Drag and drop cursors */
.drag-zone {
    cursor: copy !important;
    border: 2px dashed #87CEEB;
    transition: all 0.3s ease;
}

.drag-zone.drag-over {
    cursor: copy !important;
    background: rgba(135, 206, 235, 0.1);
    border-color: #4682B4;
}

/* Selection cursors */
.selectable {
    cursor: pointer !important;
    user-select: none;
}

.selectable.selected {
    background: rgba(135, 206, 235, 0.2);
    border: 2px solid #87CEEB;
}

/* Zoom cursors for images and media */
.zoomable {
    cursor: zoom-in !important;
}

.zoomable.zoomed {
    cursor: zoom-out !important;
}

/* Enhanced focus indicators */
*:focus-visible {
    outline: 3px solid #4682B4 !important;
    outline-offset: 3px !important;
    box-shadow: 
        0 0 0 6px rgba(70, 130, 180, 0.2) !important,
        0 0 20px rgba(135, 206, 235, 0.4) !important;
    border-radius: 8px;
}

/* Remove focus outline for mouse users */
*:focus:not(:focus-visible) {
    outline: none !important;
    box-shadow: none !important;
}

/* ===== ENHANCED HOVER EFFECTS ===== */

/* Card hover effects */
.card, .course-card, .lesson-card, .exercise-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer !important;
}

.card:hover, .course-card:hover, .lesson-card:hover, .exercise-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(70, 130, 180, 0.2),
        0 10px 20px rgba(135, 206, 235, 0.1);
    cursor: pointer !important;
}

/* Button hover effects */
.btn {
    position: relative;
    overflow: hidden;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(70, 130, 180, 0.3);
    cursor: pointer !important;
}

/* Navigation hover effects */
.nav-item, .menu-item, .sidebar-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer !important;
}

.nav-item:hover, .menu-item:hover, .sidebar-item:hover {
    background: linear-gradient(135deg,
        rgba(135, 206, 235, 0.1) 0%,
        rgba(70, 130, 180, 0.1) 100%);
    border-radius: 12px;
    transform: translateX(5px);
    cursor: pointer !important;
}

/* ===== ENHANCED ANIMATIONS ===== */

/* Smooth page transitions */
.page-transition {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animations */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

/* Pulse animation for important elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(135, 206, 235, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(135, 206, 235, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(135, 206, 235, 0);
    }
}

/* Bounce animation for notifications */
.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Slide animations */
.slide-in-right {
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
