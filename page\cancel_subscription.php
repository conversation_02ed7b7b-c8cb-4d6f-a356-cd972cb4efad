<?php
require_once '../config/config.php';
require_once '../includes/database.php';

// Set content type to JSON
header('Content-Type: application/json; charset=utf-8');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مسموح'], JSON_UNESCAPED_UNICODE);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة'], JSON_UNESCAPED_UNICODE);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get current subscription information
    $stmt = $db->prepare("SELECT u.subscription_status, u.subscription_end_date, u.current_plan_id,
                         sp.name as plan_name, sp.price, sp.discounted_price
                         FROM users u
                         LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                         WHERE u.id = ?");
    $stmt->execute([$user_id]);
    $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$subscription || $subscription['subscription_status'] !== 'active') {
        echo json_encode(['success' => false, 'message' => 'لا يوجد اشتراك نشط لإلغائه'], JSON_UNESCAPED_UNICODE);
        exit;
    }

    if (!$subscription['subscription_end_date'] || strtotime($subscription['subscription_end_date']) <= time()) {
        echo json_encode(['success' => false, 'message' => 'الاشتراك منتهي الصلاحية بالفعل'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // Calculate used days and refund amount
    $subscription_start = strtotime($subscription['subscription_end_date']) - (30 * 24 * 60 * 60); // Assuming 30 days subscription
    $current_time = time();
    $used_days = ceil(($current_time - $subscription_start) / (24 * 60 * 60));
    $total_days = 30; // Assuming 30 days subscription
    $remaining_days = max(0, $total_days - $used_days);

    $paid_amount = $subscription['discounted_price'];
    $daily_rate = $paid_amount / $total_days;
    $refund_amount = $remaining_days * $daily_rate;

    // Begin transaction
    $db->beginTransaction();

    try {
        // Start transaction
        $db->beginTransaction();

        // Cancel the subscription
        $stmt = $db->prepare("UPDATE users SET
                             subscription_status = 'cancelled',
                             current_plan_id = NULL,
                             subscription_end_date = NOW()
                             WHERE id = ?");
        $result = $stmt->execute([$user_id]);

        if (!$result || $stmt->rowCount() === 0) {
            throw new Exception('فشل في إلغاء الاشتراك - لم يتم العثور على المستخدم');
        }

        // Log the cancellation
        $stmt = $db->prepare("INSERT INTO subscription_cancellations
                             (user_id, plan_name, paid_amount, used_days, refund_amount, cancellation_date, cancellation_reason)
                             VALUES (?, ?, ?, ?, ?, NOW(), 'user_request')");

        // Create table if it doesn't exist
        $createTableSQL = "CREATE TABLE IF NOT EXISTS subscription_cancellations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            plan_name VARCHAR(100),
            paid_amount DECIMAL(10,2),
            used_days INT,
            refund_amount DECIMAL(10,2),
            cancellation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            cancellation_reason VARCHAR(100),
            refund_status ENUM('pending', 'processed', 'rejected') DEFAULT 'pending',
            notes TEXT,
            INDEX idx_user_id (user_id),
            INDEX idx_cancellation_date (cancellation_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $db->exec($createTableSQL);

        $stmt->execute([
            $user_id,
            $subscription['plan_name'],
            $paid_amount,
            $used_days,
            $refund_amount
        ]);

        // Commit transaction
        $db->commit();

        // Clear session cache
        if (isset($_SESSION['user_subscription_status'])) {
            unset($_SESSION['user_subscription_status']);
        }
        if (isset($_SESSION['current_plan_id'])) {
            unset($_SESSION['current_plan_id']);
        }

        echo json_encode([
            'success' => true,
            'message' => 'تم إلغاء الاشتراك بنجاح',
            'plan_name' => $subscription['plan_name'],
            'refund_info' => [
                'paid_amount' => $paid_amount,
                'used_days' => $used_days,
                'remaining_days' => $remaining_days,
                'refund_amount' => round($refund_amount, 2)
            ],
            'redirect' => true
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إلغاء الاشتراك: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
        error_log("Cancel subscription error: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    error_log("Database connection error in cancel_subscription.php: " . $e->getMessage());
}
?>
