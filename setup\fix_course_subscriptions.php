<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<title>إصلاح جدول course_subscriptions</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n";
echo ".success { color: green; }\n";
echo ".error { color: red; }\n";
echo ".info { color: blue; }\n";
echo ".warning { color: orange; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>إصلاح جدول course_subscriptions</h1>\n";

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<div class='info'>بدء إصلاح جدول course_subscriptions...</div><br>\n";
    
    // Check current structure
    echo "<div class='info'>فحص البنية الحالية...</div><br>\n";
    $stmt = $db->prepare("DESCRIBE course_subscriptions");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasPaymentGateway = false;
    $hasTransactionId = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'payment_gateway') {
            $hasPaymentGateway = true;
            echo "<div class='success'>✓ عمود payment_gateway موجود</div>\n";
        }
        if ($column['Field'] === 'transaction_id') {
            $hasTransactionId = true;
            echo "<div class='success'>✓ عمود transaction_id موجود</div>\n";
        }
    }
    
    // Add missing columns
    if (!$hasPaymentGateway) {
        echo "<div class='info'>إضافة عمود payment_gateway...</div>\n";
        $db->exec("ALTER TABLE course_subscriptions ADD COLUMN payment_gateway VARCHAR(50) NULL");
        echo "<div class='success'>✓ تم إضافة عمود payment_gateway</div>\n";
    }
    
    if (!$hasTransactionId) {
        echo "<div class='info'>إضافة عمود transaction_id...</div>\n";
        $db->exec("ALTER TABLE course_subscriptions ADD COLUMN transaction_id VARCHAR(100) NULL");
        echo "<div class='success'>✓ تم إضافة عمود transaction_id</div>\n";
    }
    
    // Check current activation_method values
    echo "<br><div class='info'>فحص قيم activation_method الحالية...</div><br>\n";
    $stmt = $db->prepare("SELECT DISTINCT activation_method FROM course_subscriptions");
    $stmt->execute();
    $currentValues = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='info'>القيم الموجودة حالياً:</div>\n";
    foreach ($currentValues as $value) {
        echo "<div>- " . ($value ?: 'NULL') . "</div>\n";
    }
    
    // Update invalid values first
    echo "<br><div class='info'>تحديث القيم غير الصحيحة...</div><br>\n";
    
    // Update NULL or empty values to 'code'
    $stmt = $db->prepare("UPDATE course_subscriptions SET activation_method = 'code' WHERE activation_method IS NULL OR activation_method = ''");
    $result = $stmt->execute();
    $affected = $stmt->rowCount();
    if ($affected > 0) {
        echo "<div class='success'>✓ تم تحديث $affected سجل من NULL/فارغ إلى 'code'</div>\n";
    }
    
    // Update any other invalid values
    $validValues = ['code', 'payment', 'admin'];
    $placeholders = str_repeat('?,', count($validValues) - 1) . '?';
    $stmt = $db->prepare("UPDATE course_subscriptions SET activation_method = 'code' WHERE activation_method NOT IN ($placeholders)");
    $result = $stmt->execute($validValues);
    $affected = $stmt->rowCount();
    if ($affected > 0) {
        echo "<div class='warning'>⚠ تم تحديث $affected سجل بقيم غير صحيحة إلى 'code'</div>\n";
    }
    
    // Now safely update the enum
    echo "<br><div class='info'>تحديث ENUM لـ activation_method...</div>\n";
    try {
        $db->exec("ALTER TABLE course_subscriptions MODIFY COLUMN activation_method ENUM('code', 'payment', 'online_payment', 'admin') DEFAULT 'code'");
        echo "<div class='success'>✓ تم تحديث ENUM بنجاح</div>\n";
    } catch (PDOException $e) {
        echo "<div class='error'>✗ خطأ في تحديث ENUM: " . $e->getMessage() . "</div>\n";
        
        // Try alternative approach
        echo "<div class='info'>محاولة طريقة بديلة...</div>\n";
        $db->exec("ALTER TABLE course_subscriptions MODIFY COLUMN activation_method VARCHAR(20) DEFAULT 'code'");
        echo "<div class='success'>✓ تم تحويل العمود إلى VARCHAR مؤقتاً</div>\n";
        
        $db->exec("ALTER TABLE course_subscriptions MODIFY COLUMN activation_method ENUM('code', 'payment', 'online_payment', 'admin') DEFAULT 'code'");
        echo "<div class='success'>✓ تم تحديث ENUM بنجاح</div>\n";
    }
    
    // Add indexes if they don't exist
    echo "<br><div class='info'>إضافة الفهارس...</div>\n";
    
    try {
        $db->exec("ALTER TABLE course_subscriptions ADD INDEX idx_payment_gateway (payment_gateway)");
        echo "<div class='success'>✓ تم إضافة فهرس payment_gateway</div>\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key') !== false) {
            echo "<div class='info'>⚠ فهرس payment_gateway موجود بالفعل</div>\n";
        } else {
            echo "<div class='error'>✗ خطأ في إضافة فهرس payment_gateway: " . $e->getMessage() . "</div>\n";
        }
    }
    
    try {
        $db->exec("ALTER TABLE course_subscriptions ADD INDEX idx_transaction_id (transaction_id)");
        echo "<div class='success'>✓ تم إضافة فهرس transaction_id</div>\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key') !== false) {
            echo "<div class='info'>⚠ فهرس transaction_id موجود بالفعل</div>\n";
        } else {
            echo "<div class='error'>✗ خطأ في إضافة فهرس transaction_id: " . $e->getMessage() . "</div>\n";
        }
    }
    
    // Final verification
    echo "<br><div class='info'>التحقق النهائي...</div><br>\n";
    
    $stmt = $db->prepare("DESCRIBE course_subscriptions");
    $stmt->execute();
    $finalColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $finalHasPaymentGateway = false;
    $finalHasTransactionId = false;
    $activationMethodType = '';
    
    foreach ($finalColumns as $column) {
        if ($column['Field'] === 'payment_gateway') {
            $finalHasPaymentGateway = true;
        }
        if ($column['Field'] === 'transaction_id') {
            $finalHasTransactionId = true;
        }
        if ($column['Field'] === 'activation_method') {
            $activationMethodType = $column['Type'];
        }
    }
    
    echo "<div class='" . ($finalHasPaymentGateway ? 'success' : 'error') . "'>";
    echo ($finalHasPaymentGateway ? '✓' : '✗') . " عمود payment_gateway: " . ($finalHasPaymentGateway ? 'موجود' : 'غير موجود') . "</div>\n";
    
    echo "<div class='" . ($finalHasTransactionId ? 'success' : 'error') . "'>";
    echo ($finalHasTransactionId ? '✓' : '✗') . " عمود transaction_id: " . ($finalHasTransactionId ? 'موجود' : 'غير موجود') . "</div>\n";
    
    echo "<div class='info'>نوع عمود activation_method: $activationMethodType</div>\n";
    
    if ($finalHasPaymentGateway && $finalHasTransactionId) {
        echo "<br><div class='success'><h2>✅ تم إصلاح جدول course_subscriptions بنجاح!</h2></div>\n";
    } else {
        echo "<br><div class='error'><h2>❌ فشل في إصلاح الجدول بالكامل</h2></div>\n";
    }
    
} catch (Exception $e) {
    echo "<div class='error'><h2>✗ خطأ في الإصلاح: " . $e->getMessage() . "</h2></div>\n";
}

echo "<br><a href='../page/dashboard.php'>العودة للوحة التحكم</a>\n";
echo "<br><a href='update_payment_system.php'>إعادة تشغيل تحديث النظام</a>\n";
echo "</body>\n";
echo "</html>\n";
?>
