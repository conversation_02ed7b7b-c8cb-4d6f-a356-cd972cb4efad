<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set admin session for testing
$_SESSION['admin_id'] = 1;

echo "<h2>اختبار نظام الإشعارات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
.warning { color: orange; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
table { width: 100%; border-collapse: collapse; margin: 20px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
th { background: #f8f9fa; }
.btn { padding: 8px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; color: white; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; }
</style>";

$message = '';
$error = '';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // Handle actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_tables':
                    // Create notification tables
                    include __DIR__ . '/create_notifications_table.php';
                    break;
                    
                case 'expire_user':
                    // Expire a user's subscription
                    $user_id = $_POST['user_id'];

                    $stmt = $db->prepare("UPDATE users SET
                                         subscription_status = 'expired',
                                         current_plan_id = NULL
                                         WHERE id = ?");
                    $result = $stmt->execute([$user_id]);

                    if ($result) {
                        $message = "تم انتهاء اشتراك المستخدم $user_id";
                    } else {
                        $error = "فشل في انتهاء الاشتراك";
                    }
                    break;
                    
                case 'reset_notifications':
                    // Reset notification settings for a user
                    $user_id = $_POST['user_id'];
                    $stmt = $db->prepare("DELETE FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
                    $stmt->execute([$user_id]);
                    $message = "تم إعادة تعيين إعدادات الإشعارات للمستخدم $user_id";
                    break;
                    
                case 'test_notification':
                    // Test if notification should show
                    $user_id = $_POST['user_id'];
                    $shouldShow = $notificationManager->shouldShowSubscriptionExpiredNotification($user_id);
                    $message = "المستخدم $user_id - يجب إظهار الإشعار: " . ($shouldShow ? 'نعم' : 'لا');
                    break;
                    
                case 'cleanup':
                    // Clean up old notifications
                    $notificationManager->cleanupOldNotifications();
                    $message = "تم تنظيف الإشعارات القديمة";
                    break;
            }
        }
    }
    
    if ($message) {
        echo "<div class='success'>$message</div>";
    }
    
    if ($error) {
        echo "<div class='error'>$error</div>";
    }
    
    // Show current time
    echo "<div class='info'>⏰ الوقت الحالي: " . date('Y-m-d H:i:s') . "</div>";
    
    // Get all users with their subscription status
    $stmt = $db->query("SELECT u.id, u.username, u.subscription_status, u.subscription_end_date, u.current_plan_id,
                       sp.name as plan_name
                       FROM users u
                       LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                       ORDER BY u.id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>المستخدمين وحالة الاشتراك:</h3>";
    echo "<table>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>حالة الاشتراك</th><th>الخطة</th><th>تاريخ الانتهاء</th><th>يجب إظهار الإشعار</th><th>الإجراءات</th></tr>";
    
    foreach ($users as $user) {
        $status_class = '';
        $status_text = $user['subscription_status'] ?: 'غير مشترك';
        
        if ($user['subscription_status'] === 'active') {
            $status_class = 'style="background: #ccffcc;"';
            $status_text = 'نشط';
        } elseif ($user['subscription_status'] === 'expired') {
            $status_class = 'style="background: #ffcccc;"';
            $status_text = 'منتهي';
        } elseif ($user['subscription_status'] === 'cancelled') {
            $status_class = 'style="background: #ffffcc;"';
            $status_text = 'ملغي';
        }
        
        // Check if should show notification
        $shouldShow = $notificationManager->shouldShowSubscriptionExpiredNotification($user['id']);
        
        echo "<tr $status_class>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>$status_text</td>";
        echo "<td>" . ($user['plan_name'] ?: 'لا يوجد') . "</td>";
        echo "<td>" . ($user['subscription_end_date'] ?: 'لا يوجد') . "</td>";
        echo "<td>" . ($shouldShow ? '✅ نعم' : '❌ لا') . "</td>";
        
        // Actions
        echo "<td>";
        
        // Expire subscription
        if ($user['subscription_status'] === 'active') {
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='action' value='expire_user'>";
            echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
            echo "<button type='submit' class='btn btn-danger'>انتهاء الاشتراك</button>";
            echo "</form>";
        }
        
        // Reset notifications
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='action' value='reset_notifications'>";
        echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
        echo "<button type='submit' class='btn btn-warning'>إعادة تعيين الإشعارات</button>";
        echo "</form>";
        
        // Test notification
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='action' value='test_notification'>";
        echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
        echo "<button type='submit' class='btn btn-primary'>اختبار الإشعار</button>";
        echo "</form>";
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show notification settings
    echo "<h3>إعدادات الإشعارات:</h3>";
    try {
        $stmt = $db->query("SELECT ns.*, u.username 
                           FROM notification_settings ns
                           LEFT JOIN users u ON ns.user_id = u.id
                           ORDER BY ns.user_id, ns.notification_type");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($settings) > 0) {
            echo "<table>";
            echo "<tr><th>المستخدم</th><th>نوع الإشعار</th><th>مفعل</th><th>آخر عرض</th><th>عدد العرض</th><th>الحد الأقصى</th></tr>";
            
            foreach ($settings as $setting) {
                echo "<tr>";
                echo "<td>{$setting['username']} (ID: {$setting['user_id']})</td>";
                echo "<td>{$setting['notification_type']}</td>";
                echo "<td>" . ($setting['is_enabled'] ? 'نعم' : 'لا') . "</td>";
                echo "<td>" . ($setting['last_shown'] ?: 'لم يُعرض') . "</td>";
                echo "<td>{$setting['show_count']}</td>";
                echo "<td>{$setting['max_show_count']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='info'>لا توجد إعدادات إشعارات</div>";
        }
    } catch (Exception $e) {
        echo "<div class='info'>جدول إعدادات الإشعارات غير موجود بعد</div>";
    }
    
    // Show recent notifications
    echo "<h3>الإشعارات الحديثة:</h3>";
    try {
        $stmt = $db->query("SELECT n.*, u.username 
                           FROM user_notifications n
                           LEFT JOIN users u ON n.user_id = u.id
                           ORDER BY n.created_at DESC 
                           LIMIT 10");
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($notifications) > 0) {
            echo "<table>";
            echo "<tr><th>المستخدم</th><th>النوع</th><th>العنوان</th><th>مقروء</th><th>مرفوض</th><th>تاريخ الإنشاء</th><th>تاريخ الانتهاء</th></tr>";
            
            foreach ($notifications as $notif) {
                echo "<tr>";
                echo "<td>{$notif['username']} (ID: {$notif['user_id']})</td>";
                echo "<td>{$notif['notification_type']}</td>";
                echo "<td>" . substr($notif['title'], 0, 30) . "...</td>";
                echo "<td>" . ($notif['is_read'] ? 'نعم' : 'لا') . "</td>";
                echo "<td>" . ($notif['is_dismissed'] ? 'نعم' : 'لا') . "</td>";
                echo "<td>" . date('Y-m-d H:i', strtotime($notif['created_at'])) . "</td>";
                echo "<td>" . ($notif['expires_at'] ? date('Y-m-d H:i', strtotime($notif['expires_at'])) : 'لا ينتهي') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='info'>لا توجد إشعارات</div>";
        }
    } catch (Exception $e) {
        echo "<div class='info'>جدول الإشعارات غير موجود بعد</div>";
    }
    
    // Global actions
    echo "<h3>إجراءات عامة:</h3>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='create_tables'>";
    echo "<button type='submit' class='btn btn-primary'>إنشاء جداول الإشعارات</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='cleanup'>";
    echo "<button type='submit' class='btn btn-warning'>تنظيف الإشعارات القديمة</button>";
    echo "</form>";
    
    echo "<div class='info'>";
    echo "<h4>كيفية الاستخدام:</h4>";
    echo "<ol>";
    echo "<li>اختر مستخدم لديه اشتراك نشط</li>";
    echo "<li>اضغط 'انتهاء الاشتراك' لجعل اشتراكه منتهي</li>";
    echo "<li>افتح صفحة الداشبورد أو المنهج أو الاشتراكات للمستخدم</li>";
    echo "<li>يجب أن يظهر إشعار انتهاء الاشتراك</li>";
    echo "<li>أغلق الإشعار وأعد تحميل الصفحة - لن يظهر مرة أخرى</li>";
    echo "<li>استخدم 'إعادة تعيين الإشعارات' لإظهار الإشعار مرة أخرى</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div class='error'>الملف: " . $e->getFile() . "</div>";
    echo "<div class='error'>السطر: " . $e->getLine() . "</div>";
}
?>

<p>
    <a href="test_subscription_expiry.php" class="btn btn-primary">اختبار انتهاء الاشتراك</a>
    <a href="subscription_logs.php" class="btn btn-success">عرض السجلات</a>
    <a href="../page/dashboard.php" class="btn btn-warning">الداشبورد</a>
</p>
