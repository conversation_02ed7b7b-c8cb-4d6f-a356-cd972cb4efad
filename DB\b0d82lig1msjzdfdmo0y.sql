-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: b0d82lig1msjzdfdmo0y-mysql.services.clever-cloud.com:3306
-- Generation Time: Jul 20, 2025 at 10:42 PM
-- Server version: 8.0.22-13
-- PHP Version: 8.2.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `b0d82lig1msjzdfdmo0y`
--

-- --------------------------------------------------------

--
-- Table structure for table `activation_codes`
--

CREATE TABLE `activation_codes` (
  `id` int NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `course_id` int NOT NULL,
  `user_limit` int DEFAULT '1',
  `used_count` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `activation_codes`
--

INSERT INTO `activation_codes` (`id`, `code`, `course_id`, `user_limit`, `used_count`, `is_active`, `created_by`, `created_at`, `expires_at`) VALUES
(1, 'COURSE_687AEBCC7D204', 1, 1, 1, 0, 1, '2025-07-19 00:50:57', NULL),
(2, 'COURSE_687B175A1CD99', 2, 1, 0, 0, 1, '2025-07-19 03:56:46', NULL),
(3, 'COURSE_687B7336EACB1', 3, 5, 1, 1, 1, '2025-07-19 20:22:41', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('super_admin','admin','moderator') COLLATE utf8mb4_unicode_ci DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `email`, `password_hash`, `full_name`, `role`, `is_active`, `created_at`, `updated_at`, `last_login`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'super_admin', 1, '2025-07-16 22:17:36', '2025-07-20 22:20:39', '2025-07-20 22:20:39');

-- --------------------------------------------------------

--
-- Table structure for table `admin_activity_log`
--

CREATE TABLE `admin_activity_log` (
  `id` int NOT NULL,
  `admin_id` int NOT NULL,
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` text COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_activity_log`
--

INSERT INTO `admin_activity_log` (`id`, `admin_id`, `action`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 'add_news', '{\"title\":\"\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-17 01:45:51');

-- --------------------------------------------------------

--
-- Table structure for table `admin_login_attempts`
--

CREATE TABLE `admin_login_attempts` (
  `id` int NOT NULL,
  `username_or_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `success` tinyint(1) NOT NULL,
  `attempted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `courses`
--

CREATE TABLE `courses` (
  `id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `features` text COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `discount_percentage` decimal(5,2) DEFAULT '0.00',
  `discounted_price` decimal(10,2) GENERATED ALWAYS AS ((`price` - ((`price` * `discount_percentage`) / 100))) STORED,
  `main_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `modal_images` text COLLATE utf8mb4_unicode_ci,
  `education_level` enum('primary','preparatory','secondary','all') COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `education_type` enum('azhari','general','all') COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `grade` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `specialization` enum('scientific','literary','all') COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `passing_grade` decimal(5,2) DEFAULT '60.00' COMMENT 'درجة النجاح المطلوبة بالنسبة المئوية'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `courses`
--

INSERT INTO `courses` (`id`, `title`, `subject`, `description`, `features`, `price`, `discount_percentage`, `main_image`, `modal_images`, `education_level`, `education_type`, `grade`, `specialization`, `is_active`, `created_by`, `created_at`, `updated_at`, `passing_grade`) VALUES
(1, 'تجريبي', 'تجريبي', '❤💖سلسلة الدكتور ترحب بكم في الكورس التجريبي❤💖', 'هذا الكورس تجريبي الاستخدام👍\r\nاستخدم المنصه لي تعلم الغة العربية🌹', 100.00, 10.00, 'course_687aead613964.png', '[\"course_687aead615f8d.png\"]', 'all', 'all', 'all', 'all', 0, 1, '2025-07-19 00:46:50', '2025-07-19 20:03:27', 60.00),
(2, 'كورس تحسين الكتابه', 'الخط', 'كورس متخصص في تحسين الخط العربي', 'كورس ممتاز \r\nيجب علي كل من خطه وحش ياخذه \r\nسوف تري تحسين من تاني اسبوع', 350.00, 50.00, 'course_687b16a815246.png', '[\"course_687b16a816b13.png\"]', 'all', 'all', 'all', 'all', 0, 1, '2025-07-19 03:53:48', '2025-07-19 20:03:24', 60.00),
(3, 'كورس تحسين الكتابه', 'خط', 'كورس متخصص في تحسين الخط و تحسين الكتابه ', 'اكثر من 100 طالب نجو\r\nشرح مستر محمد عبدالله', 700.00, 10.00, 'course_687b6ff6c6038.jpeg', '[\"course_687b6ff6c7a8d.jpeg\"]', 'all', 'all', 'all', 'all', 1, 1, '2025-07-19 20:08:49', '2025-07-19 20:08:49', 60.00);

-- --------------------------------------------------------

--
-- Table structure for table `course_content`
--

CREATE TABLE `course_content` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `week_number` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `content_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `course_enrollments`
--

CREATE TABLE `course_enrollments` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `status` enum('active','inactive','completed') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `enrolled_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_enrollments`
--

INSERT INTO `course_enrollments` (`id`, `user_id`, `course_id`, `status`, `enrolled_at`) VALUES
(1, 1, 3, 'active', '2025-07-20 20:00:31'),
(2, 2, 3, 'active', '2025-07-20 20:01:24');

-- --------------------------------------------------------

--
-- Table structure for table `course_exams`
--

CREATE TABLE `course_exams` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int DEFAULT '60',
  `total_marks` decimal(5,2) DEFAULT '0.00',
  `passing_marks` decimal(5,2) DEFAULT '0.00',
  `week_number` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exams`
--

INSERT INTO `course_exams` (`id`, `course_id`, `title`, `description`, `duration_minutes`, `total_marks`, `passing_marks`, `week_number`, `is_active`, `created_by`, `created_at`) VALUES
(1, 1, 'امتحان تجريبي نهائي', 'امتحان تجريبي بعد الإصلاح النهائي', 30, 10.00, 6.00, 1, 1, 1, '2025-07-19 01:23:33'),
(5, 2, 'امتحان علي الدرس الاول', 'ا', 60, 0.00, 60.00, 1, 1, 1, '2025-07-19 04:48:22'),
(6, 2, 'تجريبي', '88u', 60, 0.00, 60.00, 2, 1, 1, '2025-07-19 18:22:28'),
(7, 3, 'امتحان علي انواع الخط', 'امتحان علي الخط', 120, 0.00, 60.00, 1, 1, 1, '2025-07-19 20:21:47'),
(8, 3, 'kguik', 'ilugi', 60, 0.00, 60.00, 1, 1, 1, '2025-07-20 16:58:56'),
(9, 3, 'امتحان علي انواع الخط العربي', 'امتحان', 60, 0.00, 60.00, 1, 1, 1, '2025-07-20 17:14:25'),
(10, 3, 'بالا', 'ىبلا', 60, 0.00, 60.00, 2, 1, 1, '2025-07-20 17:59:10');

-- --------------------------------------------------------

--
-- Table structure for table `course_exam_questions`
--

CREATE TABLE `course_exam_questions` (
  `id` int NOT NULL,
  `exam_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('true_false','multiple_choice') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'true_false',
  `options` json DEFAULT NULL,
  `correct_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `explanation` text COLLATE utf8mb4_unicode_ci,
  `points` decimal(5,2) DEFAULT '1.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exam_questions`
--

INSERT INTO `course_exam_questions` (`id`, `exam_id`, `question_text`, `question_type`, `options`, `correct_answer`, `explanation`, `points`, `created_at`) VALUES
(1, 1, 'هل 2 + 2 = 4؟', 'true_false', NULL, 'true', 'نعم، 2 + 2 = 4', 5.00, '2025-07-19 01:23:33'),
(2, 1, 'ما هو ناتج 3 × 3؟', 'multiple_choice', '[\"6\", \"9\", \"12\"]', '9', '3 × 3 = 9', 5.00, '2025-07-19 01:23:33'),
(6, 5, 'لت', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-19 04:48:22'),
(7, 6, 'tt', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-19 18:22:28'),
(8, 7, 'هل الخط الثلث خط عربي', 'true_false', NULL, 'true', NULL, 5.00, '2025-07-19 20:21:47'),
(9, 7, 'هل الخط الصيني القديم خط عربي', 'true_false', NULL, 'true', NULL, 5.00, '2025-07-19 20:21:48'),
(10, 7, 'انواع الخط العربي', 'multiple_choice', '[\"الثالث\", \"الصيني القديم\", \"الانجليزي\", \"الفارسي\"]', 'الثالث', NULL, 5.00, '2025-07-19 20:21:48'),
(11, 8, 'iouiouio', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-20 16:58:56'),
(12, 9, 'هل الخط الثلث خط عربي', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-20 17:14:25'),
(13, 9, 'هل الخط الرقعه خط', 'multiple_choice', '[\"عربي\", \"فارسي\", \"اجنبي\", \"صيني\"]', 'عربي', NULL, 1.00, '2025-07-20 17:14:25'),
(14, 10, 'بؤللا', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-20 17:59:10');

-- --------------------------------------------------------

--
-- Table structure for table `course_exercises`
--

CREATE TABLE `course_exercises` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `week_number` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'تمرين',
  `description` text COLLATE utf8mb4_unicode_ci,
  `question_type` enum('true_false','multiple_choice') COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` text COLLATE utf8mb4_unicode_ci,
  `correct_answer` text COLLATE utf8mb4_unicode_ci,
  `explanation` text COLLATE utf8mb4_unicode_ci,
  `has_multiple_questions` tinyint(1) DEFAULT '0',
  `total_questions` int DEFAULT '1',
  `passing_score` decimal(5,2) DEFAULT '60.00',
  `exercise_order` int DEFAULT '0',
  `timing_info` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `points` decimal(5,2) DEFAULT '1.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exercises`
--

INSERT INTO `course_exercises` (`id`, `course_id`, `week_number`, `title`, `description`, `question_type`, `question_text`, `options`, `correct_answer`, `explanation`, `has_multiple_questions`, `total_questions`, `passing_score`, `exercise_order`, `timing_info`, `is_active`, `created_by`, `created_at`, `updated_at`, `points`) VALUES
(1, 1, 1, 'تمرين PHP', NULL, 'true_false', 'هل PHP لغة برمجة من جانب الخادم؟', NULL, 'true', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:29', '2025-07-19 15:33:29', 1.00),
(2, 1, 1, 'تمرين JavaScript', NULL, 'true_false', 'هل JavaScript تعمل فقط في المتصفح؟', NULL, 'false', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:29', '2025-07-19 15:33:29', 1.00),
(3, 1, 1, 'تمرين HTML', NULL, 'true_false', 'هل HTML لغة برمجة؟', NULL, 'false', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:29', '2025-07-19 15:33:29', 1.00),
(4, 1, 1, 'تمرين CSS', NULL, 'true_false', 'هل CSS تستخدم لتنسيق صفحات الويب؟', NULL, 'true', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:30', '2025-07-19 15:33:30', 1.00),
(5, 1, 1, 'تمرين MySQL', NULL, 'true_false', 'هل MySQL نوع من قواعد البيانات؟', NULL, 'true', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:30', '2025-07-19 15:33:30', 1.00),
(6, 2, 2, 'ممم', '', 'multiple_choice', 'تمرين متعدد الأسئلة', NULL, 'متعدد الأسئلة', NULL, 1, 2, 60.00, 1, 'مم', 1, 1, '2025-07-19 17:49:08', '2025-07-19 17:49:08', 1.00),
(9, 2, 2, 'تحو', 'تنمتنم', 'true_false', 'لتنم', NULL, 'true', 'تانمات', 0, 1, 60.00, 3, 'تن', 1, 1, '2025-07-19 19:05:14', '2025-07-19 19:05:14', 1.00),
(10, 3, 1, 'خط', 'تمرين علي الدرس الاول', 'multiple_choice', 'هل خط الرقعة', '[\"\\u0639\\u0631\\u0628\\u064a\",\"\\u0627\\u062c\\u0646\\u0628\\u064a\",\"\\u0635\\u064a\\u0646\\u064a\",\"\\u0641\\u0627\\u0631\\u0633\\u064a\"]', 'عربي', 'عربي', 0, 1, 60.00, 1, 'التمرين الاول', 1, 1, '2025-07-19 20:15:22', '2025-07-19 20:15:22', 1.00),
(11, 3, 1, 'خط', 'انواع الخط العربي', 'true_false', 'هل الخط الصيني القديم خط عربي', NULL, 'false', 'هذا الخط خط صيني اصوله صيني', 0, 1, 60.00, 2, 'التمرين الثاني', 1, 1, '2025-07-19 20:16:35', '2025-07-19 20:16:35', 1.00),
(12, 3, 1, 'jjh', 'hlh', 'multiple_choice', 'lhu', '[\"ljiklhjilhuil\",\"ulhil\",\"ilyuilyui\",\"lyiulyuil\"]', 'ljiklhjilhuil', 'yuilyui', 0, 1, 60.00, 3, 'nklhkj', 1, 1, '2025-07-20 16:58:26', '2025-07-20 16:58:26', 1.00);

-- --------------------------------------------------------

--
-- Table structure for table `course_exercise_questions`
--

CREATE TABLE `course_exercise_questions` (
  `id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('true_false','multiple_choice') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'true_false',
  `options` json DEFAULT NULL,
  `correct_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `explanation` text COLLATE utf8mb4_unicode_ci,
  `points` decimal(5,2) DEFAULT '1.00',
  `question_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exercise_questions`
--

INSERT INTO `course_exercise_questions` (`id`, `exercise_id`, `question_text`, `question_type`, `options`, `correct_answer`, `explanation`, `points`, `question_order`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 6, 'هت', 'multiple_choice', '[\"خهه\", \"خه\", \"خه\", \"هخ\"]', 'هخ', '', 1.00, 1, 1, 1, '2025-07-19 17:49:09', '2025-07-19 17:49:09'),
(2, 6, 'حخ0', 'multiple_choice', '[\"09\", \"90\", \"9\", \"حخ\"]', 'حخ', '', 1.00, 2, 1, 1, '2025-07-19 17:49:09', '2025-07-19 17:49:09');

-- --------------------------------------------------------

--
-- Stand-in structure for view `course_statistics`
-- (See below for the actual view)
--
CREATE TABLE `course_statistics` (
`active_subscribers` bigint
,`course_id` int
,`created_at` timestamp
,`pending_subscribers` bigint
,`title` varchar(255)
,`total_exams` bigint
,`total_exercises` bigint
,`total_subscribers` bigint
,`total_videos` bigint
,`total_weekly_tests` bigint
);

-- --------------------------------------------------------

--
-- Table structure for table `course_subscriptions`
--

CREATE TABLE `course_subscriptions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `activation_status` enum('pending','active','expired','rejected') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `activation_method` enum('code','payment') COLLATE utf8mb4_unicode_ci NOT NULL,
  `activation_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_request_id` int DEFAULT NULL,
  `activated_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_subscriptions`
--

INSERT INTO `course_subscriptions` (`id`, `user_id`, `course_id`, `activation_status`, `activation_method`, `activation_code`, `payment_request_id`, `activated_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 'active', 'code', 'COURSE_687AEBCC7D204', NULL, '2025-07-19 02:51:34', NULL, '2025-07-19 00:51:41', '2025-07-19 00:52:10'),
(3, 2, 2, 'active', 'payment', NULL, 1, '2025-07-19 06:36:56', NULL, '2025-07-19 04:23:13', '2025-07-19 04:37:32'),
(5, 1, 2, 'active', 'payment', NULL, 2, '2025-07-19 07:44:56', NULL, '2025-07-19 15:36:30', '2025-07-19 15:39:31'),
(7, 2, 3, 'active', 'code', 'COURSE_687B7336EACB1', NULL, '2025-07-19 12:32:23', NULL, '2025-07-19 20:26:46', '2025-07-19 20:26:57'),
(9, 3, 3, 'pending', 'payment', NULL, NULL, NULL, NULL, '2025-07-19 21:03:51', '2025-07-19 21:06:10'),
(11, 1, 3, 'pending', 'code', NULL, NULL, NULL, NULL, '2025-07-19 21:43:08', '2025-07-19 21:43:08');

-- --------------------------------------------------------

--
-- Table structure for table `course_videos`
--

CREATE TABLE `course_videos` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `week_number` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `video_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `video_platform` enum('youtube','vimeo','custom') COLLATE utf8mb4_unicode_ci DEFAULT 'youtube',
  `duration_minutes` int DEFAULT '0',
  `video_order` int DEFAULT '0',
  `timing_info` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_videos`
--

INSERT INTO `course_videos` (`id`, `course_id`, `week_number`, `title`, `description`, `video_url`, `video_platform`, `duration_minutes`, `video_order`, `timing_info`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'تجريبي', 'اليوم الاول فديو ترحيبي', 'https://youtu.be/uuIOrTtDYUs', 'youtube', 1, 1, 'اليوم الاول فديو ترحيبي', 1, 1, '2025-07-19 00:47:50', '2025-07-19 00:47:50'),
(2, 2, 1, 'الدرس الأول مقدمه عن علم الخط', 'الدرس الأول مقدمه عن علم الخط', 'https://youtu.be/uuIOrTtDYUs', 'youtube', 1, 1, 'الدرس الأول مقدمه عن علم الخط', 1, 1, '2025-07-19 04:16:07', '2025-07-19 04:16:07'),
(3, 3, 1, 'تجريبي', 'g', 'https://youtu.be/uuIOrTtDYUs', 'youtube', 1, 1, 'اليوم الاول فديو ترحيبي', 1, 1, '2025-07-20 16:57:04', '2025-07-20 16:57:04');

-- --------------------------------------------------------

--
-- Table structure for table `course_weekly_tests`
--

CREATE TABLE `course_weekly_tests` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int DEFAULT '30',
  `total_marks` decimal(5,2) DEFAULT '0.00',
  `passing_marks` decimal(5,2) DEFAULT '0.00',
  `week_number` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_weekly_tests`
--

INSERT INTO `course_weekly_tests` (`id`, `course_id`, `title`, `description`, `duration_minutes`, `total_marks`, `passing_marks`, `week_number`, `is_active`, `created_by`, `created_at`) VALUES
(1, 1, 'اختبار أسبوعي نهائي', 'اختبار أسبوعي بعد الإصلاح النهائي', 20, 6.00, 4.00, 1, 1, 1, '2025-07-19 01:23:33'),
(2, 3, 'تجريبي', 'tuyi', 30, 0.00, 0.00, 1, 1, 1, '2025-07-20 16:59:27');

-- --------------------------------------------------------

--
-- Table structure for table `course_weekly_test_questions`
--

CREATE TABLE `course_weekly_test_questions` (
  `id` int NOT NULL,
  `test_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('true_false','multiple_choice') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'true_false',
  `options` json DEFAULT NULL,
  `correct_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `explanation` text COLLATE utf8mb4_unicode_ci,
  `points` decimal(5,2) DEFAULT '1.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_weekly_test_questions`
--

INSERT INTO `course_weekly_test_questions` (`id`, `test_id`, `question_text`, `question_type`, `options`, `correct_answer`, `explanation`, `points`, `created_at`) VALUES
(1, 1, 'هل 5 > 3؟', 'true_false', NULL, 'true', 'نعم، 5 أكبر من 3', 3.00, '2025-07-19 01:23:33'),
(2, 1, 'كم عدد أيام الأسبوع؟', 'multiple_choice', '[\"5\", \"6\", \"7\", \"8\"]', '7', 'الأسبوع يحتوي على 7 أيام', 3.00, '2025-07-19 01:23:33'),
(3, 2, 'uyiy', 'multiple_choice', '[\"yuityuitt\", \"yutiyu\", \"tiyuiy\", \"tui\"]', 'yuityuitt', NULL, 1.00, '2025-07-20 16:59:27');

-- --------------------------------------------------------

--
-- Table structure for table `email_verification_tokens`
--

CREATE TABLE `email_verification_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `used` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exam_questions`
--

CREATE TABLE `exam_questions` (
  `id` int NOT NULL,
  `exam_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('multiple_choice','true_false','text') COLLATE utf8mb4_unicode_ci DEFAULT 'multiple_choice',
  `marks` int DEFAULT '1',
  `display_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exam_question_options`
--

CREATE TABLE `exam_question_options` (
  `id` int NOT NULL,
  `question_id` int NOT NULL,
  `option_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `display_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exercise_questions`
--

CREATE TABLE `exercise_questions` (
  `id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('multiple_choice','true_false','text') COLLATE utf8mb4_unicode_ci DEFAULT 'multiple_choice',
  `marks` int DEFAULT '1',
  `display_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `honor_board`
--

CREATE TABLE `honor_board` (
  `id` int NOT NULL,
  `student_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade_score` decimal(5,2) NOT NULL,
  `subject` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ranking_position` int NOT NULL,
  `achievement_type` enum('monthly','semester','yearly','special') COLLATE utf8mb4_unicode_ci DEFAULT 'monthly',
  `achievement_date` date NOT NULL,
  `education_level` enum('primary','preparatory','secondary') COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `additional_notes` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `honor_board`
--

INSERT INTO `honor_board` (`id`, `student_name`, `grade_score`, `subject`, `ranking_position`, `achievement_type`, `achievement_date`, `education_level`, `education_type`, `grade`, `specialization`, `additional_notes`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(3, 'تجريبي', 100.00, 'تجريبي', 1, 'monthly', '2025-07-17', 'primary', 'azhari', '5', NULL, 'تجريبي', 1, 1, '2025-07-17 02:33:49', '2025-07-17 02:33:49'),
(4, 'احمد محمد', 100.00, 'نحو', 2, 'special', '2025-07-19', 'primary', 'azhari', '5', NULL, 'نحو', 1, 1, '2025-07-19 04:31:24', '2025-07-19 04:31:24');

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int NOT NULL,
  `username_or_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `success` tinyint(1) NOT NULL,
  `attempted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `username_or_email`, `ip_address`, `success`, `attempted_at`) VALUES
(1, 'asa', '::1', 1, '2025-07-16 22:19:16'),
(2, 'asas', '::1', 1, '2025-07-16 22:24:17'),
(3, 'asas', '::1', 1, '2025-07-16 22:56:47'),
(4, 'asas', '::1', 1, '2025-07-17 02:03:59'),
(5, 'asas', '::1', 1, '2025-07-17 02:54:35'),
(6, 'admin', '::1', 0, '2025-07-17 03:15:44'),
(7, 'asas', '::1', 1, '2025-07-17 03:15:53'),
(8, 'admin', '::1', 0, '2025-07-17 03:42:39'),
(9, 'asas', '::1', 1, '2025-07-17 03:42:42'),
(10, 'asas', '::1', 1, '2025-07-17 04:19:52'),
(11, 'asas', '::1', 1, '2025-07-17 05:43:36'),
(12, 'asas', '::1', 1, '2025-07-17 14:54:12'),
(13, 'asas', '::1', 1, '2025-07-17 18:03:07'),
(14, 'asas', '::1', 1, '2025-07-17 18:13:04'),
(15, 'asas', '::1', 1, '2025-07-17 18:16:39'),
(16, 'asas', '::1', 1, '2025-07-17 19:36:57'),
(17, 'asa', '::1', 1, '2025-07-17 19:41:52'),
(18, 'asas', '::1', 1, '2025-07-17 19:43:45'),
(19, 'asas', '::1', 1, '2025-07-17 23:46:06'),
(20, 'asa', '::1', 1, '2025-07-18 00:49:55'),
(21, 'asa', '::1', 1, '2025-07-18 01:28:01'),
(22, 'asa', '::1', 1, '2025-07-18 10:56:53'),
(23, 'asa', '::1', 1, '2025-07-18 20:53:26'),
(24, 'asas', '::1', 1, '2025-07-18 23:37:53'),
(25, 'asas', '::1', 1, '2025-07-19 02:03:53'),
(26, 'asas', '::1', 1, '2025-07-19 02:19:06'),
(27, 'asas', '::1', 1, '2025-07-19 04:22:30'),
(28, 'asas', '::1', 1, '2025-07-19 15:56:15'),
(29, '<EMAIL>', '217.55.241.6', 1, '2025-07-19 16:47:54'),
(30, 'asas', '::1', 1, '2025-07-19 18:03:25'),
(31, 'lila', '154.236.101.45', 1, '2025-07-19 21:01:40'),
(32, 'asas', '::1', 1, '2025-07-20 01:11:22'),
(33, 'asas', '::1', 1, '2025-07-20 01:46:59'),
(34, 'asas', '::1', 1, '2025-07-20 01:49:24'),
(35, 'asas', '::1', 1, '2025-07-20 13:51:35'),
(36, 'asas', '::1', 1, '2025-07-20 19:51:41');

-- --------------------------------------------------------

--
-- Table structure for table `message_categories`
--

CREATE TABLE `message_categories` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'message',
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#4682B4',
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `message_categories`
--

INSERT INTO `message_categories` (`id`, `name`, `name_ar`, `description`, `icon`, `color`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'general', 'استفسار عام', 'استفسارات عامة حول المنصة والخدمات', 'help-circle', '#4682B4', 1, 1, '2025-07-20 22:15:45', '2025-07-20 22:15:45'),
(2, 'technical', 'مشكلة تقنية', 'مشاكل تقنية في استخدام المنصة', 'settings', '#dc3545', 1, 2, '2025-07-20 22:15:45', '2025-07-20 22:15:45'),
(3, 'course', 'استفسار عن كورس', 'أسئلة متعلقة بالكورسات والمحتوى', 'book', '#28a745', 1, 3, '2025-07-20 22:15:45', '2025-07-20 22:15:45'),
(4, 'payment', 'استفسار مالي', 'استفسارات حول الدفع والاشتراكات', 'credit-card', '#ffc107', 1, 4, '2025-07-20 22:15:45', '2025-07-20 22:15:45'),
(5, 'exam', 'استفسار عن امتحان', 'أسئلة متعلقة بالامتحانات والتقييمات', 'file-text', '#17a2b8', 1, 5, '2025-07-20 22:15:45', '2025-07-20 22:15:45'),
(6, 'complaint', 'شكوى', 'الشكاوى والمقترحات', 'alert-triangle', '#fd7e14', 1, 6, '2025-07-20 22:15:45', '2025-07-20 22:15:45'),
(7, 'suggestion', 'اقتراح', 'اقتراحات لتحسين المنصة', 'lightbulb', '#6f42c1', 1, 7, '2025-07-20 22:15:45', '2025-07-20 22:15:45');

-- --------------------------------------------------------

--
-- Table structure for table `message_responses`
--

CREATE TABLE `message_responses` (
  `id` int NOT NULL,
  `message_id` int NOT NULL,
  `admin_id` int NOT NULL,
  `response_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `response_type` enum('reply','internal_note','status_update') COLLATE utf8mb4_unicode_ci DEFAULT 'reply',
  `is_public` tinyint(1) DEFAULT '1',
  `attachment_count` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `admin_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('info','warning','success','error') COLLATE utf8mb4_unicode_ci DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT '0',
  `is_global` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `admin_id`, `title`, `message`, `type`, `is_read`, `is_global`, `created_at`, `read_at`) VALUES
(9, 3, 1, 'مرحباً بك في سلسلة الدكتور', 'أهلاً وسهلاً ليلي! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.', 'success', 0, 0, '2025-07-19 21:01:09', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `used` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_requests`
--

CREATE TABLE `payment_requests` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `receipt_image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mobile_number` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `transfer_method` enum('vodafone_cash','etisalat_cash','we_cash','orange_cash') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','approved','rejected') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `admin_notes` text COLLATE utf8mb4_unicode_ci,
  `reviewed_by` int DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_requests`
--

INSERT INTO `payment_requests` (`id`, `user_id`, `course_id`, `receipt_image`, `mobile_number`, `transfer_method`, `status`, `admin_notes`, `reviewed_by`, `reviewed_at`, `created_at`, `updated_at`) VALUES
(1, 2, 2, 'receipt_2_2_687b1da67ee4d.png', '01126130559', 'vodafone_cash', 'approved', '', 1, '2025-07-19 04:37:32', '2025-07-19 04:23:39', '2025-07-19 04:37:32'),
(2, 1, 2, 'receipt_1_2_687b30be609f9.png', '01126130559', 'vodafone_cash', 'approved', '', 1, '2025-07-19 15:39:31', '2025-07-19 15:39:04', '2025-07-19 15:39:31'),
(3, 3, 3, 'receipt_3_3_687c08c252e1f.jpg', '01126130559', 'vodafone_cash', 'pending', NULL, NULL, NULL, '2025-07-19 21:06:10', '2025-07-19 21:06:10');

-- --------------------------------------------------------

--
-- Table structure for table `question_options`
--

CREATE TABLE `question_options` (
  `id` int NOT NULL,
  `question_id` int NOT NULL,
  `option_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `display_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exam_answers`
--

CREATE TABLE `student_exam_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `selected_option_id` int DEFAULT NULL,
  `text_answer` text COLLATE utf8mb4_unicode_ci,
  `is_correct` tinyint(1) DEFAULT '0',
  `marks_awarded` int DEFAULT '0',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exam_attempts`
--

CREATE TABLE `student_exam_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exam_id` int NOT NULL,
  `course_id` int NOT NULL,
  `attempt_number` int DEFAULT '1',
  `score` int DEFAULT '0',
  `total_marks` int NOT NULL,
  `percentage` decimal(5,2) DEFAULT '0.00',
  `is_passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exercise_answers`
--

CREATE TABLE `student_exercise_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `selected_option_id` int DEFAULT NULL,
  `text_answer` text COLLATE utf8mb4_unicode_ci,
  `is_correct` tinyint(1) DEFAULT '0',
  `marks_awarded` int DEFAULT '0',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exercise_attempts`
--

CREATE TABLE `student_exercise_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `course_id` int NOT NULL,
  `attempt_number` int DEFAULT '1',
  `score` int DEFAULT '0',
  `total_marks` int NOT NULL,
  `percentage` decimal(5,2) DEFAULT '0.00',
  `is_passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_messages`
--

CREATE TABLE `student_messages` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `category_id` int NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `priority` enum('low','medium','high','urgent') COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `status` enum('pending','in_progress','resolved','closed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `is_anonymous` tinyint(1) DEFAULT '0',
  `contact_method` enum('platform','email','whatsapp','phone') COLLATE utf8mb4_unicode_ci DEFAULT 'platform',
  `contact_info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `course_id` int DEFAULT NULL,
  `attachment_count` int DEFAULT '0',
  `admin_assigned` int DEFAULT NULL,
  `first_response_at` timestamp NULL DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `student_messages`
--

INSERT INTO `student_messages` (`id`, `user_id`, `category_id`, `subject`, `message`, `priority`, `status`, `is_anonymous`, `contact_method`, `contact_info`, `course_id`, `attachment_count`, `admin_assigned`, `first_response_at`, `resolved_at`, `created_at`, `updated_at`) VALUES
(1, 2, 5, 'تجريبي', 'تجريبي تجريبي', 'medium', 'pending', 0, 'platform', '', NULL, 0, NULL, NULL, NULL, '2025-07-20 22:17:19', '2025-07-20 22:17:19');

-- --------------------------------------------------------

--
-- Table structure for table `upcoming_exams`
--

CREATE TABLE `upcoming_exams` (
  `id` int NOT NULL,
  `exam_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `exam_date` date NOT NULL,
  `exam_time` time NOT NULL,
  `duration_minutes` int NOT NULL DEFAULT '120',
  `location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `instructions` text COLLATE utf8mb4_unicode_ci,
  `education_level` enum('primary','preparatory','secondary') COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `upcoming_exams`
--

INSERT INTO `upcoming_exams` (`id`, `exam_name`, `subject`, `exam_date`, `exam_time`, `duration_minutes`, `location`, `instructions`, `education_level`, `education_type`, `grade`, `specialization`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'امتحان تجريبي', 'الرياضيات', '2025-07-24', '09:00:00', 120, 'القاعة الرئيسية', 'تعليمات الامتحان التجريبي', 'secondary', 'general', '3', 'scientific', 1, 1, '2025-07-17 01:30:12', '2025-07-17 01:30:12'),
(2, 'امتحان الرياضيات', 'الرياضيات', '2024-12-25', '10:00:00', 120, 'القاعة الرئيسية', 'تعليمات الامتحان', 'secondary', 'general', '3', 'scientific', 1, 1, '2025-07-17 02:11:20', '2025-07-17 02:11:20'),
(3, 'تجريبي', 'تجريبي', '2025-07-24', '08:10:00', 123, 'تجريبي', 'تجريبي', 'primary', 'azhari', '5', NULL, 1, 1, '2025-07-17 02:18:26', '2025-07-17 02:18:26'),
(4, 'امتحان نحو', 'نحو', '2025-07-25', '10:30:00', 120, 'المنصه', 'سوف يتم الامتحان في النحو', 'primary', 'azhari', '5', NULL, 1, 1, '2025-07-19 04:28:27', '2025-07-19 04:28:27');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `full_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `second_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `third_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fourth_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` enum('male','female') COLLATE utf8mb4_unicode_ci NOT NULL,
  `birth_date` date NOT NULL,
  `personal_phone` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `father_phone` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mother_phone` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_level` enum('primary','preparatory','secondary') COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `email_verified` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `phone`, `password`, `username`, `email`, `password_hash`, `first_name`, `second_name`, `third_name`, `fourth_name`, `gender`, `birth_date`, `personal_phone`, `father_phone`, `mother_phone`, `education_level`, `education_type`, `grade`, `specialization`, `is_active`, `email_verified`, `created_at`, `updated_at`, `last_login`) VALUES
(1, 'تجريبي تجريبي تجريبي تجريبي', '01126130559', '$2y$10$8/7mAXdKFgtmVLsxUXEGdOyqSwCKw842LoUoJIoT62h91hLpnsAvu', 'asa', '<EMAIL>', '$2y$10$8/7mAXdKFgtmVLsxUXEGdOyqSwCKw842LoUoJIoT62h91hLpnsAvu', 'تجريبي', 'تجريبي', 'تجريبي', 'تجريبي', 'male', '2010-03-17', '01126130559', '01126136055', '01126164564', 'preparatory', 'azhari', '2', NULL, 1, 0, '2025-07-16 22:19:09', '2025-07-18 20:53:26', '2025-07-18 20:53:26'),
(2, 'تجريبي تجريبي تجريبي تجريبي', '01126130559', '$2y$10$HlqjoNQYxmRmSFZtax84UOA3ef2rqVJdcgphvDsQbRSFeBx1WOIHe', 'asas', '<EMAIL>', '$2y$10$HlqjoNQYxmRmSFZtax84UOA3ef2rqVJdcgphvDsQbRSFeBx1WOIHe', 'تجريبي', 'تجريبي', 'تجريبي', 'تجريبي', 'male', '2011-03-17', '01126130559', '01126136055', '01126164564', 'primary', 'azhari', '5', NULL, 1, 0, '2025-07-16 22:24:08', '2025-07-20 19:51:41', '2025-07-20 19:51:41'),
(3, NULL, NULL, NULL, 'lila', '<EMAIL>', '$2y$10$Dr.kkwO69DZaO62tILEn/.eHDepc.VZhhqlXTQ2119z0n0KjjQehm', 'ليل', 'إبراهيم', 'حسن', 'إبراهيم', 'female', '2011-07-19', '01126130559', '01126130559', '01126130559', 'secondary', 'azhari', '3', 'scientific', 1, 0, '2025-07-19 21:01:08', '2025-07-19 21:07:10', '2025-07-19 21:01:40');

-- --------------------------------------------------------

--
-- Table structure for table `user_activity_log`
--

CREATE TABLE `user_activity_log` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `activity_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `activity_description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` json DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_activity_log`
--

INSERT INTO `user_activity_log` (`id`, `user_id`, `activity_type`, `activity_description`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-16 22:19:20'),
(2, 2, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********', '2025-07-16 22:24:20'),
(3, 2, 'todo_create', 'تم إضافة مهمة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"due_date\": \"2025-07-30\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-17 00:17:03'),
(4, 2, 'note_create', 'تم إضافة ملاحظة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"category\": \"personal\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-17 00:17:38'),
(5, 2, 'todo_delete', 'تم حذف المهمة: تجريبي', '{\"title\": \"تجريبي\", \"todo_id\": 1}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-17 03:28:03'),
(6, 2, 'note_delete', 'تم حذف الملاحظة: تجريبي', '{\"title\": \"تجريبي\", \"note_id\": 1}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-17 03:28:19'),
(7, 2, 'todo_create', 'تم إضافة مهمة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"due_date\": \"2025-07-30\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-17 03:46:44'),
(8, 3, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '154.236.101.45', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-19 21:01:42'),
(9, 3, 'profile_update', 'تم تحديث الملف الشخصي', '{\"updated_fields\": [\"first_name\", \"second_name\", \"third_name\", \"fourth_name\", \"personal_phone\", \"father_phone\", \"mother_phone\", \"education_level\", \"education_type\", \"grade\", \"specialization\"]}', '154.236.101.45', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-07-19 21:07:10'),
(10, 2, 'todo_create', 'تم إضافة مهمة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"due_date\": \"2025-07-29\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 15:19:40'),
(11, 2, 'note_create', 'تم إضافة ملاحظة جديدة: فغق', '{\"title\": \"فغق\", \"category\": \"general\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 15:20:29'),
(12, 2, 'todo_complete', 'إكمال المهمة: تجريبي', '{\"title\": \"تجريبي\", \"todo_id\": 3, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 15:26:36'),
(13, 2, 'todo_complete', 'إكمال المهمة: تجريبي', '{\"title\": \"تجريبي\", \"todo_id\": 2, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 15:26:38'),
(14, 2, 'note_pin', 'تثبيت الملاحظة: فغق', '{\"title\": \"فغق\", \"pinned\": true, \"note_id\": 2}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 15:27:14'),
(15, 2, 'todo_create', 'تم إضافة مهمة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"due_date\": \"2025-07-29\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 15:27:31'),
(16, 2, 'todo_complete', 'إكمال المهمة: تجريبي', '{\"title\": \"تجريبي\", \"todo_id\": 4, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 16:26:30'),
(17, 2, 'todo_create', 'تم إضافة مهمة جديدة: لبيب', '{\"title\": \"لبيب\", \"due_date\": \"2025-07-29\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 16:34:59'),
(18, 2, 'todo_create', 'تم إضافة مهمة جديدة: لبيب', '{\"title\": \"لبيب\", \"due_date\": \"2025-07-29\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 16:49:31'),
(19, 2, 'todo_complete', 'إكمال المهمة: لبيب', '{\"title\": \"لبيب\", \"todo_id\": 6, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 16:49:42'),
(20, 2, 'todo_complete', 'إكمال المهمة: لبيب', '{\"title\": \"لبيب\", \"todo_id\": 5, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-20 16:49:46');

-- --------------------------------------------------------

--
-- Table structure for table `user_content_attempts`
--

CREATE TABLE `user_content_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_type` enum('exercise','exam','weekly_test') COLLATE utf8mb4_unicode_ci NOT NULL,
  `content_id` int NOT NULL,
  `attempt_number` int DEFAULT '1',
  `score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) DEFAULT '0.00',
  `percentage` decimal(5,2) DEFAULT '0.00',
  `time_taken_minutes` int DEFAULT '0',
  `is_passed` tinyint(1) DEFAULT '0',
  `is_completed` tinyint(1) DEFAULT '0',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_answers`
--

CREATE TABLE `user_exam_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_answers_simple`
--

CREATE TABLE `user_exam_answers_simple` (
  `id` int NOT NULL,
  `user_id` int NOT NULL DEFAULT '1',
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `score` decimal(5,2) DEFAULT '0.00',
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exam_answers_simple`
--

INSERT INTO `user_exam_answers_simple` (`id`, `user_id`, `question_id`, `user_answer`, `is_correct`, `score`, `attempt_number`, `created_at`) VALUES
(13, 2, 1, 'true', 1, 5.00, 1, '2025-07-19 19:53:01'),
(14, 2, 2, '9', 1, 5.00, 1, '2025-07-19 19:53:03'),
(16, 2, 7, 'false', 0, 0.00, 1, '2025-07-19 20:00:51'),
(18, 2, 6, 'false', 0, 0.00, 1, '2025-07-19 20:03:05'),
(22, 2, 8, 'true', 0, 0.00, 1, '2025-07-20 02:36:58'),
(23, 2, 9, 'false', 0, 0.00, 1, '2025-07-20 02:36:58'),
(24, 2, 10, 'الثالث', 0, 0.00, 1, '2025-07-20 02:36:59'),
(26, 2, 11, 'false', 0, 0.00, 1, '2025-07-20 17:00:50'),
(29, 2, 12, 'true', 1, 1.00, 1, '2025-07-20 17:30:26'),
(30, 2, 13, 'عربي', 1, 1.00, 1, '2025-07-20 17:30:26');

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_attempts`
--

CREATE TABLE `user_exam_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exam_id` int NOT NULL,
  `total_score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) DEFAULT '0.00',
  `percentage` decimal(5,2) DEFAULT '0.00',
  `passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `attempt_number` int DEFAULT '1',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_answers`
--

CREATE TABLE `user_exercise_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_attempts`
--

CREATE TABLE `user_exercise_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exercise_attempts`
--

INSERT INTO `user_exercise_attempts` (`id`, `user_id`, `exercise_id`, `user_answer`, `is_correct`, `attempt_number`, `created_at`) VALUES
(1, 2, 5, 'true', 1, 1, '2025-07-19 16:09:03'),
(2, 2, 4, 'true', 1, 1, '2025-07-19 16:12:41'),
(3, 2, 3, 'false', 1, 1, '2025-07-19 16:41:35'),
(4, 2, 9, 'true', 1, 1, '2025-07-19 19:06:19'),
(5, 2, 1, 'true', 1, 1, '2025-07-19 19:53:17'),
(6, 2, 2, 'false', 1, 1, '2025-07-19 19:59:35'),
(7, 2, 3, 'false', 1, 2, '2025-07-19 19:59:45'),
(8, 2, 4, 'true', 1, 2, '2025-07-19 19:59:53'),
(9, 2, 10, 'عربي', 1, 1, '2025-07-19 20:27:23'),
(10, 2, 11, 'false', 1, 1, '2025-07-19 20:27:43'),
(11, 2, 11, 'false', 1, 2, '2025-07-20 02:30:06'),
(12, 2, 10, 'عربي', 1, 2, '2025-07-20 16:50:23'),
(13, 2, 11, 'false', 1, 3, '2025-07-20 16:50:42'),
(14, 2, 12, 'ljiklhjilhuil', 1, 1, '2025-07-20 17:00:20');

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_question_answers`
--

CREATE TABLE `user_exercise_question_answers` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `score` decimal(5,2) DEFAULT '0.00',
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_notes`
--

CREATE TABLE `user_notes` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'general',
  `is_pinned` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_notes`
--

INSERT INTO `user_notes` (`id`, `user_id`, `title`, `content`, `category`, `is_pinned`, `created_at`, `updated_at`) VALUES
(2, 2, 'فغق', 'غعغف', 'general', 1, '2025-07-20 15:20:29', '2025-07-20 15:27:14');

-- --------------------------------------------------------

--
-- Table structure for table `user_notifications`
--

CREATE TABLE `user_notifications` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` json DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_question_answers`
--

CREATE TABLE `user_question_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci,
  `is_correct` tinyint(1) DEFAULT '0',
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `session_token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_todos`
--

CREATE TABLE `user_todos` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_completed` tinyint(1) DEFAULT '0',
  `priority` enum('low','medium','high') COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `due_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_todos`
--

INSERT INTO `user_todos` (`id`, `user_id`, `title`, `description`, `is_completed`, `priority`, `due_date`, `created_at`, `updated_at`, `completed_at`) VALUES
(2, 2, 'تجريبي', 'تجريبي', 1, 'medium', '2025-07-30', '2025-07-17 03:46:44', '2025-07-20 15:26:38', NULL),
(3, 2, 'تجريبي', 'قفقف', 1, 'medium', '2025-07-29', '2025-07-20 15:19:40', '2025-07-20 15:26:36', NULL),
(4, 2, 'تجريبي', 'غع', 1, 'medium', '2025-07-29', '2025-07-20 15:27:31', '2025-07-20 16:26:29', NULL),
(5, 2, 'لبيب', 'بلبلبل', 1, 'medium', '2025-07-29', '2025-07-20 16:34:59', '2025-07-20 16:49:45', NULL),
(6, 2, 'لبيب', 'غفغ', 1, 'medium', '2025-07-29', '2025-07-20 16:49:31', '2025-07-20 16:49:42', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_video_progress`
--

CREATE TABLE `user_video_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `video_id` int NOT NULL,
  `progress_percentage` decimal(5,2) DEFAULT '0.00',
  `last_position_seconds` int DEFAULT '0',
  `completed` tinyint(1) DEFAULT '0',
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_weekly_test_answers`
--

CREATE TABLE `user_weekly_test_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_weekly_test_answers_simple`
--

CREATE TABLE `user_weekly_test_answers_simple` (
  `id` int NOT NULL,
  `user_id` int NOT NULL DEFAULT '1',
  `question_id` int NOT NULL,
  `user_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `score` decimal(5,2) DEFAULT '0.00',
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_weekly_test_answers_simple`
--

INSERT INTO `user_weekly_test_answers_simple` (`id`, `user_id`, `question_id`, `user_answer`, `is_correct`, `score`, `attempt_number`, `created_at`) VALUES
(9, 2, 1, 'true', 1, 3.00, 1, '2025-07-19 19:10:31'),
(10, 2, 2, '7', 1, 3.00, 1, '2025-07-19 19:10:31');

-- --------------------------------------------------------

--
-- Table structure for table `user_weekly_test_attempts`
--

CREATE TABLE `user_weekly_test_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `test_id` int NOT NULL,
  `total_score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) DEFAULT '0.00',
  `percentage` decimal(5,2) DEFAULT '0.00',
  `time_taken_minutes` int DEFAULT '0',
  `attempt_number` int DEFAULT '1',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `video_watch_progress`
--

CREATE TABLE `video_watch_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `video_id` int NOT NULL,
  `course_id` int NOT NULL,
  `watch_percentage` decimal(5,2) DEFAULT '0.00',
  `is_completed` tinyint(1) DEFAULT '0',
  `last_position_seconds` int DEFAULT '0',
  `total_watch_time_seconds` int DEFAULT '0',
  `first_watched_at` timestamp NULL DEFAULT NULL,
  `last_watched_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activation_codes`
--
ALTER TABLE `activation_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_code` (`code`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username_email` (`username_or_email`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_attempted_at` (`attempted_at`);

--
-- Indexes for table `courses`
--
ALTER TABLE `courses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `course_content`
--
ALTER TABLE `course_content`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_course_week` (`course_id`,`week_number`),
  ADD KEY `idx_content_order` (`content_order`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_enrollments`
--
ALTER TABLE `course_enrollments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`);

--
-- Indexes for table `course_exams`
--
ALTER TABLE `course_exams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_week_number` (`week_number`);

--
-- Indexes for table `course_exam_questions`
--
ALTER TABLE `course_exam_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exam_id` (`exam_id`);

--
-- Indexes for table `course_exercises`
--
ALTER TABLE `course_exercises`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_course_week` (`course_id`,`week_number`),
  ADD KEY `idx_exercise_order` (`exercise_order`),
  ADD KEY `idx_question_type` (`question_type`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_exercise_questions`
--
ALTER TABLE `course_exercise_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exercise_id` (`exercise_id`),
  ADD KEY `idx_question_order` (`exercise_id`,`question_order`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `course_subscriptions`
--
ALTER TABLE `course_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_course` (`user_id`,`course_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_activation_status` (`activation_status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `course_videos`
--
ALTER TABLE `course_videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_course_week` (`course_id`,`week_number`),
  ADD KEY `idx_video_order` (`video_order`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_weekly_tests`
--
ALTER TABLE `course_weekly_tests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_week_number` (`week_number`);

--
-- Indexes for table `course_weekly_test_questions`
--
ALTER TABLE `course_weekly_test_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_test_id` (`test_id`);

--
-- Indexes for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exam_id` (`exam_id`),
  ADD KEY `idx_question_type` (`question_type`),
  ADD KEY `idx_display_order` (`display_order`);

--
-- Indexes for table `exam_question_options`
--
ALTER TABLE `exam_question_options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `exercise_questions`
--
ALTER TABLE `exercise_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exercise_id` (`exercise_id`),
  ADD KEY `idx_question_type` (`question_type`),
  ADD KEY `idx_display_order` (`display_order`);

--
-- Indexes for table `honor_board`
--
ALTER TABLE `honor_board`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_ranking` (`subject`,`achievement_type`,`achievement_date`,`education_level`,`education_type`,`grade`,`ranking_position`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_ranking_position` (`ranking_position`),
  ADD KEY `idx_achievement_date` (`achievement_date`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_achievement_type` (`achievement_type`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username_email` (`username_or_email`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_attempted_at` (`attempted_at`);

--
-- Indexes for table `message_categories`
--
ALTER TABLE `message_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_display_order` (`display_order`);

--
-- Indexes for table `message_responses`
--
ALTER TABLE `message_responses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message_id` (`message_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_is_global` (`is_global`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `payment_requests`
--
ALTER TABLE `payment_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `reviewed_by` (`reviewed_by`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `question_options`
--
ALTER TABLE `question_options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `student_exam_answers`
--
ALTER TABLE `student_exam_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `selected_option_id` (`selected_option_id`),
  ADD KEY `idx_attempt_id` (`attempt_id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `student_exam_attempts`
--
ALTER TABLE `student_exam_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_is_passed` (`is_passed`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- Indexes for table `student_exercise_answers`
--
ALTER TABLE `student_exercise_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `selected_option_id` (`selected_option_id`),
  ADD KEY `idx_attempt_id` (`attempt_id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `student_exercise_attempts`
--
ALTER TABLE `student_exercise_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_is_passed` (`is_passed`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- Indexes for table `student_messages`
--
ALTER TABLE `student_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `upcoming_exams`
--
ALTER TABLE `upcoming_exams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_exam_date` (`exam_date`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_activity_type` (`activity_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_content_attempts`
--
ALTER TABLE `user_content_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_content` (`user_id`,`content_type`,`content_id`),
  ADD KEY `idx_completion` (`is_completed`),
  ADD KEY `idx_started_at` (`started_at`);

--
-- Indexes for table `user_exam_answers`
--
ALTER TABLE `user_exam_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `user_exam_answers_simple`
--
ALTER TABLE `user_exam_answers_simple`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_question` (`user_id`,`question_id`),
  ADD KEY `idx_attempt` (`user_id`,`question_id`,`attempt_number`);

--
-- Indexes for table `user_exam_attempts`
--
ALTER TABLE `user_exam_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_passed` (`passed`),
  ADD KEY `idx_completed_at` (`completed_at`);

--
-- Indexes for table `user_exercise_answers`
--
ALTER TABLE `user_exercise_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_id` (`attempt_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `user_exercise_attempts`
--
ALTER TABLE `user_exercise_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_is_correct` (`is_correct`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_exercise_question_answers`
--
ALTER TABLE `user_exercise_question_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_question_attempt` (`user_id`,`question_id`,`attempt_number`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_user_question` (`user_id`,`question_id`),
  ADD KEY `idx_attempt` (`user_id`,`exercise_id`,`attempt_number`);

--
-- Indexes for table `user_notes`
--
ALTER TABLE `user_notes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_pinned` (`is_pinned`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_user_unread` (`user_id`,`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_question_answers`
--
ALTER TABLE `user_question_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_session_token` (`session_token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `user_todos`
--
ALTER TABLE `user_todos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_is_completed` (`is_completed`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_video` (`user_id`,`video_id`),
  ADD KEY `video_id` (`video_id`),
  ADD KEY `idx_user_video` (`user_id`,`video_id`),
  ADD KEY `idx_completed` (`completed`);

--
-- Indexes for table `user_weekly_test_answers`
--
ALTER TABLE `user_weekly_test_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `user_weekly_test_answers_simple`
--
ALTER TABLE `user_weekly_test_answers_simple`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_question` (`user_id`,`question_id`),
  ADD KEY `idx_attempt` (`user_id`,`question_id`,`attempt_number`);

--
-- Indexes for table `user_weekly_test_attempts`
--
ALTER TABLE `user_weekly_test_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `test_id` (`test_id`),
  ADD KEY `idx_user_test` (`user_id`,`test_id`),
  ADD KEY `idx_completed_at` (`completed_at`);

--
-- Indexes for table `video_watch_progress`
--
ALTER TABLE `video_watch_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_video` (`user_id`,`video_id`),
  ADD KEY `video_id` (`video_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_video` (`user_id`,`video_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activation_codes`
--
ALTER TABLE `activation_codes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `courses`
--
ALTER TABLE `courses`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `course_content`
--
ALTER TABLE `course_content`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `course_enrollments`
--
ALTER TABLE `course_enrollments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `course_exams`
--
ALTER TABLE `course_exams`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `course_exam_questions`
--
ALTER TABLE `course_exam_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `course_exercises`
--
ALTER TABLE `course_exercises`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `course_exercise_questions`
--
ALTER TABLE `course_exercise_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `course_subscriptions`
--
ALTER TABLE `course_subscriptions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `course_videos`
--
ALTER TABLE `course_videos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `course_weekly_tests`
--
ALTER TABLE `course_weekly_tests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `course_weekly_test_questions`
--
ALTER TABLE `course_weekly_test_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exam_questions`
--
ALTER TABLE `exam_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exam_question_options`
--
ALTER TABLE `exam_question_options`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exercise_questions`
--
ALTER TABLE `exercise_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `honor_board`
--
ALTER TABLE `honor_board`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT for table `message_categories`
--
ALTER TABLE `message_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `message_responses`
--
ALTER TABLE `message_responses`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_requests`
--
ALTER TABLE `payment_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `question_options`
--
ALTER TABLE `question_options`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exam_answers`
--
ALTER TABLE `student_exam_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exam_attempts`
--
ALTER TABLE `student_exam_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exercise_answers`
--
ALTER TABLE `student_exercise_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exercise_attempts`
--
ALTER TABLE `student_exercise_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_messages`
--
ALTER TABLE `student_messages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `upcoming_exams`
--
ALTER TABLE `upcoming_exams`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `user_content_attempts`
--
ALTER TABLE `user_content_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exam_answers`
--
ALTER TABLE `user_exam_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exam_answers_simple`
--
ALTER TABLE `user_exam_answers_simple`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `user_exam_attempts`
--
ALTER TABLE `user_exam_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exercise_answers`
--
ALTER TABLE `user_exercise_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exercise_attempts`
--
ALTER TABLE `user_exercise_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `user_exercise_question_answers`
--
ALTER TABLE `user_exercise_question_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_notes`
--
ALTER TABLE `user_notes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user_notifications`
--
ALTER TABLE `user_notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_question_answers`
--
ALTER TABLE `user_question_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_todos`
--
ALTER TABLE `user_todos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_weekly_test_answers`
--
ALTER TABLE `user_weekly_test_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_weekly_test_answers_simple`
--
ALTER TABLE `user_weekly_test_answers_simple`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `user_weekly_test_attempts`
--
ALTER TABLE `user_weekly_test_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `video_watch_progress`
--
ALTER TABLE `video_watch_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

-- --------------------------------------------------------

--
-- Structure for view `course_statistics`
--
DROP TABLE IF EXISTS `course_statistics`;

CREATE ALGORITHM=UNDEFINED DEFINER=`uazcdist8yhd7872`@`%` SQL SECURITY DEFINER VIEW `course_statistics`  AS SELECT `c`.`id` AS `course_id`, `c`.`title` AS `title`, count(distinct `cs`.`user_id`) AS `total_subscribers`, count(distinct (case when (`cs`.`activation_status` = 'active') then `cs`.`user_id` end)) AS `active_subscribers`, count(distinct (case when (`cs`.`activation_status` = 'pending') then `cs`.`user_id` end)) AS `pending_subscribers`, count(distinct `cv`.`id`) AS `total_videos`, count(distinct `ce`.`id`) AS `total_exercises`, count(distinct `cex`.`id`) AS `total_exams`, count(distinct `cwt`.`id`) AS `total_weekly_tests`, `c`.`created_at` AS `created_at` FROM (((((`courses` `c` left join `course_subscriptions` `cs` on((`c`.`id` = `cs`.`course_id`))) left join `course_videos` `cv` on(((`c`.`id` = `cv`.`course_id`) and (`cv`.`is_active` = true)))) left join `course_exercises` `ce` on(((`c`.`id` = `ce`.`course_id`) and (`ce`.`is_active` = true)))) left join `course_exams` `cex` on(((`c`.`id` = `cex`.`course_id`) and (`cex`.`is_active` = true)))) left join `course_weekly_tests` `cwt` on(((`c`.`id` = `cwt`.`course_id`) and (`cwt`.`is_active` = true)))) WHERE (`c`.`is_active` = true) GROUP BY `c`.`id`, `c`.`title`, `c`.`created_at` ;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activation_codes`
--
ALTER TABLE `activation_codes`
  ADD CONSTRAINT `activation_codes_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `activation_codes_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD CONSTRAINT `admin_activity_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `courses`
--
ALTER TABLE `courses`
  ADD CONSTRAINT `courses_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_content`
--
ALTER TABLE `course_content`
  ADD CONSTRAINT `course_content_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_content_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_exercises`
--
ALTER TABLE `course_exercises`
  ADD CONSTRAINT `course_exercises_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_exercises_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_exercise_questions`
--
ALTER TABLE `course_exercise_questions`
  ADD CONSTRAINT `course_exercise_questions_ibfk_1` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_subscriptions`
--
ALTER TABLE `course_subscriptions`
  ADD CONSTRAINT `course_subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_subscriptions_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_videos`
--
ALTER TABLE `course_videos`
  ADD CONSTRAINT `course_videos_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_videos_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  ADD CONSTRAINT `email_verification_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD CONSTRAINT `exam_questions_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `course_exams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exam_question_options`
--
ALTER TABLE `exam_question_options`
  ADD CONSTRAINT `exam_question_options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `exam_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exercise_questions`
--
ALTER TABLE `exercise_questions`
  ADD CONSTRAINT `exercise_questions_ibfk_1` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `honor_board`
--
ALTER TABLE `honor_board`
  ADD CONSTRAINT `honor_board_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `message_responses`
--
ALTER TABLE `message_responses`
  ADD CONSTRAINT `message_responses_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `student_messages` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payment_requests`
--
ALTER TABLE `payment_requests`
  ADD CONSTRAINT `payment_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_requests_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_requests_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `question_options`
--
ALTER TABLE `question_options`
  ADD CONSTRAINT `question_options_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `exercise_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_exam_answers`
--
ALTER TABLE `student_exam_answers`
  ADD CONSTRAINT `student_exam_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `student_exam_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `exam_questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `exam_question_options` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_exam_attempts`
--
ALTER TABLE `student_exam_attempts`
  ADD CONSTRAINT `student_exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `course_exams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_attempts_ibfk_3` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_exercise_answers`
--
ALTER TABLE `student_exercise_answers`
  ADD CONSTRAINT `student_exercise_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `student_exercise_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `exercise_questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `question_options` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_exercise_attempts`
--
ALTER TABLE `student_exercise_attempts`
  ADD CONSTRAINT `student_exercise_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_attempts_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_attempts_ibfk_3` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_messages`
--
ALTER TABLE `student_messages`
  ADD CONSTRAINT `student_messages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_messages_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `message_categories` (`id`) ON DELETE RESTRICT;

--
-- Constraints for table `upcoming_exams`
--
ALTER TABLE `upcoming_exams`
  ADD CONSTRAINT `upcoming_exams_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD CONSTRAINT `user_activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_content_attempts`
--
ALTER TABLE `user_content_attempts`
  ADD CONSTRAINT `user_content_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exam_answers`
--
ALTER TABLE `user_exam_answers`
  ADD CONSTRAINT `user_exam_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_exam_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exam_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `course_exam_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exam_attempts`
--
ALTER TABLE `user_exam_attempts`
  ADD CONSTRAINT `user_exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `course_exams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_answers`
--
ALTER TABLE `user_exercise_answers`
  ADD CONSTRAINT `user_exercise_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_exercise_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `exercise_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_attempts`
--
ALTER TABLE `user_exercise_attempts`
  ADD CONSTRAINT `user_exercise_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_attempts_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_question_answers`
--
ALTER TABLE `user_exercise_question_answers`
  ADD CONSTRAINT `user_exercise_question_answers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_question_answers_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_question_answers_ibfk_3` FOREIGN KEY (`question_id`) REFERENCES `course_exercise_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_notes`
--
ALTER TABLE `user_notes`
  ADD CONSTRAINT `user_notes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD CONSTRAINT `user_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_question_answers`
--
ALTER TABLE `user_question_answers`
  ADD CONSTRAINT `user_question_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_content_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_question_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `course_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_todos`
--
ALTER TABLE `user_todos`
  ADD CONSTRAINT `user_todos_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  ADD CONSTRAINT `user_video_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_video_progress_ibfk_2` FOREIGN KEY (`video_id`) REFERENCES `course_videos` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_weekly_test_answers`
--
ALTER TABLE `user_weekly_test_answers`
  ADD CONSTRAINT `user_weekly_test_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_weekly_test_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_weekly_test_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `course_weekly_test_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_weekly_test_attempts`
--
ALTER TABLE `user_weekly_test_attempts`
  ADD CONSTRAINT `user_weekly_test_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_weekly_test_attempts_ibfk_2` FOREIGN KEY (`test_id`) REFERENCES `course_weekly_tests` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `video_watch_progress`
--
ALTER TABLE `video_watch_progress`
  ADD CONSTRAINT `video_watch_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `video_watch_progress_ibfk_2` FOREIGN KEY (`video_id`) REFERENCES `course_videos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `video_watch_progress_ibfk_3` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
