-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: bl7tlhd6pqdrgnrw0tkj-mysql.services.clever-cloud.com:3306
-- Generation Time: Jul 25, 2025 at 02:31 AM
-- Server version: 8.0.22-13
-- PHP Version: 8.2.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `bl7tlhd6pqdrgnrw0tkj`
--

-- --------------------------------------------------------

--
-- Table structure for table `activation_codes`
--

CREATE TABLE `activation_codes` (
  `id` int NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود التفعيل',
  `plan_id` int NOT NULL COMMENT 'معرف الخطة',
  `is_used` tinyint(1) DEFAULT '0' COMMENT 'هل تم استخدام الكود',
  `used_by` int DEFAULT NULL COMMENT 'المستخدم الذي استخدم الكود',
  `used_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الاستخدام',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
  `created_by` int NOT NULL COMMENT 'منشئ الكود',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT 'ملاحظات',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `activation_codes`
--

INSERT INTO `activation_codes` (`id`, `code`, `plan_id`, `is_used`, `used_by`, `used_at`, `expires_at`, `created_by`, `notes`, `created_at`, `updated_at`) VALUES
(2, 'CTDHSVUY', 10, 1, 4, '2025-07-23 19:00:32', NULL, 1, '', '2025-07-23 19:00:15', '2025-07-23 19:00:32'),
(3, 'RO5O3W7S', 10, 1, 4, '2025-07-23 22:10:31', NULL, 11, '', '2025-07-23 22:10:20', '2025-07-23 22:10:31'),
(4, 'L87A6GUL', 10, 1, 7, '2025-07-25 02:06:40', NULL, 11, '', '2025-07-25 02:02:06', '2025-07-25 02:06:40');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('super_admin','admin','moderator') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `email`, `password_hash`, `full_name`, `role`, `is_active`, `created_at`, `updated_at`, `last_login`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'super_admin', 1, '2025-07-16 22:17:36', '2025-07-21 00:43:16', '2025-07-21 00:43:16'),
(11, 'mohamed92173', '<EMAIL>', '$2y$10$uAAWX6ThRE5TJ/WNQgMWNOAp2KB2xb10l3rYKYD34NpcxVpvxoif6', 'Mr.: Mohamed Abdallah', 'super_admin', 1, '2025-07-21 00:45:23', '2025-07-25 01:55:05', '2025-07-25 01:55:05');

-- --------------------------------------------------------

--
-- Table structure for table `admin_activity_log`
--

CREATE TABLE `admin_activity_log` (
  `id` int NOT NULL,
  `admin_id` int NOT NULL,
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_login_attempts`
--

CREATE TABLE `admin_login_attempts` (
  `id` int NOT NULL,
  `username_or_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `success` tinyint(1) NOT NULL,
  `attempted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_replies`
--

CREATE TABLE `admin_replies` (
  `id` int NOT NULL,
  `message_id` int NOT NULL,
  `admin_id` int DEFAULT NULL,
  `reply_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_public` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_replies`
--

INSERT INTO `admin_replies` (`id`, `message_id`, `admin_id`, `reply_text`, `is_public`, `created_at`) VALUES
(5, 3, 1, 'هذا رد اختبار تم إنشاؤه في 2025-07-21 20:16:00', 1, '2025-07-21 17:43:44'),
(6, 3, 1, 'رد اختبار مباشر - 20:17:20', 1, '2025-07-21 17:45:04'),
(7, 3, 1, 'رد اختبار من MessageManager - 20:17:20', 1, '2025-07-21 17:45:04'),
(8, 4, 1, 'رد اختبار نهائي - 20:19:59', 1, '2025-07-21 17:47:43'),
(9, 3, 11, 'ايبلايب', 1, '2025-07-21 17:50:16'),
(10, 4, 11, 'عغعع', 1, '2025-07-22 22:08:28');

-- --------------------------------------------------------

--
-- Table structure for table `center_activation_codes`
--

CREATE TABLE `center_activation_codes` (
  `id` int NOT NULL,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `plan_id` int NOT NULL,
  `max_uses` int NOT NULL DEFAULT '1',
  `used_count` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `courses`
--

CREATE TABLE `courses` (
  `id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `features` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `discount_percentage` decimal(5,2) DEFAULT '0.00',
  `discounted_price` decimal(10,2) GENERATED ALWAYS AS ((`price` - ((`price` * `discount_percentage`) / 100))) STORED,
  `main_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `modal_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `education_level` enum('primary','preparatory','secondary','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `education_type` enum('azhari','general','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `grade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `specialization` enum('scientific','literary','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `passing_grade` decimal(5,2) DEFAULT '60.00' COMMENT 'درجة النجاح المطلوبة بالنسبة المئوية'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `courses`
--

INSERT INTO `courses` (`id`, `title`, `subject`, `description`, `features`, `price`, `discount_percentage`, `main_image`, `modal_images`, `education_level`, `education_type`, `grade`, `specialization`, `is_active`, `created_by`, `created_at`, `updated_at`, `passing_grade`) VALUES
(1, 'تجريبي', 'تجريبي', '❤💖سلسلة الدكتور ترحب بكم في الكورس التجريبي❤💖', 'هذا الكورس تجريبي الاستخدام👍\r\nاستخدم المنصه لي تعلم الغة العربية🌹', 100.00, 10.00, 'course_687aead613964.png', '[\"course_687aead615f8d.png\"]', 'all', 'all', 'all', 'all', 0, 1, '2025-07-19 00:46:50', '2025-07-19 20:03:27', 60.00),
(2, 'كورس تحسين الكتابه', 'الخط', 'كورس متخصص في تحسين الخط العربي', 'كورس ممتاز \r\nيجب علي كل من خطه وحش ياخذه \r\nسوف تري تحسين من تاني اسبوع', 350.00, 50.00, 'course_687b16a815246.png', '[\"course_687b16a816b13.png\"]', 'all', 'all', 'all', 'all', 0, 1, '2025-07-19 03:53:48', '2025-07-19 20:03:24', 60.00),
(3, 'كورس تحسين الكتابه', 'خط', 'كورس متخصص في تحسين الخط و تحسين الكتابه ', 'اكثر من 100 طالب نجو\r\nشرح مستر محمد عبدالله', 700.00, 10.00, 'course_687b6ff6c6038.jpeg', '[\"course_687b6ff6c7a8d.jpeg\"]', 'all', 'all', 'all', 'all', 1, 1, '2025-07-19 20:08:49', '2025-07-19 20:08:49', 60.00);

-- --------------------------------------------------------

--
-- Table structure for table `course_activation_codes`
--

CREATE TABLE `course_activation_codes` (
  `id` int NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود التفعيل',
  `course_id` int NOT NULL COMMENT 'معرف الكورس',
  `is_used` tinyint(1) DEFAULT '0' COMMENT 'هل تم استخدام الكود',
  `used_by` int DEFAULT NULL COMMENT 'المستخدم الذي استخدم الكود',
  `used_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الاستخدام',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
  `created_by` int NOT NULL COMMENT 'منشئ الكود',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT 'ملاحظات',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `course_completion_notes`
--

CREATE TABLE `course_completion_notes` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `completion_date` datetime NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `course_content`
--

CREATE TABLE `course_content` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `week_number` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `content_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `course_enrollments`
--

CREATE TABLE `course_enrollments` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `status` enum('active','inactive','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `enrolled_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_enrollments`
--

INSERT INTO `course_enrollments` (`id`, `user_id`, `course_id`, `status`, `enrolled_at`) VALUES
(1, 1, 3, 'active', '2025-07-20 20:00:31'),
(2, 2, 3, 'active', '2025-07-20 20:01:24'),
(3, 6, 3, 'active', '2025-07-22 17:57:31');

-- --------------------------------------------------------

--
-- Table structure for table `course_exams`
--

CREATE TABLE `course_exams` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int DEFAULT '60',
  `total_marks` decimal(5,2) DEFAULT '0.00',
  `passing_marks` decimal(5,2) DEFAULT '0.00',
  `week_number` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exams`
--

INSERT INTO `course_exams` (`id`, `course_id`, `title`, `description`, `duration_minutes`, `total_marks`, `passing_marks`, `week_number`, `is_active`, `created_by`, `created_at`) VALUES
(1, 1, 'امتحان تجريبي نهائي', 'امتحان تجريبي بعد الإصلاح النهائي', 30, 10.00, 6.00, 1, 1, 1, '2025-07-19 01:23:33'),
(5, 2, 'امتحان علي الدرس الاول', 'ا', 60, 0.00, 60.00, 1, 1, 1, '2025-07-19 04:48:22'),
(6, 2, 'تجريبي', '88u', 60, 0.00, 60.00, 2, 1, 1, '2025-07-19 18:22:28'),
(7, 3, 'امتحان علي انواع الخط', 'امتحان علي الخط', 120, 0.00, 60.00, 1, 1, 1, '2025-07-19 20:21:47'),
(8, 3, 'kguik', 'ilugi', 60, 0.00, 60.00, 1, 1, 1, '2025-07-20 16:58:56'),
(9, 3, 'امتحان علي انواع الخط العربي', 'امتحان', 60, 0.00, 60.00, 1, 1, 1, '2025-07-20 17:14:25'),
(10, 3, 'بالا', 'ىبلا', 60, 0.00, 60.00, 2, 1, 1, '2025-07-20 17:59:10');

-- --------------------------------------------------------

--
-- Table structure for table `course_exam_questions`
--

CREATE TABLE `course_exam_questions` (
  `id` int NOT NULL,
  `exam_id` int NOT NULL,
  `question_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('true_false','multiple_choice') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'true_false',
  `options` json DEFAULT NULL,
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `explanation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `points` decimal(5,2) DEFAULT '1.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exam_questions`
--

INSERT INTO `course_exam_questions` (`id`, `exam_id`, `question_text`, `question_type`, `options`, `correct_answer`, `explanation`, `points`, `created_at`) VALUES
(1, 1, 'هل 2 + 2 = 4؟', 'true_false', NULL, 'true', 'نعم، 2 + 2 = 4', 5.00, '2025-07-19 01:23:33'),
(2, 1, 'ما هو ناتج 3 × 3؟', 'multiple_choice', '[\"6\", \"9\", \"12\"]', '9', '3 × 3 = 9', 5.00, '2025-07-19 01:23:33'),
(6, 5, 'لت', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-19 04:48:22'),
(7, 6, 'tt', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-19 18:22:28'),
(8, 7, 'هل الخط الثلث خط عربي', 'true_false', NULL, 'true', NULL, 5.00, '2025-07-19 20:21:47'),
(9, 7, 'هل الخط الصيني القديم خط عربي', 'true_false', NULL, 'true', NULL, 5.00, '2025-07-19 20:21:48'),
(10, 7, 'انواع الخط العربي', 'multiple_choice', '[\"الثالث\", \"الصيني القديم\", \"الانجليزي\", \"الفارسي\"]', 'الثالث', NULL, 5.00, '2025-07-19 20:21:48'),
(11, 8, 'iouiouio', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-20 16:58:56'),
(12, 9, 'هل الخط الثلث خط عربي', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-20 17:14:25'),
(13, 9, 'هل الخط الرقعه خط', 'multiple_choice', '[\"عربي\", \"فارسي\", \"اجنبي\", \"صيني\"]', 'عربي', NULL, 1.00, '2025-07-20 17:14:25'),
(14, 10, 'بؤللا', 'true_false', NULL, 'true', NULL, 1.00, '2025-07-20 17:59:10');

-- --------------------------------------------------------

--
-- Table structure for table `course_exercises`
--

CREATE TABLE `course_exercises` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `week_number` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'تمرين',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `question_type` enum('true_false','multiple_choice') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `explanation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `has_multiple_questions` tinyint(1) DEFAULT '0',
  `total_questions` int DEFAULT '1',
  `passing_score` decimal(5,2) DEFAULT '60.00',
  `exercise_order` int DEFAULT '0',
  `timing_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `points` decimal(5,2) DEFAULT '1.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exercises`
--

INSERT INTO `course_exercises` (`id`, `course_id`, `week_number`, `title`, `description`, `question_type`, `question_text`, `options`, `correct_answer`, `explanation`, `has_multiple_questions`, `total_questions`, `passing_score`, `exercise_order`, `timing_info`, `is_active`, `created_by`, `created_at`, `updated_at`, `points`) VALUES
(1, 1, 1, 'تمرين PHP', NULL, 'true_false', 'هل PHP لغة برمجة من جانب الخادم؟', NULL, 'true', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:29', '2025-07-19 15:33:29', 1.00),
(2, 1, 1, 'تمرين JavaScript', NULL, 'true_false', 'هل JavaScript تعمل فقط في المتصفح؟', NULL, 'false', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:29', '2025-07-19 15:33:29', 1.00),
(3, 1, 1, 'تمرين HTML', NULL, 'true_false', 'هل HTML لغة برمجة؟', NULL, 'false', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:29', '2025-07-19 15:33:29', 1.00),
(4, 1, 1, 'تمرين CSS', NULL, 'true_false', 'هل CSS تستخدم لتنسيق صفحات الويب؟', NULL, 'true', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:30', '2025-07-19 15:33:30', 1.00),
(5, 1, 1, 'تمرين MySQL', NULL, 'true_false', 'هل MySQL نوع من قواعد البيانات؟', NULL, 'true', NULL, 0, 1, 60.00, 0, NULL, 1, 1, '2025-07-19 15:33:30', '2025-07-19 15:33:30', 1.00),
(6, 2, 2, 'ممم', '', 'multiple_choice', 'تمرين متعدد الأسئلة', NULL, 'متعدد الأسئلة', NULL, 1, 2, 60.00, 1, 'مم', 1, 1, '2025-07-19 17:49:08', '2025-07-19 17:49:08', 1.00),
(9, 2, 2, 'تحو', 'تنمتنم', 'true_false', 'لتنم', NULL, 'true', 'تانمات', 0, 1, 60.00, 3, 'تن', 1, 1, '2025-07-19 19:05:14', '2025-07-19 19:05:14', 1.00),
(10, 3, 1, 'خط', 'تمرين علي الدرس الاول', 'multiple_choice', 'هل خط الرقعة', '[\"\\u0639\\u0631\\u0628\\u064a\",\"\\u0627\\u062c\\u0646\\u0628\\u064a\",\"\\u0635\\u064a\\u0646\\u064a\",\"\\u0641\\u0627\\u0631\\u0633\\u064a\"]', 'عربي', 'عربي', 0, 1, 60.00, 1, 'التمرين الاول', 1, 1, '2025-07-19 20:15:22', '2025-07-19 20:15:22', 1.00),
(11, 3, 1, 'خط', 'انواع الخط العربي', 'true_false', 'هل الخط الصيني القديم خط عربي', NULL, 'false', 'هذا الخط خط صيني اصوله صيني', 0, 1, 60.00, 2, 'التمرين الثاني', 1, 1, '2025-07-19 20:16:35', '2025-07-19 20:16:35', 1.00),
(12, 3, 1, 'jjh', 'hlh', 'multiple_choice', 'lhu', '[\"ljiklhjilhuil\",\"ulhil\",\"ilyuilyui\",\"lyiulyuil\"]', 'ljiklhjilhuil', 'yuilyui', 0, 1, 60.00, 3, 'nklhkj', 1, 1, '2025-07-20 16:58:26', '2025-07-20 16:58:26', 1.00);

-- --------------------------------------------------------

--
-- Table structure for table `course_exercise_questions`
--

CREATE TABLE `course_exercise_questions` (
  `id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `question_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('true_false','multiple_choice') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'true_false',
  `options` json DEFAULT NULL,
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `explanation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `points` decimal(5,2) DEFAULT '1.00',
  `question_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_exercise_questions`
--

INSERT INTO `course_exercise_questions` (`id`, `exercise_id`, `question_text`, `question_type`, `options`, `correct_answer`, `explanation`, `points`, `question_order`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 6, 'هت', 'multiple_choice', '[\"خهه\", \"خه\", \"خه\", \"هخ\"]', 'هخ', '', 1.00, 1, 1, 1, '2025-07-19 17:49:09', '2025-07-19 17:49:09'),
(2, 6, 'حخ0', 'multiple_choice', '[\"09\", \"90\", \"9\", \"حخ\"]', 'حخ', '', 1.00, 2, 1, 1, '2025-07-19 17:49:09', '2025-07-19 17:49:09');

-- --------------------------------------------------------

--
-- Table structure for table `course_subscriptions`
--

CREATE TABLE `course_subscriptions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `activation_status` enum('pending','active','expired','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `activation_method` enum('code','payment','online_payment','admin') COLLATE utf8mb4_unicode_ci DEFAULT 'code',
  `payment_gateway` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `activation_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_request_id` int DEFAULT NULL,
  `activated_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_subscriptions`
--

INSERT INTO `course_subscriptions` (`id`, `user_id`, `course_id`, `activation_status`, `activation_method`, `payment_gateway`, `transaction_id`, `activation_code`, `payment_request_id`, `activated_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 'active', 'code', NULL, NULL, 'COURSE_687AEBCC7D204', NULL, '2025-07-19 02:51:34', NULL, '2025-07-19 00:51:41', '2025-07-19 00:52:10'),
(3, 2, 2, 'active', 'payment', NULL, NULL, NULL, 1, '2025-07-19 06:36:56', NULL, '2025-07-19 04:23:13', '2025-07-19 04:37:32'),
(5, 1, 2, 'active', 'payment', NULL, NULL, NULL, 2, '2025-07-19 07:44:56', NULL, '2025-07-19 15:36:30', '2025-07-19 15:39:31'),
(7, 2, 3, 'active', 'code', NULL, NULL, 'COURSE_687B7336EACB1', NULL, '2025-07-19 12:32:23', NULL, '2025-07-19 20:26:46', '2025-07-19 20:26:57'),
(9, 3, 3, 'active', 'payment', NULL, NULL, NULL, 3, '2025-07-21 12:20:56', NULL, '2025-07-19 21:03:51', '2025-07-21 00:48:28'),
(11, 1, 3, 'pending', 'code', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-19 21:43:08', '2025-07-19 21:43:08'),
(12, 4, 3, 'pending', 'code', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 02:35:42', '2025-07-22 04:15:15'),
(13, 5, 3, 'pending', 'code', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-22 03:19:19', '2025-07-22 04:15:15'),
(14, 6, 3, 'active', 'code', NULL, NULL, 'COURSE_687B7336EACB1', NULL, '2025-07-22 12:25:03', NULL, '2025-07-22 17:50:34', '2025-07-22 17:50:35');

-- --------------------------------------------------------

--
-- Table structure for table `course_videos`
--

CREATE TABLE `course_videos` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `week_number` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `video_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `video_platform` enum('youtube','vimeo','custom') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'youtube',
  `duration_minutes` int DEFAULT '0',
  `video_order` int DEFAULT '0',
  `timing_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_videos`
--

INSERT INTO `course_videos` (`id`, `course_id`, `week_number`, `title`, `description`, `video_url`, `video_platform`, `duration_minutes`, `video_order`, `timing_info`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'تجريبي', 'اليوم الاول فديو ترحيبي', 'https://youtu.be/uuIOrTtDYUs', 'youtube', 1, 1, 'اليوم الاول فديو ترحيبي', 1, 1, '2025-07-19 00:47:50', '2025-07-19 00:47:50'),
(2, 2, 1, 'الدرس الأول مقدمه عن علم الخط', 'الدرس الأول مقدمه عن علم الخط', 'https://youtu.be/uuIOrTtDYUs', 'youtube', 1, 1, 'الدرس الأول مقدمه عن علم الخط', 1, 1, '2025-07-19 04:16:07', '2025-07-19 04:16:07'),
(3, 3, 1, 'تجريبي', 'g', 'https://youtu.be/uuIOrTtDYUs', 'youtube', 1, 1, 'اليوم الاول فديو ترحيبي', 1, 1, '2025-07-20 16:57:04', '2025-07-20 16:57:04'),
(4, 1, 1, 'مقدمة الكورس', 'فيديو تعريفي بالكورس ومحتوياته', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'youtube', 5, 1, 'الأسبوع الأول - اليوم الأول', 1, 1, '2025-07-24 01:05:53', '2025-07-24 01:05:53'),
(5, 1, 1, 'الدرس الأول', 'شرح المفاهيم الأساسية', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'youtube', 15, 2, 'الأسبوع الأول - اليوم الثاني', 1, 1, '2025-07-24 01:05:53', '2025-07-24 01:05:53'),
(6, 1, 2, 'الدرس الثاني', 'تطبيق عملي على المفاهيم', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'youtube', 20, 1, 'الأسبوع الثاني - اليوم الأول', 1, 1, '2025-07-24 01:05:53', '2025-07-24 01:05:53'),
(7, 2, 1, 'مقدمة الكورس الثاني', 'فيديو تعريفي بالكورس الثاني', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'youtube', 8, 1, 'الأسبوع الأول', 1, 1, '2025-07-24 01:05:54', '2025-07-24 01:05:54');

-- --------------------------------------------------------

--
-- Table structure for table `course_weekly_tests`
--

CREATE TABLE `course_weekly_tests` (
  `id` int NOT NULL,
  `course_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int DEFAULT '30',
  `total_marks` decimal(5,2) DEFAULT '0.00',
  `passing_marks` decimal(5,2) DEFAULT '0.00',
  `week_number` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_weekly_tests`
--

INSERT INTO `course_weekly_tests` (`id`, `course_id`, `title`, `description`, `duration_minutes`, `total_marks`, `passing_marks`, `week_number`, `is_active`, `created_by`, `created_at`) VALUES
(1, 1, 'اختبار أسبوعي نهائي', 'اختبار أسبوعي بعد الإصلاح النهائي', 20, 6.00, 4.00, 1, 1, 1, '2025-07-19 01:23:33'),
(2, 3, 'تجريبي', 'tuyi', 30, 0.00, 0.00, 1, 1, 1, '2025-07-20 16:59:27');

-- --------------------------------------------------------

--
-- Table structure for table `course_weekly_test_questions`
--

CREATE TABLE `course_weekly_test_questions` (
  `id` int NOT NULL,
  `test_id` int NOT NULL,
  `question_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('true_false','multiple_choice') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'true_false',
  `options` json DEFAULT NULL,
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `explanation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `points` decimal(5,2) DEFAULT '1.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `course_weekly_test_questions`
--

INSERT INTO `course_weekly_test_questions` (`id`, `test_id`, `question_text`, `question_type`, `options`, `correct_answer`, `explanation`, `points`, `created_at`) VALUES
(1, 1, 'هل 5 > 3؟', 'true_false', NULL, 'true', 'نعم، 5 أكبر من 3', 3.00, '2025-07-19 01:23:33'),
(2, 1, 'كم عدد أيام الأسبوع؟', 'multiple_choice', '[\"5\", \"6\", \"7\", \"8\"]', '7', 'الأسبوع يحتوي على 7 أيام', 3.00, '2025-07-19 01:23:33'),
(3, 2, 'uyiy', 'multiple_choice', '[\"yuityuitt\", \"yutiyu\", \"tiyuiy\", \"tui\"]', 'yuityuitt', NULL, 1.00, '2025-07-20 16:59:27');

-- --------------------------------------------------------

--
-- Table structure for table `curriculum_sections`
--

CREATE TABLE `curriculum_sections` (
  `id` int NOT NULL,
  `section_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `section_name_ar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `education_level` enum('primary','preparatory','secondary') COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary','all') COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `display_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `curriculum_sections`
--

INSERT INTO `curriculum_sections` (`id`, `section_name`, `section_name_ar`, `description`, `education_level`, `education_type`, `grade`, `specialization`, `display_order`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'نحو', 'نحو', 'قسم نحو للصف 5', 'primary', 'azhari', '5', 'all', 1, 1, 11, '2025-07-21 22:14:40', '2025-07-21 22:14:40'),
(2, 'نصوص', 'نصوص', 'قسم نصوص للصف 5', 'primary', 'azhari', '5', 'all', 2, 1, 11, '2025-07-21 22:14:40', '2025-07-21 22:14:40'),
(3, 'قراءة', 'قراءة', 'قسم قراءة للصف 5', 'primary', 'azhari', '5', 'all', 3, 1, 11, '2025-07-21 22:14:40', '2025-07-21 22:14:40'),
(4, 'إملاء', 'إملاء', 'قسم إملاء للصف 5', 'primary', 'azhari', '5', 'all', 4, 1, 11, '2025-07-21 22:14:40', '2025-07-21 22:14:40'),
(5, 'خط', 'خط', 'قسم خط للصف 5', 'primary', 'azhari', '5', 'all', 5, 1, 11, '2025-07-21 22:14:40', '2025-07-21 22:14:40'),
(6, 'تعبير', 'تعبير', 'قسم تعبير للصف 5', 'primary', 'azhari', '5', 'all', 6, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(7, 'نحو', 'نحو', 'قسم نحو للصف 6', 'primary', 'azhari', '6', 'all', 1, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(8, 'نصوص', 'نصوص', 'قسم نصوص للصف 6', 'primary', 'azhari', '6', 'all', 2, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(9, 'قراءة', 'قراءة', 'قسم قراءة للصف 6', 'primary', 'azhari', '6', 'all', 3, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(10, 'إملاء', 'إملاء', 'قسم إملاء للصف 6', 'primary', 'azhari', '6', 'all', 4, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(11, 'خط', 'خط', 'قسم خط للصف 6', 'primary', 'azhari', '6', 'all', 5, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(12, 'تعبير', 'تعبير', 'قسم تعبير للصف 6', 'primary', 'azhari', '6', 'all', 6, 1, 11, '2025-07-21 22:14:41', '2025-07-21 22:14:41'),
(13, 'نحو', 'نحو', 'قسم نحو للصف 5', 'primary', 'general', '5', 'all', 1, 1, 11, '2025-07-21 22:14:42', '2025-07-21 22:14:42'),
(14, 'نصوص', 'نصوص', 'قسم نصوص للصف 5', 'primary', 'general', '5', 'all', 2, 1, 11, '2025-07-21 22:14:42', '2025-07-21 22:14:42'),
(15, 'قراءة', 'قراءة', 'قسم قراءة للصف 5', 'primary', 'general', '5', 'all', 3, 1, 11, '2025-07-21 22:14:42', '2025-07-21 22:14:42'),
(16, 'إملاء', 'إملاء', 'قسم إملاء للصف 5', 'primary', 'general', '5', 'all', 4, 1, 11, '2025-07-21 22:14:42', '2025-07-21 22:14:42'),
(17, 'خط', 'خط', 'قسم خط للصف 5', 'primary', 'general', '5', 'all', 5, 1, 11, '2025-07-21 22:14:42', '2025-07-21 22:14:42'),
(18, 'تعبير', 'تعبير', 'قسم تعبير للصف 5', 'primary', 'general', '5', 'all', 6, 1, 11, '2025-07-21 22:14:42', '2025-07-21 22:14:42'),
(19, 'نحو', 'نحو', 'قسم نحو للصف 6', 'primary', 'general', '6', 'all', 1, 1, 11, '2025-07-21 22:14:43', '2025-07-21 22:14:43'),
(20, 'نصوص', 'نصوص', 'قسم نصوص للصف 6', 'primary', 'general', '6', 'all', 2, 1, 11, '2025-07-21 22:14:43', '2025-07-21 22:14:43'),
(21, 'قراءة', 'قراءة', 'قسم قراءة للصف 6', 'primary', 'general', '6', 'all', 3, 1, 11, '2025-07-21 22:14:43', '2025-07-21 22:14:43'),
(22, 'إملاء', 'إملاء', 'قسم إملاء للصف 6', 'primary', 'general', '6', 'all', 4, 1, 11, '2025-07-21 22:14:43', '2025-07-21 22:14:43'),
(23, 'خط', 'خط', 'قسم خط للصف 6', 'primary', 'general', '6', 'all', 5, 1, 11, '2025-07-21 22:14:43', '2025-07-21 22:14:43'),
(24, 'تعبير', 'تعبير', 'قسم تعبير للصف 6', 'primary', 'general', '6', 'all', 6, 1, 11, '2025-07-21 22:14:43', '2025-07-21 22:14:43'),
(25, 'نحو', 'نحو', 'قسم نحو للصف 1', 'preparatory', 'azhari', '1', 'all', 1, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(26, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 1', 'preparatory', 'azhari', '1', 'all', 2, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(27, 'نصوص', 'نصوص', 'قسم نصوص للصف 1', 'preparatory', 'azhari', '1', 'all', 3, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(28, 'إملاء', 'إملاء', 'قسم إملاء للصف 1', 'preparatory', 'azhari', '1', 'all', 4, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(29, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 1', 'preparatory', 'azhari', '1', 'all', 5, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(30, 'خط', 'خط', 'قسم خط للصف 1', 'preparatory', 'azhari', '1', 'all', 6, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(31, 'نحو', 'نحو', 'قسم نحو للصف 2', 'preparatory', 'azhari', '2', 'all', 1, 1, 11, '2025-07-21 22:14:44', '2025-07-21 22:14:44'),
(32, 'صرف', 'صرف', 'قسم صرف للصف 2', 'preparatory', 'azhari', '2', 'all', 2, 1, 11, '2025-07-21 22:14:45', '2025-07-21 22:14:45'),
(33, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 2', 'preparatory', 'azhari', '2', 'all', 3, 1, 11, '2025-07-21 22:14:45', '2025-07-21 22:14:45'),
(34, 'نصوص', 'نصوص', 'قسم نصوص للصف 2', 'preparatory', 'azhari', '2', 'all', 4, 1, 11, '2025-07-21 22:14:45', '2025-07-21 22:14:45'),
(35, 'إملاء', 'إملاء', 'قسم إملاء للصف 2', 'preparatory', 'azhari', '2', 'all', 5, 1, 11, '2025-07-21 22:14:45', '2025-07-21 22:14:45'),
(36, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 2', 'preparatory', 'azhari', '2', 'all', 6, 1, 11, '2025-07-21 22:14:45', '2025-07-21 22:14:45'),
(37, 'خط', 'خط', 'قسم خط للصف 2', 'preparatory', 'azhari', '2', 'all', 7, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(38, 'نحو', 'نحو', 'قسم نحو للصف 3', 'preparatory', 'azhari', '3', 'all', 1, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(39, 'صرف', 'صرف', 'قسم صرف للصف 3', 'preparatory', 'azhari', '3', 'all', 2, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(40, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 3', 'preparatory', 'azhari', '3', 'all', 3, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(41, 'نصوص', 'نصوص', 'قسم نصوص للصف 3', 'preparatory', 'azhari', '3', 'all', 4, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(42, 'إملاء', 'إملاء', 'قسم إملاء للصف 3', 'preparatory', 'azhari', '3', 'all', 5, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(43, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 3', 'preparatory', 'azhari', '3', 'all', 6, 1, 11, '2025-07-21 22:14:46', '2025-07-21 22:14:46'),
(44, 'خط', 'خط', 'قسم خط للصف 3', 'preparatory', 'azhari', '3', 'all', 7, 1, 11, '2025-07-21 22:14:47', '2025-07-21 22:14:47'),
(45, 'نحو', 'نحو', 'قسم نحو للصف 1', 'preparatory', 'general', '1', 'all', 1, 1, 11, '2025-07-21 22:14:47', '2025-07-21 22:14:47'),
(46, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 1', 'preparatory', 'general', '1', 'all', 2, 1, 11, '2025-07-21 22:14:47', '2025-07-21 22:14:47'),
(47, 'نصوص', 'نصوص', 'قسم نصوص للصف 1', 'preparatory', 'general', '1', 'all', 3, 1, 11, '2025-07-21 22:14:47', '2025-07-21 22:14:47'),
(48, 'إملاء', 'إملاء', 'قسم إملاء للصف 1', 'preparatory', 'general', '1', 'all', 4, 1, 11, '2025-07-21 22:14:48', '2025-07-21 22:14:48'),
(49, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 1', 'preparatory', 'general', '1', 'all', 5, 1, 11, '2025-07-21 22:14:49', '2025-07-21 22:14:49'),
(50, 'خط', 'خط', 'قسم خط للصف 1', 'preparatory', 'general', '1', 'all', 6, 1, 11, '2025-07-21 22:14:50', '2025-07-21 22:14:50'),
(51, 'نحو', 'نحو', 'قسم نحو للصف 2', 'preparatory', 'general', '2', 'all', 1, 1, 11, '2025-07-21 22:14:50', '2025-07-21 22:14:50'),
(52, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 2', 'preparatory', 'general', '2', 'all', 2, 1, 11, '2025-07-21 22:14:50', '2025-07-21 22:14:50'),
(53, 'نصوص', 'نصوص', 'قسم نصوص للصف 2', 'preparatory', 'general', '2', 'all', 3, 1, 11, '2025-07-21 22:14:50', '2025-07-21 22:14:50'),
(54, 'إملاء', 'إملاء', 'قسم إملاء للصف 2', 'preparatory', 'general', '2', 'all', 4, 1, 11, '2025-07-21 22:14:50', '2025-07-21 22:14:50'),
(55, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 2', 'preparatory', 'general', '2', 'all', 5, 1, 11, '2025-07-21 22:14:51', '2025-07-21 22:14:51'),
(56, 'خط', 'خط', 'قسم خط للصف 2', 'preparatory', 'general', '2', 'all', 6, 1, 11, '2025-07-21 22:14:51', '2025-07-21 22:14:51'),
(57, 'نحو', 'نحو', 'قسم نحو للصف 3', 'preparatory', 'general', '3', 'all', 1, 1, 11, '2025-07-21 22:14:51', '2025-07-21 22:14:51'),
(58, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 3', 'preparatory', 'general', '3', 'all', 2, 1, 11, '2025-07-21 22:14:51', '2025-07-21 22:14:51'),
(59, 'نصوص', 'نصوص', 'قسم نصوص للصف 3', 'preparatory', 'general', '3', 'all', 3, 1, 11, '2025-07-21 22:14:51', '2025-07-21 22:14:51'),
(60, 'إملاء', 'إملاء', 'قسم إملاء للصف 3', 'preparatory', 'general', '3', 'all', 4, 1, 11, '2025-07-21 22:14:51', '2025-07-21 22:14:51'),
(61, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 3', 'preparatory', 'general', '3', 'all', 5, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(62, 'خط', 'خط', 'قسم خط للصف 3', 'preparatory', 'general', '3', 'all', 6, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(63, 'نحو', 'نحو', 'قسم نحو للصف 1', 'secondary', 'general', '1', 'scientific', 1, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(64, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 1', 'secondary', 'general', '1', 'scientific', 2, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(65, 'نصوص', 'نصوص', 'قسم نصوص للصف 1', 'secondary', 'general', '1', 'scientific', 3, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(66, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 1', 'secondary', 'general', '1', 'scientific', 4, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(67, 'قصة', 'قصة', 'قسم قصة للصف 1', 'secondary', 'general', '1', 'scientific', 5, 1, 11, '2025-07-21 22:14:52', '2025-07-21 22:14:52'),
(68, 'نحو', 'نحو', 'قسم نحو للصف 2', 'secondary', 'general', '2', 'scientific', 1, 1, 11, '2025-07-21 22:14:53', '2025-07-21 22:14:53'),
(69, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 2', 'secondary', 'general', '2', 'scientific', 2, 1, 11, '2025-07-21 22:14:53', '2025-07-21 22:14:53'),
(70, 'نصوص', 'نصوص', 'قسم نصوص للصف 2', 'secondary', 'general', '2', 'scientific', 3, 1, 11, '2025-07-21 22:14:53', '2025-07-21 22:14:53'),
(71, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 2', 'secondary', 'general', '2', 'scientific', 4, 1, 11, '2025-07-21 22:14:53', '2025-07-21 22:14:53'),
(72, 'قصة', 'قصة', 'قسم قصة للصف 2', 'secondary', 'general', '2', 'scientific', 5, 1, 11, '2025-07-21 22:14:53', '2025-07-21 22:14:53'),
(73, 'نحو', 'نحو', 'قسم نحو للصف 3', 'secondary', 'general', '3', 'scientific', 1, 1, 11, '2025-07-21 22:14:53', '2025-07-21 22:14:53'),
(74, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 3', 'secondary', 'general', '3', 'scientific', 2, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(75, 'نصوص', 'نصوص', 'قسم نصوص للصف 3', 'secondary', 'general', '3', 'scientific', 3, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(76, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 3', 'secondary', 'general', '3', 'scientific', 4, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(77, 'قصة', 'قصة', 'قسم قصة للصف 3', 'secondary', 'general', '3', 'scientific', 5, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(78, 'نحو', 'نحو', 'قسم نحو للصف 1', 'secondary', 'general', '1', 'literary', 1, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(79, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 1', 'secondary', 'general', '1', 'literary', 2, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(80, 'نصوص', 'نصوص', 'قسم نصوص للصف 1', 'secondary', 'general', '1', 'literary', 3, 1, 11, '2025-07-21 22:14:54', '2025-07-21 22:14:54'),
(81, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 1', 'secondary', 'general', '1', 'literary', 4, 1, 11, '2025-07-21 22:14:55', '2025-07-21 22:14:55'),
(82, 'قصة', 'قصة', 'قسم قصة للصف 1', 'secondary', 'general', '1', 'literary', 5, 1, 11, '2025-07-21 22:14:55', '2025-07-21 22:14:55'),
(83, 'نحو', 'نحو', 'قسم نحو للصف 2', 'secondary', 'general', '2', 'literary', 1, 1, 11, '2025-07-21 22:14:55', '2025-07-21 22:14:55'),
(84, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 2', 'secondary', 'general', '2', 'literary', 2, 1, 11, '2025-07-21 22:14:55', '2025-07-21 22:14:55'),
(85, 'نصوص', 'نصوص', 'قسم نصوص للصف 2', 'secondary', 'general', '2', 'literary', 3, 1, 11, '2025-07-21 22:14:55', '2025-07-21 22:14:55'),
(86, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 2', 'secondary', 'general', '2', 'literary', 4, 1, 11, '2025-07-21 22:14:55', '2025-07-21 22:14:55'),
(87, 'قصة', 'قصة', 'قسم قصة للصف 2', 'secondary', 'general', '2', 'literary', 5, 1, 11, '2025-07-21 22:14:56', '2025-07-21 22:14:56'),
(88, 'نحو', 'نحو', 'قسم نحو للصف 3', 'secondary', 'general', '3', 'literary', 1, 1, 11, '2025-07-21 22:14:56', '2025-07-21 22:14:56'),
(89, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 3', 'secondary', 'general', '3', 'literary', 2, 1, 11, '2025-07-21 22:14:56', '2025-07-21 22:14:56'),
(90, 'نصوص', 'نصوص', 'قسم نصوص للصف 3', 'secondary', 'general', '3', 'literary', 3, 1, 11, '2025-07-21 22:14:56', '2025-07-21 22:14:56'),
(91, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 3', 'secondary', 'general', '3', 'literary', 4, 1, 11, '2025-07-21 22:14:56', '2025-07-21 22:14:56'),
(92, 'قصة', 'قصة', 'قسم قصة للصف 3', 'secondary', 'general', '3', 'literary', 5, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(93, 'نحو', 'نحو', 'قسم نحو للصف 1', 'secondary', 'azhari', '1', 'all', 1, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(94, 'صرف', 'صرف', 'قسم صرف للصف 1', 'secondary', 'azhari', '1', 'all', 2, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(95, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 1', 'secondary', 'azhari', '1', 'all', 3, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(96, 'بلاغة', 'بلاغة', 'قسم بلاغة للصف 1', 'secondary', 'azhari', '1', 'all', 4, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(97, 'أدب', 'أدب', 'قسم أدب للصف 1', 'secondary', 'azhari', '1', 'all', 5, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(98, 'نصوص', 'نصوص', 'قسم نصوص للصف 1', 'secondary', 'azhari', '1', 'all', 6, 1, 11, '2025-07-21 22:14:57', '2025-07-21 22:14:57'),
(99, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 1', 'secondary', 'azhari', '1', 'all', 7, 1, 11, '2025-07-21 22:14:58', '2025-07-21 22:14:58'),
(100, 'نحو', 'نحو', 'قسم نحو للصف 2', 'secondary', 'azhari', '2', 'all', 1, 1, 11, '2025-07-21 22:14:58', '2025-07-21 22:14:58'),
(101, 'صرف', 'صرف', 'قسم صرف للصف 2', 'secondary', 'azhari', '2', 'all', 2, 1, 11, '2025-07-21 22:14:58', '2025-07-21 22:14:58'),
(102, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 2', 'secondary', 'azhari', '2', 'all', 3, 1, 11, '2025-07-21 22:14:58', '2025-07-21 22:14:58'),
(103, 'بلاغة', 'بلاغة', 'قسم بلاغة للصف 2', 'secondary', 'azhari', '2', 'all', 4, 1, 11, '2025-07-21 22:14:58', '2025-07-21 22:14:58'),
(104, 'أدب', 'أدب', 'قسم أدب للصف 2', 'secondary', 'azhari', '2', 'all', 5, 1, 11, '2025-07-21 22:14:58', '2025-07-21 22:14:58'),
(105, 'نصوص', 'نصوص', 'قسم نصوص للصف 2', 'secondary', 'azhari', '2', 'all', 6, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(106, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 2', 'secondary', 'azhari', '2', 'all', 7, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(107, 'عروض', 'عروض', 'قسم عروض للصف 2', 'secondary', 'azhari', '2', 'all', 8, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(108, 'قافية', 'قافية', 'قسم قافية للصف 2', 'secondary', 'azhari', '2', 'all', 9, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(109, 'نحو', 'نحو', 'قسم نحو للصف 3', 'secondary', 'azhari', '3', 'all', 1, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(110, 'صرف', 'صرف', 'قسم صرف للصف 3', 'secondary', 'azhari', '3', 'all', 2, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(111, 'مطالعة', 'مطالعة', 'قسم مطالعة للصف 3', 'secondary', 'azhari', '3', 'all', 3, 1, 11, '2025-07-21 22:14:59', '2025-07-21 22:14:59'),
(112, 'بلاغة', 'بلاغة', 'قسم بلاغة للصف 3', 'secondary', 'azhari', '3', 'all', 4, 1, 11, '2025-07-21 22:15:00', '2025-07-21 22:15:00'),
(113, 'أدب', 'أدب', 'قسم أدب للصف 3', 'secondary', 'azhari', '3', 'all', 5, 1, 11, '2025-07-21 22:15:00', '2025-07-21 22:15:00'),
(114, 'نصوص', 'نصوص', 'قسم نصوص للصف 3', 'secondary', 'azhari', '3', 'all', 6, 1, 11, '2025-07-21 22:15:00', '2025-07-21 22:15:00'),
(115, 'إنشاء', 'إنشاء', 'قسم إنشاء للصف 3', 'secondary', 'azhari', '3', 'all', 7, 1, 11, '2025-07-21 22:15:00', '2025-07-21 22:15:00');

-- --------------------------------------------------------

--
-- Table structure for table `curriculum_subjects`
--

CREATE TABLE `curriculum_subjects` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم القسم/المادة',
  `name_en` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'اسم القسم بالإنجليزية',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'وصف القسم',
  `education_level` enum('primary','preparatory','secondary') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'المرحلة التعليمية',
  `education_type` enum('azhari','general') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'نوع التعليم',
  `grade` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'الصف',
  `specialization` enum('scientific','literary','all') COLLATE utf8mb4_unicode_ci DEFAULT 'all' COMMENT 'التخصص للثانوي',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '?' COMMENT 'أيقونة القسم',
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#4682B4' COMMENT 'لون القسم',
  `sort_order` int DEFAULT '0' COMMENT 'ترتيب العرض',
  `is_active` tinyint(1) DEFAULT '1' COMMENT 'حالة القسم',
  `created_by` int DEFAULT NULL COMMENT 'منشئ القسم',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `curriculum_subjects`
--

INSERT INTO `curriculum_subjects` (`id`, `name`, `name_en`, `description`, `education_level`, `education_type`, `grade`, `specialization`, `icon`, `color`, `sort_order`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'نحو', 'Grammar', NULL, 'primary', 'azhari', '4', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 17:02:47'),
(2, 'نصوص', 'Texts', NULL, 'primary', 'azhari', '4', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 02:15:17'),
(3, 'قراءة', 'Reading', NULL, 'primary', 'azhari', '4', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 02:15:17'),
(4, 'إملاء', 'Dictation', NULL, 'primary', 'azhari', '4', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 02:15:17'),
(5, 'خط', 'Calligraphy', NULL, 'primary', 'azhari', '4', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 02:15:17'),
(6, 'تعبير', 'Expression', NULL, 'primary', 'azhari', '4', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 02:15:17'),
(7, 'نحو', 'Grammar', NULL, 'primary', 'azhari', '5', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:17', '2025-07-24 02:15:17'),
(8, 'نصوص', 'Texts', NULL, 'primary', 'azhari', '5', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(9, 'قراءة', 'Reading', NULL, 'primary', 'azhari', '5', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(10, 'إملاء', 'Dictation', NULL, 'primary', 'azhari', '5', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(11, 'خط', 'Calligraphy', NULL, 'primary', 'azhari', '5', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(12, 'تعبير', 'Expression', NULL, 'primary', 'azhari', '5', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(13, 'نحو', 'Grammar', NULL, 'primary', 'azhari', '6', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(14, 'نصوص', 'Texts', NULL, 'primary', 'azhari', '6', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(15, 'قراءة', 'Reading', NULL, 'primary', 'azhari', '6', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(16, 'إملاء', 'Dictation', NULL, 'primary', 'azhari', '6', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(17, 'خط', 'Calligraphy', NULL, 'primary', 'azhari', '6', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(18, 'تعبير', 'Expression', NULL, 'primary', 'azhari', '6', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(19, 'نحو', 'Grammar', NULL, 'primary', 'general', '4', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(20, 'نصوص', 'Texts', NULL, 'primary', 'general', '4', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(21, 'قراءة', 'Reading', NULL, 'primary', 'general', '4', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:18', '2025-07-24 02:15:18'),
(22, 'إملاء', 'Dictation', NULL, 'primary', 'general', '4', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(23, 'خط', 'Calligraphy', NULL, 'primary', 'general', '4', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(24, 'تعبير', 'Expression', NULL, 'primary', 'general', '4', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(25, 'نحو', 'Grammar', NULL, 'primary', 'general', '5', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(26, 'نصوص', 'Texts', NULL, 'primary', 'general', '5', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(27, 'قراءة', 'Reading', NULL, 'primary', 'general', '5', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(28, 'إملاء', 'Dictation', NULL, 'primary', 'general', '5', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(29, 'خط', 'Calligraphy', NULL, 'primary', 'general', '5', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(30, 'تعبير', 'Expression', NULL, 'primary', 'general', '5', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(31, 'نحو', 'Grammar', NULL, 'primary', 'general', '6', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(32, 'نصوص', 'Texts', NULL, 'primary', 'general', '6', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(33, 'قراءة', 'Reading', NULL, 'primary', 'general', '6', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(34, 'إملاء', 'Dictation', NULL, 'primary', 'general', '6', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:19', '2025-07-24 02:15:19'),
(35, 'خط', 'Calligraphy', NULL, 'primary', 'general', '6', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:15:20'),
(36, 'تعبير', 'Expression', NULL, 'primary', 'general', '6', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:15:20'),
(37, 'نحو', 'Grammar', NULL, 'preparatory', 'azhari', '1', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:15:20'),
(38, 'نصوص', 'Texts', NULL, 'preparatory', 'azhari', '1', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:15:20'),
(39, 'مطالعة', 'Reading', NULL, 'preparatory', 'azhari', '1', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:34:12'),
(41, 'إملاء', 'Dictation', NULL, 'preparatory', 'azhari', '1', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:43:29'),
(42, 'خط', 'Calligraphy', NULL, 'preparatory', 'azhari', '1', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:43:29'),
(43, 'تعبير', 'Expression', NULL, 'preparatory', 'azhari', '1', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:43:29'),
(44, 'نحو', 'Grammar', NULL, 'preparatory', 'azhari', '2', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:15:20'),
(45, 'نصوص', 'Texts', NULL, 'preparatory', 'azhari', '2', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:15:20'),
(46, 'مطالعة', 'Reading', NULL, 'preparatory', 'azhari', '2', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:34:12'),
(48, 'إملاء', 'Dictation', NULL, 'preparatory', 'azhari', '2', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:20', '2025-07-24 02:43:30'),
(49, 'خط', 'Calligraphy', NULL, 'preparatory', 'azhari', '2', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:43:30'),
(50, 'تعبير', 'Expression', NULL, 'preparatory', 'azhari', '2', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:43:30'),
(51, 'نحو', 'Grammar', NULL, 'preparatory', 'azhari', '3', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(52, 'نصوص', 'Texts', NULL, 'preparatory', 'azhari', '3', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(53, 'مطالعة', 'Reading', NULL, 'preparatory', 'azhari', '3', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:34:12'),
(55, 'إملاء', 'Dictation', NULL, 'preparatory', 'azhari', '3', 'all', '✍️', '#9370DB', 4, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:43:30'),
(56, 'خط', 'Calligraphy', NULL, 'preparatory', 'azhari', '3', 'all', '🖋️', '#DAA520', 5, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:43:30'),
(57, 'تعبير', 'Expression', NULL, 'preparatory', 'azhari', '3', 'all', '💭', '#20B2AA', 6, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:43:30'),
(58, 'نحو', 'Grammar', NULL, 'preparatory', 'general', '1', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(59, 'نصوص', 'Texts', NULL, 'preparatory', 'general', '1', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(60, 'قراءة', 'Reading', NULL, 'preparatory', 'general', '1', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(61, 'مطالعة', 'Reading Comprehension', NULL, 'preparatory', 'general', '1', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(62, 'إملاء', 'Dictation', NULL, 'preparatory', 'general', '1', 'all', '✍️', '#9370DB', 5, 1, NULL, '2025-07-24 02:15:21', '2025-07-24 02:15:21'),
(63, 'خط', 'Calligraphy', NULL, 'preparatory', 'general', '1', 'all', '🖋️', '#DAA520', 6, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(64, 'تعبير', 'Expression', NULL, 'preparatory', 'general', '1', 'all', '💭', '#20B2AA', 7, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(65, 'نحو', 'Grammar', NULL, 'preparatory', 'general', '2', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(66, 'نصوص', 'Texts', NULL, 'preparatory', 'general', '2', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(67, 'قراءة', 'Reading', NULL, 'preparatory', 'general', '2', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(68, 'مطالعة', 'Reading Comprehension', NULL, 'preparatory', 'general', '2', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(69, 'إملاء', 'Dictation', NULL, 'preparatory', 'general', '2', 'all', '✍️', '#9370DB', 5, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(70, 'خط', 'Calligraphy', NULL, 'preparatory', 'general', '2', 'all', '🖋️', '#DAA520', 6, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(71, 'تعبير', 'Expression', NULL, 'preparatory', 'general', '2', 'all', '💭', '#20B2AA', 7, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(72, 'نحو', 'Grammar', NULL, 'preparatory', 'general', '3', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(73, 'نصوص', 'Texts', NULL, 'preparatory', 'general', '3', 'all', '📖', '#4682B4', 2, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(74, 'قراءة', 'Reading', NULL, 'preparatory', 'general', '3', 'all', '📚', '#FF6347', 3, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(75, 'مطالعة', 'Reading Comprehension', NULL, 'preparatory', 'general', '3', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(76, 'إملاء', 'Dictation', NULL, 'preparatory', 'general', '3', 'all', '✍️', '#9370DB', 5, 1, NULL, '2025-07-24 02:15:22', '2025-07-24 02:15:22'),
(77, 'خط', 'Calligraphy', NULL, 'preparatory', 'general', '3', 'all', '🖋️', '#DAA520', 6, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(78, 'تعبير', 'Expression', NULL, 'preparatory', 'general', '3', 'all', '💭', '#20B2AA', 7, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(79, 'نحو', 'Grammar', NULL, 'secondary', 'azhari', '1', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(80, 'صرف', 'Morphology', NULL, 'secondary', 'azhari', '1', 'all', '🔤', '#FF4500', 2, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(81, 'نصوص', 'Texts', NULL, 'secondary', 'azhari', '1', 'all', '📖', '#4682B4', 3, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(82, 'مطالعة', 'Reading Comprehension', NULL, 'secondary', 'azhari', '1', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(83, 'إنشاء', 'Composition', NULL, 'secondary', 'azhari', '1', 'all', '✍️', '#8B4513', 5, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(84, 'بلاغة', 'Rhetoric', NULL, 'secondary', 'azhari', '1', 'all', '🎭', '#800080', 6, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(85, 'نحو', 'Grammar', NULL, 'secondary', 'azhari', '2', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(86, 'صرف', 'Morphology', NULL, 'secondary', 'azhari', '2', 'all', '🔤', '#FF4500', 2, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(87, 'نصوص', 'Texts', NULL, 'secondary', 'azhari', '2', 'all', '📖', '#4682B4', 3, 1, NULL, '2025-07-24 02:15:23', '2025-07-24 02:15:23'),
(88, 'مطالعة', 'Reading Comprehension', NULL, 'secondary', 'azhari', '2', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(89, 'إنشاء', 'Composition', NULL, 'secondary', 'azhari', '2', 'all', '✍️', '#8B4513', 5, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(90, 'بلاغة', 'Rhetoric', NULL, 'secondary', 'azhari', '2', 'all', '🎭', '#800080', 6, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(91, 'نحو', 'Grammar', NULL, 'secondary', 'azhari', '3', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(92, 'صرف', 'Morphology', NULL, 'secondary', 'azhari', '3', 'all', '🔤', '#FF4500', 2, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(93, 'نصوص', 'Texts', NULL, 'secondary', 'azhari', '3', 'all', '📖', '#4682B4', 3, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(94, 'مطالعة', 'Reading Comprehension', NULL, 'secondary', 'azhari', '3', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(95, 'إنشاء', 'Composition', NULL, 'secondary', 'azhari', '3', 'all', '✍️', '#8B4513', 5, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(96, 'بلاغة', 'Rhetoric', NULL, 'secondary', 'azhari', '3', 'all', '🎭', '#800080', 6, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(97, 'نحو', 'Grammar', NULL, 'secondary', 'general', '1', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(98, 'صرف', 'Morphology', NULL, 'secondary', 'general', '1', 'all', '🔤', '#FF4500', 2, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(99, 'نصوص', 'Texts', NULL, 'secondary', 'general', '1', 'all', '📖', '#4682B4', 3, 1, NULL, '2025-07-24 02:15:24', '2025-07-24 02:15:24'),
(100, 'مطالعة', 'Reading Comprehension', NULL, 'secondary', 'general', '1', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(101, 'إنشاء', 'Composition', NULL, 'secondary', 'general', '1', 'all', '✍️', '#8B4513', 5, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(102, 'بلاغة', 'Rhetoric', NULL, 'secondary', 'general', '1', 'all', '🎭', '#800080', 6, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(103, 'نحو', 'Grammar', NULL, 'secondary', 'general', '2', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(104, 'صرف', 'Morphology', NULL, 'secondary', 'general', '2', 'all', '🔤', '#FF4500', 2, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(105, 'نصوص', 'Texts', NULL, 'secondary', 'general', '2', 'all', '📖', '#4682B4', 3, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(106, 'مطالعة', 'Reading Comprehension', NULL, 'secondary', 'general', '2', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(107, 'إنشاء', 'Composition', NULL, 'secondary', 'general', '2', 'all', '✍️', '#8B4513', 5, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(108, 'بلاغة', 'Rhetoric', NULL, 'secondary', 'general', '2', 'all', '🎭', '#800080', 6, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(109, 'نحو', 'Grammar', NULL, 'secondary', 'general', '3', 'all', '📝', '#2E8B57', 1, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(110, 'صرف', 'Morphology', NULL, 'secondary', 'general', '3', 'all', '🔤', '#FF4500', 2, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(111, 'نصوص', 'Texts', NULL, 'secondary', 'general', '3', 'all', '📖', '#4682B4', 3, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(112, 'مطالعة', 'Reading Comprehension', NULL, 'secondary', 'general', '3', 'all', '📖', '#32CD32', 4, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(113, 'إنشاء', 'Composition', NULL, 'secondary', 'general', '3', 'all', '✍️', '#8B4513', 5, 1, NULL, '2025-07-24 02:15:25', '2025-07-24 02:15:25'),
(114, 'بلاغة', 'Rhetoric', NULL, 'secondary', 'general', '3', 'all', '🎭', '#800080', 6, 1, NULL, '2025-07-24 02:15:26', '2025-07-24 02:15:26');

-- --------------------------------------------------------

--
-- Table structure for table `email_verification_tokens`
--

CREATE TABLE `email_verification_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `used` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exam_attempts`
--

CREATE TABLE `exam_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exam_id` int NOT NULL,
  `attempt_number` int NOT NULL DEFAULT '1',
  `score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) NOT NULL,
  `percentage` decimal(5,2) GENERATED ALWAYS AS (round(((`score` / `max_score`) * 100),2)) STORED,
  `time_taken` int DEFAULT '0',
  `answers` json DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0',
  `passed` tinyint(1) GENERATED ALWAYS AS ((`percentage` >= 60)) STORED
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exam_questions`
--

CREATE TABLE `exam_questions` (
  `id` int NOT NULL,
  `exam_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('multiple_choice','true_false','short_answer') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'multiple_choice',
  `options` json DEFAULT NULL,
  `correct_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `points` decimal(4,2) NOT NULL DEFAULT '2.00',
  `explanation` text COLLATE utf8mb4_unicode_ci,
  `question_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `exam_questions`
--

INSERT INTO `exam_questions` (`id`, `exam_id`, `question_text`, `question_type`, `options`, `correct_answer`, `points`, `explanation`, `question_order`, `is_active`, `created_at`) VALUES
(1, 1, 'تجريبي', 'true_false', NULL, 'true', 1.00, 'تجريبي', 0, 1, '2025-07-23 22:33:53'),
(2, 1, 'تجريبي', 'true_false', NULL, 'true', 1.00, 'تجريبي', 0, 1, '2025-07-23 22:33:59'),
(3, 2, 'فقغقف', 'true_false', NULL, 'true', 1.00, 'فقيغقفغ', 1, 1, '2025-07-23 22:43:47'),
(4, 2, 'فقغقف', 'true_false', NULL, 'true', 1.00, 'فقيغقفغ', 1, 1, '2025-07-23 22:43:51');

-- --------------------------------------------------------

--
-- Table structure for table `exercise_attempts`
--

CREATE TABLE `exercise_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `attempt_number` int NOT NULL DEFAULT '1',
  `score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) NOT NULL,
  `percentage` decimal(5,2) GENERATED ALWAYS AS (round(((`score` / `max_score`) * 100),2)) STORED,
  `time_taken` int DEFAULT '0',
  `answers` json DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exercise_questions`
--

CREATE TABLE `exercise_questions` (
  `id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `question_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `question_type` enum('multiple_choice','true_false','short_answer') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'multiple_choice',
  `options` json DEFAULT NULL,
  `correct_answer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `points` decimal(4,2) NOT NULL DEFAULT '1.00',
  `explanation` text COLLATE utf8mb4_unicode_ci,
  `question_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `exercise_questions`
--

INSERT INTO `exercise_questions` (`id`, `exercise_id`, `question_text`, `question_type`, `options`, `correct_answer`, `points`, `explanation`, `question_order`, `is_active`, `created_at`) VALUES
(1, 1, 'تجريبي', 'true_false', NULL, 'true', 1.00, 'تجريبي', 1, 1, '2025-07-23 21:30:13'),
(2, 1, 'تجريبي', 'true_false', NULL, 'true', 1.00, 'تجريبي', 1, 1, '2025-07-23 21:30:22'),
(3, 2, 'عغعع', 'multiple_choice', '{\"option_a\": \"بلابلا\", \"option_b\": \"بلابلابلابل\", \"option_c\": \"ابل\", \"option_d\": \"ابلابلابل\"}', 'option_a', 1.00, 'لبابلا', 1, 1, '2025-07-23 21:41:13'),
(4, 2, 'عغعع', 'multiple_choice', '{\"option_a\": \"بلابلا\", \"option_b\": \"بلابلابلابل\", \"option_c\": \"ابل\", \"option_d\": \"ابلابلابل\"}', 'option_a', 1.00, 'لبابلا', 1, 1, '2025-07-23 21:41:16'),
(5, 2, 'عغعع', 'multiple_choice', '{\"option_a\": \"بلابلا\", \"option_b\": \"بلابلابلابل\", \"option_c\": \"ابل\", \"option_d\": \"ابلابلابل\"}', 'option_a', 1.00, 'لبابلا', 1, 1, '2025-07-23 21:41:20'),
(6, 3, 'هل خط الثلث خط عربي', 'true_false', NULL, 'true', 1.00, 'نعم لنه خط عربي', 1, 1, '2025-07-23 22:15:46'),
(7, 3, 'هل خط .......... خط عربي', 'multiple_choice', '{\"option_a\": \"الثلث\", \"option_b\": \"الصيني القديم\", \"option_c\": \"الفارسي\", \"option_d\": \"الانجليزي\"}', 'option_a', 1.00, 'خط الثلث', 2, 1, '2025-07-23 22:16:45');

-- --------------------------------------------------------

--
-- Table structure for table `fawry_payments`
--

CREATE TABLE `fawry_payments` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `fawry_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','failed','expired') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `fawry_reference` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `honor_board`
--

CREATE TABLE `honor_board` (
  `id` int NOT NULL,
  `student_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade_score` decimal(5,2) NOT NULL,
  `subject` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ranking_position` int NOT NULL,
  `achievement_type` enum('monthly','semester','yearly','special') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'monthly',
  `achievement_date` date NOT NULL,
  `education_level` enum('primary','preparatory','secondary') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `additional_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `lessons`
--

CREATE TABLE `lessons` (
  `id` int NOT NULL,
  `subject_id` int NOT NULL COMMENT 'معرف القسم',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'عنوان الدرس',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'وصف الدرس',
  `lesson_number` int NOT NULL COMMENT 'رقم الدرس',
  `is_free` tinyint(1) DEFAULT '0' COMMENT 'هل الدرس مجاني',
  `is_active` tinyint(1) DEFAULT '1' COMMENT 'حالة الدرس',
  `sort_order` int DEFAULT '0' COMMENT 'ترتيب العرض',
  `created_by` int DEFAULT NULL COMMENT 'منشئ الدرس',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `lesson_exams`
--

CREATE TABLE `lesson_exams` (
  `id` int NOT NULL,
  `lesson_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `instructions` text COLLATE utf8mb4_unicode_ci,
  `total_marks` decimal(5,2) DEFAULT '0.00',
  `passing_marks` decimal(5,2) DEFAULT '0.00',
  `duration_minutes` int DEFAULT '60',
  `max_attempts` int DEFAULT '1',
  `exam_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `lesson_exams`
--

INSERT INTO `lesson_exams` (`id`, `lesson_id`, `title`, `description`, `instructions`, `total_marks`, `passing_marks`, `duration_minutes`, `max_attempts`, `exam_order`, `is_active`, `created_at`) VALUES
(1, 2, 'تجريبي', 'تجريبي', NULL, 0.00, 0.00, 60, 1, 1, 1, '2025-07-23 22:33:28'),
(2, 2, 'تجريبي', 'تجريبي', NULL, 0.00, 0.00, 60, 1, 1, 1, '2025-07-23 22:43:27');

-- --------------------------------------------------------

--
-- Table structure for table `lesson_exercises`
--

CREATE TABLE `lesson_exercises` (
  `id` int NOT NULL,
  `lesson_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `instructions` text COLLATE utf8mb4_unicode_ci,
  `total_marks` decimal(5,2) DEFAULT '0.00',
  `passing_marks` decimal(5,2) DEFAULT '0.00',
  `time_limit_minutes` int DEFAULT '30',
  `max_attempts` int DEFAULT '3',
  `exercise_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `lesson_exercises`
--

INSERT INTO `lesson_exercises` (`id`, `lesson_id`, `title`, `description`, `instructions`, `total_marks`, `passing_marks`, `time_limit_minutes`, `max_attempts`, `exercise_order`, `is_active`, `created_by`, `created_at`) VALUES
(1, 1, '1', '1', NULL, 0.00, 0.00, 30, 3, 1, 1, 11, '2025-07-23 21:29:10'),
(2, 1, '1', '1', NULL, 0.00, 0.00, 30, 3, 1, 1, 11, '2025-07-23 21:40:22'),
(3, 2, 'ما هو الخط العربي', 'ما هو الخط العربي', NULL, 0.00, 0.00, 30, 3, 1, 1, 11, '2025-07-23 22:15:03'),
(4, 3, 'تجريبي', 'تجريبي', NULL, 0.00, 0.00, 30, 3, 2, 1, 11, '2025-07-24 00:21:19');

-- --------------------------------------------------------

--
-- Table structure for table `lesson_files`
--

CREATE TABLE `lesson_files` (
  `id` int NOT NULL,
  `lesson_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_type` enum('pdf','doc','ppt','image','other') COLLATE utf8mb4_unicode_ci DEFAULT 'pdf',
  `file_size` int DEFAULT '0',
  `file_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `lesson_files`
--

INSERT INTO `lesson_files` (`id`, `lesson_id`, `title`, `description`, `file_path`, `file_type`, `file_size`, `file_order`, `is_active`, `created_at`) VALUES
(1, 1, 'ملخص الدرس', 'ملخص شامل لمحتوى الدرس', '/uploads/sample_pdf_file.pdf', 'pdf', 1024000, 1, 1, '2025-07-21 21:40:07'),
(2, 1, 'أوراق عمل', 'أوراق عمل للتطبيق العملي', '/uploads/sample_doc_file.doc', 'doc', 512000, 2, 1, '2025-07-21 21:40:07'),
(3, 1, 'عرض تقديمي', 'عرض تقديمي للدرس', '/uploads/sample_ppt_file.ppt', 'ppt', 2048000, 3, 1, '2025-07-21 21:40:07');

-- --------------------------------------------------------

--
-- Table structure for table `lesson_progress`
--

CREATE TABLE `lesson_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `lesson_id` int NOT NULL,
  `content_type` enum('video','file','exercise','exam') COLLATE utf8mb4_unicode_ci NOT NULL,
  `content_id` int NOT NULL,
  `completed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `additional_data` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `lesson_summaries`
--

CREATE TABLE `lesson_summaries` (
  `id` int NOT NULL,
  `lesson_id` int NOT NULL COMMENT 'معرف الدرس',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'عنوان الملخص',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'وصف الملخص',
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'مسار ملف PDF',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الملف',
  `file_size` int DEFAULT '0' COMMENT 'حجم الملف بالبايت',
  `summary_order` int DEFAULT '0' COMMENT 'ترتيب الملخص',
  `is_active` tinyint(1) DEFAULT '1' COMMENT 'حالة الملخص',
  `created_by` int DEFAULT NULL COMMENT 'منشئ الملخص',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `lesson_videos`
--

CREATE TABLE `lesson_videos` (
  `id` int NOT NULL,
  `lesson_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `video_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_platform` enum('youtube','vimeo','local') COLLATE utf8mb4_unicode_ci DEFAULT 'youtube',
  `duration_minutes` int DEFAULT '0',
  `video_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `youtube_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'رابط اليوتيوب',
  `youtube_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'معرف الفيديو في اليوتيوب',
  `created_by` int DEFAULT NULL COMMENT 'منشئ الفيديو'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `lesson_videos`
--

INSERT INTO `lesson_videos` (`id`, `lesson_id`, `title`, `description`, `video_url`, `video_platform`, `duration_minutes`, `video_order`, `is_active`, `created_at`, `youtube_url`, `youtube_id`, `created_by`) VALUES
(1, 3, 'تجريبي', 'تجريبي', NULL, 'youtube', 0, 0, 1, '2025-07-24 00:20:37', NULL, NULL, NULL),
(2, 1, 'تجريبي', 'رل', NULL, 'youtube', 0, 4, 1, '2025-07-24 00:45:42', NULL, NULL, NULL),
(3, 3, 'تجريبي', 'يلاب', NULL, 'youtube', 10, 1, 1, '2025-07-24 00:51:38', 'https://youtu.be/nTt7DIGGnOs', 'nTt7DIGGnOs', 11),
(4, 3, 'تجريبي', 'b', NULL, 'youtube', 0, 3, 1, '2025-07-24 00:59:11', 'https://youtu.be/nTt7DIGGnOs', 'nTt7DIGGnOs', 11),
(5, 1, 'مقدمة الدرس الأول', 'فيديو تعريفي بالدرس الأول ومحتوياته', NULL, 'youtube', 5, 1, 1, '2025-07-24 01:18:23', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 1),
(6, 1, 'شرح المفاهيم الأساسية', 'شرح تفصيلي للمفاهيم الأساسية في الدرس', NULL, 'youtube', 15, 2, 1, '2025-07-24 01:18:23', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 1),
(7, 1, 'أمثلة تطبيقية', 'أمثلة عملية وتطبيقية على المفاهيم', NULL, 'youtube', 20, 3, 1, '2025-07-24 01:18:23', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 1),
(8, 2, 'مقدمة الدرس الثاني', 'فيديو تعريفي بالدرس الثاني', NULL, 'youtube', 8, 1, 1, '2025-07-24 01:18:23', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 1),
(9, 3, 'مقدمة الدرس الثالث', 'فيديو تعريفي بالدرس الثالث', NULL, 'youtube', 10, 1, 1, '2025-07-24 01:18:23', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 1);

-- --------------------------------------------------------

--
-- Table structure for table `login_activity`
--

CREATE TABLE `login_activity` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `login_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `logout_time` timestamp NULL DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `device_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `browser` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل نشاط تسجيل الدخول';

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int NOT NULL,
  `username_or_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `success` tinyint(1) NOT NULL,
  `attempted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `username_or_email`, `ip_address`, `success`, `attempted_at`) VALUES
(1, 'asa', '::1', 1, '2025-07-16 22:19:16'),
(2, 'asas', '::1', 1, '2025-07-16 22:24:17'),
(3, 'asas', '::1', 1, '2025-07-16 22:56:47'),
(4, 'asas', '::1', 1, '2025-07-17 02:03:59'),
(5, 'asas', '::1', 1, '2025-07-17 02:54:35'),
(6, 'admin', '::1', 0, '2025-07-17 03:15:44'),
(7, 'asas', '::1', 1, '2025-07-17 03:15:53'),
(8, 'admin', '::1', 0, '2025-07-17 03:42:39'),
(9, 'asas', '::1', 1, '2025-07-17 03:42:42'),
(10, 'asas', '::1', 1, '2025-07-17 04:19:52'),
(11, 'asas', '::1', 1, '2025-07-17 05:43:36'),
(12, 'asas', '::1', 1, '2025-07-17 14:54:12'),
(13, 'asas', '::1', 1, '2025-07-17 18:03:07'),
(14, 'asas', '::1', 1, '2025-07-17 18:13:04'),
(15, 'asas', '::1', 1, '2025-07-17 18:16:39'),
(16, 'asas', '::1', 1, '2025-07-17 19:36:57'),
(17, 'asa', '::1', 1, '2025-07-17 19:41:52'),
(18, 'asas', '::1', 1, '2025-07-17 19:43:45'),
(19, 'asas', '::1', 1, '2025-07-17 23:46:06'),
(20, 'asa', '::1', 1, '2025-07-18 00:49:55'),
(21, 'asa', '::1', 1, '2025-07-18 01:28:01'),
(22, 'asa', '::1', 1, '2025-07-18 10:56:53'),
(23, 'asa', '::1', 1, '2025-07-18 20:53:26'),
(24, 'asas', '::1', 1, '2025-07-18 23:37:53'),
(25, 'asas', '::1', 1, '2025-07-19 02:03:53'),
(26, 'asas', '::1', 1, '2025-07-19 02:19:06'),
(27, 'asas', '::1', 1, '2025-07-19 04:22:30'),
(28, 'asas', '::1', 1, '2025-07-19 15:56:15'),
(29, '<EMAIL>', '217.55.241.6', 1, '2025-07-19 16:47:54'),
(30, 'asas', '::1', 1, '2025-07-19 18:03:25'),
(31, 'lila', '154.236.101.45', 1, '2025-07-19 21:01:40'),
(32, 'asas', '::1', 1, '2025-07-20 01:11:22'),
(33, 'asas', '::1', 1, '2025-07-20 01:46:59'),
(34, 'asas', '::1', 1, '2025-07-20 01:49:24'),
(35, 'asas', '::1', 1, '2025-07-20 13:51:35'),
(36, 'asas', '::1', 1, '2025-07-20 19:51:41'),
(37, 'asas', '::1', 1, '2025-07-21 00:36:21'),
(38, 'asas', '::1', 1, '2025-07-21 02:14:47'),
(39, 'asas', '::1', 1, '2025-07-21 02:39:14'),
(40, 'asas', '::1', 1, '2025-07-21 04:27:11'),
(41, 'asas', '::1', 1, '2025-07-21 04:48:14'),
(42, 'asas', '::1', 1, '2025-07-21 05:45:24'),
(43, 'abdallah', '::1', 1, '2025-07-21 14:51:31'),
(44, 'asas', '::1', 1, '2025-07-21 14:54:07'),
(45, 'asas', '::1', 1, '2025-07-21 16:02:54'),
(46, 'asas', '::1', 1, '2025-07-21 18:22:02'),
(47, 'abdallah', '::1', 1, '2025-07-21 18:25:19'),
(48, 'abdallah', '::1', 1, '2025-07-21 20:00:12'),
(49, 'asas', '::1', 1, '2025-07-21 21:38:17'),
(50, 'abdallah', '::1', 1, '2025-07-21 21:38:57'),
(51, 'abdallah', '::1', 1, '2025-07-21 22:15:51'),
(52, 'abdallah', '::1', 1, '2025-07-22 01:46:14'),
(53, 'abdallah', '::1', 1, '2025-07-22 02:32:53'),
(54, 'lilia', '::1', 0, '2025-07-22 02:51:44'),
(55, 'lilia', '::1', 0, '2025-07-22 02:51:50'),
(56, 'lilia', '::1', 0, '2025-07-22 02:52:00'),
(57, 'lila', '::1', 1, '2025-07-22 02:52:53'),
(58, 'asmaa', '::1', 1, '2025-07-22 02:54:34'),
(59, 'asmaa', '::1', 1, '2025-07-22 03:18:57'),
(60, 'asasasa', '::1', 1, '2025-07-22 04:17:05'),
(61, '<EMAIL>', '217.55.222.179', 1, '2025-07-22 07:33:16'),
(62, 'asasasa', '::1', 1, '2025-07-22 07:34:35'),
(63, 'asasasa', '::1', 1, '2025-07-22 08:54:45'),
(64, 'asasasa', '::1', 1, '2025-07-23 12:41:28'),
(65, '<EMAIL>', '::1', 0, '2025-07-23 17:01:49'),
(66, 'asasasa', '::1', 1, '2025-07-23 17:02:06'),
(67, 'abdallah', '::1', 1, '2025-07-23 17:03:34'),
(68, 'asas', '::1', 1, '2025-07-23 19:46:04'),
(69, 'abdallah', '::1', 1, '2025-07-23 20:17:08'),
(70, 'asa', '::1', 1, '2025-07-23 20:18:21'),
(71, 'abdallah', '::1', 1, '2025-07-23 21:05:41'),
(72, 'abdallah', '::1', 1, '2025-07-23 23:02:35'),
(73, 'abdallah', '::1', 1, '2025-07-23 23:19:33'),
(74, 'abdallah', '::1', 1, '2025-07-23 23:25:48'),
(75, 'abdallah', '::1', 1, '2025-07-23 23:46:30'),
(76, 'abdallah', '::1', 1, '2025-07-23 23:58:07'),
(77, 'abdallah', '::1', 1, '2025-07-24 00:32:33'),
(78, 'abdallah', '::1', 1, '2025-07-24 00:57:51'),
(79, 'asmaa', '::1', 1, '2025-07-24 02:49:12'),
(80, 'abdallah', '::1', 1, '2025-07-24 03:41:41'),
(81, 'abdallah', '::1', 1, '2025-07-24 04:23:59'),
(82, 'abdallah', '::1', 1, '2025-07-24 04:39:14'),
(83, 'abdallah', '::1', 1, '2025-07-24 13:45:04'),
(84, '<EMAIL>', '::1', 0, '2025-07-24 22:36:50'),
(85, 'abdallah', '::1', 1, '2025-07-24 22:37:16'),
(86, '<EMAIL>', '**************', 1, '2025-07-25 00:30:53'),
(87, 'abdallah', '::1', 1, '2025-07-25 00:40:03'),
(88, '<EMAIL>', '**************', 0, '2025-07-25 00:42:11'),
(89, '<EMAIL>', '**************', 1, '2025-07-25 00:43:10'),
(90, 'abdallah2011', '**************', 1, '2025-07-25 02:04:36');

-- --------------------------------------------------------

--
-- Table structure for table `message_categories`
--

CREATE TABLE `message_categories` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '?',
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#4682B4',
  `is_active` tinyint(1) DEFAULT '1',
  `sort_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `message_categories`
--

INSERT INTO `message_categories` (`id`, `name`, `name_ar`, `description`, `icon`, `color`, `is_active`, `sort_order`, `created_at`) VALUES
(1, 'general', 'عام', 'أسئلة عامة', '📝', '#4682B4', 1, 1, '2025-07-21 17:01:10'),
(2, 'academic', 'أكاديمي', 'أسئلة أكاديمية ودراسية', '📚', '#28a745', 1, 2, '2025-07-21 17:01:10'),
(3, 'technical', 'تقني', 'مشاكل تقنية', '🔧', '#dc3545', 1, 3, '2025-07-21 17:01:10'),
(4, 'payment', 'دفع', 'استفسارات الدفع والاشتراكات', '💳', '#ffc107', 1, 4, '2025-07-21 17:01:10'),
(5, 'complaint', 'شكوى', 'الشكاوى والاقتراحات', '📢', '#fd7e14', 1, 5, '2025-07-21 17:01:10');

-- --------------------------------------------------------

--
-- Table structure for table `message_notifications`
--

CREATE TABLE `message_notifications` (
  `id` int NOT NULL,
  `message_id` int NOT NULL,
  `recipient_type` enum('admin','student') COLLATE utf8mb4_unicode_ci NOT NULL,
  `recipient_id` int NOT NULL,
  `notification_type` enum('new_message','new_reply','status_change') COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `message_notifications`
--

INSERT INTO `message_notifications` (`id`, `message_id`, `recipient_type`, `recipient_id`, `notification_type`, `is_read`, `created_at`, `read_at`) VALUES
(1, 1, 'admin', 1, 'new_message', 0, '2025-07-21 17:03:04', NULL),
(2, 1, 'admin', 11, 'new_message', 0, '2025-07-21 17:03:04', NULL),
(4, 2, 'admin', 1, 'new_message', 0, '2025-07-21 17:06:34', NULL),
(5, 2, 'admin', 11, 'new_message', 0, '2025-07-21 17:06:34', NULL),
(7, 3, 'admin', 1, 'new_message', 0, '2025-07-21 17:14:44', NULL),
(8, 3, 'admin', 11, 'new_message', 0, '2025-07-21 17:14:44', NULL),
(10, 3, 'student', 2, 'new_reply', 0, '2025-07-21 17:43:44', NULL),
(11, 3, 'student', 2, 'new_reply', 0, '2025-07-21 17:45:04', NULL),
(12, 4, 'admin', 1, 'new_message', 0, '2025-07-21 17:47:43', NULL),
(13, 4, 'admin', 11, 'new_message', 0, '2025-07-21 17:47:43', NULL),
(15, 4, 'student', 1, 'new_reply', 0, '2025-07-21 17:47:43', NULL),
(16, 3, 'student', 2, 'new_reply', 0, '2025-07-21 17:50:16', NULL),
(17, 4, 'student', 1, 'new_reply', 0, '2025-07-22 22:08:28', NULL),
(18, 5, 'admin', 1, 'new_message', 0, '2025-07-24 23:13:46', NULL),
(19, 5, 'admin', 11, 'new_message', 0, '2025-07-24 23:13:46', NULL),
(20, 6, 'admin', 1, 'new_message', 0, '2025-07-25 01:47:16', NULL),
(21, 6, 'admin', 11, 'new_message', 0, '2025-07-25 01:47:16', NULL),
(23, 7, 'admin', 1, 'new_message', 0, '2025-07-25 01:49:40', NULL),
(24, 7, 'admin', 11, 'new_message', 0, '2025-07-25 01:49:40', NULL),
(26, 8, 'admin', 1, 'new_message', 0, '2025-07-25 01:50:01', NULL),
(27, 8, 'admin', 11, 'new_message', 0, '2025-07-25 01:50:01', NULL),
(29, 9, 'admin', 1, 'new_message', 0, '2025-07-25 01:50:52', NULL),
(30, 9, 'admin', 11, 'new_message', 0, '2025-07-25 01:50:52', NULL),
(32, 10, 'admin', 1, 'new_message', 0, '2025-07-25 01:52:06', NULL),
(33, 10, 'admin', 11, 'new_message', 0, '2025-07-25 01:52:06', NULL),
(35, 11, 'admin', 1, 'new_message', 0, '2025-07-25 01:52:38', NULL),
(36, 11, 'admin', 11, 'new_message', 0, '2025-07-25 01:52:38', NULL),
(38, 12, 'admin', 1, 'new_message', 0, '2025-07-25 01:53:57', NULL),
(39, 12, 'admin', 11, 'new_message', 0, '2025-07-25 01:53:57', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `message_templates`
--

CREATE TABLE `message_templates` (
  `id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `admin_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('info','warning','success','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT '0',
  `is_global` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `admin_id`, `title`, `message`, `type`, `is_read`, `is_global`, `created_at`, `read_at`) VALUES
(9, 3, 1, 'مرحباً بك في سلسلة الدكتور', 'أهلاً وسهلاً ليلي! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.', 'success', 0, 0, '2025-07-19 21:01:09', NULL),
(10, 1, 1, 'سلسلة الدكتور في الوضع التجريبي', '✨مرحبا بكم في سلسلة الدكتور لي تعليم الغة العربيه مع افضل معلم مستر: محمد عبدالله اكبر المدريس في المادة اكثر من 1000 طالب متفوق خبره 15سنة✨', 'info', 0, 0, '2025-07-21 00:47:56', NULL),
(11, 2, 1, 'سلسلة الدكتور في الوضع التجريبي', '✨مرحبا بكم في سلسلة الدكتور لي تعليم الغة العربيه مع افضل معلم مستر: محمد عبدالله اكبر المدريس في المادة اكثر من 1000 طالب متفوق خبره 15سنة✨', 'info', 0, 0, '2025-07-21 00:47:56', NULL),
(12, 3, 1, 'سلسلة الدكتور في الوضع التجريبي', '✨مرحبا بكم في سلسلة الدكتور لي تعليم الغة العربيه مع افضل معلم مستر: محمد عبدالله اكبر المدريس في المادة اكثر من 1000 طالب متفوق خبره 15سنة✨', 'info', 0, 0, '2025-07-21 00:47:57', NULL),
(13, 4, 1, 'مرحباً بك في سلسلة الدكتور', 'أهلاً وسهلاً عبدالله! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.', 'success', 1, 0, '2025-07-21 14:51:25', '2025-07-24 12:53:07'),
(14, 2, 1, 'رد جديد على رسالتك', 'تم الرد على رسالتك من قبل الإدارة. يمكنك مراجعة الرد في صفحة \"اسأل معلم\".', 'info', 0, 0, '2025-07-21 17:43:44', NULL),
(15, 2, 1, 'رد جديد على رسالتك', 'تم الرد على رسالتك من قبل الإدارة. يمكنك مراجعة الرد في صفحة \"اسأل معلم\".', 'info', 0, 0, '2025-07-21 17:45:04', NULL),
(16, 1, 1, 'رد جديد على رسالتك', 'تم الرد على رسالتك من قبل الإدارة. يمكنك مراجعة الرد في صفحة \"اسأل معلم\".', 'info', 0, 0, '2025-07-21 17:47:44', NULL),
(17, 2, 11, 'رد جديد على رسالتك', 'تم الرد على رسالتك من قبل الإدارة. يمكنك مراجعة الرد في صفحة \"اسأل معلم\".', 'info', 0, 0, '2025-07-21 17:50:16', NULL),
(18, 5, 1, 'مرحباً بك في سلسلة الدكتور', 'أهلاً وسهلاً عبدالله! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.', 'success', 1, 0, '2025-07-22 02:54:27', '2025-07-24 03:19:01'),
(19, 6, 1, 'مرحباً بك في سلسلة الدكتور', 'أهلاً وسهلاً عبدالله! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.', 'success', 0, 0, '2025-07-22 04:16:59', NULL),
(20, 1, 11, 'إشعار تجريبي من الإدارة 16:39:22', 'هذا إشعار تجريبي تم إرساله من لوحة التحكم', 'info', 0, 0, '2025-07-22 22:04:53', NULL),
(21, 1, 11, 'إشعار جماعي تجريبي', 'هذا إشعار تم إرساله لعدة مستخدمين', 'warning', 0, 0, '2025-07-22 22:04:53', NULL),
(22, 2, 11, 'إشعار جماعي تجريبي', 'هذا إشعار تم إرساله لعدة مستخدمين', 'warning', 0, 0, '2025-07-22 22:04:53', NULL),
(23, 1, 11, 'رد جديد على رسالتك', 'تم الرد على رسالتك من قبل الإدارة. يمكنك مراجعة الرد في صفحة \"اسأل معلم\".', 'info', 0, 0, '2025-07-22 22:08:28', NULL),
(24, 7, 1, 'مرحباً بك في سلسلة الدكتور', 'أهلاً وسهلاً عبدالله! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.', 'success', 0, 0, '2025-07-25 02:04:25', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `notification_settings`
--

CREATE TABLE `notification_settings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `notification_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_enabled` tinyint(1) DEFAULT '1',
  `last_shown` timestamp NULL DEFAULT NULL,
  `show_count` int DEFAULT '0',
  `max_show_count` int DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notification_settings`
--

INSERT INTO `notification_settings` (`id`, `user_id`, `notification_type`, `is_enabled`, `last_shown`, `show_count`, `max_show_count`) VALUES
(1, 1, 'subscription_expired', 1, '2025-07-23 20:18:32', 1, 1);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `used` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` int NOT NULL,
  `subscription_id` int NOT NULL COMMENT 'معرف الاشتراك',
  `user_id` int NOT NULL COMMENT 'معرف المستخدم',
  `payment_method` enum('code','fawry','visa','wallet') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'طريقة الدفع',
  `payment_gateway` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'بوابة الدفع',
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'معرف المعاملة',
  `gateway_response` json DEFAULT NULL COMMENT 'استجابة البوابة',
  `amount` decimal(10,2) NOT NULL COMMENT 'المبلغ',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'EGP' COMMENT 'العملة',
  `status` enum('pending','processing','completed','failed','cancelled','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT 'حالة الدفع',
  `payment_date` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الدفع',
  `refund_date` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الاسترداد',
  `refund_amount` decimal(10,2) DEFAULT NULL COMMENT 'مبلغ الاسترداد',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT 'ملاحظات',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_logs`
--

CREATE TABLE `payment_logs` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `payment_method` enum('fawry','paymob','code','manual') COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_reference` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','completed','failed','refunded') COLLATE utf8mb4_unicode_ci NOT NULL,
  `gateway_response` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_requests`
--

CREATE TABLE `payment_requests` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `phone_number` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `receipt_image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','approved','rejected') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `admin_notes` text COLLATE utf8mb4_unicode_ci,
  `processed_by` int DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_transactions`
--

CREATE TABLE `payment_transactions` (
  `id` int NOT NULL,
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `gateway` enum('fawry','paymob') COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'EGP',
  `status` enum('pending','completed','failed','cancelled','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `gateway_transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_transactions`
--

INSERT INTO `payment_transactions` (`id`, `transaction_id`, `user_id`, `course_id`, `gateway`, `amount`, `currency`, `status`, `gateway_transaction_id`, `gateway_response`, `created_at`, `updated_at`) VALUES
(1, 'TXN_1753160099_687f19a3692b9', 6, 3, 'paymob', 630.00, 'EGP', 'pending', '358954046', '{\"order_id\": 358954046, \"payment_token\": \"ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pZek1EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRZM0xDSnZjbVJsY2w5cFpDSTZNelU0T1RVME1EUTJMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SW1GellYTmhjMkVpTENKc1lYTjBYMjVoYldVaU9pSlZjMlZ5SWl3aWMzUnlaV1YwSWpvaVRrRWlMQ0ppZFdsc1pHbHVaeUk2SWs1Qklpd2labXh2YjNJaU9pSk9RU0lzSW1Gd1lYSjBiV1Z1ZENJNklrNUJJaXdpWTJsMGVTSTZJa05oYVhKdklpd2ljM1JoZEdVaU9pSkRZV2x5YnlJc0ltTnZkVzUwY25raU9pSkZSeUlzSW1WdFlXbHNJam9pWVhOaGMyRkFaMjFoYVd3dVkyOXRJaXdpY0dodmJtVmZiblZ0WW1WeUlqb2lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFMk1UYzNNU3dpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5ONDB2R09VSjgxWTVIS2hLRnh0dng1WUFyYXFERk53R0F5dE5qSzV2cUtXUjhPanVwZlJJdnZNVDE4ZnNLNS02MHZaWUcwNms1SzZWS2NILTJWb09tUQ==\"}', '2025-07-22 04:22:42', '2025-07-22 04:22:51'),
(2, 'TXN_1753160661_687f1bd5697f4', 6, 3, 'fawry', 630.00, 'EGP', 'failed', NULL, NULL, '2025-07-22 04:32:04', '2025-07-22 04:32:11'),
(3, 'TXN_1753161104_687f1d903ffb8', 6, 3, 'paymob', 630.00, 'EGP', 'pending', '358955587', '{\"order_id\": 358955587, \"payment_token\": \"ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pZek1EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRZM0xDSnZjbVJsY2w5cFpDSTZNelU0T1RVMU5UZzNMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SW1GellYTmhjMkVpTENKc1lYTjBYMjVoYldVaU9pSlZjMlZ5SWl3aWMzUnlaV1YwSWpvaVRrRWlMQ0ppZFdsc1pHbHVaeUk2SWs1Qklpd2labXh2YjNJaU9pSk9RU0lzSW1Gd1lYSjBiV1Z1ZENJNklrNUJJaXdpWTJsMGVTSTZJa05oYVhKdklpd2ljM1JoZEdVaU9pSkRZV2x5YnlJc0ltTnZkVzUwY25raU9pSkZSeUlzSW1WdFlXbHNJam9pWVhOaGMyRkFaMjFoYVd3dVkyOXRJaXdpY0dodmJtVmZiblZ0WW1WeUlqb2lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFMk1qYzRNaXdpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5xY043dV80YUQzR0JMYlBwQ29BdHZhbW9UbnFadk53VUtRVUxWemszaEt6MGh0QTRHTTA1WXJYc1pIY0ttbnBJaTA5Zm1LRVBhY3hLNzhmZzE4bmRJQQ==\"}', '2025-07-22 04:39:27', '2025-07-22 04:39:42'),
(4, 'TXN_1753161481_687f1f09d5669', 6, 3, 'paymob', 630.00, 'EGP', 'pending', '358956133', '{\"order_id\": 358956133, \"payment_token\": \"ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pZek1EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRNeUxDSnZjbVJsY2w5cFpDSTZNelU0T1RVMk1UTXpMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SW1GellYTmhjMkVpTENKc1lYTjBYMjVoYldVaU9pSlZjMlZ5SWl3aWMzUnlaV1YwSWpvaVRrRWlMQ0ppZFdsc1pHbHVaeUk2SWs1Qklpd2labXh2YjNJaU9pSk9RU0lzSW1Gd1lYSjBiV1Z1ZENJNklrNUJJaXdpWTJsMGVTSTZJa05oYVhKdklpd2ljM1JoZEdVaU9pSkRZV2x5YnlJc0ltTnZkVzUwY25raU9pSkZSeUlzSW1WdFlXbHNJam9pWVhOaGMyRkFaMjFoYVd3dVkyOXRJaXdpY0dodmJtVmZiblZ0WW1WeUlqb2lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFMk16RTFOU3dpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5mZTVPZHpSSGd1U1FvclZERGFEZDBBOXU5THA1bW5MZGlnMmMtRElfVGVYamJPTGdSc010VTktZ1paVHdUbi1OMWE2Uld3NGlaTTNPcjA2WTZTSHY0UQ==\"}', '2025-07-22 04:45:45', '2025-07-22 04:45:55'),
(5, 'TXN_1753165062_687f2d0666dba', 6, 3, 'paymob', 630.00, 'EGP', 'pending', '358982167', '{\"order_id\": 358982167, \"payment_token\": \"ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pZek1EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRNeUxDSnZjbVJsY2w5cFpDSTZNelU0T1RneU1UWTNMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SW1GellYTmhjMkVpTENKc1lYTjBYMjVoYldVaU9pSlZjMlZ5SWl3aWMzUnlaV1YwSWpvaVRrRWlMQ0ppZFdsc1pHbHVaeUk2SWs1Qklpd2labXh2YjNJaU9pSk9RU0lzSW1Gd1lYSjBiV1Z1ZENJNklrNUJJaXdpWTJsMGVTSTZJa05oYVhKdklpd2ljM1JoZEdVaU9pSkRZV2x5YnlJc0ltTnZkVzUwY25raU9pSkZSeUlzSW1WdFlXbHNJam9pWVhOaGMyRkFaMjFoYVd3dVkyOXRJaXdpY0dodmJtVmZiblZ0WW1WeUlqb2lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFM016TXdOaXdpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5EQm9qVjRETXlwT0pQcnlEQWxfOFRHdUhEcTEySER6eF9ISmxnR3hIN2hnSVY2UnpPd18wOHlFYll1cUMtNW1waXI4a3pNeUt3amxQeGtaaU96SV9rdw==\"}', '2025-07-22 07:35:02', '2025-07-22 07:35:06'),
(6, 'TXN_1753166391_687f323774dc6', 6, 3, 'paymob', 630.00, 'EGP', 'pending', '358987195', '{\"order_id\": 358987195, \"payment_token\": \"ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pZek1EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRNeUxDSnZjbVJsY2w5cFpDSTZNelU0T1RnM01UazFMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SW1GellYTmhjMkVpTENKc1lYTjBYMjVoYldVaU9pSlZjMlZ5SWl3aWMzUnlaV1YwSWpvaVRrRWlMQ0ppZFdsc1pHbHVaeUk2SWs1Qklpd2labXh2YjNJaU9pSk9RU0lzSW1Gd1lYSjBiV1Z1ZENJNklrNUJJaXdpWTJsMGVTSTZJa05oYVhKdklpd2ljM1JoZEdVaU9pSkRZV2x5YnlJc0ltTnZkVzUwY25raU9pSkZSeUlzSW1WdFlXbHNJam9pWVhOaGMyRkFaMjFoYVd3dVkyOXRJaXdpY0dodmJtVmZiblZ0WW1WeUlqb2lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFM05EWXpOU3dpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5Jb3JQQ3IyM3RpWGxFY25CYTVUM25OeTV3b21iZnRZZERzOGtZVmE4MUJLdkUyUGd0M3Q5bU40LXkxSGoyX2VFYklUeG96clhBN19yRFdpX1BJWDludw==\"}', '2025-07-22 07:57:11', '2025-07-22 07:57:15'),
(7, 'TXN_1753173971_687f4fd36db5a', 6, 3, 'paymob', 630.00, 'EGP', 'pending', '359171156', '{\"order_id\": 359171156, \"payment_token\": \"ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pZek1EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRNeUxDSnZjbVJsY2w5cFpDSTZNelU1TVRjeE1UVTJMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SW1GellYTmhjMkVpTENKc1lYTjBYMjVoYldVaU9pSlZjMlZ5SWl3aWMzUnlaV1YwSWpvaVRrRWlMQ0ppZFdsc1pHbHVaeUk2SWs1Qklpd2labXh2YjNJaU9pSk9RU0lzSW1Gd1lYSjBiV1Z1ZENJNklrNUJJaXdpWTJsMGVTSTZJa05oYVhKdklpd2ljM1JoZEdVaU9pSkRZV2x5YnlJc0ltTnZkVzUwY25raU9pSkZSeUlzSW1WdFlXbHNJam9pWVhOaGMyRkFaMjFoYVd3dVkyOXRJaXdpY0dodmJtVmZiblZ0WW1WeUlqb2lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpJd05ETXdOeXdpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS4xVmx5eDFQYTRyQVRaaExJYktqcWZBQUZqbzRUaW1JcmJheFhNdnZHT1VHSVN1NnIxbUlxeThHclF3T1NuZDJmTjhEcDZjYklsLUxiSzNRcDJ2MkZkUQ==\"}', '2025-07-22 16:11:43', '2025-07-22 16:11:47');

-- --------------------------------------------------------

--
-- Table structure for table `paymob_orders`
--

CREATE TABLE `paymob_orders` (
  `id` int NOT NULL,
  `paymob_order_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'EGP',
  `payment_key` text COLLATE utf8mb4_unicode_ci,
  `status` enum('pending','completed','failed','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `customer_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subscription_id` int DEFAULT NULL,
  `callback_data` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `paymob_payments`
--

CREATE TABLE `paymob_payments` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `order_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','failed','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `paymob_payments`
--

INSERT INTO `paymob_payments` (`id`, `user_id`, `course_id`, `order_id`, `payment_token`, `amount`, `status`, `transaction_id`, `payment_method`, `payment_date`, `created_at`, `updated_at`) VALUES
(1, 4, 3, '358945014', 'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pjd01EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRZM0xDSnZjbVJsY2w5cFpDSTZNelU0T1RRMU1ERTBMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SWx4MU1EWXpPVngxTURZeU9GeDFNRFl5Wmx4MU1EWXlOMXgxTURZME5GeDFNRFkwTkZ4MU1EWTBOeUlzSW14aGMzUmZibUZ0WlNJNklsVnpaWElpTENKemRISmxaWFFpT2lKT1FTSXNJbUoxYVd4a2FXNW5Jam9pVGtFaUxDSm1iRzl2Y2lJNklrNUJJaXdpWVhCaGNuUnRaVzUwSWpvaVRrRWlMQ0pqYVhSNUlqb2lRMkZwY204aUxDSnpkR0YwWlNJNklrTmhhWEp2SWl3aVkyOTFiblJ5ZVNJNklrVkhJaXdpWlcxaGFXd2lPaUpoWW1SaGJHeGhhR2xpY21Gb1pXMDVNakUzTTBCb2IzUnRZV2xzTG1OdmJTSXNJbkJvYjI1bFgyNTFiV0psY2lJNklpc3lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFMU5UWXpOU3dpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5Sdkx4bWF4MTFvM2hBZElXbGNtb19HREhiOFR1YnVwaTBNRDdXZ0VJdzZHV0ZpRVhXR1l4Nk1CVU1odTVEX1Y0TE5CY3RTd05fbEM1UkxpNmtzdzl4dw==', 700.00, 'pending', NULL, NULL, NULL, '2025-07-22 02:40:35', '2025-07-22 02:40:35'),
(2, 5, 3, '358949605', 'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pjd01EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRZM0xDSnZjbVJsY2w5cFpDSTZNelU0T1RRNU5qQTFMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SWx4MU1EWXpPVngxTURZeU9GeDFNRFl5Wmx4MU1EWXlOMXgxTURZME5GeDFNRFkwTkZ4MU1EWTBOeUlzSW14aGMzUmZibUZ0WlNJNklsVnpaWElpTENKemRISmxaWFFpT2lKT1FTSXNJbUoxYVd4a2FXNW5Jam9pVGtFaUxDSm1iRzl2Y2lJNklrNUJJaXdpWVhCaGNuUnRaVzUwSWpvaVRrRWlMQ0pqYVhSNUlqb2lRMkZwY204aUxDSnpkR0YwWlNJNklrTmhhWEp2SWl3aVkyOTFiblJ5ZVNJNklrVkhJaXdpWlcxaGFXd2lPaUpoYzIxaFFHZHRZV2xzTG1OdmJTSXNJbkJvYjI1bFgyNTFiV0psY2lJNklpc3lNREV3TURBd01EQXdNREFpTENKd2IzTjBZV3hmWTI5a1pTSTZJazVCSWl3aVpYaDBjbUZmWkdWelkzSnBjSFJwYjI0aU9pSk9RU0o5TENKc2IyTnJYMjl5WkdWeVgzZG9aVzVmY0dGcFpDSTZabUZzYzJVc0ltVjRkSEpoSWpwN2ZTd2ljMmx1WjJ4bFgzQmhlVzFsYm5SZllYUjBaVzF3ZENJNlptRnNjMlVzSW1WNGNDSTZNVGMxTXpFMU9EY3dOQ3dpY0cxclgybHdJam9pTWpFM0xqVTFMakl5TWk0eE56a2lmUS5HR0dUT3pkWG9OS2VRNjhVbXo4blZ2UllfUkxiWGFVTUZqeFhGMUc1X2RwTkVrWUVYbkVYdFlPOHNhOF9aajNEZWpGZ0Q5ZmxqaWVzbjVhbHhqblZQUQ==', 700.00, 'pending', NULL, NULL, NULL, '2025-07-22 03:31:44', '2025-07-22 03:31:44');

-- --------------------------------------------------------

--
-- Table structure for table `regular_activation_codes`
--

CREATE TABLE `regular_activation_codes` (
  `id` int NOT NULL,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `plan_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `is_used` tinyint(1) DEFAULT '0',
  `used_by` int DEFAULT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exam_answers`
--

CREATE TABLE `student_exam_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `selected_option_id` int DEFAULT NULL,
  `text_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_correct` tinyint(1) DEFAULT '0',
  `marks_awarded` int DEFAULT '0',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exam_attempts`
--

CREATE TABLE `student_exam_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exam_id` int NOT NULL,
  `course_id` int NOT NULL,
  `attempt_number` int DEFAULT '1',
  `score` int DEFAULT '0',
  `total_marks` int NOT NULL,
  `percentage` decimal(5,2) DEFAULT '0.00',
  `is_passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exercise_answers`
--

CREATE TABLE `student_exercise_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `selected_option_id` int DEFAULT NULL,
  `text_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_correct` tinyint(1) DEFAULT '0',
  `marks_awarded` int DEFAULT '0',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_exercise_attempts`
--

CREATE TABLE `student_exercise_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `course_id` int NOT NULL,
  `attempt_number` int DEFAULT '1',
  `score` int DEFAULT '0',
  `total_marks` int NOT NULL,
  `percentage` decimal(5,2) DEFAULT '0.00',
  `is_passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_messages`
--

CREATE TABLE `student_messages` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_type` enum('question','help','complaint','suggestion','other') COLLATE utf8mb4_unicode_ci DEFAULT 'question',
  `category_id` int DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `status` enum('pending','read','replied','closed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `student_messages`
--

INSERT INTO `student_messages` (`id`, `user_id`, `subject`, `message`, `message_type`, `category_id`, `priority`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'رسالة اختبار - 2025-07-21 19:35:20', 'هذه رسالة اختبار للتأكد من أن نظام الرسائل يعمل بشكل صحيح.', 'question', 1, 'medium', 'pending', '2025-07-21 17:03:04', '2025-07-21 17:03:04'),
(2, 2, 'رسالة اختبار - 2025-07-21 19:38:50', 'هذه رسالة اختبار تم إنشاؤها تلقائياً للتأكد من عمل النظام.', 'question', 1, 'medium', 'pending', '2025-07-21 17:06:34', '2025-07-21 17:06:34'),
(3, 2, 'تجريبي', 'ابل', 'question', 1, 'medium', 'replied', '2025-07-21 17:14:44', '2025-07-21 17:50:16'),
(4, 1, 'رسالة اختبار نهائية - 20:19:59', 'هذه رسالة اختبار للتأكد من عمل النظام بشكل كامل', 'question', 1, 'medium', 'replied', '2025-07-21 17:47:43', '2025-07-22 22:08:28'),
(5, 4, 'تجريبي', 'j[vdfd', 'question', 4, 'high', 'pending', '2025-07-24 23:13:46', '2025-07-24 23:13:46'),
(6, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:47:16', '2025-07-25 01:47:16'),
(7, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:49:40', '2025-07-25 01:49:40'),
(8, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:50:01', '2025-07-25 01:50:01'),
(9, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:50:52', '2025-07-25 01:50:52'),
(10, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:52:06', '2025-07-25 01:52:06'),
(11, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:52:38', '2025-07-25 01:52:38'),
(12, 5, 'تجريبي', 'بق', 'question', 1, 'medium', 'pending', '2025-07-25 01:53:57', '2025-07-25 01:53:57');

-- --------------------------------------------------------

--
-- Table structure for table `subject_lessons`
--

CREATE TABLE `subject_lessons` (
  `id` int NOT NULL,
  `subject_id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `lesson_order` int DEFAULT '0',
  `is_free` tinyint(1) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subject_lessons`
--

INSERT INTO `subject_lessons` (`id`, `subject_id`, `title`, `description`, `lesson_order`, `is_free`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 39, 'تجريبي', 'تجريبي', 1, 1, 1, 11, '2025-07-21 18:53:39', '2025-07-21 18:53:39'),
(2, 39, 'تجريبي', 'تجريبي', 1, 1, 1, 11, '2025-07-21 18:53:40', '2025-07-21 18:53:40'),
(3, 39, 'تجريبي', 'تجريبي', 1, 1, 1, 11, '2025-07-21 18:53:40', '2025-07-21 18:53:40'),
(4, 39, 'تجريبي', 'تجريبي', 1, 1, 1, 11, '2025-07-21 18:53:41', '2025-07-21 18:53:41'),
(5, 38, 'تجريبي', 'تجريبي', 1, 0, 1, 11, '2025-07-21 19:37:14', '2025-07-21 19:37:14');

-- --------------------------------------------------------

--
-- Stand-in structure for view `subject_progress_summary`
-- (See below for the actual view)
--
CREATE TABLE `subject_progress_summary` (
);

-- --------------------------------------------------------

--
-- Table structure for table `subscription_cancellations`
--

CREATE TABLE `subscription_cancellations` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `plan_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paid_amount` decimal(10,2) DEFAULT NULL,
  `used_days` int DEFAULT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  `cancellation_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cancellation_reason` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `refund_status` enum('pending','processed','rejected') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscription_cancellations`
--

INSERT INTO `subscription_cancellations` (`id`, `user_id`, `plan_name`, `paid_amount`, `used_days`, `refund_amount`, `cancellation_date`, `cancellation_reason`, `refund_status`, `notes`) VALUES
(1, 4, 'خطة الماكس توفير', 700.00, 1, 676.67, '2025-07-23 19:33:01', 'user_request', 'pending', NULL),
(2, 4, 'خطة الماكس توفير', 700.00, 1, 676.67, '2025-07-24 15:25:56', 'user_request', 'pending', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `subscription_history`
--

CREATE TABLE `subscription_history` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `plan_id` int DEFAULT NULL,
  `plan_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('active','expired','cancelled','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `refund_amount` decimal(10,2) DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تاريخ الاشتراكات';

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الخطة',
  `name_en` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'اسم الخطة بالإنجليزية',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT 'وصف الخطة',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'السعر الأصلي',
  `discount_percentage` int DEFAULT '0' COMMENT 'نسبة الخصم',
  `discounted_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'السعر بعد الخصم',
  `duration_days` int NOT NULL DEFAULT '30' COMMENT 'مدة الاشتراك بالأيام',
  `features` json DEFAULT NULL COMMENT 'مميزات الخطة',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '?' COMMENT 'أيقونة الخطة',
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#4682B4' COMMENT 'لون الخطة',
  `is_popular` tinyint(1) DEFAULT '0' COMMENT 'هل الخطة شائعة',
  `is_active` tinyint(1) DEFAULT '1' COMMENT 'هل الخطة نشطة',
  `sort_order` int DEFAULT '0' COMMENT 'ترتيب العرض',
  `created_by` int NOT NULL DEFAULT '1' COMMENT 'منشئ الخطة',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`id`, `name`, `name_en`, `description`, `price`, `discount_percentage`, `discounted_price`, `duration_days`, `features`, `icon`, `color`, `is_popular`, `is_active`, `sort_order`, `created_by`, `created_at`, `updated_at`) VALUES
(4, 'خطة تجريبية 20:36:34', 'Test Plan 20:36:34', 'هذه خطة تجريبية تم إنشاؤها في 2025-07-22 20:36:34', 100.00, 10, 90.00, 30, '[\"الوصول لجميع الأقسام\", \"دعم فني متميز\", \"تحديثات مجانية\", \"اختبارات تفاعلية\"]', '🎓', '#28a745', 0, 1, 1, 1, '2025-07-23 18:41:56', '2025-07-23 18:41:56'),
(5, 'خطة تجريبية 20:36:34', 'Test Plan 20:36:34', 'هذه خطة تجريبية تم إنشاؤها في 2025-07-22 20:36:34', 100.00, 10, 90.00, 30, '[\"الوصول لجميع الأقسام\", \"دعم فني متميز\", \"تحديثات مجانية\", \"اختبارات تفاعلية\"]', '🎓', '#28a745', 0, 1, 1, 1, '2025-07-23 18:42:37', '2025-07-23 18:42:37'),
(6, 'خطة تجريبية 20:36:34', 'Test Plan 20:36:34', 'هذه خطة تجريبية تم إنشاؤها في 2025-07-22 20:36:34', 100.00, 10, 90.00, 30, '[\"الوصول لجميع الأقسام\", \"دعم فني متميز\", \"تحديثات مجانية\", \"اختبارات تفاعلية\"]', '🎓', '#28a745', 0, 1, 1, 1, '2025-07-23 18:42:43', '2025-07-23 18:42:43'),
(10, 'خطة الماكس توفير', 'plan max', 'تاةاتةةتااتت\r\nتنتنت\r\nنتن\r\nوتنتن', 700.00, 0, 700.00, 30, '[\"ةمن\", \"نتاتا\", \"نتن\", \"نمنم\", \"ككنمن\", \"نتنت\"]', 'fas fa-star', '#4f46e5', 0, 1, 1, 1, '2025-07-23 18:59:03', '2025-07-23 18:59:03');

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans_backup`
--

CREATE TABLE `subscription_plans_backup` (
  `id` int NOT NULL DEFAULT '0',
  `plan_name_ar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `duration_days` int NOT NULL DEFAULT '30',
  `features` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `education_level` enum('primary','preparatory','secondary','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `education_type` enum('azhari','general','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `grade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `specialization` enum('scientific','literary','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'all',
  `is_popular` tinyint(1) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `subscription_plans_backup`
--

INSERT INTO `subscription_plans_backup` (`id`, `plan_name_ar`, `description`, `price`, `duration_days`, `features`, `education_level`, `education_type`, `grade`, `specialization`, `is_popular`, `is_active`, `display_order`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'اتنالتن', 'نان', 700.00, 30, '[\"\\u0644\\u0627\\u0646\\u062a\\u0644\\u0627\\u0646\",\"\\u0639\\u0647\\u062d\\u062e\\u0627\",\"\\u0644\\u0639\\u0647\\u062e\\u0639\\u0644\",\"\\u0644\\u0639\\u0646\\u0643\\u0644\\u0647\\u0627\\u062e\\u0645\"]', 'preparatory', 'azhari', '3', 'all', 1, 1, 1, 11, '2025-07-21 23:56:04', '2025-07-21 23:56:04'),
(2, 'تجريبي', 'تجريبي', 122.00, 232, '[\"\\u064a\\u0633\\u0626\\u0628\\u0633\\u064a\\u0628\\u0633\\u064a\\u0633\\u064a\\u0628\\u0633\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\\u062a\\u062c\\u0631\\u064a\\u0628\\u064a\"]', 'preparatory', 'azhari', '3', 'all', 0, 1, 2, 11, '2025-07-21 23:58:15', '2025-07-21 23:58:15');

-- --------------------------------------------------------

--
-- Table structure for table `subscription_stats`
--

CREATE TABLE `subscription_stats` (
  `id` int NOT NULL,
  `date` date NOT NULL COMMENT 'التاريخ',
  `total_subscriptions` int DEFAULT '0' COMMENT 'إجمالي الاشتراكات',
  `new_subscriptions` int DEFAULT '0' COMMENT 'الاشتراكات الجديدة',
  `cancelled_subscriptions` int DEFAULT '0' COMMENT 'الاشتراكات الملغاة',
  `expired_subscriptions` int DEFAULT '0' COMMENT 'الاشتراكات المنتهية',
  `total_revenue` decimal(10,2) DEFAULT '0.00' COMMENT 'إجمالي الإيرادات',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `upcoming_exams`
--

CREATE TABLE `upcoming_exams` (
  `id` int NOT NULL,
  `exam_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exam_date` date NOT NULL,
  `exam_time` time NOT NULL,
  `duration_minutes` int NOT NULL DEFAULT '120',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `education_level` enum('primary','preparatory','secondary') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `upcoming_exams`
--

INSERT INTO `upcoming_exams` (`id`, `exam_name`, `subject`, `exam_date`, `exam_time`, `duration_minutes`, `location`, `instructions`, `education_level`, `education_type`, `grade`, `specialization`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(5, 'تجريبي', 'تجريبي', '2025-07-24', '13:12:00', 120, 'المنصه', 'تجريبي', 'preparatory', 'azhari', '3', NULL, 1, 11, '2025-07-24 17:06:15', '2025-07-24 17:06:15');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('student','teacher','admin') COLLATE utf8mb4_unicode_ci DEFAULT 'student',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `second_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `third_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `fourth_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` enum('male','female') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `birth_date` date NOT NULL,
  `personal_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `father_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `mother_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_level` enum('primary','preparatory','secondary') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `education_type` enum('azhari','general') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `grade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `specialization` enum('scientific','literary') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `email_verified` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `current_plan_id` int DEFAULT NULL COMMENT 'معرف الخطة الحالية',
  `subscription_status` enum('none','active','expired','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'none' COMMENT 'حالة الاشتراك',
  `subscription_end_date` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الاشتراك',
  `show_profile` tinyint(1) DEFAULT '1' COMMENT 'إظهار الملف الشخصي',
  `show_activity` tinyint(1) DEFAULT '0' COMMENT 'إظهار نشاط التعلم',
  `allow_messages` tinyint(1) DEFAULT '1' COMMENT 'السماح بالرسائل',
  `account_status` enum('active','deactivated','deleted') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT 'حالة الحساب',
  `theme_preference` enum('light','dark','auto') COLLATE utf8mb4_unicode_ci DEFAULT 'light' COMMENT 'تفضيل المظهر',
  `deactivated_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ إلغاء التفعيل',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الحذف'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `phone`, `password`, `username`, `email`, `role`, `password_hash`, `first_name`, `second_name`, `third_name`, `fourth_name`, `gender`, `birth_date`, `personal_phone`, `father_phone`, `mother_phone`, `education_level`, `education_type`, `grade`, `specialization`, `is_active`, `email_verified`, `created_at`, `updated_at`, `last_login`, `current_plan_id`, `subscription_status`, `subscription_end_date`, `show_profile`, `show_activity`, `allow_messages`, `account_status`, `theme_preference`, `deactivated_at`, `deleted_at`) VALUES
(1, 'تجريبي تجريبي تجريبي تجريبي', '***********', '$2y$10$8/7mAXdKFgtmVLsxUXEGdOyqSwCKw842LoUoJIoT62h91hLpnsAvu', 'asa', '<EMAIL>', 'admin', '$2y$10$8/7mAXdKFgtmVLsxUXEGdOyqSwCKw842LoUoJIoT62h91hLpnsAvu', 'تجريبي', 'تجريبي', 'تجريبي', 'تجريبي', 'male', '2010-03-17', '***********', '***********', '***********', 'preparatory', 'azhari', '2', NULL, 1, 0, '2025-07-16 22:19:09', '2025-07-23 20:18:21', '2025-07-23 20:18:21', NULL, 'expired', '2025-07-22 21:45:09', 1, 0, 1, 'active', 'light', NULL, NULL),
(2, 'تجريبي تجريبي تجريبي تجريبي', '***********', '$2y$10$HlqjoNQYxmRmSFZtax84UOA3ef2rqVJdcgphvDsQbRSFeBx1WOIHe', 'asas', '<EMAIL>', 'student', '$2y$10$HlqjoNQYxmRmSFZtax84UOA3ef2rqVJdcgphvDsQbRSFeBx1WOIHe', 'تجريبي', 'تجريبي', 'تجريبي', 'تجريبي', 'male', '2011-03-17', '***********', '***********', '***********', 'primary', 'azhari', '5', NULL, 1, 0, '2025-07-16 22:24:08', '2025-07-23 19:46:04', '2025-07-23 19:46:04', NULL, 'none', NULL, 1, 0, 1, 'active', 'light', NULL, NULL),
(3, NULL, NULL, NULL, 'lila', '<EMAIL>', 'student', '$2y$10$Dr.kkwO69DZaO62tILEn/.eHDepc.VZhhqlXTQ2119z0n0KjjQehm', 'ليل', 'إبراهيم', 'حسن', 'إبراهيم', 'female', '2011-07-19', '***********', '***********', '***********', 'secondary', 'azhari', '3', 'scientific', 1, 0, '2025-07-19 21:01:08', '2025-07-22 02:52:53', '2025-07-22 02:52:53', NULL, 'none', NULL, 1, 0, 1, 'active', 'light', NULL, NULL),
(4, NULL, NULL, NULL, 'abdallah', '<EMAIL>', 'student', '$2y$10$R9PqJaD4DqXYCLF3VGTxBO7pz41iEXIKrYdrwCxiqtFSqmgsvRRam', 'عبدالله', 'إبراهيم', 'حسن', 'إبراهيم', 'male', '2008-02-25', '***********', '***********', '***********', 'preparatory', 'azhari', '3', NULL, 1, 0, '2025-07-21 14:51:25', '2025-07-25 00:40:03', '2025-07-25 00:40:03', NULL, 'cancelled', '2025-07-24 15:25:56', 1, 0, 1, 'active', 'light', NULL, NULL),
(5, NULL, NULL, NULL, 'asmaa', '<EMAIL>', 'student', '$2y$10$TGpPpXqPZ/XRXAm0ZwMx.e2LdwRl28SF0nv90cuIqk/mKOljDcp5S', 'عبدالله', 'إبراهيم', 'حسن', 'إبراهيم', 'male', '2011-03-02', '***********', '***********', '***********', 'primary', 'azhari', '5', NULL, 1, 0, '2025-07-22 02:54:27', '2025-07-25 00:43:10', '2025-07-25 00:43:10', NULL, 'none', NULL, 1, 0, 1, 'active', 'light', NULL, NULL),
(6, NULL, NULL, NULL, 'asasasa', '<EMAIL>', 'student', '$2y$10$oIU3bq0enQIkSbjtNs8ePeF4pCRP4p9db6SSDaf9upfIq/Fdkaa6u', 'عبدالله', 'إبراهيم', 'حسن', 'إبراهيم', 'male', '2004-07-12', '***********', '***********', '***********', 'primary', 'azhari', '5', NULL, 1, 0, '2025-07-22 04:16:59', '2025-07-25 00:30:53', '2025-07-25 00:30:53', NULL, 'active', '2025-08-21 18:44:17', 1, 0, 1, 'active', 'light', NULL, NULL),
(7, NULL, NULL, NULL, 'abdallah2011', '<EMAIL>', 'student', '$2y$10$./ltJg3qrtqmHoCO6QtgZ.8ZlgbNXttXIlVm3LcSH2n2V8LgV.NMS', 'عبدالله', 'إبراهيم', 'حسن', 'إبراهيم', 'male', '2003-02-25', '***********', '***********', '***********', 'preparatory', 'azhari', '3', NULL, 1, 0, '2025-07-25 02:04:24', '2025-07-25 02:06:40', '2025-07-25 02:04:36', 10, 'active', '2025-08-23 22:06:40', 1, 0, 1, 'active', 'light', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_activity_log`
--

CREATE TABLE `user_activity_log` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `activity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `activity_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` json DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_activity_log`
--

INSERT INTO `user_activity_log` (`id`, `user_id`, `activity_type`, `activity_description`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(21, 2, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 00:36:24'),
(22, 2, 'note_delete', 'تم حذف الملاحظة: فغق', '{\"title\": \"فغق\", \"note_id\": 2}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 02:40:42'),
(23, 4, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 14:51:36'),
(24, 4, 'todo_create', 'تم إضافة مهمة جديدة: المنهج', '{\"title\": \"المنهج\", \"due_date\": \"2025-07-29\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 14:52:19'),
(25, 2, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"1\", \"message_type\": \"question\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 16:54:50'),
(26, 2, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"high\", \"message_id\": \"2\", \"message_type\": \"help\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 16:59:13'),
(27, 1, 'message_sent', 'تم إرسال رسالة جديدة: رسالة اختبار - 2025-07-21 19:35:20', '{\"subject\": \"رسالة اختبار - 2025-07-21 19:35:20\", \"priority\": \"medium\", \"message_id\": \"1\", \"message_type\": \"question\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 17:03:04'),
(28, 2, 'message_sent', 'تم إرسال رسالة جديدة: رسالة اختبار - 2025-07-21 19:38:50', '{\"subject\": \"رسالة اختبار - 2025-07-21 19:38:50\", \"priority\": \"medium\", \"message_id\": \"2\", \"message_type\": \"question\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 17:06:34'),
(29, 2, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"3\", \"message_type\": \"question\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 17:14:44'),
(30, 1, 'message_sent', 'تم إرسال رسالة جديدة: رسالة اختبار نهائية - 20:19:59', '{\"subject\": \"رسالة اختبار نهائية - 20:19:59\", \"priority\": \"medium\", \"message_id\": \"4\", \"message_type\": \"question\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-21 17:47:43'),
(31, 3, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-22 02:52:56'),
(32, 5, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-22 02:54:37'),
(33, 6, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-22 04:17:08'),
(34, 6, 'todo_create', 'تم إضافة مهمة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"due_date\": \"2025-07-23\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-22 08:55:35'),
(35, 4, 'todo_create', 'تم إضافة مهمة جديدة: تجريبي', '{\"title\": \"تجريبي\", \"due_date\": \"2025-07-30\", \"priority\": \"medium\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-24 12:44:56'),
(36, 4, 'todo_complete', 'إكمال المهمة: تجريبي', '{\"title\": \"تجريبي\", \"todo_id\": 9, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-24 13:15:12'),
(37, 4, 'todo_complete', 'إكمال المهمة: المنهج', '{\"title\": \"المنهج\", \"todo_id\": 7, \"completed\": true}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-24 13:49:23'),
(38, 4, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"high\", \"message_id\": \"5\", \"message_type\": \"question\"}', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-24 23:13:47'),
(39, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"6\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:47:16'),
(40, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"7\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:49:40'),
(41, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"8\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:50:01'),
(42, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"9\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:50:52'),
(43, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"10\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:52:06'),
(44, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"11\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:52:38'),
(45, 5, 'message_sent', 'تم إرسال رسالة جديدة: تجريبي', '{\"subject\": \"تجريبي\", \"priority\": \"medium\", \"message_id\": \"12\", \"message_type\": \"question\"}', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 01:53:57'),
(46, 7, 'login', 'مرحباً بك في منصة سلسلة الدكتور!', NULL, '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-07-25 02:04:38');

-- --------------------------------------------------------

--
-- Table structure for table `user_content_attempts`
--

CREATE TABLE `user_content_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_type` enum('exercise','exam','weekly_test') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content_id` int NOT NULL,
  `attempt_number` int DEFAULT '1',
  `score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) DEFAULT '0.00',
  `percentage` decimal(5,2) DEFAULT '0.00',
  `time_taken_minutes` int DEFAULT '0',
  `is_passed` tinyint(1) DEFAULT '0',
  `is_completed` tinyint(1) DEFAULT '0',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_answers`
--

CREATE TABLE `user_exam_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_answers_simple`
--

CREATE TABLE `user_exam_answers_simple` (
  `id` int NOT NULL,
  `user_id` int NOT NULL DEFAULT '1',
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `score` decimal(5,2) DEFAULT '0.00',
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exam_answers_simple`
--

INSERT INTO `user_exam_answers_simple` (`id`, `user_id`, `question_id`, `user_answer`, `is_correct`, `score`, `attempt_number`, `created_at`) VALUES
(13, 2, 1, 'true', 1, 5.00, 1, '2025-07-19 19:53:01'),
(14, 2, 2, '9', 1, 5.00, 1, '2025-07-19 19:53:03'),
(16, 2, 7, 'false', 0, 0.00, 1, '2025-07-19 20:00:51'),
(18, 2, 6, 'false', 0, 0.00, 1, '2025-07-19 20:03:05'),
(29, 2, 12, 'true', 1, 1.00, 1, '2025-07-20 17:30:26'),
(30, 2, 13, 'عربي', 1, 1.00, 1, '2025-07-20 17:30:26'),
(42, 2, 14, 'true', 1, 1.00, 1, '2025-07-21 03:20:54'),
(51, 2, 8, 'true', 1, 5.00, 1, '2025-07-21 05:35:00'),
(52, 2, 9, 'true', 1, 5.00, 1, '2025-07-21 05:35:00'),
(53, 2, 10, 'الثالث', 1, 5.00, 1, '2025-07-21 05:35:00'),
(54, 2, 12, 'true', 1, 1.00, 2, '2025-07-21 05:46:11'),
(55, 2, 13, 'عربي', 1, 1.00, 2, '2025-07-21 05:46:11'),
(56, 2, 14, 'true', 1, 1.00, 2, '2025-07-21 05:46:49'),
(59, 2, 11, 'true', 1, 1.00, 1, '2025-07-21 05:48:07'),
(60, 6, 8, 'true', 1, 5.00, 1, '2025-07-22 17:58:09'),
(61, 6, 9, 'true', 1, 5.00, 1, '2025-07-22 17:58:09'),
(62, 6, 10, 'الثالث', 1, 5.00, 1, '2025-07-22 17:58:09');

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_attempts`
--

CREATE TABLE `user_exam_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exam_id` int NOT NULL,
  `total_score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) DEFAULT '0.00',
  `percentage` decimal(5,2) DEFAULT '0.00',
  `passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `attempt_number` int DEFAULT '1',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exam_attempts`
--

INSERT INTO `user_exam_attempts` (`id`, `user_id`, `exam_id`, `total_score`, `max_score`, `percentage`, `passed`, `time_taken_minutes`, `attempt_number`, `started_at`, `completed_at`) VALUES
(1, 2, 7, 15.00, 15.00, 100.00, 1, 0, 1, '2025-07-21 05:35:00', '2025-07-21 05:35:00'),
(2, 2, 9, 2.00, 2.00, 100.00, 1, 0, 2, '2025-07-21 05:46:11', '2025-07-21 05:46:11'),
(3, 2, 10, 1.00, 1.00, 100.00, 1, 0, 2, '2025-07-21 05:46:49', '2025-07-21 05:46:49'),
(4, 2, 8, 0.00, 1.00, 0.00, 0, 0, 4, '2025-07-21 05:47:20', '2025-07-21 05:47:20'),
(5, 2, 8, 0.00, 1.00, 0.00, 0, 0, 1, '2025-07-21 05:47:53', '2025-07-21 05:47:53'),
(6, 2, 8, 1.00, 1.00, 100.00, 1, 0, 1, '2025-07-21 05:48:07', '2025-07-21 05:48:07'),
(7, 6, 7, 15.00, 15.00, 100.00, 1, 0, 1, '2025-07-22 17:58:09', '2025-07-22 17:58:09');

-- --------------------------------------------------------

--
-- Table structure for table `user_exam_progress`
--

CREATE TABLE `user_exam_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT 'معرف المستخدم',
  `exam_id` int NOT NULL COMMENT 'معرف الامتحان',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT 'هل تم إكمال الامتحان',
  `score` decimal(5,2) DEFAULT '0.00' COMMENT 'النتيجة',
  `total_questions` int DEFAULT '0' COMMENT 'إجمالي الأسئلة',
  `correct_answers` int DEFAULT '0' COMMENT 'الإجابات الصحيحة',
  `is_passed` tinyint(1) DEFAULT '0' COMMENT 'هل نجح في الامتحان',
  `time_taken_minutes` int DEFAULT '0' COMMENT 'الوقت المستغرق بالدقائق',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الإكمال',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exam_progress`
--

INSERT INTO `user_exam_progress` (`id`, `user_id`, `exam_id`, `is_completed`, `score`, `total_questions`, `correct_answers`, `is_passed`, `time_taken_minutes`, `completed_at`, `created_at`, `updated_at`) VALUES
(1, 4, 2, 1, 100.00, 0, 0, 1, 0, '2025-07-23 23:28:08', '2025-07-23 22:46:44', '2025-07-23 23:28:08'),
(2, 4, 1, 1, 100.00, 0, 0, 1, 0, '2025-07-23 23:03:34', '2025-07-23 23:03:34', '2025-07-23 23:03:34');

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_answers`
--

CREATE TABLE `user_exercise_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_attempts`
--

CREATE TABLE `user_exercise_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exercise_attempts`
--

INSERT INTO `user_exercise_attempts` (`id`, `user_id`, `exercise_id`, `user_answer`, `is_correct`, `attempt_number`, `created_at`) VALUES
(1, 2, 5, 'true', 1, 1, '2025-07-19 16:09:03'),
(2, 2, 4, 'true', 1, 1, '2025-07-19 16:12:41'),
(3, 2, 3, 'false', 1, 1, '2025-07-19 16:41:35'),
(4, 2, 9, 'true', 1, 1, '2025-07-19 19:06:19'),
(5, 2, 1, 'true', 1, 1, '2025-07-19 19:53:17'),
(6, 2, 2, 'false', 1, 1, '2025-07-19 19:59:35'),
(7, 2, 3, 'false', 1, 2, '2025-07-19 19:59:45'),
(8, 2, 4, 'true', 1, 2, '2025-07-19 19:59:53'),
(15, 2, 10, 'عربي', 1, 1, '2025-07-21 04:59:40'),
(16, 2, 11, 'false', 1, 1, '2025-07-21 04:59:40'),
(17, 2, 12, 'lyiulyuil', 0, 1, '2025-07-21 04:59:40'),
(18, 2, 12, 'ljiklhjilhuil', 1, 2, '2025-07-21 05:07:06'),
(19, 6, 10, 'عربي', 1, 1, '2025-07-22 17:57:01'),
(20, 6, 11, 'false', 1, 1, '2025-07-22 17:57:10'),
(21, 6, 12, 'ljiklhjilhuil', 1, 1, '2025-07-22 17:57:22');

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_progress`
--

CREATE TABLE `user_exercise_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT 'معرف المستخدم',
  `exercise_id` int NOT NULL COMMENT 'معرف التدريب',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT 'هل تم إكمال التدريب',
  `score` decimal(5,2) DEFAULT '0.00' COMMENT 'النتيجة',
  `total_questions` int DEFAULT '0' COMMENT 'إجمالي الأسئلة',
  `correct_answers` int DEFAULT '0' COMMENT 'الإجابات الصحيحة',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الإكمال',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_exercise_progress`
--

INSERT INTO `user_exercise_progress` (`id`, `user_id`, `exercise_id`, `is_completed`, `score`, `total_questions`, `correct_answers`, `completed_at`, `created_at`, `updated_at`) VALUES
(1, 4, 1, 1, 100.00, 2, 2, '2025-07-23 21:35:28', '2025-07-23 21:35:28', '2025-07-23 21:35:28'),
(2, 4, 2, 1, 100.00, 3, 3, '2025-07-23 21:51:45', '2025-07-23 21:51:27', '2025-07-23 21:51:45'),
(3, 4, 3, 1, 100.00, 2, 2, '2025-07-24 02:09:28', '2025-07-23 22:50:36', '2025-07-24 02:09:28');

-- --------------------------------------------------------

--
-- Table structure for table `user_exercise_question_answers`
--

CREATE TABLE `user_exercise_question_answers` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `exercise_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `score` decimal(5,2) DEFAULT '0.00',
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_lesson_progress`
--

CREATE TABLE `user_lesson_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT 'معرف المستخدم',
  `lesson_id` int NOT NULL COMMENT 'معرف الدرس',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT 'هل تم إكمال الدرس',
  `completion_percentage` decimal(5,2) DEFAULT '0.00' COMMENT 'نسبة الإكمال',
  `last_accessed_at` timestamp NULL DEFAULT NULL COMMENT 'آخر وصول للدرس',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الإكمال',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_notes`
--

CREATE TABLE `user_notes` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'general',
  `is_pinned` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_notifications`
--

CREATE TABLE `user_notifications` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` json DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `action_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_notifications`
--

INSERT INTO `user_notifications` (`id`, `user_id`, `type`, `title`, `message`, `data`, `is_read`, `action_url`, `created_at`, `read_at`) VALUES
(2, 4, 'success', '🎉 تم تفعيل اشتراكك بنجاح!', 'تهانينا! تم تفعيل اشتراكك وأصبح بإمكانك الآن الوصول لجميع الدروس والمحتوى المدفوع. استمتع بتجربة التعلم!', NULL, 0, 'curriculum.php', '2025-07-21 19:34:43', NULL),
(4, 4, 'success', 'نجحت في التدريب!', 'تهانينا! لقد نجحت في تدريب \"تجريبي\" بنسبة 100.0%', NULL, 0, 'lesson_content.php?lesson_id=5', '2025-07-21 20:57:04', NULL),
(5, 4, 'success', 'نجحت في التدريب!', 'تهانينا! لقد نجحت في تدريب \"تدريب تجريبي\" بنسبة 66.7%', NULL, 0, 'lesson_content.php?lesson_id=5', '2025-07-21 21:11:37', NULL),
(6, 4, 'success', 'نجحت في التدريب!', 'تهانينا! لقد نجحت في تدريب \"تدريب تجريبي\" بنسبة 100.0%', NULL, 0, 'lesson_content.php?lesson_id=5', '2025-07-21 21:11:57', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_question_answers`
--

CREATE TABLE `user_question_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_correct` tinyint(1) DEFAULT '0',
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `answered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `session_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_settings`
--

CREATE TABLE `user_settings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `email_notifications` tinyint(1) DEFAULT '1',
  `browser_notifications` tinyint(1) DEFAULT '0',
  `exam_notifications` tinyint(1) DEFAULT '1',
  `subscription_notifications` tinyint(1) DEFAULT '1',
  `message_notifications` tinyint(1) DEFAULT '1',
  `font_size` enum('small','medium','large') COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `auto_play_videos` tinyint(1) DEFAULT '1',
  `animations_enabled` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات المستخدم';

-- --------------------------------------------------------

--
-- Table structure for table `user_study_sessions`
--

CREATE TABLE `user_study_sessions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `lesson_id` int DEFAULT NULL,
  `session_start` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `session_end` timestamp NULL DEFAULT NULL,
  `duration_minutes` int GENERATED ALWAYS AS (timestampdiff(MINUTE,`session_start`,`session_end`)) STORED,
  `activity_type` enum('video','reading','exercise','exam','general') COLLATE utf8mb4_unicode_ci DEFAULT 'general',
  `content_id` int DEFAULT NULL,
  `break_reminders_shown` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_subscriptions`
--

CREATE TABLE `user_subscriptions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT 'معرف المستخدم',
  `plan_id` int NOT NULL COMMENT 'معرف الخطة',
  `activation_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'كود التفعيل المستخدم',
  `payment_method` enum('code','fawry','visa','wallet') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'code' COMMENT 'طريقة الدفع',
  `payment_status` enum('pending','completed','failed','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'completed' COMMENT 'حالة الدفع',
  `amount_paid` decimal(10,2) DEFAULT '0.00' COMMENT 'المبلغ المدفوع',
  `start_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ بداية الاشتراك',
  `end_date` timestamp NOT NULL COMMENT 'تاريخ انتهاء الاشتراك',
  `cancelled_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الإلغاء',
  `cancellation_reason` text COLLATE utf8mb4_unicode_ci COMMENT 'سبب الإلغاء',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `duration_days` int DEFAULT NULL COMMENT 'مدة الاشتراك بالأيام'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_subscriptions`
--

INSERT INTO `user_subscriptions` (`id`, `user_id`, `plan_id`, `activation_code`, `payment_method`, `payment_status`, `amount_paid`, `start_date`, `end_date`, `cancelled_at`, `cancellation_reason`, `created_at`, `updated_at`, `duration_days`) VALUES
(15, 4, 10, 'CTDHSVUY', 'code', 'completed', 0.00, '2025-07-23 19:00:32', '2025-08-21 20:57:14', NULL, NULL, '2025-07-23 19:00:32', '2025-07-25 02:26:05', 30),
(16, 4, 10, 'RO5O3W7S', 'code', 'cancelled', 0.00, '2025-07-23 22:10:31', '2025-08-22 00:07:13', '2025-07-24 16:28:07', '', '2025-07-23 22:10:31', '2025-07-25 02:26:05', 30),
(17, 7, 10, 'L87A6GUL', 'code', 'completed', 0.00, '2025-07-25 02:06:40', '2025-08-23 22:06:40', NULL, NULL, '2025-07-25 02:06:40', '2025-07-25 02:26:05', 30);

-- --------------------------------------------------------

--
-- Table structure for table `user_summary_progress`
--

CREATE TABLE `user_summary_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT 'معرف المستخدم',
  `summary_id` int NOT NULL COMMENT 'معرف الملخص',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT 'هل تم إكمال الملخص',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الإكمال',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_todos`
--

CREATE TABLE `user_todos` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_completed` tinyint(1) DEFAULT '0',
  `priority` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `due_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_todos`
--

INSERT INTO `user_todos` (`id`, `user_id`, `title`, `description`, `is_completed`, `priority`, `due_date`, `created_at`, `updated_at`, `completed_at`) VALUES
(2, 2, 'تجريبي', 'تجريبي', 1, 'medium', '2025-07-30', '2025-07-17 03:46:44', '2025-07-20 15:26:38', NULL),
(3, 2, 'تجريبي', 'قفقف', 1, 'medium', '2025-07-29', '2025-07-20 15:19:40', '2025-07-20 15:26:36', NULL),
(4, 2, 'تجريبي', 'غع', 1, 'medium', '2025-07-29', '2025-07-20 15:27:31', '2025-07-20 16:26:29', NULL),
(5, 2, 'لبيب', 'بلبلبل', 1, 'medium', '2025-07-29', '2025-07-20 16:34:59', '2025-07-20 16:49:45', NULL),
(6, 2, 'لبيب', 'غفغ', 1, 'medium', '2025-07-29', '2025-07-20 16:49:31', '2025-07-20 16:49:42', NULL),
(7, 4, 'المنهج', 'المنهج', 1, 'medium', '2025-07-29', '2025-07-21 14:52:19', '2025-07-24 13:49:23', NULL),
(8, 6, 'تجريبي', 'dtred', 0, 'medium', '2025-07-23', '2025-07-22 08:55:35', '2025-07-22 08:55:35', NULL),
(9, 4, 'تجريبي', 'لالا', 1, 'medium', '2025-07-30', '2025-07-24 12:44:56', '2025-07-24 13:15:12', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_video_progress`
--

CREATE TABLE `user_video_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `video_id` int NOT NULL,
  `progress_percentage` decimal(5,2) DEFAULT '0.00',
  `last_position_seconds` int DEFAULT '0',
  `completed` tinyint(1) DEFAULT '0',
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_completed` tinyint(1) DEFAULT '0' COMMENT 'هل تم إكمال الفيديو'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_video_progress`
--

INSERT INTO `user_video_progress` (`id`, `user_id`, `video_id`, `progress_percentage`, `last_position_seconds`, `completed`, `completed_at`, `created_at`, `updated_at`, `is_completed`) VALUES
(1, 4, 2, 0.00, 0, 0, '2025-07-24 01:17:47', '2025-07-24 01:17:47', '2025-07-24 01:17:47', 1),
(2, 4, 1, 0.00, 0, 0, '2025-07-24 01:23:10', '2025-07-24 01:19:50', '2025-07-24 01:23:10', 1),
(3, 4, 3, 0.00, 0, 0, '2025-07-24 01:23:38', '2025-07-24 01:23:38', '2025-07-24 01:23:38', 1),
(5, 4, 4, 0.00, 0, 0, '2025-07-24 01:25:06', '2025-07-24 01:25:06', '2025-07-24 01:25:06', 1);

-- --------------------------------------------------------

--
-- Table structure for table `user_weekly_test_answers`
--

CREATE TABLE `user_weekly_test_answers` (
  `id` int NOT NULL,
  `attempt_id` int NOT NULL,
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) NOT NULL,
  `points_earned` decimal(5,2) DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_weekly_test_answers_simple`
--

CREATE TABLE `user_weekly_test_answers_simple` (
  `id` int NOT NULL,
  `user_id` int NOT NULL DEFAULT '1',
  `question_id` int NOT NULL,
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_correct` tinyint(1) DEFAULT '0',
  `score` decimal(5,2) DEFAULT '0.00',
  `attempt_number` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_weekly_test_answers_simple`
--

INSERT INTO `user_weekly_test_answers_simple` (`id`, `user_id`, `question_id`, `user_answer`, `is_correct`, `score`, `attempt_number`, `created_at`) VALUES
(9, 2, 1, 'true', 1, 3.00, 1, '2025-07-19 19:10:31'),
(10, 2, 2, '7', 1, 3.00, 1, '2025-07-19 19:10:31'),
(12, 2, 3, 'yuityuitt', 1, 1.00, 1, '2025-07-21 16:03:37');

-- --------------------------------------------------------

--
-- Table structure for table `user_weekly_test_attempts`
--

CREATE TABLE `user_weekly_test_attempts` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `test_id` int NOT NULL,
  `total_score` decimal(5,2) DEFAULT '0.00',
  `max_score` decimal(5,2) DEFAULT '0.00',
  `percentage` decimal(5,2) DEFAULT '0.00',
  `passed` tinyint(1) DEFAULT '0',
  `time_taken_minutes` int DEFAULT '0',
  `attempt_number` int DEFAULT '1',
  `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_weekly_test_attempts`
--

INSERT INTO `user_weekly_test_attempts` (`id`, `user_id`, `test_id`, `total_score`, `max_score`, `percentage`, `passed`, `time_taken_minutes`, `attempt_number`, `started_at`, `completed_at`) VALUES
(1, 2, 2, 1.00, 1.00, 100.00, 1, 0, 1, '2025-07-21 16:03:37', '2025-07-21 16:03:37');

-- --------------------------------------------------------

--
-- Table structure for table `video_watch_progress`
--

CREATE TABLE `video_watch_progress` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `video_id` int NOT NULL,
  `course_id` int NOT NULL,
  `watch_percentage` decimal(5,2) DEFAULT '0.00',
  `is_completed` tinyint(1) DEFAULT '0',
  `last_position_seconds` int DEFAULT '0',
  `total_watch_time_seconds` int DEFAULT '0',
  `first_watched_at` timestamp NULL DEFAULT NULL,
  `last_watched_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `wallet_payments`
--

CREATE TABLE `wallet_payments` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `course_id` int NOT NULL,
  `order_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `wallet_number` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `wallet_type` enum('vodafone_cash','etisalat_cash','orange_cash','we_cash') COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','failed','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `wallet_payments`
--

INSERT INTO `wallet_payments` (`id`, `user_id`, `course_id`, `order_id`, `payment_token`, `wallet_number`, `wallet_type`, `amount`, `status`, `transaction_id`, `payment_date`, `created_at`, `updated_at`) VALUES
(1, 5, 3, '358947827', 'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjFjMlZ5WDJsa0lqb3lNREEzTURrd0xDSmhiVzkxYm5SZlkyVnVkSE1pT2pjd01EQXdMQ0pqZFhKeVpXNWplU0k2SWtWSFVDSXNJbWx1ZEdWbmNtRjBhVzl1WDJsa0lqbzFNakEzTkRZM0xDSnZjbVJsY2w5cFpDSTZNelU0T1RRM09ESTNMQ0ppYVd4c2FXNW5YMlJoZEdFaU9uc2labWx5YzNSZmJtRnRaU0k2SWx4MU1EWXpPVngxTURZeU9GeDFNRFl5Wmx4MU1EWXlOMXgxTURZME5GeDFNRFkwTkZ4MU1EWTBOeUlzSW14aGMzUmZibUZ0WlNJNklsVnpaWElpTENKemRISmxaWFFpT2lKT1FTSXNJbUoxYVd4a2FXNW5Jam9pVGtFaUxDSm1iRzl2Y2lJNklrNUJJaXdpWVhCaGNuUnRaVzUwSWpvaVRrRWlMQ0pqYVhSNUlqb2lRMkZwY204aUxDSnpkR0YwWlNJNklrTmhhWEp2SWl3aVkyOTFiblJ5ZVNJNklrVkhJaXdpWlcxaGFXd2lPaUpoYzIxaFFHZHRZV2xzTG1OdmJTSXNJbkJvYjI1bFgyNTFiV0psY2lJNklqQXhNREU0TWpReU5ERTBJaXdpY0c5emRHRnNYMk52WkdVaU9pSk9RU0lzSW1WNGRISmhYMlJsYzJOeWFYQjBhVzl1SWpvaVRrRWlmU3dpYkc5amExOXZjbVJsY2w5M2FHVnVYM0JoYVdRaU9tWmhiSE5sTENKbGVIUnlZU0k2ZTMwc0luTnBibWRzWlY5d1lYbHRaVzUwWDJGMGRHVnRjSFFpT21aaGJITmxMQ0psZUhBaU9qRTNOVE14TlRjME1qQXNJbkJ0YTE5cGNDSTZJakl4Tnk0MU5TNHlNakl1TVRjNUluMC5aNzNIOFozQVdaRkh3aHQ1OVU2UzBSbVoybTA3ellpSXFVZ0hYN244b2RtMkFqWnBVM2xzMGs5ZFFEczFWYTVQVjVXTnFnTTVYcG9GZkR3MWxWUkZBZw==', '01018242414', 'vodafone_cash', 700.00, 'pending', NULL, NULL, '2025-07-22 03:10:21', '2025-07-22 03:10:21');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activation_codes`
--
ALTER TABLE `activation_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `plan_id` (`plan_id`),
  ADD KEY `used_by` (`used_by`),
  ADD KEY `idx_code` (`code`),
  ADD KEY `idx_used` (`is_used`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username_email` (`username_or_email`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_attempted_at` (`attempted_at`);

--
-- Indexes for table `admin_replies`
--
ALTER TABLE `admin_replies`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message_id` (`message_id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `center_activation_codes`
--
ALTER TABLE `center_activation_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_code` (`code`),
  ADD KEY `idx_plan_id` (`plan_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `courses`
--
ALTER TABLE `courses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `course_activation_codes`
--
ALTER TABLE `course_activation_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `used_by` (`used_by`),
  ADD KEY `idx_code` (`code`),
  ADD KEY `idx_course_unused` (`course_id`,`is_used`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `course_completion_notes`
--
ALTER TABLE `course_completion_notes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_completion` (`user_id`,`course_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_completion_date` (`completion_date`);

--
-- Indexes for table `course_content`
--
ALTER TABLE `course_content`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_course_week` (`course_id`,`week_number`),
  ADD KEY `idx_content_order` (`content_order`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_enrollments`
--
ALTER TABLE `course_enrollments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`);

--
-- Indexes for table `course_exams`
--
ALTER TABLE `course_exams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_week_number` (`week_number`);

--
-- Indexes for table `course_exam_questions`
--
ALTER TABLE `course_exam_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exam_id` (`exam_id`);

--
-- Indexes for table `course_exercises`
--
ALTER TABLE `course_exercises`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_course_week` (`course_id`,`week_number`),
  ADD KEY `idx_exercise_order` (`exercise_order`),
  ADD KEY `idx_question_type` (`question_type`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_exercise_questions`
--
ALTER TABLE `course_exercise_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exercise_id` (`exercise_id`),
  ADD KEY `idx_question_order` (`exercise_id`,`question_order`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `course_subscriptions`
--
ALTER TABLE `course_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_course` (`user_id`,`course_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_activation_status` (`activation_status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_course_subscriptions_method` (`activation_method`),
  ADD KEY `idx_course_subscriptions_status` (`activation_status`),
  ADD KEY `idx_payment_gateway` (`payment_gateway`),
  ADD KEY `idx_transaction_id` (`transaction_id`);

--
-- Indexes for table `course_videos`
--
ALTER TABLE `course_videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_course_week` (`course_id`,`week_number`),
  ADD KEY `idx_video_order` (`video_order`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `course_weekly_tests`
--
ALTER TABLE `course_weekly_tests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_week_number` (`week_number`);

--
-- Indexes for table `course_weekly_test_questions`
--
ALTER TABLE `course_weekly_test_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_test_id` (`test_id`);

--
-- Indexes for table `curriculum_sections`
--
ALTER TABLE `curriculum_sections`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_section` (`section_name`,`education_level`,`education_type`,`grade`,`specialization`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_specialization` (`specialization`),
  ADD KEY `idx_display_order` (`display_order`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `curriculum_subjects`
--
ALTER TABLE `curriculum_subjects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_specialization` (`specialization`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_completed` (`is_completed`),
  ADD KEY `idx_score` (`score`),
  ADD KEY `idx_percentage` (`percentage`),
  ADD KEY `idx_passed` (`passed`);

--
-- Indexes for table `exam_questions`
--
ALTER TABLE `exam_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exam` (`exam_id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_order` (`question_order`);

--
-- Indexes for table `exercise_attempts`
--
ALTER TABLE `exercise_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_completed` (`is_completed`),
  ADD KEY `idx_score` (`score`),
  ADD KEY `idx_percentage` (`percentage`);

--
-- Indexes for table `exercise_questions`
--
ALTER TABLE `exercise_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exercise` (`exercise_id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_order` (`question_order`);

--
-- Indexes for table `fawry_payments`
--
ALTER TABLE `fawry_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_course_fawry` (`user_id`,`course_id`),
  ADD KEY `idx_fawry_code` (`fawry_code`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `honor_board`
--
ALTER TABLE `honor_board`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_ranking` (`subject`,`achievement_type`,`achievement_date`,`education_level`,`education_type`,`grade`,`ranking_position`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_ranking_position` (`ranking_position`),
  ADD KEY `idx_achievement_date` (`achievement_date`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_achievement_type` (`achievement_type`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `lessons`
--
ALTER TABLE `lessons`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_subject_lesson` (`subject_id`,`lesson_number`),
  ADD KEY `idx_subject_id` (`subject_id`),
  ADD KEY `idx_lesson_number` (`lesson_number`),
  ADD KEY `idx_is_free` (`is_free`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `lesson_exams`
--
ALTER TABLE `lesson_exams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_exam_order` (`exam_order`);

--
-- Indexes for table `lesson_exercises`
--
ALTER TABLE `lesson_exercises`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_exercise_order` (`exercise_order`);

--
-- Indexes for table `lesson_files`
--
ALTER TABLE `lesson_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_file_type` (`file_type`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `lesson_progress`
--
ALTER TABLE `lesson_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_progress` (`user_id`,`lesson_id`,`content_type`,`content_id`),
  ADD KEY `lesson_id` (`lesson_id`),
  ADD KEY `idx_user_lesson` (`user_id`,`lesson_id`),
  ADD KEY `idx_content` (`content_type`,`content_id`),
  ADD KEY `idx_completed_at` (`completed_at`);

--
-- Indexes for table `lesson_summaries`
--
ALTER TABLE `lesson_summaries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_summary_order` (`summary_order`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `lesson_videos`
--
ALTER TABLE `lesson_videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_video_order` (`video_order`);

--
-- Indexes for table `login_activity`
--
ALTER TABLE `login_activity`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_login_time` (`login_time`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username_email` (`username_or_email`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_attempted_at` (`attempted_at`);

--
-- Indexes for table `message_categories`
--
ALTER TABLE `message_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `message_notifications`
--
ALTER TABLE `message_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message_id` (`message_id`),
  ADD KEY `idx_recipient` (`recipient_type`,`recipient_id`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `message_templates`
--
ALTER TABLE `message_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_admin_id` (`admin_id`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_is_global` (`is_global`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_type` (`user_id`,`notification_type`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subscription_id` (`subscription_id`),
  ADD KEY `idx_user_payments` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_transaction` (`transaction_id`),
  ADD KEY `idx_payment_date` (`payment_date`);

--
-- Indexes for table `payment_logs`
--
ALTER TABLE `payment_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_payment_method` (`payment_method`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_payment_reference` (`payment_reference`);

--
-- Indexes for table `payment_requests`
--
ALTER TABLE `payment_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `processed_by` (`processed_by`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_plan_id` (`plan_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `payment_transactions`
--
ALTER TABLE `payment_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `transaction_id` (`transaction_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_gateway` (`gateway`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `paymob_orders`
--
ALTER TABLE `paymob_orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `paymob_order_id` (`paymob_order_id`),
  ADD KEY `subscription_id` (`subscription_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_plan_id` (`plan_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_paymob_order_id` (`paymob_order_id`);

--
-- Indexes for table `paymob_payments`
--
ALTER TABLE `paymob_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_course_paymob` (`user_id`,`course_id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `regular_activation_codes`
--
ALTER TABLE `regular_activation_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `used_by` (`used_by`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_code` (`code`),
  ADD KEY `idx_plan_id` (`plan_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_is_used` (`is_used`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `student_exam_answers`
--
ALTER TABLE `student_exam_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `selected_option_id` (`selected_option_id`),
  ADD KEY `idx_attempt_id` (`attempt_id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `student_exam_attempts`
--
ALTER TABLE `student_exam_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_is_passed` (`is_passed`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- Indexes for table `student_exercise_answers`
--
ALTER TABLE `student_exercise_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `selected_option_id` (`selected_option_id`),
  ADD KEY `idx_attempt_id` (`attempt_id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `student_exercise_attempts`
--
ALTER TABLE `student_exercise_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_is_passed` (`is_passed`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- Indexes for table `student_messages`
--
ALTER TABLE `student_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `subject_lessons`
--
ALTER TABLE `subject_lessons`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_subject_id` (`subject_id`),
  ADD KEY `idx_is_free` (`is_free`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_lesson_order` (`lesson_order`);

--
-- Indexes for table `subscription_cancellations`
--
ALTER TABLE `subscription_cancellations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_cancellation_date` (`cancellation_date`);

--
-- Indexes for table `subscription_history`
--
ALTER TABLE `subscription_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_date` (`start_date`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_sort` (`sort_order`);

--
-- Indexes for table `subscription_stats`
--
ALTER TABLE `subscription_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_date` (`date`),
  ADD KEY `idx_date` (`date`);

--
-- Indexes for table `upcoming_exams`
--
ALTER TABLE `upcoming_exams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_exam_date` (`exam_date`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_education` (`education_level`,`education_type`,`grade`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `current_plan_id` (`current_plan_id`);

--
-- Indexes for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_activity_type` (`activity_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_content_attempts`
--
ALTER TABLE `user_content_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_content` (`user_id`,`content_type`,`content_id`),
  ADD KEY `idx_completion` (`is_completed`),
  ADD KEY `idx_started_at` (`started_at`);

--
-- Indexes for table `user_exam_answers`
--
ALTER TABLE `user_exam_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `user_exam_answers_simple`
--
ALTER TABLE `user_exam_answers_simple`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_question` (`user_id`,`question_id`),
  ADD KEY `idx_attempt` (`user_id`,`question_id`,`attempt_number`);

--
-- Indexes for table `user_exam_attempts`
--
ALTER TABLE `user_exam_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exam_id` (`exam_id`),
  ADD KEY `idx_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_passed` (`passed`),
  ADD KEY `idx_completed_at` (`completed_at`);

--
-- Indexes for table `user_exam_progress`
--
ALTER TABLE `user_exam_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_exam` (`user_id`,`exam_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_exam_id` (`exam_id`),
  ADD KEY `idx_is_completed` (`is_completed`),
  ADD KEY `idx_is_passed` (`is_passed`);

--
-- Indexes for table `user_exercise_answers`
--
ALTER TABLE `user_exercise_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_id` (`attempt_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `user_exercise_attempts`
--
ALTER TABLE `user_exercise_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_is_correct` (`is_correct`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_exercise_progress`
--
ALTER TABLE `user_exercise_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_exercise_id` (`exercise_id`),
  ADD KEY `idx_is_completed` (`is_completed`);

--
-- Indexes for table `user_exercise_question_answers`
--
ALTER TABLE `user_exercise_question_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_question_attempt` (`user_id`,`question_id`,`attempt_number`),
  ADD KEY `exercise_id` (`exercise_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_user_exercise` (`user_id`,`exercise_id`),
  ADD KEY `idx_user_question` (`user_id`,`question_id`),
  ADD KEY `idx_attempt` (`user_id`,`exercise_id`,`attempt_number`);

--
-- Indexes for table `user_lesson_progress`
--
ALTER TABLE `user_lesson_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_lesson` (`user_id`,`lesson_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_is_completed` (`is_completed`);

--
-- Indexes for table `user_notes`
--
ALTER TABLE `user_notes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_pinned` (`is_pinned`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_user_unread` (`user_id`,`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_question_answers`
--
ALTER TABLE `user_question_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_session_token` (`session_token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `user_settings`
--
ALTER TABLE `user_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `user_study_sessions`
--
ALTER TABLE `user_study_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user` (`user_id`),
  ADD KEY `idx_lesson` (`lesson_id`),
  ADD KEY `idx_session_start` (`session_start`),
  ADD KEY `idx_duration` (`duration_minutes`);

--
-- Indexes for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plan_id` (`plan_id`),
  ADD KEY `idx_user_subscription` (`user_id`,`end_date`),
  ADD KEY `idx_payment_status` (`payment_status`);

--
-- Indexes for table `user_summary_progress`
--
ALTER TABLE `user_summary_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_summary` (`user_id`,`summary_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_summary_id` (`summary_id`),
  ADD KEY `idx_is_completed` (`is_completed`);

--
-- Indexes for table `user_todos`
--
ALTER TABLE `user_todos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_is_completed` (`is_completed`),
  ADD KEY `idx_due_date` (`due_date`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_video` (`user_id`,`video_id`),
  ADD KEY `video_id` (`video_id`),
  ADD KEY `idx_user_video` (`user_id`,`video_id`),
  ADD KEY `idx_completed` (`completed`);

--
-- Indexes for table `user_weekly_test_answers`
--
ALTER TABLE `user_weekly_test_answers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `idx_attempt_question` (`attempt_id`,`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- Indexes for table `user_weekly_test_answers_simple`
--
ALTER TABLE `user_weekly_test_answers_simple`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_question` (`user_id`,`question_id`),
  ADD KEY `idx_attempt` (`user_id`,`question_id`,`attempt_number`);

--
-- Indexes for table `user_weekly_test_attempts`
--
ALTER TABLE `user_weekly_test_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `test_id` (`test_id`),
  ADD KEY `idx_user_test` (`user_id`,`test_id`),
  ADD KEY `idx_completed_at` (`completed_at`);

--
-- Indexes for table `video_watch_progress`
--
ALTER TABLE `video_watch_progress`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_video` (`user_id`,`video_id`),
  ADD KEY `video_id` (`video_id`),
  ADD KEY `course_id` (`course_id`),
  ADD KEY `idx_user_video` (`user_id`,`video_id`),
  ADD KEY `idx_user_course` (`user_id`,`course_id`),
  ADD KEY `idx_completed` (`is_completed`);

--
-- Indexes for table `wallet_payments`
--
ALTER TABLE `wallet_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_course_wallet` (`user_id`,`course_id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `idx_wallet_number` (`wallet_number`),
  ADD KEY `idx_wallet_type` (`wallet_type`),
  ADD KEY `idx_status` (`status`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activation_codes`
--
ALTER TABLE `activation_codes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_replies`
--
ALTER TABLE `admin_replies`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `center_activation_codes`
--
ALTER TABLE `center_activation_codes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `courses`
--
ALTER TABLE `courses`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `course_activation_codes`
--
ALTER TABLE `course_activation_codes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `course_completion_notes`
--
ALTER TABLE `course_completion_notes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `course_content`
--
ALTER TABLE `course_content`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `course_enrollments`
--
ALTER TABLE `course_enrollments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `course_exams`
--
ALTER TABLE `course_exams`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `course_exam_questions`
--
ALTER TABLE `course_exam_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `course_exercises`
--
ALTER TABLE `course_exercises`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `course_exercise_questions`
--
ALTER TABLE `course_exercise_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `course_subscriptions`
--
ALTER TABLE `course_subscriptions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `course_videos`
--
ALTER TABLE `course_videos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `course_weekly_tests`
--
ALTER TABLE `course_weekly_tests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `course_weekly_test_questions`
--
ALTER TABLE `course_weekly_test_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `curriculum_sections`
--
ALTER TABLE `curriculum_sections`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=116;

--
-- AUTO_INCREMENT for table `curriculum_subjects`
--
ALTER TABLE `curriculum_subjects`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=115;

--
-- AUTO_INCREMENT for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exam_questions`
--
ALTER TABLE `exam_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `exercise_attempts`
--
ALTER TABLE `exercise_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `exercise_questions`
--
ALTER TABLE `exercise_questions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `fawry_payments`
--
ALTER TABLE `fawry_payments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `honor_board`
--
ALTER TABLE `honor_board`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `lessons`
--
ALTER TABLE `lessons`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `lesson_exams`
--
ALTER TABLE `lesson_exams`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `lesson_exercises`
--
ALTER TABLE `lesson_exercises`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `lesson_files`
--
ALTER TABLE `lesson_files`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `lesson_progress`
--
ALTER TABLE `lesson_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `lesson_summaries`
--
ALTER TABLE `lesson_summaries`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `lesson_videos`
--
ALTER TABLE `lesson_videos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `login_activity`
--
ALTER TABLE `login_activity`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=91;

--
-- AUTO_INCREMENT for table `message_categories`
--
ALTER TABLE `message_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `message_notifications`
--
ALTER TABLE `message_notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `message_templates`
--
ALTER TABLE `message_templates`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `notification_settings`
--
ALTER TABLE `notification_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `payment_logs`
--
ALTER TABLE `payment_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_requests`
--
ALTER TABLE `payment_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_transactions`
--
ALTER TABLE `payment_transactions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `paymob_orders`
--
ALTER TABLE `paymob_orders`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `paymob_payments`
--
ALTER TABLE `paymob_payments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `regular_activation_codes`
--
ALTER TABLE `regular_activation_codes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exam_answers`
--
ALTER TABLE `student_exam_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exam_attempts`
--
ALTER TABLE `student_exam_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exercise_answers`
--
ALTER TABLE `student_exercise_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_exercise_attempts`
--
ALTER TABLE `student_exercise_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_messages`
--
ALTER TABLE `student_messages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `subject_lessons`
--
ALTER TABLE `subject_lessons`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `subscription_cancellations`
--
ALTER TABLE `subscription_cancellations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `subscription_history`
--
ALTER TABLE `subscription_history`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `subscription_stats`
--
ALTER TABLE `subscription_stats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `upcoming_exams`
--
ALTER TABLE `upcoming_exams`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `user_content_attempts`
--
ALTER TABLE `user_content_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exam_answers`
--
ALTER TABLE `user_exam_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exam_answers_simple`
--
ALTER TABLE `user_exam_answers_simple`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=63;

--
-- AUTO_INCREMENT for table `user_exam_attempts`
--
ALTER TABLE `user_exam_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `user_exam_progress`
--
ALTER TABLE `user_exam_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user_exercise_answers`
--
ALTER TABLE `user_exercise_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_exercise_attempts`
--
ALTER TABLE `user_exercise_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `user_exercise_progress`
--
ALTER TABLE `user_exercise_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `user_exercise_question_answers`
--
ALTER TABLE `user_exercise_question_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_lesson_progress`
--
ALTER TABLE `user_lesson_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `user_notes`
--
ALTER TABLE `user_notes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user_notifications`
--
ALTER TABLE `user_notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `user_question_answers`
--
ALTER TABLE `user_question_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_settings`
--
ALTER TABLE `user_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_study_sessions`
--
ALTER TABLE `user_study_sessions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `user_summary_progress`
--
ALTER TABLE `user_summary_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user_todos`
--
ALTER TABLE `user_todos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `user_weekly_test_answers`
--
ALTER TABLE `user_weekly_test_answers`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_weekly_test_answers_simple`
--
ALTER TABLE `user_weekly_test_answers_simple`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `user_weekly_test_attempts`
--
ALTER TABLE `user_weekly_test_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `video_watch_progress`
--
ALTER TABLE `video_watch_progress`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `wallet_payments`
--
ALTER TABLE `wallet_payments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

-- --------------------------------------------------------

--
-- Structure for view `subject_progress_summary`
--
DROP TABLE IF EXISTS `subject_progress_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`uci0vbkzf4zm3w6h`@`%` SQL SECURITY DEFINER VIEW `subject_progress_summary`  AS SELECT `u`.`id` AS `user_id`, `u`.`username` AS `username`, `cs`.`id` AS `subject_id`, `cs`.`name_ar` AS `subject_name`, count(distinct `sl`.`id`) AS `total_lessons`, count(distinct `lp`.`lesson_id`) AS `completed_lessons`, round(((count(distinct `lp`.`lesson_id`) / nullif(count(distinct `sl`.`id`),0)) * 100),2) AS `completion_percentage`, max(`lp`.`completed_at`) AS `last_activity` FROM (((`users` `u` join `curriculum_subjects` `cs`) left join `subject_lessons` `sl` on(((`cs`.`id` = `sl`.`subject_id`) and (`sl`.`is_active` = 1)))) left join `lesson_progress` `lp` on(((`u`.`id` = `lp`.`user_id`) and (`sl`.`id` = `lp`.`lesson_id`)))) GROUP BY `u`.`id`, `cs`.`id` ;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `activation_codes`
--
ALTER TABLE `activation_codes`
  ADD CONSTRAINT `activation_codes_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `activation_codes_ibfk_2` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD CONSTRAINT `admin_activity_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `center_activation_codes`
--
ALTER TABLE `center_activation_codes`
  ADD CONSTRAINT `center_activation_codes_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `center_activation_codes_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `courses`
--
ALTER TABLE `courses`
  ADD CONSTRAINT `courses_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_activation_codes`
--
ALTER TABLE `course_activation_codes`
  ADD CONSTRAINT `course_activation_codes_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_activation_codes_ibfk_2` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `course_content`
--
ALTER TABLE `course_content`
  ADD CONSTRAINT `course_content_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_content_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_exercises`
--
ALTER TABLE `course_exercises`
  ADD CONSTRAINT `course_exercises_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_exercises_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_exercise_questions`
--
ALTER TABLE `course_exercise_questions`
  ADD CONSTRAINT `course_exercise_questions_ibfk_1` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_subscriptions`
--
ALTER TABLE `course_subscriptions`
  ADD CONSTRAINT `course_subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_subscriptions_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `course_videos`
--
ALTER TABLE `course_videos`
  ADD CONSTRAINT `course_videos_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `course_videos_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `curriculum_sections`
--
ALTER TABLE `curriculum_sections`
  ADD CONSTRAINT `curriculum_sections_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  ADD CONSTRAINT `email_verification_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exam_attempts`
--
ALTER TABLE `exam_attempts`
  ADD CONSTRAINT `exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `lesson_exams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `exercise_attempts`
--
ALTER TABLE `exercise_attempts`
  ADD CONSTRAINT `exercise_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exercise_attempts_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `lesson_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `honor_board`
--
ALTER TABLE `honor_board`
  ADD CONSTRAINT `honor_board_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `lessons`
--
ALTER TABLE `lessons`
  ADD CONSTRAINT `lessons_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `curriculum_subjects` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `lesson_progress`
--
ALTER TABLE `lesson_progress`
  ADD CONSTRAINT `lesson_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `lesson_progress_ibfk_2` FOREIGN KEY (`lesson_id`) REFERENCES `subject_lessons` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `lesson_summaries`
--
ALTER TABLE `lesson_summaries`
  ADD CONSTRAINT `lesson_summaries_ibfk_1` FOREIGN KEY (`lesson_id`) REFERENCES `lessons` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `login_activity`
--
ALTER TABLE `login_activity`
  ADD CONSTRAINT `login_activity_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD CONSTRAINT `notification_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`subscription_id`) REFERENCES `user_subscriptions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payment_requests`
--
ALTER TABLE `payment_requests`
  ADD CONSTRAINT `payment_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_requests_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_requests_ibfk_3` FOREIGN KEY (`processed_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `payment_transactions`
--
ALTER TABLE `payment_transactions`
  ADD CONSTRAINT `payment_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payment_transactions_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `paymob_orders`
--
ALTER TABLE `paymob_orders`
  ADD CONSTRAINT `paymob_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `paymob_orders_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `paymob_orders_ibfk_3` FOREIGN KEY (`subscription_id`) REFERENCES `user_subscriptions` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `regular_activation_codes`
--
ALTER TABLE `regular_activation_codes`
  ADD CONSTRAINT `regular_activation_codes_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `regular_activation_codes_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `regular_activation_codes_ibfk_3` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `regular_activation_codes_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_exam_answers`
--
ALTER TABLE `student_exam_answers`
  ADD CONSTRAINT `student_exam_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `student_exam_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `exam_questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `exam_question_options` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_exam_attempts`
--
ALTER TABLE `student_exam_attempts`
  ADD CONSTRAINT `student_exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `course_exams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exam_attempts_ibfk_3` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_exercise_answers`
--
ALTER TABLE `student_exercise_answers`
  ADD CONSTRAINT `student_exercise_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `student_exercise_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `exercise_questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_answers_ibfk_3` FOREIGN KEY (`selected_option_id`) REFERENCES `question_options` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `student_exercise_attempts`
--
ALTER TABLE `student_exercise_attempts`
  ADD CONSTRAINT `student_exercise_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_attempts_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `student_exercise_attempts_ibfk_3` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `subscription_history`
--
ALTER TABLE `subscription_history`
  ADD CONSTRAINT `subscription_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `upcoming_exams`
--
ALTER TABLE `upcoming_exams`
  ADD CONSTRAINT `upcoming_exams_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`current_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `users_ibfk_2` FOREIGN KEY (`current_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD CONSTRAINT `user_activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_content_attempts`
--
ALTER TABLE `user_content_attempts`
  ADD CONSTRAINT `user_content_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exam_answers`
--
ALTER TABLE `user_exam_answers`
  ADD CONSTRAINT `user_exam_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_exam_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exam_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `course_exam_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exam_attempts`
--
ALTER TABLE `user_exam_attempts`
  ADD CONSTRAINT `user_exam_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exam_attempts_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `course_exams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exam_progress`
--
ALTER TABLE `user_exam_progress`
  ADD CONSTRAINT `user_exam_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exam_progress_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `lesson_exams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_answers`
--
ALTER TABLE `user_exercise_answers`
  ADD CONSTRAINT `user_exercise_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_exercise_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `exercise_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_attempts`
--
ALTER TABLE `user_exercise_attempts`
  ADD CONSTRAINT `user_exercise_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_attempts_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_progress`
--
ALTER TABLE `user_exercise_progress`
  ADD CONSTRAINT `user_exercise_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_progress_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `lesson_exercises` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_exercise_question_answers`
--
ALTER TABLE `user_exercise_question_answers`
  ADD CONSTRAINT `user_exercise_question_answers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_question_answers_ibfk_2` FOREIGN KEY (`exercise_id`) REFERENCES `course_exercises` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_exercise_question_answers_ibfk_3` FOREIGN KEY (`question_id`) REFERENCES `course_exercise_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_lesson_progress`
--
ALTER TABLE `user_lesson_progress`
  ADD CONSTRAINT `user_lesson_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_lesson_progress_ibfk_2` FOREIGN KEY (`lesson_id`) REFERENCES `lessons` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_notes`
--
ALTER TABLE `user_notes`
  ADD CONSTRAINT `user_notes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD CONSTRAINT `user_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_settings`
--
ALTER TABLE `user_settings`
  ADD CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_study_sessions`
--
ALTER TABLE `user_study_sessions`
  ADD CONSTRAINT `user_study_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_study_sessions_ibfk_2` FOREIGN KEY (`lesson_id`) REFERENCES `subject_lessons` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  ADD CONSTRAINT `user_subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_subscriptions_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_summary_progress`
--
ALTER TABLE `user_summary_progress`
  ADD CONSTRAINT `user_summary_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_summary_progress_ibfk_2` FOREIGN KEY (`summary_id`) REFERENCES `lesson_summaries` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_todos`
--
ALTER TABLE `user_todos`
  ADD CONSTRAINT `user_todos_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_video_progress`
--
ALTER TABLE `user_video_progress`
  ADD CONSTRAINT `user_video_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_video_progress_ibfk_2` FOREIGN KEY (`video_id`) REFERENCES `course_videos` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_weekly_test_answers`
--
ALTER TABLE `user_weekly_test_answers`
  ADD CONSTRAINT `user_weekly_test_answers_ibfk_1` FOREIGN KEY (`attempt_id`) REFERENCES `user_weekly_test_attempts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_weekly_test_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `course_weekly_test_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_weekly_test_attempts`
--
ALTER TABLE `user_weekly_test_attempts`
  ADD CONSTRAINT `user_weekly_test_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_weekly_test_attempts_ibfk_2` FOREIGN KEY (`test_id`) REFERENCES `course_weekly_tests` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `video_watch_progress`
--
ALTER TABLE `video_watch_progress`
  ADD CONSTRAINT `video_watch_progress_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `video_watch_progress_ibfk_2` FOREIGN KEY (`video_id`) REFERENCES `course_videos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `video_watch_progress_ibfk_3` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
