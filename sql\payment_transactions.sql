-- Payment transactions table for online payment gateways
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    gateway ENUM('fawry', 'paymob') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EGP',
    status ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    gateway_transaction_id VARCHAR(255) NULL,
    gateway_response JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_user_course (user_id, course_id),
    INDEX idx_gateway (gateway),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add gateway field to course_subscriptions table
ALTER TABLE course_subscriptions
ADD COLUMN payment_gateway VARCHAR(50) NULL AFTER activation_method;

-- Add transaction_id field to course_subscriptions table
ALTER TABLE course_subscriptions
ADD COLUMN transaction_id VARCHAR(100) NULL AFTER payment_gateway;

-- Add indexes
ALTER TABLE course_subscriptions
ADD INDEX idx_payment_gateway (payment_gateway);

ALTER TABLE course_subscriptions
ADD INDEX idx_transaction_id (transaction_id);

-- Update activation_method enum to include online_payment
ALTER TABLE course_subscriptions
MODIFY COLUMN activation_method ENUM('code', 'payment', 'online_payment', 'admin') DEFAULT 'code';
