<?php
// Subscription notification component
// Include this in pages where you want to show subscription expiry notifications

if (!isset($_SESSION['user_id'])) {
    return; // No user logged in
}

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $user_id = $_SESSION['user_id'];

    // Check if user has expired subscription
    $stmt = $db->prepare("SELECT subscription_status, subscription_end_date FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user || $user['subscription_status'] !== 'expired') {
        return; // No expired subscription
    }

    // Check if notification was already shown
    $stmt = $db->prepare("SELECT last_shown, show_count, max_show_count
                         FROM notification_settings
                         WHERE user_id = ? AND notification_type = 'subscription_expired'");
    $stmt->execute([$user_id]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($settings) {
        // If already shown max times, don't show again
        if ($settings['show_count'] >= $settings['max_show_count']) {
            return;
        }

        // If shown recently (within 1 hour), don't show again
        if ($settings['last_shown'] &&
            strtotime($settings['last_shown']) > (time() - 3600)) {
            return;
        }
    }

    // Mark as shown
    $stmt = $db->prepare("INSERT INTO notification_settings
                         (user_id, notification_type, last_shown, show_count)
                         VALUES (?, ?, NOW(), 1)
                         ON DUPLICATE KEY UPDATE
                         last_shown = NOW(),
                         show_count = show_count + 1");
    $stmt->execute([$user_id, 'subscription_expired']);

    // Show notification
    $notification = [
        'title' => 'انتهى اشتراكك! 🔔',
        'message' => 'لقد انتهت صلاحية اشتراكك. قم بتجديد الاشتراك للاستمرار في الوصول لجميع المحتويات التعليمية المتميزة.',
        'action_url' => 'page/subscriptions.php',
        'action_text' => '🔄 تجديد الاشتراك الآن',
        'icon' => '⚠️',
        'color' => '#dc3545'
    ];

    if ($notification): ?>
        <!-- Subscription Expired Notification Modal -->
        <div id="subscriptionExpiredModal" class="subscription-notification-modal">
            <div class="subscription-notification-overlay"></div>
            <div class="subscription-notification-content">
                <div class="notification-header">
                    <div class="notification-icon" style="background: <?php echo $notification['color']; ?>;">
                        <?php echo $notification['icon']; ?>
                    </div>
                    <h3><?php echo htmlspecialchars($notification['title']); ?></h3>
                    <button class="notification-close" onclick="closeSubscriptionNotification()">&times;</button>
                </div>
                
                <div class="notification-body">
                    <p><?php echo htmlspecialchars($notification['message']); ?></p>
                    
                    <div class="notification-features">
                        <div class="feature-item">
                            <span class="feature-icon">📚</span>
                            <span>الوصول لجميع المحتويات التعليمية</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🎯</span>
                            <span>اختبارات تفاعلية ومتابعة التقدم</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">💬</span>
                            <span>دعم فني متميز على مدار الساعة</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🏆</span>
                            <span>شهادات إتمام معتمدة</span>
                        </div>
                    </div>
                </div>
                
                <div class="notification-actions">
                    <button class="btn-dismiss" onclick="closeSubscriptionNotification()">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                    <a href="<?php echo $notification['action_url']; ?>" class="btn-action">
                        <i class="fas fa-crown"></i>
                        <?php echo htmlspecialchars($notification['action_text']); ?>
                    </a>
                </div>
            </div>
        </div>

        <style>
            .subscription-notification-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            }

            .subscription-notification-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(5px);
            }

            .subscription-notification-content {
                position: relative;
                background: white;
                border-radius: 20px;
                max-width: 500px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                animation: slideUp 0.3s ease;
            }

            .notification-header {
                background: linear-gradient(135deg, #4682B4, #20B2AA);
                color: white;
                padding: 25px;
                border-radius: 20px 20px 0 0;
                text-align: center;
                position: relative;
            }

            .notification-icon {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2.5rem;
                margin: 0 auto 15px;
                background: rgba(255, 255, 255, 0.2);
                border: 3px solid rgba(255, 255, 255, 0.3);
            }

            .notification-header h3 {
                margin: 0;
                font-size: 1.5rem;
                font-weight: 700;
            }

            .notification-close {
                position: absolute;
                top: 15px;
                right: 15px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                width: 35px;
                height: 35px;
                border-radius: 50%;
                font-size: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .notification-close:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.1);
            }

            .notification-body {
                padding: 30px;
                text-align: center;
            }

            .notification-body p {
                font-size: 16px;
                line-height: 1.6;
                color: #666;
                margin: 0 0 25px 0;
            }

            .notification-features {
                display: grid;
                gap: 15px;
                margin: 25px 0;
            }

            .feature-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 10px;
                border-right: 4px solid #4682B4;
            }

            .feature-icon {
                font-size: 1.2rem;
                width: 30px;
                text-align: center;
            }

            .notification-actions {
                padding: 20px 30px 30px;
                display: flex;
                gap: 15px;
                justify-content: center;
            }

            .btn-dismiss {
                padding: 12px 25px;
                border: 2px solid #6c757d;
                background: transparent;
                color: #6c757d;
                border-radius: 10px;
                cursor: pointer;
                font-weight: 600;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .btn-dismiss:hover {
                background: #6c757d;
                color: white;
                transform: translateY(-2px);
            }

            .btn-action {
                padding: 12px 25px;
                background: linear-gradient(135deg, #4682B4, #20B2AA);
                color: white;
                border-radius: 10px;
                text-decoration: none;
                font-weight: 600;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
                border: none;
            }

            .btn-action:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(70, 130, 180, 0.4);
                color: white;
                text-decoration: none;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from { 
                    transform: translateY(50px); 
                    opacity: 0; 
                }
                to { 
                    transform: translateY(0); 
                    opacity: 1; 
                }
            }

            @media (max-width: 768px) {
                .subscription-notification-content {
                    width: 95%;
                    margin: 10px;
                }

                .notification-header {
                    padding: 20px;
                }

                .notification-icon {
                    width: 60px;
                    height: 60px;
                    font-size: 2rem;
                }

                .notification-body {
                    padding: 20px;
                }

                .notification-actions {
                    flex-direction: column;
                    padding: 15px 20px 25px;
                }
            }
        </style>

        <script>
            function closeSubscriptionNotification() {
                const modal = document.getElementById('subscriptionExpiredModal');
                if (modal) {
                    // Send dismiss request to server
                    fetch('api/dismiss_notification.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            notification_type: 'subscription_expired'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Notification dismissed:', data);
                    })
                    .catch(error => {
                        console.error('Error dismissing notification:', error);
                    });

                    // Close modal with animation
                    modal.style.animation = 'fadeOut 0.3s ease';
                    setTimeout(() => {
                        modal.remove();
                    }, 300);
                }
            }

            // Close on overlay click
            document.addEventListener('DOMContentLoaded', function() {
                const overlay = document.querySelector('.subscription-notification-overlay');
                if (overlay) {
                    overlay.addEventListener('click', closeSubscriptionNotification);
                }

                // Close on Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        closeSubscriptionNotification();
                    }
                });
            });

            // Add fadeOut animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeOut {
                    from { opacity: 1; }
                    to { opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        </script>
    <?php endif;

} catch (Exception $e) {
    error_log("Subscription notification error: " . $e->getMessage());
    // Don't show notification if there's an error
}
?>
