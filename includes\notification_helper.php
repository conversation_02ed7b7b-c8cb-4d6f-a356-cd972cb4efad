<?php
/**
 * Notification Helper Functions
 * Provides easy-to-use functions for creating and managing notifications
 */

class NotificationHelper {
    private $userManager;
    private $notificationManager;
    
    public function __construct() {
        $this->userManager = new UserManager();
        $this->notificationManager = new NotificationManager();
    }
    
    /**
     * Send a welcome notification to a new user
     */
    public function sendWelcomeNotification($userId) {
        $user = $this->userManager->getUserById($userId);
        if (!$user) return false;
        
        $title = "مرحباً بك في " . SITE_NAME;
        $message = "أهلاً وسهلاً {$user['first_name']}! نحن سعداء لانضمامك إلينا. يمكنك الآن الاستفادة من جميع الخدمات المتاحة في المنصة.";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'success'
        );
    }
    
    /**
     * Send exam reminder notification
     */
    public function sendExamReminder($userId, $examTitle, $examDate) {
        $title = "تذكير: امتحان قادم";
        $message = "لديك امتحان في {$examTitle} مقرر في {$examDate}. تأكد من مراجعة المواد والاستعداد جيداً.";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'warning'
        );
    }
    
    /**
     * Send grade notification
     */
    public function sendGradeNotification($userId, $examTitle, $grade) {
        $title = "نتيجة الامتحان متاحة";
        $message = "تم نشر نتيجة امتحان {$examTitle}. درجتك: {$grade}";
        $type = $grade >= 60 ? 'success' : 'warning';
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            $type
        );
    }
    
    /**
     * Send system maintenance notification
     */
    public function sendMaintenanceNotification($message, $scheduledTime = null) {
        $title = "إشعار صيانة النظام";
        $fullMessage = $message;
        
        if ($scheduledTime) {
            $fullMessage .= " الصيانة مقررة في: {$scheduledTime}";
        }
        
        // Send to all users
        $allUsers = $this->userManager->getAllActiveUsers();
        $successCount = 0;
        
        foreach ($allUsers as $user) {
            if ($this->notificationManager->createNotification(
                $user['id'],
                $title,
                $fullMessage,
                'info'
            )) {
                $successCount++;
            }
        }
        
        return $successCount;
    }
    
    /**
     * Send custom notification to specific users
     */
    public function sendCustomNotification($userIds, $title, $message, $type = 'info') {
        if (!is_array($userIds)) {
            $userIds = [$userIds];
        }
        
        $successCount = 0;
        foreach ($userIds as $userId) {
            if ($this->notificationManager->createNotification(
                $userId,
                $title,
                $message,
                $type
            )) {
                $successCount++;
            }
        }
        
        return $successCount;
    }
    
    /**
     * Send notification to users by education level
     */
    public function sendNotificationByEducationLevel($educationLevel, $title, $message, $type = 'info') {
        $users = $this->userManager->getUsersByEducationLevel($educationLevel);
        $successCount = 0;
        
        foreach ($users as $user) {
            if ($this->notificationManager->createNotification(
                $user['id'],
                $title,
                $message,
                $type
            )) {
                $successCount++;
            }
        }
        
        return $successCount;
    }
    
    /**
     * Send birthday notification
     */
    public function sendBirthdayNotification($userId) {
        $user = $this->userManager->getUserById($userId);
        if (!$user) return false;
        
        $title = "🎉 عيد ميلاد سعيد!";
        $message = "عيد ميلاد سعيد {$user['first_name']}! نتمنى لك عاماً مليئاً بالنجاح والسعادة.";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'success'
        );
    }
    
    /**
     * Send course enrollment notification
     */
    public function sendCourseEnrollmentNotification($userId, $courseName) {
        $title = "تم التسجيل في الدورة";
        $message = "تم تسجيلك بنجاح في دورة {$courseName}. يمكنك الآن الوصول إلى المحتوى والبدء في التعلم.";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'success'
        );
    }
    
    /**
     * Send payment confirmation notification
     */
    public function sendPaymentConfirmation($userId, $amount, $service) {
        $title = "تأكيد الدفع";
        $message = "تم استلام دفعتك بقيمة {$amount} جنيه مقابل {$service}. شكراً لك!";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'success'
        );
    }
    
    /**
     * Send account verification notification
     */
    public function sendAccountVerificationNotification($userId) {
        $title = "تم تفعيل الحساب";
        $message = "تم تفعيل حسابك بنجاح! يمكنك الآن الاستفادة من جميع الخدمات المتاحة.";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'success'
        );
    }
    
    /**
     * Send password change notification
     */
    public function sendPasswordChangeNotification($userId) {
        $title = "تم تغيير كلمة المرور";
        $message = "تم تغيير كلمة مرور حسابك بنجاح. إذا لم تقم بهذا التغيير، يرجى التواصل معنا فوراً.";
        
        return $this->notificationManager->createNotification(
            $userId,
            $title,
            $message,
            'warning'
        );
    }
    
    /**
     * Get notification statistics
     */
    public function getNotificationStats() {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) as success,
                    SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) as error,
                    SUM(CASE WHEN type = 'warning' THEN 1 ELSE 0 END) as warning,
                    SUM(CASE WHEN type = 'info' THEN 1 ELSE 0 END) as info
                FROM notifications 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting notification stats: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Clean old notifications (older than 90 days)
     */
    public function cleanOldNotifications() {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                DELETE FROM notifications 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
            ");
            
            $stmt->execute();
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            error_log("Error cleaning old notifications: " . $e->getMessage());
            return 0;
        }
    }
}

// Create global instance for easy access
$notificationHelper = new NotificationHelper();
?>
