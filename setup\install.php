<?php
require_once '../config/config.php';

// Check if installation is already done
if (file_exists('../config/.installed')) {
    die('التطبيق مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف .installed من مجلد config');
}

$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Check if PDO MySQL driver is available
        if (!extension_loaded('pdo_mysql')) {
            throw new Exception('PDO MySQL driver غير مثبت. الرجاء تثبيت إضافة pdo_mysql في PHP');
        }

        // Create database connection without specifying database first
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        $pdo->exec("SET NAMES utf8mb4");

        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE " . DB_NAME);

        // Read and execute SQL file
        $sql = file_get_contents('../DB/bl7tlhd6pqdrgnrw0tkj-mysql_services_clever-cloud_com.sql');

        // Remove comments and empty lines
        $lines = explode("\n", $sql);
        $cleanedLines = [];
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && !preg_match('/^\s*--/', $line)) {
                $cleanedLines[] = $line;
            }
        }
        $cleanedSql = implode("\n", $cleanedLines);

        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $cleanedSql)),
            function($stmt) {
                return !empty($stmt);
            }
        );

        $executedStatements = 0;
        $errors = [];

        // Execute each statement
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement . ';');
                    $executedStatements++;
                } catch (PDOException $e) {
                    $errors[] = "خطأ في تنفيذ: " . substr($statement, 0, 50) . "... - " . $e->getMessage();
                }
            }
        }

        // Verify tables were created
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $requiredTables = ['users', 'user_sessions', 'login_attempts', 'password_reset_tokens', 'email_verification_tokens'];
        $missingTables = array_diff($requiredTables, $tables);

        if (!empty($missingTables)) {
            throw new Exception('فشل في إنشاء الجداول التالية: ' . implode(', ', $missingTables) .
                              '. تم تنفيذ ' . $executedStatements . ' من أصل ' . count($statements) . ' استعلام.');
        }

        if (!empty($errors)) {
            error_log("Installation errors: " . implode("; ", $errors));
        }

        // Create installation marker file
        file_put_contents('../config/.installed', date('Y-m-d H:i:s'));

        $success = true;

    } catch (PDOException $e) {
        $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    } catch (Exception $e) {
        $error = 'خطأ: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت منصة سلسلة الدكتور</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .install-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .install-btn {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .install-btn:hover {
            transform: translateY(-2px);
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #bee5eb;
            text-align: right;
        }

        .continue-btn {
            background: #28a745;
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 15px;
            transition: background 0.3s ease;
        }

        .continue-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="logo">
            <img src="../img/logo-b.png" alt="شعار سلسلة الدكتور">
        </div>

        <h1>تثبيت منصة سلسلة الدكتور</h1>
        <p class="subtitle">إعداد قاعدة البيانات والنظام</p>

        <?php if (!$success && empty($error)): ?>
            <div class="info">
                <strong>متطلبات التثبيت:</strong><br>
                • خادم MySQL يعمل<br>
                • PHP 7.4 أو أحدث<br>
                • امتداد PDO MySQL<br>
                • صلاحيات إنشاء قاعدة البيانات
            </div>

            <form method="POST">
                <button type="submit" class="install-btn">بدء التثبيت</button>
            </form>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="success">
                <strong>تم التثبيت بنجاح!</strong><br>
                تم إنشاء قاعدة البيانات والجداول بنجاح.<br>
                يمكنك الآن استخدام المنصة.
            </div>
            <a href="../index.php" class="continue-btn">الانتقال للصفحة الرئيسية</a>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="error">
                <strong>فشل التثبيت:</strong><br>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <form method="POST">
                <button type="submit" class="install-btn">إعادة المحاولة</button>
            </form>
        <?php endif; ?>
    </div>
</body>
</html>