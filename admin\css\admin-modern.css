/* ===== MODERN ADMIN PANEL STYLES - COMPLETE REDESIGN ===== */

/* CSS Variables for Consistent Theming */
:root {
    /* Primary Colors - Modern Blue Gradient */
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --secondary-color: #06b6d4;
    
    /* Accent Colors */
    --accent-blue: #3b82f6;
    --accent-purple: #8b5cf6;
    --accent-pink: #ec4899;
    --accent-green: #10b981;
    --accent-orange: #f59e0b;
    --accent-red: #ef4444;
    
    /* Neutral Colors */
    --bg-primary: #f8fafc;
    --bg-secondary: #f1f5f9;
    --bg-card: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Header Height */
    --header-height: 70px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary);
    direction: rtl;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
}

/* ===== HEADER STYLES ===== */
.admin-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: linear-gradient(135deg, var(--bg-card) 0%, rgba(255, 255, 255, 0.95) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-lg);
    max-width: 100%;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition-normal);
}

.mobile-menu-toggle:hover {
    background: var(--bg-secondary);
}

/* Header Brand */
.header-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-logo {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    object-fit: cover;
}

.brand-text h1 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.brand-text span {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 300px;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    font-size: 0.875rem;
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-card);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-btn {
    position: absolute;
    left: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
}

.search-btn:hover {
    color: var(--primary-color);
}

/* Notification Dropdown */
.notification-dropdown {
    position: relative;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition-normal);
}

.notification-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--accent-red);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.notification-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: 1001;
    margin-top: var(--spacing-sm);
}

.notification-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.875rem;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
}

.mark-all-read:hover {
    background: var(--bg-secondary);
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.notification-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.notification-footer a:hover {
    text-decoration: underline;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.quick-action-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition-normal);
}

.quick-action-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

/* Admin Profile Dropdown */
.admin-profile-dropdown {
    position: relative;
}

.admin-profile-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
}

.admin-profile-btn:hover {
    background: var(--bg-secondary);
}

.admin-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.admin-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: right;
}

.admin-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.admin-role {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.dropdown-arrow {
    color: var(--text-muted);
    transition: var(--transition-normal);
}

.admin-profile-dropdown.open .dropdown-arrow {
    transform: rotate(180deg);
}

.admin-profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    z-index: 1001;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
}

.admin-profile-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.dropdown-item.logout {
    color: var(--accent-red);
}

.dropdown-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--accent-red);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-sm) 0;
}

/* ===== SIDEBAR STYLES ===== */
.modern-sidebar {
    position: fixed;
    top: var(--header-height);
    right: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background: linear-gradient(180deg, var(--bg-card) 0%, rgba(255, 255, 255, 0.98) 100%);
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
    z-index: 999;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.modern-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* Sidebar Brand */
.sidebar-brand {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
}

.brand-info h3 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.brand-info span {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.sidebar-toggle {
    background: none;
    border: none;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-muted);
    transition: var(--transition-normal);
}

.sidebar-toggle:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

/* Sidebar Profile */
.sidebar-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(241, 245, 249, 0.5) 100%);
}

.profile-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-info span {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.profile-status {
    flex-shrink: 0;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-green);
    position: relative;
}

.status-indicator.online::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--accent-green);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.3;
    }
}

/* Sidebar Navigation */
.sidebar-navigation {
    padding: var(--spacing-md) 0;
}

.nav-section {
    margin-bottom: var(--spacing-lg);
}

.section-title {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--text-muted);
    padding: 0 var(--spacing-lg) var(--spacing-sm);
    margin: 0;
    letter-spacing: 0.5px;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0 var(--spacing-md) var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
}

.nav-link:hover::before {
    opacity: 0.1;
}

.nav-link:hover {
    color: var(--primary-color);
    transform: translateX(-2px);
}

.nav-item.active .nav-link {
    color: var(--primary-color);
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(99, 102, 241, 0.05));
    font-weight: 600;
}

.nav-item.active .nav-link::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    bottom: 20%;
    width: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 2px 0 0 2px;
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.nav-text {
    flex: 1;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    opacity: 0;
    transition: var(--transition-normal);
}

.nav-item.active .nav-indicator {
    opacity: 1;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-card);
}

.logout-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--accent-red);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.logout-link:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--accent-red);
}

.logout-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== MAIN CONTENT AREA ===== */
.admin-container {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

.main-content {
    flex: 1;
    margin-top: var(--header-height);
    margin-right: var(--sidebar-width);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    min-height: calc(100vh - var(--header-height));
}

.main-content.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
}

/* Page Header */
.page-header {
    margin-bottom: var(--spacing-xl);
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.page-header p {
    color: var(--text-muted);
    font-size: 1rem;
    margin: 0;
}

/* Content Cards */
.content-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.content-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.content-card h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: linear-gradient(135deg, var(--bg-card) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
}

.stat-change.positive {
    color: var(--accent-green);
    background: rgba(16, 185, 129, 0.1);
}

.stat-change.negative {
    color: var(--accent-red);
    background: rgba(239, 68, 68, 0.1);
}

.stat-change.neutral {
    color: var(--text-muted);
    background: var(--bg-secondary);
}

/* Grid Layouts */
.grid-2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-green), #059669);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--accent-orange), #d97706);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--accent-red), #dc2626);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--accent-blue), #2563eb);
    color: white;
}

/* Tables */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    margin: 0 auto;
    padding: 20px 40px;
    font-size: 20px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
}

.table th,
.table td {
    padding: 20px 40px;
    margin: 0 auto;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-left: 200px;
}

.table td {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.table tr:hover {
    background: var(--bg-secondary);
}



/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: var(--radius-md);
}

.badge-primary {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state svg {
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* System Info */
.system-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.info-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-glow {
    transition: var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        margin-right: var(--sidebar-width);
        padding: var(--spacing-lg);
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 992px) {
    .mobile-menu-toggle {
        display: flex;
    }
    
    .modern-sidebar {
        transform: translateX(100%);
        transition: transform var(--transition-normal);
    }
    
    .modern-sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .search-input {
        width: 200px;
    }
    
    .header-actions {
        gap: var(--spacing-sm);
    }
    
    .admin-info {
        display: none;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 var(--spacing-md);
    }
    
    .main-content {
        padding: var(--spacing-md);
    }
    
    .search-container {
        display: none;
    }
    
    .quick-actions {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .stat-card {
        padding: var(--spacing-lg);
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .modern-sidebar {
        width: 100%;
    }
    
    .brand-text {
        display: none;
    }
    
    .content-card {
        padding: var(--spacing-lg);
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8rem;
    }
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Modal */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    z-index: 2001;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.modal.show {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    transition: var(--transition-normal);
    background: var(--bg-card);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-md);
    min-width: 300px;
    max-width: 500px;
    z-index: 3000;
    transform: translateX(-100%);
    opacity: 0;
    transition: var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-md);
}

.toast-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
}

.toast-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.toast-success {
    border-left: 4px solid var(--accent-green);
}

.toast-error {
    border-left: 4px solid var(--accent-red);
}

.toast-warning {
    border-left: 4px solid var(--accent-orange);
}

.toast-info {
    border-left: 4px solid var(--accent-blue);
}

/* Tooltip */
.tooltip {
    position: absolute;
    background: var(--text-primary);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 3000;
    opacity: 0;
    transform: translateY(5px);
    transition: var(--transition-fast);
    pointer-events: none;
}

.tooltip::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-primary);
}

.tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.mobile-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Notification Item Styles */
.notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
}

.notification-item:hover {
    background: var(--bg-secondary);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: rgba(79, 70, 229, 0.05);
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.notification-content {
    flex: 1;
}

.notification-content h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.notification-content p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.notification-indicator {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 4px;
}

/* Enhanced Button Styles */
.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
    border-color: var(--text-muted);
}

/* Loading Animation */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error State */
.error {
    color: var(--accent-red);
    text-align: center;
    padding: var(--spacing-lg);
    font-size: 0.875rem;
}

/* Enhanced Mobile Styles */
@media (max-width: 1920px) {
    .toast {
        left: var(--spacing-md);
        right: var(--spacing-md);
        min-width: auto;
        max-width: none;
    }

    .notification-dropdown-menu {
        width: calc(100vw - 2rem);
        right: var(--spacing-md);
        left: var(--spacing-md);
    }

    .admin-profile-dropdown-menu {
        width: 250px;
    }
}

/* Enhanced Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Enhanced Card Styles */
.enhanced-card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    animation: slideInUp 0.6s ease-out;
}

.enhanced-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

/* Enhanced Button Styles */
.btn-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

/* Enhanced Form Styles */
.form-floating {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-floating input,
.form-floating textarea,
.form-floating select {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--bg-card);
    transition: all var(--transition-normal);
}

.form-floating input:focus,
.form-floating textarea:focus,
.form-floating select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-floating label {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-md);
    font-size: 1rem;
    color: var(--text-muted);
    transition: all var(--transition-normal);
    pointer-events: none;
    background: var(--bg-card);
    padding: 0 var(--spacing-xs);
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label,
.form-floating textarea:focus + label,
.form-floating textarea:not(:placeholder-shown) + label,
.form-floating select:focus + label,
.form-floating select:not([value=""]) + label {
    top: -8px;
    font-size: 0.75rem;
    color: var(--primary-color);
    font-weight: 600;
}

/* Print Styles */
@media print {
    .admin-header,
    .modern-sidebar,
    .mobile-menu-toggle,
    .toast,
    .modal-overlay,
    .modal {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .content-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
    }
}
