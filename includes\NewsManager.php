<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

class NewsManager {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Add a new news item
     */
    public function addNews($data) {
        try {
            $sql = "INSERT INTO curriculum_news (
                title, content, summary, category, education_level, 
                education_type, grade, specialization, publication_date, 
                is_published, is_featured, created_by
            ) VALUES (
                :title, :content, :summary, :category, :education_level,
                :education_type, :grade, :specialization, :publication_date,
                :is_published, :is_featured, :created_by
            )";
            
            $stmt = $this->db->prepare($sql);

            // Handle specialization - only allow valid ENUM values
            $specialization = 'all';
            if (!empty($data['specialization']) && in_array($data['specialization'], ['scientific', 'literary', 'all'])) {
                $specialization = $data['specialization'];
            }

            return $stmt->execute([
                ':title' => $data['title'],
                ':content' => $data['content'],
                ':summary' => $data['summary'],
                ':category' => $data['category'],
                ':education_level' => $data['education_level'],
                ':education_type' => $data['education_type'],
                ':grade' => $data['grade'],
                ':specialization' => $specialization,
                ':publication_date' => $data['publication_date'],
                ':is_published' => $data['is_published'],
                ':is_featured' => $data['is_featured'],
                ':created_by' => $data['created_by']
            ]);
        } catch (PDOException $e) {
            error_log("Error adding news: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update an existing news item
     */
    public function updateNews($id, $data) {
        try {
            $sql = "UPDATE curriculum_news SET 
                title = :title,
                content = :content,
                summary = :summary,
                category = :category,
                education_level = :education_level,
                education_type = :education_type,
                grade = :grade,
                specialization = :specialization,
                publication_date = :publication_date,
                is_published = :is_published,
                is_featured = :is_featured,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :id";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                ':id' => $id,
                ':title' => $data['title'],
                ':content' => $data['content'],
                ':summary' => $data['summary'],
                ':category' => $data['category'],
                ':education_level' => $data['education_level'],
                ':education_type' => $data['education_type'],
                ':grade' => $data['grade'],
                ':specialization' => $data['specialization'],
                ':publication_date' => $data['publication_date'],
                ':is_published' => $data['is_published'],
                ':is_featured' => $data['is_featured']
            ]);
        } catch (PDOException $e) {
            error_log("Error updating news: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a news item
     */
    public function deleteNews($id) {
        try {
            $sql = "DELETE FROM curriculum_news WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error deleting news: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get news by ID
     */
    public function getNewsById($id) {
        try {
            $sql = "SELECT * FROM curriculum_news WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting news: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all news with optional filters
     */
    public function getAllNews($filters = []) {
        try {
            $sql = "SELECT n.*, a.full_name as created_by_name 
                    FROM curriculum_news n 
                    LEFT JOIN admins a ON n.created_by = a.id 
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['category'])) {
                $sql .= " AND n.category = :category";
                $params[':category'] = $filters['category'];
            }
            
            if (!empty($filters['education_level'])) {
                $sql .= " AND (n.education_level = :education_level OR n.education_level = 'all')";
                $params[':education_level'] = $filters['education_level'];
            }
            
            if (!empty($filters['education_type'])) {
                $sql .= " AND (n.education_type = :education_type OR n.education_type = 'all')";
                $params[':education_type'] = $filters['education_type'];
            }
            
            if (!empty($filters['grade'])) {
                $sql .= " AND (n.grade = :grade OR n.grade = 'all')";
                $params[':grade'] = $filters['grade'];
            }
            
            if (isset($filters['is_published'])) {
                $sql .= " AND n.is_published = :is_published";
                $params[':is_published'] = $filters['is_published'];
            }
            
            if (isset($filters['is_featured'])) {
                $sql .= " AND n.is_featured = :is_featured";
                $params[':is_featured'] = $filters['is_featured'];
            }
            
            $sql .= " ORDER BY n.publication_date DESC, n.created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting news: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get latest news for dashboard widget
     */
    public function getLatestNews($limit = 5, $userEducation = null) {
        try {
            $sql = "SELECT * FROM curriculum_news
                    WHERE is_published = 1 AND publication_date <= CURDATE()";

            $params = [];

            if ($userEducation) {
                // More flexible filtering - show news that match user's education or are for all
                $sql .= " AND (education_level = :education_level OR education_level = 'all')
                         AND (education_type = :education_type OR education_type = 'all')
                         AND (grade = :grade OR grade = 'all')";
                $params[':education_level'] = $userEducation['education_level'];
                $params[':education_type'] = $userEducation['education_type'];
                $params[':grade'] = $userEducation['grade'];
            }

            $sql .= " ORDER BY is_featured DESC, publication_date DESC, created_at DESC LIMIT :limit";

            $stmt = $this->db->prepare($sql);

            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);

            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting latest news: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get total news count
     */
    public function getTotalNewsCount() {
        try {
            $sql = "SELECT COUNT(*) FROM curriculum_news WHERE is_published = 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Error getting total news count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Toggle news published status
     */
    public function toggleNewsStatus($id) {
        try {
            $sql = "UPDATE curriculum_news SET is_published = NOT is_published WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error toggling news status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Toggle news featured status
     */
    public function toggleFeaturedStatus($id) {
        try {
            $sql = "UPDATE curriculum_news SET is_featured = NOT is_featured WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            error_log("Error toggling featured status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get news categories
     */
    public function getCategories() {
        try {
            $sql = "SELECT DISTINCT category FROM curriculum_news WHERE category IS NOT NULL ORDER BY category";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Error getting categories: " . $e->getMessage());
            return [];
        }
    }
}
?>
