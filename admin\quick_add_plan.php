<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set admin session for testing
$_SESSION['admin_id'] = 1;

echo "<h2>إضافة خطة اشتراك سريعة</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.form-group { margin: 15px 0; }
.form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
.form-group input, .form-group textarea, .form-group select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
.btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
.btn:hover { background: #0056b3; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
</style>";

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if table exists
        $stmt = $db->query("SHOW TABLES LIKE 'subscription_plans'");
        if ($stmt->rowCount() == 0) {
            // Create table
            $createTableSQL = "CREATE TABLE subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                name_en VARCHAR(100) NULL,
                description TEXT NULL,
                price DECIMAL(10,2) NOT NULL DEFAULT 0,
                discount_percentage INT DEFAULT 0,
                discounted_price DECIMAL(10,2) NOT NULL DEFAULT 0,
                duration_days INT NOT NULL DEFAULT 30,
                features JSON NULL,
                icon VARCHAR(50) DEFAULT '📚',
                color VARCHAR(7) DEFAULT '#4682B4',
                is_popular BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_by INT NOT NULL DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($createTableSQL);
            $message .= "تم إنشاء جدول subscription_plans. ";
        }
        
        // Calculate discounted price
        $price = floatval($_POST['price']);
        $discount = intval($_POST['discount_percentage']);
        $discounted_price = $price - ($price * $discount / 100);
        
        // Process features
        $features = array_filter(array_map('trim', explode("\n", $_POST['features'])));
        $features_json = json_encode($features, JSON_UNESCAPED_UNICODE);
        
        // Insert plan
        $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, is_active, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $result = $stmt->execute([
            $_POST['name'],
            $_POST['name_en'],
            $_POST['description'],
            $price,
            $discount,
            $discounted_price,
            $_POST['duration_days'],
            $features_json,
            $_POST['icon'],
            $_POST['color'],
            isset($_POST['is_popular']) ? 1 : 0,
            isset($_POST['is_active']) ? 1 : 0,
            $_POST['sort_order'],
            $_SESSION['admin_id']
        ]);
        
        if ($result) {
            $planId = $db->lastInsertId();
            $message .= "تم إضافة الخطة بنجاح! ID: $planId";
            
            // Verify
            $verifyStmt = $db->prepare("SELECT * FROM subscription_plans WHERE id = ?");
            $verifyStmt->execute([$planId]);
            $plan = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($plan) {
                $message .= " - اسم الخطة: " . $plan['name'];
            }
        } else {
            $error = "فشل في إضافة الخطة";
        }
        
    } catch (Exception $e) {
        $error = "خطأ: " . $e->getMessage();
    }
}

if ($message) {
    echo "<div class='success'>$message</div>";
}

if ($error) {
    echo "<div class='error'>$error</div>";
}
?>

<form method="POST">
    <div class="form-group">
        <label>اسم الخطة (عربي):</label>
        <input type="text" name="name" required value="خطة تجريبية <?php echo date('H:i:s'); ?>">
    </div>
    
    <div class="form-group">
        <label>اسم الخطة (إنجليزي):</label>
        <input type="text" name="name_en" value="Test Plan <?php echo date('H:i:s'); ?>">
    </div>
    
    <div class="form-group">
        <label>الوصف:</label>
        <textarea name="description">هذه خطة تجريبية تم إنشاؤها في <?php echo date('Y-m-d H:i:s'); ?></textarea>
    </div>
    
    <div class="form-group">
        <label>السعر:</label>
        <input type="number" name="price" step="0.01" required value="100.00">
    </div>
    
    <div class="form-group">
        <label>نسبة الخصم (%):</label>
        <input type="number" name="discount_percentage" min="0" max="100" value="10">
    </div>
    
    <div class="form-group">
        <label>مدة الاشتراك (أيام):</label>
        <input type="number" name="duration_days" required value="30">
    </div>
    
    <div class="form-group">
        <label>المميزات (كل ميزة في سطر):</label>
        <textarea name="features" rows="4">الوصول لجميع الأقسام
دعم فني متميز
تحديثات مجانية
اختبارات تفاعلية</textarea>
    </div>
    
    <div class="form-group">
        <label>الأيقونة:</label>
        <input type="text" name="icon" value="🎓">
    </div>
    
    <div class="form-group">
        <label>اللون:</label>
        <input type="color" name="color" value="#28a745">
    </div>
    
    <div class="form-group">
        <label>ترتيب العرض:</label>
        <input type="number" name="sort_order" value="1">
    </div>
    
    <div class="form-group">
        <label>
            <input type="checkbox" name="is_popular"> خطة مميزة
        </label>
    </div>
    
    <div class="form-group">
        <label>
            <input type="checkbox" name="is_active" checked> نشطة
        </label>
    </div>
    
    <div class="form-group">
        <button type="submit" class="btn">إضافة الخطة</button>
    </div>
</form>

<?php
// Show current plans
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM subscription_plans");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<h3>الخطط الموجودة حالياً: $count</h3>";
    
    if ($count > 0) {
        $stmt = $db->query("SELECT id, name, price, discounted_price, is_active, created_at FROM subscription_plans ORDER BY created_at DESC LIMIT 10");
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 20px;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>السعر</th><th>السعر المخفض</th><th>نشط</th><th>تاريخ الإنشاء</th></tr>";
        foreach ($plans as $plan) {
            $status = $plan['is_active'] ? 'نعم' : 'لا';
            echo "<tr>";
            echo "<td>{$plan['id']}</td>";
            echo "<td>{$plan['name']}</td>";
            echo "<td>{$plan['price']}</td>";
            echo "<td>{$plan['discounted_price']}</td>";
            echo "<td>$status</td>";
            echo "<td>{$plan['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<div class='error'>خطأ في عرض الخطط: " . $e->getMessage() . "</div>";
}
?>

<p><a href="subscription_plans.php">العودة لصفحة إدارة الخطط</a></p>
