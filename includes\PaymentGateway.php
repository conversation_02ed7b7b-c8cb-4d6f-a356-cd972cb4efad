<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

class PaymentGateway {
    private $db;
    private $gateways;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->gateways = PAYMENT_GATEWAYS;
    }
    
    /**
     * Get available payment gateways
     */
    public function getAvailableGateways() {
        $available = [];
        foreach ($this->gateways as $gateway => $config) {
            if ($config['enabled']) {
                $available[$gateway] = $config;
            }
        }
        return $available;
    }
    
    /**
     * Create payment transaction record
     */
    public function createTransaction($userId, $courseId, $gateway, $amount, $currency = 'EGP') {
        try {
            $transactionId = $this->generateTransactionId();
            
            $stmt = $this->db->prepare("
                INSERT INTO payment_transactions 
                (transaction_id, user_id, course_id, gateway, amount, currency, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
            ");
            
            $result = $stmt->execute([
                $transactionId,
                $userId,
                $courseId,
                $gateway,
                $amount,
                $currency
            ]);
            
            if ($result) {
                return $transactionId;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("Error creating payment transaction: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update transaction status
     */
    public function updateTransactionStatus($transactionId, $status, $gatewayTransactionId = null, $gatewayResponse = null) {
        try {
            $sql = "UPDATE payment_transactions SET status = ?, updated_at = NOW()";
            $params = [$status];
            
            if ($gatewayTransactionId) {
                $sql .= ", gateway_transaction_id = ?";
                $params[] = $gatewayTransactionId;
            }
            
            if ($gatewayResponse) {
                $sql .= ", gateway_response = ?";
                $params[] = json_encode($gatewayResponse);
            }
            
            $sql .= " WHERE transaction_id = ?";
            $params[] = $transactionId;
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
        } catch (Exception $e) {
            error_log("Error updating transaction status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get transaction by ID
     */
    public function getTransaction($transactionId) {
        try {
            $stmt = $this->db->prepare("
                SELECT pt.*, u.username, u.email, c.title as course_title, c.price as course_price
                FROM payment_transactions pt
                JOIN users u ON pt.user_id = u.id
                JOIN courses c ON pt.course_id = c.id
                WHERE pt.transaction_id = ?
            ");
            $stmt->execute([$transactionId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting transaction: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId() {
        return 'TXN_' . time() . '_' . uniqid();
    }
    
    /**
     * Verify payment signature/hash
     */
    public function verifySignature($gateway, $data, $signature) {
        switch ($gateway) {
            case 'fawry':
                return $this->verifyFawrySignature($data, $signature);
            case 'paymob':
                return $this->verifyPaymobSignature($data, $signature);
            default:
                return false;
        }
    }
    
    /**
     * Verify Fawry signature
     */
    private function verifyFawrySignature($data, $signature) {
        $config = $this->gateways['fawry'];
        $securityKey = $config['security_key'];
        
        // Fawry signature verification logic
        $signatureString = $data['merchantRefNumber'] . $config['merchant_code'] . 
                          $data['paymentAmount'] . $securityKey;
        $expectedSignature = hash('sha256', $signatureString);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Verify Paymob signature
     */
    private function verifyPaymobSignature($data, $signature) {
        $config = $this->gateways['paymob'];
        $hmacSecret = $config['hmac_secret'];
        
        // Paymob HMAC verification logic
        $signatureString = $data['amount_cents'] . $data['created_at'] . 
                          $data['currency'] . $data['error_occured'] . 
                          $data['has_parent_transaction'] . $data['id'] . 
                          $data['integration_id'] . $data['is_3d_secure'] . 
                          $data['is_auth'] . $data['is_capture'] . 
                          $data['is_refunded'] . $data['is_standalone_payment'] . 
                          $data['is_voided'] . $data['order']['id'] . 
                          $data['owner'] . $data['pending'] . 
                          $data['source_data']['pan'] . $data['source_data']['sub_type'] . 
                          $data['source_data']['type'] . $data['success'];
        
        $expectedSignature = hash_hmac('sha512', $signatureString, $hmacSecret);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Process successful payment and activate course
     */
    public function processSuccessfulPayment($transactionId, $gatewayTransactionId, $gatewayResponse = null) {
        try {
            $this->db->beginTransaction();
            
            // Get transaction details
            $transaction = $this->getTransaction($transactionId);
            if (!$transaction) {
                throw new Exception('Transaction not found');
            }
            
            // Update transaction status
            $this->updateTransactionStatus($transactionId, 'completed', $gatewayTransactionId, $gatewayResponse);
            
            // Activate course subscription
            require_once __DIR__ . '/CourseManager.php';
            $courseManager = new CourseManager();
            
            // Create subscription if not exists
            $courseManager->createSubscription($transaction['user_id'], $transaction['course_id'], 'online_payment');
            
            // Activate subscription
            $activationResult = $courseManager->activateSubscription(
                $transaction['user_id'],
                $transaction['course_id'],
                'online_payment',
                [
                    'transaction_id' => $transactionId,
                    'gateway_transaction_id' => $gatewayTransactionId,
                    'gateway' => $transaction['gateway']
                ],
                false // Don't handle transaction since we're already in one
            );
            
            if (!$activationResult) {
                throw new Exception('Failed to activate course subscription');
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log("Error processing successful payment: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get gateway configuration
     */
    public function getGatewayConfig($gateway) {
        return isset($this->gateways[$gateway]) ? $this->gateways[$gateway] : null;
    }
}
?>
