<?php
require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');


// For testing, allow access without login
// if (!isset($_SESSION['user_id'])) {
//     http_response_code(401);
//     echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
//     exit;
// }

// Use a test user ID if not logged in
$userId = $_SESSION['user_id'] ?? 1;

try {
    $db = Database::getInstance()->getConnection();
    // $userId is already set above
    
    $courseId = $_GET['course_id'] ?? null;
    $weekNumber = $_GET['week_number'] ?? null;
    
    if (!$courseId || !$weekNumber) {
        echo json_encode(['success' => false, 'message' => 'معاملات مفقودة']);
        exit;
    }
    
    // Get exercises for this week
    $stmt = $db->prepare("
        SELECT id, question_text, question_type, options, points
        FROM course_exercises 
        WHERE course_id = ? AND week_number = ? 
        ORDER BY id ASC
    ");
    $stmt->execute([$courseId, $weekNumber]);
    $exercises = $stmt->fetchAll();
    
    if (empty($exercises)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد تمارين لهذا الأسبوع']);
        exit;
    }
    
    // Check if user has already completed this exercise set
    $exerciseIds = array_column($exercises, 'id');
    $stmt = $db->prepare("
        SELECT COUNT(DISTINCT exercise_id) as completed_count,
               MAX(attempt_number) as last_attempt
        FROM user_exercise_attempts 
        WHERE user_id = ? AND exercise_id IN (" . implode(',', $exerciseIds) . ")
        AND is_correct = 1
    ");
    $stmt->execute([$userId]);
    $completionData = $stmt->fetch();
    
    $isCompleted = $completionData['completed_count'] == count($exercises);
    
    // Get user's last attempt results if any
    $lastResults = [];
    if ($completionData['last_attempt']) {
        $stmt = $db->prepare("
            SELECT exercise_id, user_answer, is_correct
            FROM user_exercise_attempts 
            WHERE user_id = ? AND exercise_id IN (" . implode(',', $exerciseIds) . ")
            AND attempt_number = ?
        ");
        $stmt->execute([$userId, $completionData['last_attempt']]);
        $attempts = $stmt->fetchAll();
        
        foreach ($attempts as $attempt) {
            $lastResults[$attempt['exercise_id']] = [
                'user_answer' => $attempt['user_answer'],
                'is_correct' => $attempt['is_correct']
            ];
        }
    }
    
    // Format exercises for frontend
    $formattedExercises = [];
    foreach ($exercises as $exercise) {
        $formattedExercise = [
            'id' => $exercise['id'],
            'question_text' => $exercise['question_text'],
            'question_type' => $exercise['question_type'],
            'points' => $exercise['points'],
            'options' => null
        ];
        
        // Add options for multiple choice questions
        if ($exercise['question_type'] === 'multiple_choice' && $exercise['options']) {
            $formattedExercise['options'] = json_decode($exercise['options'], true);
        }
        
        // Add last attempt data if available
        if (isset($lastResults[$exercise['id']])) {
            $formattedExercise['last_attempt'] = $lastResults[$exercise['id']];
        }
        
        $formattedExercises[] = $formattedExercise;
    }
    
    // Get previous results summary if completed
    $previousResults = null;
    if ($isCompleted) {
        // Get course passing grade
        $stmt = $db->prepare("SELECT passing_grade FROM courses WHERE id = ?");
        $stmt->execute([$courseId]);
        $rawPassingGrade = $stmt->fetchColumn();
        $passingGrade = floatval($rawPassingGrade ?: 60);

        // Calculate results from last attempt
        $correctAnswers = 0;
        $totalQuestions = count($exercises);

        foreach ($lastResults as $result) {
            if ($result['is_correct']) {
                $correctAnswers++;
            }
        }

        $score = round(($correctAnswers / $totalQuestions) * 100, 1);
        $scoreFloat = floatval($score);
        $passed = $scoreFloat >= $passingGrade;

        // Debug logging
        error_log("=== Get Week Exercises Results Debug ===");
        error_log("Course ID: $courseId, Week: $weekNumber");
        error_log("Passing Grade: $passingGrade%");
        error_log("Correct Answers: $correctAnswers");
        error_log("Total Questions: $totalQuestions");
        error_log("Calculated Score: $score%");
        error_log("Passed Status: " . ($passed ? 'Yes' : 'No'));
        error_log("========================================");

        $previousResults = [
            'score' => $score,
            'correct_answers' => $correctAnswers,
            'total_questions' => $totalQuestions,
            'passed' => $passed,
            'passing_grade' => $passingGrade,
            'attempt_number' => $completionData['last_attempt'] ?? 1
        ];
    }

    echo json_encode([
        'success' => true,
        'exercises' => $formattedExercises,
        'is_completed' => $isCompleted,
        'last_attempt_number' => $completionData['last_attempt'] ?? 0,
        'total_exercises' => count($exercises),
        'results' => $previousResults
    ]);
    
} catch (Exception $e) {
    error_log("Error getting week exercises: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
