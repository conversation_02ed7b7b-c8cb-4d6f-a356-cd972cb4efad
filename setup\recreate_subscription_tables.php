<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🗑️ حذف وإعادة إنشاء جداول الاشتراكات</h2>";
    echo "<p style='color: red; font-weight: bold;'>⚠️ تحذير: سيتم حذف جميع جداول الاشتراكات وإعادة إنشائها!</p>";
    
    $success = 0;
    $errors = 0;
    
    // 1. إيقاف فحص Foreign Keys مؤقتاً
    echo "<h3>🔧 إيقاف فحص Foreign Keys...</h3>";
    $db->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "<p style='color: blue;'>ℹ️ تم إيقاف فحص Foreign Keys مؤقتاً</p>";

    // 2. حذف الجداول بالترتيب الصحيح (بسبب Foreign Keys)
    echo "<h3>🗑️ حذف الجداول الموجودة...</h3>";

    $tables_to_drop = [
        'subscription_extensions',
        'payments',
        'user_subscriptions',
        'course_activation_codes',
        'activation_codes',
        'subscription_stats',
        'subscription_plans'
    ];

    foreach ($tables_to_drop as $table) {
        try {
            $db->exec("DROP TABLE IF EXISTS $table");
            echo "<p style='color: green;'>✅ تم حذف جدول $table</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في حذف جدول $table: " . $e->getMessage() . "</p>";
            $errors++;
        }
    }
    
    // 3. إزالة أعمدة الاشتراك من جدول users
    echo "<h3>🔄 تنظيف جدول users...</h3>";

    $user_columns_to_drop = ['subscription_status', 'current_plan_id', 'subscription_end_date'];

    foreach ($user_columns_to_drop as $column) {
        try {
            // فحص وجود العمود أولاً
            $stmt = $db->query("SHOW COLUMNS FROM users LIKE '$column'");
            if ($stmt->rowCount() > 0) {
                $db->exec("ALTER TABLE users DROP COLUMN $column");
                echo "<p style='color: green;'>✅ تم حذف عمود $column من جدول users</p>";
                $success++;
            } else {
                echo "<p style='color: blue;'>ℹ️ عمود $column غير موجود</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ خطأ في حذف عمود $column: " . $e->getMessage() . "</p>";
        }
    }
    
    // 4. إعادة إنشاء الجداول
    echo "<h3>🔨 إعادة إنشاء الجداول...</h3>";
    
    // جدول خطط الاشتراك
    echo "<p>📋 إنشاء جدول subscription_plans...</p>";
    $sql = "CREATE TABLE subscription_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة',
        name_en VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية',
        description TEXT NULL COMMENT 'وصف الخطة',
        price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر الأصلي',
        discount_percentage INT DEFAULT 0 COMMENT 'نسبة الخصم',
        discounted_price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر بعد الخصم',
        duration_days INT NOT NULL DEFAULT 30 COMMENT 'مدة الاشتراك بالأيام',
        features JSON NULL COMMENT 'مميزات الخطة',
        icon VARCHAR(10) DEFAULT '📚' COMMENT 'أيقونة الخطة',
        color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة',
        is_popular BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة شائعة',
        is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الخطة نشطة',
        sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
        created_by INT NOT NULL DEFAULT 1 COMMENT 'منشئ الخطة',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول subscription_plans</p>";
    $success++;
    
    // جدول اشتراكات المستخدمين
    echo "<p>👥 إنشاء جدول user_subscriptions...</p>";
    $sql = "CREATE TABLE user_subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL COMMENT 'معرف المستخدم',
        plan_id INT NOT NULL COMMENT 'معرف الخطة',
        activation_code VARCHAR(50) NULL COMMENT 'كود التفعيل المستخدم',
        payment_method ENUM('code', 'fawry', 'visa', 'wallet') NOT NULL DEFAULT 'code' COMMENT 'طريقة الدفع',
        payment_status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed' COMMENT 'حالة الدفع',
        amount_paid DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ المدفوع',
        start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ بداية الاشتراك',
        end_date TIMESTAMP NOT NULL COMMENT 'تاريخ انتهاء الاشتراك',
        cancelled_at TIMESTAMP NULL COMMENT 'تاريخ الإلغاء',
        cancellation_reason TEXT NULL COMMENT 'سبب الإلغاء',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
        INDEX idx_user_subscription (user_id, end_date),
        INDEX idx_payment_status (payment_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول user_subscriptions</p>";
    $success++;
    
    // جدول أكواد التفعيل للاشتراكات
    echo "<p>🎫 إنشاء جدول activation_codes...</p>";
    $sql = "CREATE TABLE activation_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود التفعيل',
        plan_id INT NOT NULL COMMENT 'معرف الخطة',
        is_used BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود',
        used_by INT NULL COMMENT 'المستخدم الذي استخدم الكود',
        used_at TIMESTAMP NULL COMMENT 'تاريخ الاستخدام',
        expires_at TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
        created_by INT NOT NULL COMMENT 'منشئ الكود',
        notes TEXT NULL COMMENT 'ملاحظات',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
        FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_code (code),
        INDEX idx_used (is_used),
        INDEX idx_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول activation_codes</p>";
    $success++;
    
    // جدول أكواد تفعيل الكورسات
    echo "<p>📚 إنشاء جدول course_activation_codes...</p>";
    $sql = "CREATE TABLE course_activation_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود التفعيل',
        course_id INT NOT NULL COMMENT 'معرف الكورس',
        is_used BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود',
        used_by INT NULL COMMENT 'المستخدم الذي استخدم الكود',
        used_at TIMESTAMP NULL COMMENT 'تاريخ الاستخدام',
        expires_at TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
        created_by INT NOT NULL COMMENT 'منشئ الكود',
        notes TEXT NULL COMMENT 'ملاحظات',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_code (code),
        INDEX idx_course_unused (course_id, is_used),
        INDEX idx_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول course_activation_codes</p>";
    $success++;
    
    // جدول المدفوعات
    echo "<p>💳 إنشاء جدول payments...</p>";
    $sql = "CREATE TABLE payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subscription_id INT NOT NULL COMMENT 'معرف الاشتراك',
        user_id INT NOT NULL COMMENT 'معرف المستخدم',
        payment_method ENUM('code', 'fawry', 'visa', 'wallet') NOT NULL COMMENT 'طريقة الدفع',
        payment_gateway VARCHAR(50) NULL COMMENT 'بوابة الدفع',
        transaction_id VARCHAR(100) NULL COMMENT 'معرف المعاملة',
        gateway_response JSON NULL COMMENT 'استجابة البوابة',
        amount DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
        currency VARCHAR(3) DEFAULT 'EGP' COMMENT 'العملة',
        status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending' COMMENT 'حالة الدفع',
        payment_date TIMESTAMP NULL COMMENT 'تاريخ الدفع',
        refund_date TIMESTAMP NULL COMMENT 'تاريخ الاسترداد',
        refund_amount DECIMAL(10,2) NULL COMMENT 'مبلغ الاسترداد',
        notes TEXT NULL COMMENT 'ملاحظات',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_payments (user_id),
        INDEX idx_status (status),
        INDEX idx_transaction (transaction_id),
        INDEX idx_payment_date (payment_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول payments</p>";
    $success++;
    
    // جدول إحصائيات الاشتراكات
    echo "<p>📊 إنشاء جدول subscription_stats...</p>";
    $sql = "CREATE TABLE subscription_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE NOT NULL COMMENT 'التاريخ',
        total_subscriptions INT DEFAULT 0 COMMENT 'إجمالي الاشتراكات',
        new_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات الجديدة',
        cancelled_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات الملغاة',
        expired_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات المنتهية',
        total_revenue DECIMAL(10,2) DEFAULT 0 COMMENT 'إجمالي الإيرادات',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        UNIQUE KEY unique_date (date),
        INDEX idx_date (date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول subscription_stats</p>";
    $success++;
    
    // 5. إعادة تفعيل فحص Foreign Keys
    echo "<h3>🔧 إعادة تفعيل فحص Foreign Keys...</h3>";
    $db->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "<p style='color: blue;'>ℹ️ تم إعادة تفعيل فحص Foreign Keys</p>";

    // 6. إضافة أعمدة الاشتراك لجدول users
    echo "<h3>👤 إضافة أعمدة الاشتراك لجدول users...</h3>";
    
    $user_columns = [
        "subscription_status ENUM('none', 'active', 'expired', 'cancelled') DEFAULT 'none' COMMENT 'حالة الاشتراك'",
        "current_plan_id INT NULL COMMENT 'معرف الخطة الحالية'",
        "subscription_end_date TIMESTAMP NULL COMMENT 'تاريخ انتهاء الاشتراك'"
    ];
    
    foreach ($user_columns as $column) {
        try {
            $db->exec("ALTER TABLE users ADD COLUMN $column");
            echo "<p style='color: green;'>✅ تم إضافة عمود للمستخدمين</p>";
            $success++;
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ خطأ في إضافة عمود: " . $e->getMessage() . "</p>";
        }
    }
    
    // إضافة Foreign Key للخطة الحالية
    try {
        $db->exec("ALTER TABLE users ADD FOREIGN KEY (current_plan_id) REFERENCES subscription_plans(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✅ تم إضافة Foreign Key للخطة الحالية</p>";
        $success++;
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ خطأ في إضافة Foreign Key: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إعادة إنشاء جميع الجداول بنجاح!</h3>";
    echo "<p>✅ العمليات الناجحة: {$success}</p>";
    echo "<p>❌ العمليات الفاشلة: {$errors}</p>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='reset_subscription_system.php' style='color: #4682B4; font-weight: bold;'>إضافة البيانات التجريبية</a></li>";
    echo "<li><a href='test_subscription_system.php' style='color: #4682B4; font-weight: bold;'>اختبار النظام</a></li>";
    echo "<li><a href='../admin/subscription_plans.php' style='color: #4682B4; font-weight: bold;'>إدارة الخطط</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        direction: rtl;
        min-height: 100vh;
    }
    
    h2, h3 {
        background: rgba(255, 255, 255, 0.9);
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin: 20px 0;
    }
    
    p {
        background: rgba(255, 255, 255, 0.8);
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #4682B4;
    }
    
    ul {
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        list-style-type: none;
    }
    
    li {
        margin: 15px 0;
        padding: 10px;
        background: rgba(70, 130, 180, 0.1);
        border-radius: 5px;
        border-right: 4px solid #4682B4;
    }
    
    a {
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    a:hover {
        text-decoration: underline;
        transform: translateX(-5px);
    }
    
    hr {
        margin: 30px 0;
        border: none;
        border-top: 2px solid rgba(255, 255, 255, 0.3);
    }
</style>
