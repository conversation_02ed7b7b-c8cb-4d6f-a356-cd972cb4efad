<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/NewsManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Check if news ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الخبر مطلوب']);
    exit;
}

$newsManager = new NewsManager();
$news = $newsManager->getNewsById($_GET['id']);

if ($news) {
    echo json_encode(['success' => true, 'news' => $news]);
} else {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'الخبر غير موجود']);
}
?>
