<?php
/**
 * Todo Widget Content
 * Returns HTML content for todo widget
 */

require_once __DIR__ . '/../../includes/database.php';

if (!isset($_SESSION['user_id'])) {
    echo '<div class="error">غير مصرح</div>';
    exit;
}

$userManager = new UserManager();
$todos = $userManager->getUserTodos($_SESSION['user_id']);

// Separate completed and pending todos
$pendingTodos = array_filter($todos, function($todo) {
    return !$todo['is_completed'];
});

$completedTodos = array_filter($todos, function($todo) {
    return $todo['is_completed'];
});
?>

<div class="todo-section">
    <h4>المهام المعلقة</h4>
    <?php if (empty($pendingTodos)): ?>
        <div class="empty-state">
            <p>لا توجد مهام معلقة</p>
        </div>
    <?php else: ?>
        <div class="todo-list">
            <?php foreach ($pendingTodos as $todo): ?>
                <div class="todo-item" data-id="<?php echo $todo['id']; ?>">
                    <div class="todo-checkbox">
                        <input type="checkbox" id="todo_<?php echo $todo['id']; ?>" 
                               onchange="toggleTodo(<?php echo $todo['id']; ?>)">
                        <label for="todo_<?php echo $todo['id']; ?>"></label>
                    </div>
                    <div class="todo-content">
                        <span class="todo-text"><?php echo htmlspecialchars($todo['task']); ?></span>
                        <?php if ($todo['due_date']): ?>
                            <small class="todo-date">
                                <?php echo date('Y-m-d', strtotime($todo['due_date'])); ?>
                            </small>
                        <?php endif; ?>
                    </div>
                    <div class="todo-actions">
                        <button class="edit-btn" onclick="editTodo(<?php echo $todo['id']; ?>)" title="تعديل">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <button class="delete-btn" onclick="deleteTodo(<?php echo $todo['id']; ?>)" title="حذف">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>



<div class="add-todo-section">
    <button class="add-todo-btn" onclick="openTodoModal()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        إضافة مهمة جديدة
    </button>
</div>
