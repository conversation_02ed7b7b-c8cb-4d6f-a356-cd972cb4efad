<?php
/**
 * System Status Checker
 * Checks the status of the communication system tables and data
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

class SystemStatusChecker {
    private $db;
    
    public function __construct() {
        try {
            $this->db = Database::getInstance()->getConnection();
        } catch (Exception $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function checkStatus() {
        echo "<h2>فحص حالة نظام التواصل</h2>\n";
        
        $this->checkTables();
        $this->checkData();
        $this->checkPermissions();
        
        return true;
    }
    
    private function checkTables() {
        echo "<h3>فحص الجداول:</h3>\n";
        
        $requiredTables = [
            'message_categories' => [
                'id', 'name', 'name_ar', 'description', 'icon', 'color', 'is_active', 'sort_order', 'created_at'
            ],
            'student_messages' => [
                'id', 'user_id', 'subject', 'message', 'message_type', 'category_id', 'priority', 'status', 'created_at'
            ],
            'admin_replies' => [
                'id', 'message_id', 'admin_id', 'reply_text', 'is_public', 'created_at'
            ],
            'message_notifications' => [
                'id', 'message_id', 'recipient_type', 'recipient_id', 'notification_type', 'is_read', 'created_at'
            ],
            'message_templates' => [
                'id', 'title', 'content', 'category_id', 'is_active', 'created_by', 'created_at'
            ]
        ];
        
        $existingTables = $this->db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>الجدول</th><th>الحالة</th><th>الأعمدة</th><th>عدد السجلات</th></tr>\n";
        
        foreach ($requiredTables as $tableName => $requiredColumns) {
            $exists = in_array($tableName, $existingTables);
            $status = $exists ? '✅ موجود' : '❌ غير موجود';
            $statusColor = $exists ? 'green' : 'red';
            
            $columnInfo = '';
            $recordCount = 0;
            
            if ($exists) {
                try {
                    // Check columns
                    $columns = $this->db->query("SHOW COLUMNS FROM {$tableName}")->fetchAll(PDO::FETCH_COLUMN);
                    $missingColumns = array_diff($requiredColumns, $columns);
                    
                    if (empty($missingColumns)) {
                        $columnInfo = '✅ جميع الأعمدة موجودة';
                    } else {
                        $columnInfo = '⚠️ أعمدة مفقودة: ' . implode(', ', $missingColumns);
                    }
                    
                    // Get record count
                    $count = $this->db->query("SELECT COUNT(*) FROM {$tableName}")->fetchColumn();
                    $recordCount = $count;
                    
                } catch (Exception $e) {
                    $columnInfo = '❌ خطأ: ' . $e->getMessage();
                }
            }
            
            echo "<tr>\n";
            echo "<td>{$tableName}</td>\n";
            echo "<td style='color: {$statusColor};'>{$status}</td>\n";
            echo "<td>{$columnInfo}</td>\n";
            echo "<td>{$recordCount}</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    }
    
    private function checkData() {
        echo "<h3>فحص البيانات:</h3>\n";
        
        try {
            // Check categories
            $categoriesCount = $this->db->query("SELECT COUNT(*) FROM message_categories")->fetchColumn();
            echo "<p>📂 عدد التصنيفات: <strong>{$categoriesCount}</strong></p>\n";
            
            if ($categoriesCount == 0) {
                echo "<p style='color: orange;'>⚠️ لا توجد تصنيفات. يُنصح بإدراج التصنيفات الافتراضية.</p>\n";
            }
            
            // Check templates
            $templatesCount = $this->db->query("SELECT COUNT(*) FROM message_templates")->fetchColumn();
            echo "<p>📝 عدد قوالب الردود: <strong>{$templatesCount}</strong></p>\n";
            
            // Check messages
            $messagesCount = $this->db->query("SELECT COUNT(*) FROM student_messages")->fetchColumn();
            echo "<p>📬 عدد رسائل الطلاب: <strong>{$messagesCount}</strong></p>\n";
            
            // Check replies
            $repliesCount = $this->db->query("SELECT COUNT(*) FROM admin_replies")->fetchColumn();
            echo "<p>💬 عدد ردود الإدارة: <strong>{$repliesCount}</strong></p>\n";
            
            // Check notifications
            $notificationsCount = $this->db->query("SELECT COUNT(*) FROM message_notifications")->fetchColumn();
            echo "<p>🔔 عدد الإشعارات: <strong>{$notificationsCount}</strong></p>\n";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في فحص البيانات: " . $e->getMessage() . "</p>\n";
        }
    }
    
    private function checkPermissions() {
        echo "<h3>فحص الصلاحيات:</h3>\n";
        
        try {
            // Check if we can create a test record
            $this->db->beginTransaction();
            
            // Test insert into categories
            $stmt = $this->db->prepare("INSERT INTO message_categories (name, name_ar, description) VALUES (?, ?, ?)");
            $result = $stmt->execute(['Test', 'اختبار', 'اختبار الصلاحيات']);
            
            if ($result) {
                $testId = $this->db->lastInsertId();
                echo "<p>✅ صلاحية الإدراج: متاحة</p>\n";
                
                // Test update
                $updateStmt = $this->db->prepare("UPDATE message_categories SET description = ? WHERE id = ?");
                $updateResult = $updateStmt->execute(['اختبار محدث', $testId]);
                
                if ($updateResult) {
                    echo "<p>✅ صلاحية التحديث: متاحة</p>\n";
                } else {
                    echo "<p>❌ صلاحية التحديث: غير متاحة</p>\n";
                }
                
                // Test delete
                $deleteStmt = $this->db->prepare("DELETE FROM message_categories WHERE id = ?");
                $deleteResult = $deleteStmt->execute([$testId]);
                
                if ($deleteResult) {
                    echo "<p>✅ صلاحية الحذف: متاحة</p>\n";
                } else {
                    echo "<p>❌ صلاحية الحذف: غير متاحة</p>\n";
                }
                
            } else {
                echo "<p>❌ صلاحية الإدراج: غير متاحة</p>\n";
            }
            
            $this->db->rollBack(); // Rollback test changes
            
        } catch (Exception $e) {
            $this->db->rollBack();
            echo "<p style='color: red;'>❌ خطأ في فحص الصلاحيات: " . $e->getMessage() . "</p>\n";
        }
    }
}

// Run the check if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'check_system_status.php') {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فحص حالة النظام - <?php echo SITE_NAME; ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { padding: 10px; text-align: right; border: 1px solid #ddd; }
            th { background: #f8f9fa; font-weight: bold; }
            .btn { background: #4682B4; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
            .btn:hover { background: #357abd; }
            .btn-danger { background: #dc3545; }
            .btn-danger:hover { background: #c82333; }
            .actions { text-align: center; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>فحص حالة نظام التواصل</h1>
            
            <?php
            $checker = new SystemStatusChecker();
            $checker->checkStatus();
            ?>
            
            <div class="actions">
                <h3>الإجراءات المتاحة:</h3>
                <a href="setup_communication_system.php" class="btn">إعداد النظام</a>
                <a href="reset_communication_system.php" class="btn btn-danger">إعادة تعيين النظام</a>
                <a href="../admin/student_messages.php" class="btn">إدارة الرسائل</a>
                <a href="../page/ask_teacher.php" class="btn">صفحة اسأل معلم</a>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
