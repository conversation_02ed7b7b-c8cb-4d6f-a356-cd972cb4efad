<?php
header('Content-Type: application/json');
session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
error_log("Session data: " . print_r($_SESSION, true));
if (!isset($_SESSION['admin_id'])) {
    error_log("Admin not logged in - admin_id not in session");
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access',
        'debug' => 'Admin ID not found in session'
    ]);
    exit;
}
error_log("Admin logged in with ID: " . $_SESSION['admin_id']);

try {
    $query = trim($_GET['q'] ?? '');
    $limit = max(1, min(20, intval($_GET['limit'] ?? 10))); // Max 20 results

    error_log("Search query: '$query', limit: $limit");

    if (strlen($query) < 2) {
        error_log("Query too short, returning empty results");
        echo json_encode([
            'success' => true,
            'data' => []
        ]);
        exit;
    }
    
    $db = Database::getInstance()->getConnection();
    
    // Search users by name, username, or email
    $stmt = $db->prepare("
        SELECT 
            id, username, email, 
            first_name, second_name, third_name, fourth_name,
            education_level, education_type, grade
        FROM users 
        WHERE is_active = 1 
        AND (
            username LIKE ? OR 
            email LIKE ? OR 
            first_name LIKE ? OR 
            second_name LIKE ? OR 
            third_name LIKE ? OR 
            fourth_name LIKE ? OR
            CONCAT(first_name, ' ', second_name, ' ', third_name, ' ', fourth_name) LIKE ?
        )
        ORDER BY 
            CASE 
                WHEN username LIKE ? THEN 1
                WHEN email LIKE ? THEN 2
                WHEN first_name LIKE ? THEN 3
                ELSE 4
            END,
            first_name, second_name
        LIMIT ?
    ");
    
    $searchParam = "%{$query}%";
    $exactSearchParam = "{$query}%";
    
    $stmt->execute([
        $searchParam, $searchParam, $searchParam, $searchParam, 
        $searchParam, $searchParam, $searchParam,
        $exactSearchParam, $exactSearchParam, $exactSearchParam,
        $limit
    ]);
    
    $users = $stmt->fetchAll();

    error_log("Found " . count($users) . " users");

    // Format results
    $results = [];
    foreach ($users as $user) {
        $fullName = trim(implode(' ', array_filter([
            $user['first_name'],
            $user['second_name'],
            $user['third_name'],
            $user['fourth_name']
        ])));
        
        // Format education info
        $levels = [
            'primary' => 'ابتدائي',
            'preparatory' => 'إعدادي', 
            'secondary' => 'ثانوي'
        ];
        $types = [
            'azhari' => 'أزهري',
            'general' => 'عام'
        ];
        $grades = [
            '1' => 'الأول', '2' => 'الثاني', '3' => 'الثالث',
            '4' => 'الرابع', '5' => 'الخامس', '6' => 'السادس'
        ];
        
        $levelText = $levels[$user['education_level']] ?? $user['education_level'];
        $typeText = $types[$user['education_type']] ?? $user['education_type'];
        $gradeText = $grades[$user['grade']] ?? $user['grade'];
        
        $educationInfo = "{$gradeText} {$levelText} {$typeText}";
        
        $results[] = [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'full_name' => $fullName ?: 'غير محدد',
            'education_info' => $educationInfo,
            'display_text' => $fullName . ' (' . $user['username'] . ')',
            'secondary_text' => $user['email'] . ' - ' . $educationInfo
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $results,
        'query' => $query,
        'count' => count($results)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("API Error in search_users.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'حدث خطأ في البحث'
    ], JSON_UNESCAPED_UNICODE);
}
?>
