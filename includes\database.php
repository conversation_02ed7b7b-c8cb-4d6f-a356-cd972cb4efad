<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../DB/all-sheet.mysql';

class Database {
    private static $instance = null;
    private $pdo;

    private function __construct() {
        $this->pdo = DatabaseConnection::getInstance()->getConnection();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->pdo;
    }

    // Prevent cloning
    private function __clone() {}

    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

class UserManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function usernameExists($username) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function emailExists($email) {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            $errors[] = 'كلمة المرور يجب أن تكون ' . PASSWORD_MIN_LENGTH . ' أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return $errors;
    }
    
    public function validatePhone($phone) {
        // Remove any spaces or dashes
        $phone = preg_replace('/[\s-]/', '', $phone);
        
        // Check if it's exactly 11 digits and starts with valid prefix
        if (strlen($phone) !== 11) {
            return false;
        }
        
        $prefix = substr($phone, 0, 3);
        return in_array($prefix, PHONE_PREFIXES);
    }
    
    public function validateArabicName($name) {
        // Check if name contains only Arabic characters and spaces
        return preg_match('/^[\p{Arabic}\s]+$/u', $name);
    }
    
    public function validateAge($birthDate) {
        $today = new DateTime();
        $birth = new DateTime($birthDate);
        $age = $today->diff($birth)->y;
        
        return $age >= MIN_AGE;
    }
    
    public function createUser($userData) {
        try {
            $this->db->beginTransaction();
            
            $stmt = $this->db->prepare("
                INSERT INTO users (
                    username, email, password_hash, first_name, second_name, 
                    third_name, fourth_name, gender, birth_date, personal_phone, 
                    father_phone, mother_phone, education_level, education_type, 
                    grade, specialization
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $userData['username'],
                $userData['email'],
                password_hash($userData['password'], PASSWORD_DEFAULT),
                $userData['first_name'],
                $userData['second_name'],
                $userData['third_name'],
                $userData['fourth_name'],
                $userData['gender'],
                $userData['birth_date'],
                $userData['personal_phone'],
                $userData['father_phone'],
                $userData['mother_phone'],
                $userData['education_level'],
                $userData['education_type'],
                $userData['grade'],
                $userData['specialization'] ?? null
            ]);
            
            $userId = $this->db->lastInsertId();
            $this->db->commit();
            
            return $userId;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    public function authenticateUser($usernameOrEmail, $password) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, email, password_hash, first_name, last_login
                FROM users
                WHERE (username = ? OR email = ?) AND is_active = 1
            ");
            $stmt->execute([$usernameOrEmail, $usernameOrEmail]);
            $user = $stmt->fetch();

            // Debug logging
            error_log("User found: " . ($user ? "Yes" : "No"));
            if ($user) {
                error_log("Password verification: " . (password_verify($password, $user['password_hash']) ? "Success" : "Failed"));
            }

            if ($user && password_verify($password, $user['password_hash'])) {
                // Update last login
                $updateStmt = $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$user['id']]);

                return $user;
            }

            return false;
        } catch (Exception $e) {
            error_log("Authentication error: " . $e->getMessage());
            return false;
        }
    }
    
    public function logLoginAttempt($usernameOrEmail, $success, $ipAddress) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO login_attempts (username_or_email, success, ip_address)
                VALUES (?, ?, ?)
            ");
            // Convert boolean to integer for database
            $successInt = $success ? 1 : 0;
            $stmt->execute([$usernameOrEmail, $successInt, $ipAddress]);
        } catch (Exception $e) {
            error_log("Error logging login attempt: " . $e->getMessage());
        }
    }

    public function getUserById($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, email, first_name, second_name, third_name, fourth_name,
                       gender, birth_date, personal_phone, father_phone, mother_phone,
                       education_level, education_type, grade, specialization,
                       is_active, email_verified, created_at, last_login
                FROM users
                WHERE id = ? AND is_active = 1
            ");
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting user by ID: " . $e->getMessage());
            return false;
        }
    }

    public function getUnreadNotificationCount($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM notifications
                WHERE (user_id = ? OR is_global = 1) AND is_read = 0
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            $count = $result['count'] ?? 0;

            error_log("getUnreadNotificationCount - User: $userId, Count: $count");

            return $count;
        } catch (Exception $e) {
            error_log("Error getting notification count: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return 0;
        }
    }

    public function getUserNotifications($userId, $limit = 20) {
        try {
            // Convert limit to integer to avoid binding issues
            $limit = (int)$limit;

            $stmt = $this->db->prepare("
                SELECT id, title, message, type, is_read, created_at, is_global
                FROM notifications
                WHERE (user_id = ? OR is_global = 1)
                ORDER BY created_at DESC
                LIMIT " . $limit
            );
            $stmt->execute([$userId]);
            $result = $stmt->fetchAll();

            error_log("getUserNotifications - User: $userId, Found: " . count($result) . " notifications");

            return $result;
        } catch (Exception $e) {
            error_log("Error getting user notifications: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return [];
        }
    }

    public function markNotificationAsRead($notificationId, $userId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE notifications
                SET is_read = 1, read_at = NOW()
                WHERE id = ? AND (user_id = ? OR is_global = 1)
            ");
            return $stmt->execute([$notificationId, $userId]);
        } catch (Exception $e) {
            error_log("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }

    public function markAllNotificationsAsRead($userId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE notifications
                SET is_read = 1, read_at = NOW()
                WHERE (user_id = ? OR is_global = 1) AND is_read = 0
            ");
            return $stmt->execute([$userId]);
        } catch (Exception $e) {
            error_log("Error marking all notifications as read: " . $e->getMessage());
            return false;
        }
    }

    public function createNotification($userId, $title, $message, $type = 'info', $isGlobal = false, $adminId = null) {
        try {
            // If no admin_id provided, use system admin (ID 1) or create a system notification
            if ($adminId === null) {
                $adminId = 1; // Assuming system admin has ID 1
            }

            $stmt = $this->db->prepare("
                INSERT INTO notifications (user_id, admin_id, title, message, type, is_global)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            return $stmt->execute([$userId, $adminId, $title, $message, $type, $isGlobal ? 1 : 0]);
        } catch (Exception $e) {
            error_log("Error creating notification: " . $e->getMessage());
            return false;
        }
    }

    public function getTotalUsersCount() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting total users count: " . $e->getMessage());
            return 0;
        }
    }

    public function getActiveUsersCount() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting active users count: " . $e->getMessage());
            return 0;
        }
    }

    public function getNewUsersToday() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting new users today: " . $e->getMessage());
            return 0;
        }
    }

    // Todo Management Methods
    public function getUserTodos($userId, $completed = null) {
        try {
            $sql = "SELECT * FROM user_todos WHERE user_id = ?";
            $params = [$userId];

            if ($completed !== null) {
                $sql .= " AND is_completed = ?";
                $params[] = $completed;
            }

            $sql .= " ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting user todos: " . $e->getMessage());
            return [];
        }
    }

    public function createTodo($userId, $title, $description = '', $priority = 'medium', $dueDate = null) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO user_todos (user_id, title, description, priority, due_date)
                VALUES (?, ?, ?, ?, ?)
            ");
            $result = $stmt->execute([$userId, $title, $description, $priority, $dueDate]);

            if ($result) {
                $this->logActivity($userId, 'todo_create', "تم إضافة مهمة جديدة: {$title}", [
                    'title' => $title,
                    'priority' => $priority,
                    'due_date' => $dueDate
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error creating todo: " . $e->getMessage());
            return false;
        }
    }

    public function updateTodo($todoId, $userId, $title, $description = '', $priority = 'medium', $dueDate = null) {
        try {
            $stmt = $this->db->prepare("
                UPDATE user_todos
                SET title = ?, description = ?, priority = ?, due_date = ?
                WHERE id = ? AND user_id = ?
            ");
            return $stmt->execute([$title, $description, $priority, $dueDate, $todoId, $userId]);
        } catch (Exception $e) {
            error_log("Error updating todo: " . $e->getMessage());
            return false;
        }
    }

    public function toggleTodoComplete($todoId, $userId) {
        try {
            // Get current todo info
            $stmt = $this->db->prepare("SELECT title, is_completed FROM user_todos WHERE id = ? AND user_id = ?");
            $stmt->execute([$todoId, $userId]);
            $todo = $stmt->fetch();

            if (!$todo) return false;

            $stmt = $this->db->prepare("
                UPDATE user_todos
                SET is_completed = NOT is_completed,
                    completed_at = CASE WHEN is_completed = 0 THEN NOW() ELSE NULL END
                WHERE id = ? AND user_id = ?
            ");
            $result = $stmt->execute([$todoId, $userId]);

            if ($result) {
                $action = $todo['is_completed'] ? 'إعادة فتح' : 'إكمال';
                $this->logActivity($userId, 'todo_complete', "{$action} المهمة: {$todo['title']}", [
                    'todo_id' => $todoId,
                    'title' => $todo['title'],
                    'completed' => !$todo['is_completed']
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error toggling todo complete: " . $e->getMessage());
            return false;
        }
    }

    public function deleteTodo($todoId, $userId) {
        try {
            // Get todo info before deletion
            $stmt = $this->db->prepare("SELECT title FROM user_todos WHERE id = ? AND user_id = ?");
            $stmt->execute([$todoId, $userId]);
            $todo = $stmt->fetch();

            if (!$todo) return false;

            $stmt = $this->db->prepare("DELETE FROM user_todos WHERE id = ? AND user_id = ?");
            $result = $stmt->execute([$todoId, $userId]);

            if ($result) {
                $this->logActivity($userId, 'todo_delete', "تم حذف المهمة: {$todo['title']}", [
                    'todo_id' => $todoId,
                    'title' => $todo['title']
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error deleting todo: " . $e->getMessage());
            return false;
        }
    }

    // Notes Management Methods
    public function getUserNotes($userId, $category = null) {
        try {
            $sql = "SELECT * FROM user_notes WHERE user_id = ?";
            $params = [$userId];

            if ($category) {
                $sql .= " AND category = ?";
                $params[] = $category;
            }

            $sql .= " ORDER BY is_pinned DESC, updated_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting user notes: " . $e->getMessage());
            return [];
        }
    }

    public function createNote($userId, $title, $content, $category = 'general') {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO user_notes (user_id, title, content, category)
                VALUES (?, ?, ?, ?)
            ");
            $result = $stmt->execute([$userId, $title, $content, $category]);

            if ($result) {
                $this->logActivity($userId, 'note_create', "تم إضافة ملاحظة جديدة: {$title}", [
                    'title' => $title,
                    'category' => $category
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error creating note: " . $e->getMessage());
            return false;
        }
    }

    public function updateNote($noteId, $userId, $title, $content, $category = 'general') {
        try {
            $stmt = $this->db->prepare("
                UPDATE user_notes
                SET title = ?, content = ?, category = ?
                WHERE id = ? AND user_id = ?
            ");
            return $stmt->execute([$title, $content, $category, $noteId, $userId]);
        } catch (Exception $e) {
            error_log("Error updating note: " . $e->getMessage());
            return false;
        }
    }

    public function toggleNotePin($noteId, $userId) {
        try {
            // Get current note info
            $stmt = $this->db->prepare("SELECT title, is_pinned FROM user_notes WHERE id = ? AND user_id = ?");
            $stmt->execute([$noteId, $userId]);
            $note = $stmt->fetch();

            if (!$note) return false;

            $stmt = $this->db->prepare("
                UPDATE user_notes
                SET is_pinned = NOT is_pinned
                WHERE id = ? AND user_id = ?
            ");
            $result = $stmt->execute([$noteId, $userId]);

            if ($result) {
                $action = $note['is_pinned'] ? 'إلغاء تثبيت' : 'تثبيت';
                $this->logActivity($userId, 'note_pin', "{$action} الملاحظة: {$note['title']}", [
                    'note_id' => $noteId,
                    'title' => $note['title'],
                    'pinned' => !$note['is_pinned']
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error toggling note pin: " . $e->getMessage());
            return false;
        }
    }

    public function deleteNote($noteId, $userId) {
        try {
            // Get note info before deletion
            $stmt = $this->db->prepare("SELECT title FROM user_notes WHERE id = ? AND user_id = ?");
            $stmt->execute([$noteId, $userId]);
            $note = $stmt->fetch();

            if (!$note) return false;

            $stmt = $this->db->prepare("DELETE FROM user_notes WHERE id = ? AND user_id = ?");
            $result = $stmt->execute([$noteId, $userId]);

            if ($result) {
                $this->logActivity($userId, 'note_delete', "تم حذف الملاحظة: {$note['title']}", [
                    'note_id' => $noteId,
                    'title' => $note['title']
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error deleting note: " . $e->getMessage());
            return false;
        }
    }

    public function updateUserProfile($userId, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users
                SET first_name = ?, second_name = ?, third_name = ?, fourth_name = ?,
                    personal_phone = ?, father_phone = ?, mother_phone = ?,
                    education_level = ?, education_type = ?, grade = ?, specialization = ?
                WHERE id = ?
            ");
            $result = $stmt->execute([
                $data['first_name'],
                $data['second_name'],
                $data['third_name'],
                $data['fourth_name'],
                $data['personal_phone'],
                $data['father_phone'],
                $data['mother_phone'],
                $data['education_level'],
                $data['education_type'],
                $data['grade'],
                $data['specialization'],
                $userId
            ]);

            if ($result) {
                $this->logActivity($userId, 'profile_update', 'تم تحديث الملف الشخصي', [
                    'updated_fields' => array_keys($data)
                ]);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error updating user profile: " . $e->getMessage());
            return false;
        }
    }

    public function updateUserPassword($userId, $newPassword) {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
            $result = $stmt->execute([$hashedPassword, $userId]);

            if ($result) {
                $this->logActivity($userId, 'password_change', 'تم تغيير كلمة المرور');
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error updating user password: " . $e->getMessage());
            return false;
        }
    }

    // Activity Log Methods
    public function logActivity($userId, $activityType, $description, $details = null) {
        try {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;

            $stmt = $this->db->prepare("
                INSERT INTO user_activity_log (user_id, activity_type, activity_description, details, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $detailsJson = $details ? json_encode($details, JSON_UNESCAPED_UNICODE) : null;

            return $stmt->execute([$userId, $activityType, $description, $detailsJson, $ipAddress, $userAgent]);
        } catch (Exception $e) {
            error_log("Error logging activity: " . $e->getMessage());
            return false;
        }
    }

    public function getUserActivityLog($userId, $limit = 20, $offset = 0) {
        try {
            // Convert to integers to avoid PDO issues with LIMIT/OFFSET
            $limit = (int)$limit;
            $offset = (int)$offset;

            $stmt = $this->db->prepare("
                SELECT * FROM user_activity_log
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT {$limit} OFFSET {$offset}
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetchAll();

            // Debug logging
            error_log("getUserActivityLog - User ID: {$userId}, Limit: {$limit}, Offset: {$offset}");
            error_log("getUserActivityLog - Result count: " . count($result));

            return $result;
        } catch (Exception $e) {
            error_log("Error getting user activity log: " . $e->getMessage());
            return [];
        }
    }

    public function getActivityLogCount($userId) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM user_activity_log WHERE user_id = ?");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting activity log count: " . $e->getMessage());
            return 0;
        }
    }

    // Admin User Management Methods
    public function updateUserStatus($userId, $status) {
        try {
            $stmt = $this->db->prepare("UPDATE users SET is_active = ? WHERE id = ?");
            $result = $stmt->execute([$status, $userId]);

            if ($result) {
                $action = $status ? 'تفعيل' : 'إلغاء تفعيل';
                $this->logActivity($userId, 'status_change', "تم {$action} الحساب بواسطة الإدارة");
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error updating user status: " . $e->getMessage());
            return false;
        }
    }

    public function deleteUser($userId) {
        try {
            // Get user info before deletion
            $stmt = $this->db->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) return false;

            // Delete user (this will cascade delete related data due to foreign keys)
            $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
            $result = $stmt->execute([$userId]);

            if ($result) {
                error_log("User deleted by admin: {$user['username']} (ID: {$userId})");
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error deleting user: " . $e->getMessage());
            return false;
        }
    }

    public function debugActivityLog($userId) {
        try {
            // Check if table exists
            $stmt = $this->db->query("SHOW TABLES LIKE 'user_activity_log'");
            $tableExists = $stmt->fetch();

            if (!$tableExists) {
                return ['error' => 'Table user_activity_log does not exist'];
            }

            // Get count
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM user_activity_log WHERE user_id = ?");
            $stmt->execute([$userId]);
            $count = $stmt->fetch()['count'];

            // Get recent activities
            $stmt = $this->db->prepare("SELECT * FROM user_activity_log WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
            $stmt->execute([$userId]);
            $activities = $stmt->fetchAll();

            return [
                'table_exists' => true,
                'count' => $count,
                'activities' => $activities
            ];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function getAllActiveUsers() {
        try {
            $stmt = $this->db->prepare("SELECT id, username, email, first_name, second_name, third_name, fourth_name FROM users WHERE is_active = 1");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting all active users: " . $e->getMessage());
            return [];
        }
    }

    public function getUsersByIds($userIds) {
        try {
            if (empty($userIds)) return [];

            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $stmt = $this->db->prepare("SELECT id, username, email, first_name, second_name, third_name, fourth_name FROM users WHERE id IN ({$placeholders}) AND is_active = 1");
            $stmt->execute($userIds);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting users by IDs: " . $e->getMessage());
            return [];
        }
    }

    public function getUsersByFilters($filters) {
        try {
            $where_conditions = ['is_active = 1'];
            $params = [];

            if (!empty($filters['education_level'])) {
                $where_conditions[] = "education_level = ?";
                $params[] = $filters['education_level'];
            }

            if (!empty($filters['education_type'])) {
                $where_conditions[] = "education_type = ?";
                $params[] = $filters['education_type'];
            }

            if (!empty($filters['grade'])) {
                $where_conditions[] = "grade = ?";
                $params[] = $filters['grade'];
            }

            $where_clause = implode(' AND ', $where_conditions);
            $stmt = $this->db->prepare("SELECT id, username, email, first_name, second_name, third_name, fourth_name FROM users WHERE {$where_clause}");
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting users by filters: " . $e->getMessage());
            return [];
        }
    }
}

// Admin Manager Class
class AdminManager {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    public function authenticateAdmin($usernameOrEmail, $password) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, email, password_hash, full_name, role, last_login
                FROM admins
                WHERE (username = ? OR email = ?) AND is_active = 1
            ");
            $stmt->execute([$usernameOrEmail, $usernameOrEmail]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['password_hash'])) {
                // Update last login
                $updateStmt = $this->db->prepare("UPDATE admins SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$admin['id']]);

                return $admin;
            }

            return false;
        } catch (Exception $e) {
            error_log("Admin authentication error: " . $e->getMessage());
            return false;
        }
    }

    public function getAdminById($adminId) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, username, email, full_name, role, is_active, created_at, last_login
                FROM admins
                WHERE id = ? AND is_active = 1
            ");
            $stmt->execute([$adminId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting admin by ID: " . $e->getMessage());
            return false;
        }
    }

    public function getTotalNotificationsCount() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM notifications");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting total notifications count: " . $e->getMessage());
            return 0;
        }
    }

    public function adminExists($username, $email) {
        try {
            $stmt = $this->db->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            error_log("Error checking admin existence: " . $e->getMessage());
            return true; // Return true to prevent creation on error
        }
    }

    public function createAdmin($username, $email, $password, $fullName, $role = 'admin') {
        try {
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("
                INSERT INTO admins (username, email, password_hash, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            $result = $stmt->execute([$username, $email, $hashedPassword, $fullName, $role]);

            if ($result) {
                error_log("New admin created: {$username} ({$email}) with role: {$role}");
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error creating admin: " . $e->getMessage());
            return false;
        }
    }
}

// Notification Manager Class
class NotificationManager {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    public function createNotification($userId, $title, $message, $type = 'info', $isGlobal = false, $adminId = null) {
        try {
            // If no admin_id provided, use system admin (ID 1) or session admin
            if ($adminId === null) {
                $adminId = $_SESSION['admin_id'] ?? 1; // Use session admin or system admin
            }

            $stmt = $this->db->prepare("
                INSERT INTO notifications (user_id, admin_id, title, message, type, is_global)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            return $stmt->execute([$userId, $adminId, $title, $message, $type, $isGlobal ? 1 : 0]);
        } catch (Exception $e) {
            error_log("Error creating notification: " . $e->getMessage());
            return false;
        }
    }

    public function createBulkNotifications($userIds, $title, $message, $type = 'info', $adminId = null) {
        try {
            // If no admin_id provided, use session admin or system admin
            if ($adminId === null) {
                $adminId = $_SESSION['admin_id'] ?? 1;
            }

            $this->db->beginTransaction();

            $stmt = $this->db->prepare("
                INSERT INTO notifications (user_id, admin_id, title, message, type)
                VALUES (?, ?, ?, ?, ?)
            ");

            $successCount = 0;
            foreach ($userIds as $userId) {
                if ($stmt->execute([$userId, $adminId, $title, $message, $type])) {
                    $successCount++;
                }
            }

            $this->db->commit();
            return $successCount;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("Error creating bulk notifications: " . $e->getMessage());
            return 0;
        }
    }

    public function deleteNotification($notificationId) {
        try {
            $stmt = $this->db->prepare("DELETE FROM notifications WHERE id = ?");
            return $stmt->execute([$notificationId]);
        } catch (Exception $e) {
            error_log("Error deleting notification: " . $e->getMessage());
            return false;
        }
    }

    public function markAsRead($notificationId) {
        try {
            $stmt = $this->db->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
            return $stmt->execute([$notificationId]);
        } catch (Exception $e) {
            error_log("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }

    public function markAsUnread($notificationId) {
        try {
            $stmt = $this->db->prepare("UPDATE notifications SET is_read = 0 WHERE id = ?");
            return $stmt->execute([$notificationId]);
        } catch (Exception $e) {
            error_log("Error marking notification as unread: " . $e->getMessage());
            return false;
        }
    }

    public function bulkDeleteNotifications($notificationIds) {
        try {
            if (empty($notificationIds)) {
                return 0;
            }

            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
            $stmt = $this->db->prepare("DELETE FROM notifications WHERE id IN ($placeholders)");
            $stmt->execute($notificationIds);

            return $stmt->rowCount();
        } catch (Exception $e) {
            error_log("Error bulk deleting notifications: " . $e->getMessage());
            return 0;
        }
    }
}
?>
