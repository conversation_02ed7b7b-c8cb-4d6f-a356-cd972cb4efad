<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/PaymobGateway.php';

// Log callback for debugging
error_log("Paymob callback received: " . file_get_contents('php://input'));

// Get callback data
$callbackData = json_decode(file_get_contents('php://input'), true);

// If JSON decode fails, try to get POST data
if (!$callbackData) {
    $callbackData = $_POST;
}

// Log the callback data
error_log("Paymob callback data: " . print_r($callbackData, true));

// Function to activate subscription with correct dates
function activateSubscriptionWithDates($db, $subscription_id, $user_id, $plan_id) {
    try {
        // Get subscription and plan details
        $stmt = $db->prepare("SELECT us.*, sp.duration_days FROM user_subscriptions us
                             JOIN subscription_plans sp ON us.plan_id = sp.id
                             WHERE us.id = ?");
        $stmt->execute([$subscription_id]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$subscription) {
            return false;
        }

        // Calculate dates from now (payment confirmation time)
        $start_date = date('Y-m-d H:i:s');
        $end_date = date('Y-m-d H:i:s', strtotime('+' . $subscription['duration_days'] . ' days'));

        // Update subscription with correct dates
        $stmt = $db->prepare("UPDATE user_subscriptions SET
                             payment_status = 'completed',
                             start_date = ?,
                             end_date = ?,
                             updated_at = NOW()
                             WHERE id = ?");
        $stmt->execute([$start_date, $end_date, $subscription_id]);

        // Update user subscription status
        $stmt = $db->prepare("UPDATE users SET
                             subscription_status = 'active',
                             current_plan_id = ?,
                             subscription_end_date = ?
                             WHERE id = ?");
        $stmt->execute([$plan_id, $end_date, $user_id]);

        return true;
    } catch (Exception $e) {
        error_log("Error activating subscription: " . $e->getMessage());
        return false;
    }
}

try {
    $paymobGateway = new PaymobGateway();
    $result = $paymobGateway->handleCallback($callbackData);

    if ($result) {
        // If payment was successful, update subscription dates
        if (isset($callbackData['success']) && $callbackData['success'] === 'true' && !$callbackData['error_occured']) {
            try {
                $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
                $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Get subscription from payment order
                $order_id = $callbackData['order']['id'] ?? $callbackData['merchant_order_id'];
                $stmt = $db->prepare("SELECT p.subscription_id, us.user_id, us.plan_id
                                     FROM payments p
                                     JOIN user_subscriptions us ON p.subscription_id = us.id
                                     WHERE p.transaction_id LIKE ?");
                $stmt->execute(['%' . $order_id . '%']);
                $payment_data = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($payment_data) {
                    activateSubscriptionWithDates($db, $payment_data['subscription_id'], $payment_data['user_id'], $payment_data['plan_id']);
                }
            } catch (Exception $e) {
                error_log("Error updating subscription dates: " . $e->getMessage());
            }
        }

        http_response_code(200);
        echo "OK";
    } else {
        http_response_code(400);
        echo "FAILED";
    }

} catch (Exception $e) {
    error_log("Paymob callback error: " . $e->getMessage());
    http_response_code(500);
    echo "ERROR";
}
?>
