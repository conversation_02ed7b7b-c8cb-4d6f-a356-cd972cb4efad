<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>تثبيت نظام الأقسام الدراسية</h2>";
    
    // Read and execute SQL file
    $sql = file_get_contents(__DIR__ . '/../sql/curriculum_subjects.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            try {
                $db->exec($statement);
                echo "<p>✅ تم تنفيذ الاستعلام بنجاح</p>";
            } catch (Exception $e) {
                echo "<p>❌ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
                echo "<pre>" . htmlspecialchars($statement) . "</pre>";
            }
        }
    }
    
    // Check if data was inserted
    $stmt = $db->query("SELECT COUNT(*) as count FROM curriculum_subjects");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>النتائج:</h3>";
    echo "<p>عدد الأقسام المضافة: " . $result['count'] . "</p>";
    
    // Show sample data
    $stmt = $db->query("SELECT * FROM curriculum_subjects LIMIT 10");
    $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>عينة من البيانات:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الاسم</th><th>المرحلة</th><th>النوع</th><th>الصف</th><th>التخصص</th></tr>";
    
    foreach ($subjects as $subject) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($subject['name']) . "</td>";
        echo "<td>" . htmlspecialchars($subject['education_level']) . "</td>";
        echo "<td>" . htmlspecialchars($subject['education_type']) . "</td>";
        echo "<td>" . htmlspecialchars($subject['grade']) . "</td>";
        echo "<td>" . htmlspecialchars($subject['specialization']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم تثبيت نظام الأقسام الدراسية بنجاح!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
