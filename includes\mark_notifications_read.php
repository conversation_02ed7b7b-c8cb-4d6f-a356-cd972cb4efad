<?php
session_start();

// Determine the correct path based on where this file is called from
$configPath = file_exists('../config/config.php') ? '../config/config.php' : 'config/config.php';
$databasePath = file_exists('database.php') ? 'database.php' : 'includes/database.php';

require_once $configPath;
require_once $databasePath;

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    // Set proper headers
    header('Content-Type: application/json; charset=utf-8');

    $input = json_decode(file_get_contents('php://input'), true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON input: ' . json_last_error_msg());
    }

    $userManager = new UserManager();
    $userId = $_SESSION['user_id'];

    error_log("Mark notifications read - User: $userId, Input: " . print_r($input, true));

    if (isset($input['mark_all']) && $input['mark_all']) {
        // Mark all notifications as read
        $result = $userManager->markAllNotificationsAsRead($userId);
        $message = 'تم تحديد جميع الإشعارات كمقروءة';
    } elseif (isset($input['notification_id'])) {
        // Mark specific notification as read
        $notificationId = (int)$input['notification_id'];
        $result = $userManager->markNotificationAsRead($notificationId, $userId);
        $message = 'تم تحديد الإشعار كمقروء';
    } else {
        throw new Exception('معطيات غير صحيحة - يجب تحديد notification_id أو mark_all');
    }

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('فشل في تحديث الإشعارات في قاعدة البيانات');
    }

} catch (Exception $e) {
    error_log("Error in mark_notifications_read.php: " . $e->getMessage());

    header('Content-Type: application/json; charset=utf-8');
    http_response_code(500);

    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
