<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/UserSettingsManager.php';

try {
    $settingsManager = new UserSettingsManager();
    
    // Create the user_settings table
    if ($settingsManager->createUserSettingsTable()) {
        echo "✅ تم إنشاء جدول user_settings بنجاح\n";
    } else {
        echo "❌ فشل في إنشاء جدول user_settings\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?>
