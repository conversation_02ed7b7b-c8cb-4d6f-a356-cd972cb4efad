<?php
require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// For testing, allow access without login
// if (!isset($_SESSION['user_id'])) {
//     http_response_code(401);
//     echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
//     exit;
// }

// Use a test user ID if not logged in
$userId = $_SESSION['user_id'] ?? 1;

// Get exam ID
$examId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$examId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الامتحان مطلوب']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();

    // Check if tables exist and create if needed
    try {
        $db->query("SELECT 1 FROM course_exams LIMIT 1");
    } catch (Exception $e) {
        // Create tables if they don't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS course_exams (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                duration_minutes INT DEFAULT 60,
                total_marks DECIMAL(5,2) DEFAULT 0,
                passing_marks DECIMAL(5,2) DEFAULT 0,
                week_number INT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_course_id (course_id),
                INDEX idx_week_number (week_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS course_exam_questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                exam_id INT NOT NULL,
                question_text TEXT NOT NULL,
                question_type ENUM('true_false', 'multiple_choice') NOT NULL,
                options JSON,
                correct_answer TEXT NOT NULL,
                explanation TEXT,
                points DECIMAL(5,2) DEFAULT 1.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_exam_id (exam_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS user_exam_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                exam_id INT NOT NULL,
                total_score DECIMAL(5,2) DEFAULT 0.00,
                max_score DECIMAL(5,2) DEFAULT 0.00,
                percentage DECIMAL(5,2) DEFAULT 0.00,
                passed BOOLEAN DEFAULT FALSE,
                time_taken_minutes INT DEFAULT 0,
                attempt_number INT DEFAULT 1,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP NULL,
                INDEX idx_user_exam (user_id, exam_id),
                INDEX idx_passed (passed),
                INDEX idx_completed_at (completed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_course (user_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $db->exec("
            CREATE TABLE IF NOT EXISTS courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                subject VARCHAR(100),
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Create course_enrollments table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_course (user_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }
    
    // Get exam details
    $stmt = $db->prepare("
        SELECT e.*, c.title as course_title, c.subject,
               COALESCE(e.duration_minutes, 60) as time_limit_minutes,
               COALESCE(e.description, '') as instructions
        FROM course_exams e
        JOIN courses c ON e.course_id = c.id
        WHERE e.id = ? AND e.is_active = 1
    ");
    $stmt->execute([$examId]);
    $exam = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$exam) {
        // Create sample data if no exams exist
        $stmt = $db->query("SELECT COUNT(*) FROM course_exams");
        $examCount = $stmt->fetchColumn();

        if ($examCount == 0) {
            // Create a sample course first
            $stmt = $db->query("SELECT COUNT(*) FROM courses");
            $courseCount = $stmt->fetchColumn();

            if ($courseCount == 0) {
                $stmt = $db->prepare("
                    INSERT INTO courses (title, subject, description, is_active)
                    VALUES ('كورس تجريبي', 'البرمجة', 'كورس تجريبي للاختبار', 1)
                ");
                $stmt->execute();
                $courseId = $db->lastInsertId();

                // Create enrollment for the user
                $stmt = $db->prepare("
                    INSERT INTO course_enrollments (user_id, course_id, status)
                    VALUES (?, ?, 'active')
                ");
                $stmt->execute([$userId, $courseId]);
            } else {
                $stmt = $db->query("SELECT id FROM courses LIMIT 1");
                $courseId = $stmt->fetchColumn();
            }

            // Create sample exams
            for ($i = 1; $i <= 10; $i++) {
                $stmt = $db->prepare("
                    INSERT INTO course_exams (course_id, title, description, duration_minutes, total_marks, passing_marks, week_number, is_active, created_by)
                    VALUES (?, ?, ?, 30, 10, 6, 1, 1, 1)
                ");
                $stmt->execute([$courseId, "امتحان تجريبي $i", "امتحان تجريبي رقم $i للاختبار"]);
                $newExamId = $db->lastInsertId();

                // Add sample questions
                $questions = [
                    ['هل PHP لغة برمجة؟', 'true_false', 'true', 2],
                    ['هل HTML لغة برمجة؟', 'true_false', 'false', 2],
                    ['هل CSS تستخدم للتنسيق؟', 'true_false', 'true', 3],
                    ['هل JavaScript تعمل في المتصفح؟', 'true_false', 'true', 3]
                ];

                foreach ($questions as $q) {
                    $stmt = $db->prepare("
                        INSERT INTO course_exam_questions (exam_id, question_text, question_type, correct_answer, points)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$newExamId, $q[0], $q[1], $q[2], $q[3]]);
                }
            }

            // Now try to get the requested exam again
            $stmt = $db->prepare("
                SELECT e.*, c.title as course_title, c.subject
                FROM course_exams e
                JOIN courses c ON e.course_id = c.id
                WHERE e.id = ? AND e.is_active = 1
            ");
            $stmt->execute([$examId]);
            $exam = $stmt->fetch(PDO::FETCH_ASSOC);
        }

        if (!$exam) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
            exit;
        }
    }
    
    // Check if user is enrolled in the course (create enrollment if not exists for testing)
    try {
        $stmt = $db->prepare("
            SELECT id FROM course_enrollments
            WHERE user_id = ? AND course_id = ? AND status = 'active'
        ");
        $stmt->execute([$userId, $exam['course_id']]);

        if (!$stmt->fetch()) {
            // For testing, automatically enroll the user
            $stmt = $db->prepare("
                INSERT IGNORE INTO course_enrollments (user_id, course_id, status)
                VALUES (?, ?, 'active')
            ");
            $stmt->execute([$userId, $exam['course_id']]);
        }
    } catch (Exception $e) {
        // If course_enrollments table doesn't exist, create it
        $db->exec("
            CREATE TABLE IF NOT EXISTS course_enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_course (user_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Now enroll the user
        $stmt = $db->prepare("
            INSERT INTO course_enrollments (user_id, course_id, status)
            VALUES (?, ?, 'active')
        ");
        $stmt->execute([$userId, $exam['course_id']]);
    }
    
    // Check if user has already completed this exam
    $stmt = $db->prepare("
        SELECT id, total_score, max_score, percentage, passed, attempt_number, completed_at
        FROM user_exam_attempts
        WHERE user_id = ? AND exam_id = ? AND completed_at IS NOT NULL
        ORDER BY attempt_number DESC
        LIMIT 1
    ");
    $stmt->execute([$userId, $examId]);
    $lastSubmission = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get exam questions
    $stmt = $db->prepare("
        SELECT id, question_text, question_type, options, correct_answer, points
        FROM course_exam_questions 
        WHERE exam_id = ? 
        ORDER BY id ASC
    ");
    $stmt->execute([$examId]);
    $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process questions (hide correct answers from client)
    $processedQuestions = [];
    foreach ($questions as $question) {
        $processedQuestion = [
            'id' => $question['id'],
            'question_text' => $question['question_text'],
            'question_type' => $question['question_type'],
            'points' => $question['points']
        ];
        
        // Add options for multiple choice questions
        if ($question['question_type'] === 'multiple_choice' && $question['options']) {
            $processedQuestion['options'] = json_decode($question['options'], true);
        }
        
        $processedQuestions[] = $processedQuestion;
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'exam' => [
            'id' => $exam['id'],
            'title' => $exam['title'],
            'description' => $exam['description'],
            'course_title' => $exam['course_title'],
            'subject' => $exam['subject'],
            'total_marks' => $exam['total_marks'],
            'passing_marks' => $exam['passing_marks'],
            'time_limit_minutes' => $exam['time_limit_minutes'],
            'instructions' => $exam['instructions'],
            'questions' => $processedQuestions
        ]
    ];
    
    // Add previous attempt info if exists
    if ($lastSubmission) {
        $response['last_attempt'] = [
            'total_score' => $lastSubmission['total_score'],
            'max_score' => $lastSubmission['max_score'],
            'percentage' => $lastSubmission['percentage'],
            'passed' => (bool)$lastSubmission['passed'],
            'attempt_number' => $lastSubmission['attempt_number'],
            'completed_at' => $lastSubmission['completed_at']
        ];
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Error in get_exam.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في الخادم']);
}
?>
