body{
    text-decoration: none;
}

/* Dashboard Responsive Design */

/* Desktop Layout */
.dashboard-layout {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-columns: 280px 1fr;
    grid-template-rows: auto 1fr;
    min-height: 100vh;
}

.dashboard-header {
    grid-area: header;
}

.dashboard-sidebar {
    grid-area: sidebar;
}

.dashboard-main {
    grid-area: main;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow-y: auto;
    min-height: calc(100vh - 80px);
}

/* Enhanced Sidebar Overlay */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease;
}

.sidebar-overlay.show {
    display: block;
    opacity: 1;
}

/* Mobile Toggle Button */
.sidebar-toggle {
    display: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid rgba(70, 130, 180, 0.2);
    font-size: 20px;
    color: #4682B4;
    cursor: pointer;
    padding: 10px;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
}

/* Overlay for mobile sidebar */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 998;
}

.sidebar-overlay.show {
    display: block;
}

/* Tablet Design (768px - 1024px) */
@media (max-width: 1024px) {
    .dashboard-layout {
        grid-template-columns: 250px 1fr;
    }
    
    .dashboard-main {
        padding: 20px;
    }
    
    .dashboard-sidebar {
        width: 250px;
    }
}

/* Mobile Design (max-width: 768px) */
@media (max-width: 768px) {
    .dashboard-layout {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .dashboard-main {
        padding: 20px;
        padding-bottom: 85px; /* Space for bottom navigation */
        min-height: calc(100vh - 160px);
    }

    /* Hide desktop sidebar */
    .dashboard-sidebar {
        position: fixed;
        top: 0;
        right: -100%;
        width: 320px;
        max-width: 85vw;
        height: 100vh;
        z-index: 1001;
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: -8px 0 25px rgba(70, 130, 180, 0.2);
    }

    .dashboard-sidebar.show {
        right: 0;
    }

    /* Show mobile toggle button */
    .sidebar-toggle {
        display: flex;
    }
    
    /* Show mobile bottom navigation */
    .mobile-bottom-nav {
        display: flex;
    }
    
    /* Adjust header for mobile */
    .header-container {
        padding: 0 15px;
    }
    
    .header-actions {
        gap: 10px;
    }
    
    .header-datetime {
        display: none;
    }
    
    .user-greeting {
        display: none;
    }
    
    /* Adjust notifications dropdown for mobile */
    .notifications-dropdown {
        width: 300px;
        right: -50px;
    }
    
    /* Widget adjustments for mobile */
    .todo-widget,
    .notes-widget,
    .activity-widget {
        margin-bottom: 20px;
        padding: 20px;
        border-radius: 15px;
    }

    .todo-stats,
    .notes-stats,
    .activity-stats {
        flex-direction: column;
        gap: 12px;
    }

    .notes-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    /* Welcome section adjustments */
    .welcome-section {
        padding: 25px;
        margin-bottom: 25px;
    }

    .welcome-content {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
    }

    .welcome-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        padding: 12px;
    }

    .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 20px;
    }

    /* Quick actions adjustments */
    .quick-actions-section {
        padding: 20px;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .action-card {
        padding: 20px;
    }

    .action-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .widgets-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Form adjustments */
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    /* Modal adjustments */
    .modal-content {
        width: 95%;
        margin: 15px;
        max-height: 95vh;
    }
}

/* Small Mobile Design (max-width: 480px) */
@media (max-width: 480px) {
    .dashboard-main {
        padding: 15px;
        padding-bottom: 80px;
        min-height: calc(100vh - 140px);
    }

    .dashboard-sidebar {
        width: 100%;
        max-width: 300px;
        right: -100%;
    }

    /* Widget adjustments for small mobile */
    .todo-widget,
    .notes-widget,
    .activity-widget {
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 12px;
    }

    .todo-header,
    .notes-header,
    .activity-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .btn-add-todo,
    .btn-add-note {
        justify-content: center;
        min-height: 40px;
        font-size: 14px;
    }

    /* Welcome section for small mobile */
    .welcome-section {
        padding: 20px;
        margin-bottom: 20px;
    }

    .welcome-text h1 {
        font-size: 22px;
    }

    .welcome-text p {
        font-size: 14px;
    }

    .welcome-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .stat-item {
        padding: 10px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    /* Quick actions for small mobile */
    .quick-actions-section {
        padding: 15px;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .action-card {
        padding: 15px;
    }

    .action-icon {
        font-size: 36px;
        margin-bottom: 10px;
    }

    .action-card h4 {
        font-size: 16px;
    }

    .action-card p {
        font-size: 13px;
    }

    .widgets-grid {
        gap: 15px;
    }
    
    .note-card {
        padding: 15px;
    }
    
    .todo-item {
        padding: 12px;
    }
    
    .modal-content {
        width: 98%;
        margin: 5px;
    }
    
    .modal form {
        padding: 20px;
    }
}

/* Very Small Mobile Design (max-width: 320px) */
@media (max-width: 320px) {
    .dashboard-main {
        padding: 12px;
        padding-bottom: 75px;
        min-height: calc(100vh - 120px);
    }

    .dashboard-sidebar {
        width: 100%;
        max-width: 280px;
    }

    /* Widget adjustments for very small mobile */
    .todo-widget,
    .notes-widget,
    .activity-widget {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 10px;
    }

    .todo-header h3,
    .notes-header h3,
    .activity-header h3 {
        font-size: 16px;
    }

    .btn-add-todo,
    .btn-add-note {
        min-height: 36px;
        font-size: 13px;
        padding: 8px 12px;
    }

    /* Welcome section for very small mobile */
    .welcome-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .welcome-text h1 {
        font-size: 20px;
    }

    .welcome-text p {
        font-size: 13px;
    }

    .stat-item {
        padding: 8px;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 11px;
    }

    /* Quick actions for very small mobile */
    .quick-actions-section {
        padding: 12px;
    }

    .quick-actions-section h2 {
        font-size: 18px;
    }

    .quick-actions-grid {
        gap: 10px;
    }

    .action-card {
        padding: 12px;
    }

    .action-icon {
        font-size: 32px;
        margin-bottom: 8px;
    }

    .action-card h4 {
        font-size: 15px;
    }

    .action-card p {
        font-size: 12px;
    }

    .widgets-grid {
        gap: 12px;
    }

    /* Form and modal adjustments */
    .modal-content {
        width: 98%;
        margin: 3px;
        border-radius: 8px;
    }

    .modal form {
        padding: 15px;
    }

    .form-input,
    .form-select,
    .form-textarea {
        padding: 10px 12px;
        font-size: 14px;
    }

    .btn {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 36px;
    }
}

/* Landscape Mobile (max-height: 500px) */
@media (max-height: 500px) and (orientation: landscape) {
    .dashboard-main {
        padding-bottom: 70px;
    }
    
    .mobile-bottom-nav {
        padding: 8px 0;
    }
    
    .mobile-nav-item {
        padding: 6px 8px;
    }
    
    .mobile-nav-icon {
        font-size: 16px;
        margin-bottom: 2px;
    }
    
    .mobile-nav-text {
        font-size: 8px;
    }
}

/* Print Styles */
@media print {
    .dashboard-header,
    .dashboard-sidebar,
    .mobile-bottom-nav,
    .sidebar-toggle,
    .note-actions,
    .todo-actions,
    .btn-add-todo,
    .btn-add-note {
        display: none !important;
    }
    
    .dashboard-layout {
        grid-template-areas: "main";
        grid-template-columns: 1fr;
    }
    
    .dashboard-main {
        padding: 0;
        background: white;
    }
    
    .todo-widget,
    .notes-widget {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .dashboard-sidebar {
        border-left: 2px solid #000;
    }
    
    .nav-link {
        border: 1px solid transparent;
    }
    
    .nav-link:hover,
    .nav-link.active {
        border-color: #000;
    }
    
    .todo-item,
    .note-card {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .dashboard-sidebar,
    .sidebar-overlay,
    .nav-link,
    .todo-item,
    .note-card,
    .btn-add-todo,
    .btn-add-note,
    .notification-btn,
    .todo-action-btn,
    .note-action-btn {
        transition: none;
    }
    
    .stat-card:hover,
    .note-card:hover,
    .btn-add-todo:hover,
    .btn-add-note:hover {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dashboard-main {
        background: #1a1a1a;
    }
    
    .todo-widget,
    .notes-widget {
        background: #2d2d2d;
        color: #fff;
    }
    
    .todo-item,
    .note-card {
        background: #3d3d3d;
        border-color: #555;
        color: #fff;
    }
    
    .note-card.pinned {
        background: linear-gradient(135deg, #4a4a00 0%, #6a6a00 100%);
    }
    
    .section-title,
    .todo-header h3,
    .notes-header h3 {
        color: #fff;
    }
    
    .todo-title,
    .note-title {
        color: #fff;
    }
    
    .todo-description,
    .note-content {
        color: #ccc;
    }
    
    .empty-state {
        color: #ccc;
    }
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.todo-action-btn:focus,
.note-action-btn:focus,
.btn-add-todo:focus,
.btn-add-note:focus,
.sidebar-toggle:focus {
    outline: 2px solid #4682B4;
    outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #4682B4;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1001;
}

.skip-link:focus {
    top: 6px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4682B4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
