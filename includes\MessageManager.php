<?php
/**
 * Message Manager Class
 * Handles student-teacher communication system operations
 */

require_once __DIR__ . '/database.php';

class MessageManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Send a new student message (alias for createMessage)
     */
    public function sendMessage($userId, $subject, $message, $messageType = 'question', $categoryId = null, $priority = 'medium') {
        return $this->createMessage($userId, $subject, $message, $messageType, $priority, $categoryId);
    }

    /**
     * Create a new student message
     */
    public function createMessage($userId, $subject, $message, $messageType = 'question', $priority = 'medium', $categoryId = null) {
        try {
            $this->db->beginTransaction();
            
            // Insert message
            $stmt = $this->db->prepare("
                INSERT INTO student_messages (user_id, subject, message, message_type, priority, category_id, status)
                VALUES (?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            $result = $stmt->execute([$userId, $subject, $message, $messageType, $priority, $categoryId]);
            
            if (!$result) {
                throw new Exception('فشل في إنشاء الرسالة');
            }
            
            $messageId = $this->db->lastInsertId();
            
            // Create notifications for all active admins
            $this->createAdminNotifications($messageId, 'new_message');
            
            // Log activity
            $userManager = new UserManager();
            $userManager->logActivity($userId, 'message_sent', "تم إرسال رسالة جديدة: {$subject}", [
                'message_id' => $messageId,
                'subject' => $subject,
                'message_type' => $messageType,
                'priority' => $priority
            ]);
            
            $this->db->commit();
            return $messageId;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("Error creating message: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create admin reply to a message
     */
    public function createReply($messageId, $adminId, $replyText, $isPublic = false) {
        try {
            $this->db->beginTransaction();
            
            // Insert reply
            $stmt = $this->db->prepare("
                INSERT INTO admin_replies (message_id, admin_id, reply_text, is_public)
                VALUES (?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([$messageId, $adminId, $replyText, $isPublic ? 1 : 0]);
            
            if (!$result) {
                throw new Exception('فشل في إنشاء الرد');
            }

            $replyId = $this->db->lastInsertId();
            
            // Update message status
            $updateStmt = $this->db->prepare("
                UPDATE student_messages
                SET status = 'replied', updated_at = NOW()
                WHERE id = ?
            ");
            $updateStmt->execute([$messageId]);
            
            // Get student info for notification
            $studentStmt = $this->db->prepare("SELECT user_id FROM student_messages WHERE id = ?");
            $studentStmt->execute([$messageId]);
            $student = $studentStmt->fetch();
            
            if ($student) {
                // Create notification for student
                $notificationStmt = $this->db->prepare("
                    INSERT INTO message_notifications (message_id, recipient_type, recipient_id, notification_type)
                    VALUES (?, 'student', ?, 'new_reply')
                ");
                $notificationStmt->execute([$messageId, $student['user_id']]);
                
                // Create system notification for student
                $userManager = new UserManager();
                $userManager->createNotification(
                    $student['user_id'],
                    'رد جديد على رسالتك',
                    'تم الرد على رسالتك من قبل الإدارة. يمكنك مراجعة الرد في صفحة "اسأل معلم".',
                    'info',
                    false,
                    $adminId
                );
            }
            
            $this->db->commit();
            return $replyId;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("Error creating reply: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Update message status
     */
    public function updateMessageStatus($messageId, $newStatus) {
        try {
            $stmt = $this->db->prepare("UPDATE student_messages SET status = ? WHERE id = ?");
            return $stmt->execute([$newStatus, $messageId]);
        } catch (Exception $e) {
            error_log("Error updating message status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get messages with filters
     */
    public function getMessages($filters = [], $page = 1, $perPage = 20) {
        try {
            $where_conditions = ['1=1'];
            $params = [];
            
            if (!empty($filters['status'])) {
                $where_conditions[] = "sm.status = ?";
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['category'])) {
                $where_conditions[] = "sm.category_id = ?";
                $params[] = $filters['category'];
            }
            
            if (!empty($filters['priority'])) {
                $where_conditions[] = "sm.priority = ?";
                $params[] = $filters['priority'];
            }
            
            if (!empty($filters['search'])) {
                $where_conditions[] = "(sm.subject LIKE ? OR sm.message LIKE ? OR u.first_name LIKE ? OR u.username LIKE ?)";
                $search_term = "%{$filters['search']}%";
                $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
            }
            
            $where_clause = implode(' AND ', $where_conditions);
            $offset = ($page - 1) * $perPage;
            
            // Get total count
            $count_query = "
                SELECT COUNT(*) as total
                FROM student_messages sm
                LEFT JOIN users u ON sm.user_id = u.id
                WHERE {$where_clause}
            ";
            $count_stmt = $this->db->prepare($count_query);
            $count_stmt->execute($params);
            $total = $count_stmt->fetch()['total'];
            
            // Get messages
            $query = "
                SELECT sm.*, 
                       u.username, u.first_name, u.second_name, u.third_name, u.fourth_name,
                       mc.name_ar as category_name, mc.icon as category_icon,
                       (SELECT COUNT(*) FROM admin_replies ar WHERE ar.message_id = sm.id) as reply_count
                FROM student_messages sm
                LEFT JOIN users u ON sm.user_id = u.id
                LEFT JOIN message_categories mc ON sm.category_id = mc.id
                WHERE {$where_clause}
                ORDER BY sm.created_at DESC
                LIMIT {$perPage} OFFSET {$offset}
            ";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $messages = $stmt->fetchAll();
            
            return [
                'messages' => $messages,
                'total' => $total,
                'pages' => ceil($total / $perPage),
                'current_page' => $page
            ];
            
        } catch (Exception $e) {
            error_log("Error getting messages: " . $e->getMessage());
            return [
                'messages' => [],
                'total' => 0,
                'pages' => 0,
                'current_page' => 1
            ];
        }
    }
    
    /**
     * Get message by ID with replies
     */
    public function getMessageWithReplies($messageId) {
        try {
            // Get message
            $stmt = $this->db->prepare("
                SELECT sm.*, 
                       u.username, u.first_name, u.second_name, u.third_name, u.fourth_name,
                       mc.name_ar as category_name, mc.icon as category_icon
                FROM student_messages sm
                LEFT JOIN users u ON sm.user_id = u.id
                LEFT JOIN message_categories mc ON sm.category_id = mc.id
                WHERE sm.id = ?
            ");
            $stmt->execute([$messageId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                return null;
            }
            
            // Get replies
            $replies_stmt = $this->db->prepare("
                SELECT ar.*, a.full_name as admin_name
                FROM admin_replies ar
                JOIN admins a ON ar.admin_id = a.id
                WHERE ar.message_id = ?
                ORDER BY ar.created_at ASC
            ");
            $replies_stmt->execute([$messageId]);
            $message['replies'] = $replies_stmt->fetchAll();
            
            return $message;
            
        } catch (Exception $e) {
            error_log("Error getting message with replies: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get user messages with pagination
     */
    public function getUserMessages($userId, $page = 1, $perPage = 10) {
        try {
            $offset = ($page - 1) * $perPage;

            // Debug: Log the user ID being searched
            error_log("getUserMessages called with userId: $userId, page: $page, perPage: $perPage");

            // Get total count with simpler query
            $countStmt = $this->db->prepare("SELECT COUNT(*) FROM student_messages WHERE user_id = ?");
            $countStmt->execute([$userId]);
            $total = $countStmt->fetchColumn();

            error_log("Total messages found: $total");

            // If no messages, return early
            if ($total == 0) {
                return [
                    'messages' => [],
                    'total' => 0,
                    'pages' => 0,
                    'current_page' => 1
                ];
            }

            // Get messages with simpler query first
            $stmt = $this->db->prepare("
                SELECT sm.*,
                       COALESCE(mc.name_ar, 'غير محدد') as category_name,
                       COALESCE(mc.icon, '📝') as category_icon,
                       0 as reply_count
                FROM student_messages sm
                LEFT JOIN message_categories mc ON sm.category_id = mc.id
                WHERE sm.user_id = ?
                ORDER BY sm.created_at DESC
                LIMIT ? OFFSET ?
            ");

            // Bind parameters explicitly
            $stmt->bindValue(1, $userId, PDO::PARAM_INT);
            $stmt->bindValue(2, $perPage, PDO::PARAM_INT);
            $stmt->bindValue(3, $offset, PDO::PARAM_INT);

            $stmt->execute();
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

            error_log("Messages retrieved: " . count($messages));

            // Add reply count separately to avoid complex subquery issues
            foreach ($messages as &$message) {
                $replyStmt = $this->db->prepare("SELECT COUNT(*) FROM admin_replies WHERE message_id = ?");
                $replyStmt->execute([$message['id']]);
                $message['reply_count'] = $replyStmt->fetchColumn();
            }

            return [
                'messages' => $messages,
                'total' => (int)$total,
                'pages' => ceil($total / $perPage),
                'current_page' => $page
            ];

        } catch (Exception $e) {
            error_log("Error getting user messages: " . $e->getMessage());
            error_log("Error trace: " . $e->getTraceAsString());
            return [
                'messages' => [],
                'total' => 0,
                'pages' => 0,
                'current_page' => 1
            ];
        }
    }

    /**
     * Get replies for a specific message
     */
    public function getMessageReplies($messageId) {
        try {
            $stmt = $this->db->prepare("
                SELECT ar.*,
                       COALESCE(a.full_name, CONCAT('مدير ', ar.admin_id)) as admin_name
                FROM admin_replies ar
                LEFT JOIN admins a ON ar.admin_id = a.id
                WHERE ar.message_id = ? AND ar.is_public = 1
                ORDER BY ar.created_at ASC
            ");
            $stmt->execute([$messageId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting message replies: " . $e->getMessage());
            // Try simpler query without admin join
            try {
                $stmt = $this->db->prepare("
                    SELECT ar.*,
                           CONCAT('مدير ', ar.admin_id) as admin_name
                    FROM admin_replies ar
                    WHERE ar.message_id = ? AND ar.is_public = 1
                    ORDER BY ar.created_at ASC
                ");
                $stmt->execute([$messageId]);
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (Exception $e2) {
                error_log("Error with simple query: " . $e2->getMessage());
                return [];
            }
        }
    }

    /**
     * Get message categories
     */
    public function getCategories() {
        try {
            $stmt = $this->db->prepare("SELECT * FROM message_categories WHERE is_active = 1 ORDER BY sort_order");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting categories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get message templates
     */
    public function getTemplates($categoryId = null) {
        try {
            $sql = "SELECT * FROM message_templates WHERE is_active = 1";
            $params = [];
            
            if ($categoryId) {
                $sql .= " AND (category_id = ? OR category_id IS NULL)";
                $params[] = $categoryId;
            }
            
            $sql .= " ORDER BY title";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting templates: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get message statistics
     */
    public function getStatistics() {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_count,
                    SUM(CASE WHEN status = 'replied' THEN 1 ELSE 0 END) as replied,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed,
                    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as today,
                    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as this_week
                FROM student_messages
            ");
            $stmt->execute();
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting statistics: " . $e->getMessage());
            return [
                'total' => 0,
                'pending' => 0,
                'read_count' => 0,
                'replied' => 0,
                'closed' => 0,
                'today' => 0,
                'this_week' => 0
            ];
        }
    }
    
    /**
     * Create notifications for all active admins
     */
    private function createAdminNotifications($messageId, $notificationType) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO message_notifications (message_id, recipient_type, recipient_id, notification_type)
                SELECT ?, 'admin', id, ? FROM admins WHERE is_active = 1
            ");
            return $stmt->execute([$messageId, $notificationType]);
        } catch (Exception $e) {
            error_log("Error creating admin notifications: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark message notifications as read for admin
     */
    public function markNotificationsAsRead($adminId, $messageId = null) {
        try {
            $sql = "UPDATE message_notifications SET is_read = 1, read_at = NOW() WHERE recipient_type = 'admin' AND recipient_id = ?";
            $params = [$adminId];
            
            if ($messageId) {
                $sql .= " AND message_id = ?";
                $params[] = $messageId;
            }
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($params);
        } catch (Exception $e) {
            error_log("Error marking notifications as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get unread message count for admin
     */
    public function getUnreadMessageCount($adminId = null) {
        try {
            if ($adminId) {
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) as count 
                    FROM message_notifications 
                    WHERE recipient_type = 'admin' AND recipient_id = ? AND is_read = 0
                ");
                $stmt->execute([$adminId]);
            } else {
                $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM student_messages WHERE status = 'pending'");
                $stmt->execute();
            }
            
            return $stmt->fetch()['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting unread message count: " . $e->getMessage());
            return 0;
        }
    }
}
?>
