<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get subscribers with their subscription details
    $stmt = $db->query("SELECT u.id, u.username, u.email, u.phone, u.grade, u.created_at,
                       u.subscription_status, u.subscription_end_date,
                       sp.name as plan_name, sp.icon as plan_icon, sp.color as plan_color,
                       us.start_date, us.payment_method, us.amount_paid,
                       CASE
                           WHEN u.subscription_end_date IS NULL THEN NULL
                           ELSE DATEDIFF(u.subscription_end_date, NOW())
                       END as days_left
                       FROM users u
                       LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                       LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.end_date = u.subscription_end_date
                       ORDER BY u.subscription_status DESC, u.subscription_end_date ASC");
    $subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get statistics
    $stats = [
        'total_subscribers' => 0,
        'active_subscribers' => 0,
        'expired_subscribers' => 0,
        'expiring_soon' => 0,
        'total_revenue' => 0,
        'monthly_revenue' => 0
    ];

    // Total subscribers
    $stmt = $db->query("SELECT COUNT(*) as total FROM users WHERE subscription_status != 'none'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_subscribers'] = $result ? $result['total'] : 0;

    // Active subscribers
    $stmt = $db->query("SELECT COUNT(*) as active FROM users WHERE subscription_status = 'active' AND subscription_end_date > NOW()");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['active_subscribers'] = $result ? $result['active'] : 0;

    // Expired subscribers
    $stmt = $db->query("SELECT COUNT(*) as expired FROM users WHERE subscription_status = 'active' AND subscription_end_date <= NOW()");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['expired_subscribers'] = $result ? $result['expired'] : 0;

    // Expiring soon (within 7 days)
    $stmt = $db->query("SELECT COUNT(*) as expiring FROM users WHERE subscription_status = 'active' AND subscription_end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['expiring_soon'] = $result ? $result['expiring'] : 0;

    // Total revenue
    $stmt = $db->query("SELECT SUM(amount_paid) as revenue FROM user_subscriptions WHERE payment_status = 'completed'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_revenue'] = $result && $result['revenue'] ? $result['revenue'] : 0;

    // Revenue this month
    $stmt = $db->query("SELECT SUM(amount_paid) as revenue FROM user_subscriptions WHERE payment_status = 'completed' AND MONTH(start_date) = MONTH(NOW()) AND YEAR(start_date) = YEAR(NOW())");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['monthly_revenue'] = $result && $result['revenue'] ? $result['revenue'] : 0;
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $subscribers = [];
    $stats = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتركين - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" >
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="admin-container">
                <div class="page-header">
                    <h1>إدارة المشتركين</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openModal('addModal')" onclick="exportSubscribers()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                        <button class="btn btn-success" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card total">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي المشتركين</h3>
                            <span class="stat-number"><?php echo number_format($stats['total_subscribers']); ?></span>
                            <span class="stat-label">مشترك</span>
                        </div>
                    </div>
                    
                    <div class="stat-card active">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-info">
                            <h3>اشتراكات نشطة</h3>
                            <span class="stat-number"><?php echo number_format($stats['active_subscribers']); ?></span>
                            <span class="stat-label">مشترك نشط</span>
                        </div>
                    </div>
                    
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>تنتهي قريباً</h3>
                            <span class="stat-number"><?php echo number_format($stats['expiring_soon']); ?></span>
                            <span class="stat-label">خلال 7 أيام</span>
                        </div>
                    </div>
                    
                    <div class="stat-card expired">
                        <div class="stat-icon">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-info">
                            <h3>اشتراكات منتهية</h3>
                            <span class="stat-number"><?php echo number_format($stats['expired_subscribers']); ?></span>
                            <span class="stat-label">مشترك منتهي</span>
                        </div>
                    </div>
                    
                    <div class="stat-card revenue">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إجمالي الإيرادات</h3>
                            <span class="stat-number"><?php echo number_format($stats['total_revenue'], 0); ?></span>
                            <span class="stat-label">جنيه</span>
                        </div>
                    </div>
                    
                    <div class="stat-card monthly">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>إيرادات الشهر</h3>
                            <span class="stat-number"><?php echo number_format($stats['monthly_revenue'], 0); ?></span>
                            <span class="stat-label">جنيه</span>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters-section">
                    <div class="filter-group">
                        <label for="statusFilter">حالة الاشتراك:</label>
                        <select id="statusFilter" onchange="filterSubscribers()">
                            <option value="all">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="expired">منتهي</option>
                            <option value="cancelled">ملغي</option>
                            <option value="none">غير مشترك</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="planFilter">الخطة:</label>
                        <select id="planFilter" onchange="filterSubscribers()">
                            <option value="all">جميع الخطط</option>
                            <?php
                            $stmt = $db->query("SELECT DISTINCT sp.name FROM subscription_plans sp JOIN users u ON sp.id = u.current_plan_id WHERE u.current_plan_id IS NOT NULL");
                            $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            foreach ($plans as $plan):
                            ?>
                                <option value="<?php echo htmlspecialchars($plan['name']); ?>"><?php echo htmlspecialchars($plan['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="searchInput">البحث:</label>
                        <input type="text" id="searchInput" placeholder="اسم المستخدم أو البريد الإلكتروني" onkeyup="filterSubscribers()">
                    </div>
                </div>

                <!-- Subscribers Table -->
                <div class="table-container">
                    <table class="admin-table" id="subscribersTable">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>الخطة</th>
                                <th>حالة الاشتراك</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الأيام المتبقية</th>
                                <th>طريقة الدفع</th>
                                <th>المبلغ المدفوع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subscribers as $subscriber): ?>
                                <?php
                                $status_class = '';
                                $status_text = '';
                                $days_left = $subscriber['days_left'];
                                
                                if ($subscriber['subscription_status'] === 'active' && $days_left > 0) {
                                    $status_class = 'active';
                                    $status_text = 'نشط';
                                } elseif ($subscriber['subscription_status'] === 'active' && $days_left <= 0) {
                                    $status_class = 'expired';
                                    $status_text = 'منتهي';
                                } elseif ($subscriber['subscription_status'] === 'cancelled') {
                                    $status_class = 'cancelled';
                                    $status_text = 'ملغي';
                                } else {
                                    $status_class = 'none';
                                    $status_text = 'غير مشترك';
                                }
                                
                                if ($days_left > 0 && $days_left <= 7) {
                                    $status_class .= ' warning';
                                }
                                ?>
                                <tr class="subscriber-row" data-status="<?php echo $subscriber['subscription_status']; ?>" data-plan="<?php echo htmlspecialchars($subscriber['plan_name'] ?: ''); ?>" data-search="<?php echo htmlspecialchars(strtolower($subscriber['username'] . ' ' . $subscriber['email'])); ?>">
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="user-details">
                                                <strong><?php echo htmlspecialchars($subscriber['username']); ?></strong>
                                                <span class="user-email"><?php echo htmlspecialchars($subscriber['email']); ?></span>
                                                <span class="user-grade">الصف: <?php echo htmlspecialchars($subscriber['grade']); ?></span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($subscriber['plan_name']): ?>
                                            <div class="plan-info">
                                                <span class="plan-icon" style="color: <?php echo $subscriber['plan_color']; ?>">
                                                    <?php echo $subscriber['plan_icon']; ?>
                                                </span>
                                                <?php echo htmlspecialchars($subscriber['plan_name']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($subscriber['start_date']): ?>
                                            <?php echo date('Y-m-d', strtotime($subscriber['start_date'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($subscriber['subscription_end_date']): ?>
                                            <?php echo date('Y-m-d', strtotime($subscriber['subscription_end_date'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($days_left !== null): ?>
                                            <?php if ($days_left > 0): ?>
                                                <span class="days-left <?php echo $days_left <= 7 ? 'warning' : ''; ?>">
                                                    <?php echo $days_left; ?> يوم
                                                </span>
                                            <?php else: ?>
                                                <span class="days-left expired">منتهي</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($subscriber['payment_method']): ?>
                                            <?php
                                            $payment_methods = [
                                                'code' => 'كود السنتر',
                                                'fawry' => 'فوري',
                                                'visa' => 'فيزا',
                                                'wallet' => 'محفظة إلكترونية'
                                            ];
                                            echo $payment_methods[$subscriber['payment_method']] ?? $subscriber['payment_method'];
                                            ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($subscriber['amount_paid']): ?>
                                            <?php echo number_format($subscriber['amount_paid'], 0); ?> جنيه
                                        <?php else: ?>
                                            <span class="text-muted">مجاني</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" onclick="viewUserDetails(<?php echo $subscriber['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </button>
                                            <?php if ($subscriber['subscription_status'] === 'active'): ?>
                                                <button class="btn btn-sm btn-warning" onclick="extendSubscription(<?php echo $subscriber['id']; ?>)">
                                                    <i class="fas fa-clock"></i>
                                                    تمديد
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function filterSubscribers() {
            const statusFilter = document.getElementById('statusFilter').value;
            const planFilter = document.getElementById('planFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('.subscriber-row');
            
            rows.forEach(row => {
                const status = row.dataset.status;
                const plan = row.dataset.plan;
                const searchText = row.dataset.search;
                
                const statusMatch = statusFilter === 'all' || status === statusFilter;
                const planMatch = planFilter === 'all' || plan === planFilter;
                const searchMatch = !searchInput || searchText.includes(searchInput);
                
                if (statusMatch && planMatch && searchMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function viewUserDetails(userId) {
            // Open user details modal or page
            window.open(`user_details.php?id=${userId}`, '_blank');
        }

        function extendSubscription(userId) {
            const days = prompt('كم يوم تريد إضافتها للاشتراك؟', '30');
            if (days && !isNaN(days) && days > 0) {
                if (confirm(`هل أنت متأكد من إضافة ${days} يوم للاشتراك؟`)) {
                    // Send AJAX request to extend subscription
                    fetch('extend_subscription.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user_id: userId,
                            days: parseInt(days)
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم تمديد الاشتراك بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('حدث خطأ في الاتصال');
                    });
                }
            }
        }

        function exportSubscribers() {
            window.location.href = 'export_subscribers.php';
        }

        function refreshData() {
            location.reload();
        }
    </script>

    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-card.total .stat-icon {
            background: linear-gradient(135deg, #4682B4, #20B2AA);
        }

        .stat-card.active .stat-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .stat-card.warning .stat-icon {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .stat-card.expired .stat-icon {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .stat-card.revenue .stat-icon {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .stat-card.monthly .stat-icon {
            background: linear-gradient(135deg, #6f42c1, #5a2d91);
        }

        .stat-info h3 {
            color: #333;
            margin: 0 0 5px 0;
            font-size: 14px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            display: block;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }

        .filters-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            min-width: 150px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4682B4, #20B2AA);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .user-email {
            color: #666;
            font-size: 12px;
        }

        .user-grade {
            color: #888;
            font-size: 11px;
        }

        .plan-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plan-icon {
            font-size: 1.2rem;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.expired {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.none {
            background: #e2e3e5;
            color: #6c757d;
        }

        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }

        .days-left {
            font-weight: bold;
        }

        .days-left.warning {
            color: #856404;
        }

        .days-left.expired {
            color: #dc3545;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .text-muted {
            color: #6c757d;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filters-section {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                width: 100%;
            }

            .filter-group select,
            .filter-group input {
                min-width: auto;
                width: 100%;
            }

            .header-actions {
                flex-direction: column;
            }

            .user-info {
                flex-direction: column;
                text-align: center;
            }

            .action-buttons {
                justify-content: center;
            }
        }
    </style>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
