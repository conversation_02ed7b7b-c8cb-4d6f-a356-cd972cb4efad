/* Admin Layout */
.admin-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.admin-layout {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 70px 1fr;
    min-height: 100vh;
}



/* Admin Header */
.admin-header {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-header .admin-logo h2 {
    color: white;
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.admin-header .admin-user {
    color: white;
    font-weight: 500;
}

.admin-header .admin-user span {
    margin-left: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

/* Admin Navigation */
.admin-nav {
    background: white;
    border-bottom: 1px solid rgba(70, 130, 180, 0.1);
    padding: 0 30px;
    box-shadow: 0 2px 8px rgba(70, 130, 180, 0.1);
}

.admin-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 0;
    overflow-x: auto;
}

.admin-nav li {
    margin: 0;
    flex-shrink: 0;
}

.admin-nav a {
    display: block;
    padding: 15px 25px;
    color: #6c757d;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.admin-nav a:hover {
    color: #4682B4;
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(70, 130, 180, 0.05) 100%);
    transform: translateY(-2px);
}

.admin-nav a.active {
    color: #4682B4;
    border-bottom-color: #4682B4;
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.15) 0%, rgba(70, 130, 180, 0.1) 100%);
    font-weight: 600;
}

/* Admin Main Content */
.admin-main {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.admin-content {
    background: transparent;
}

/* Logout Button */
.logout-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white !important;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Enhanced Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
    border: 2px solid rgba(70, 130, 180, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 20px 20px 0 0;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(70, 130, 180, 0.2);
    border-color: rgba(135, 206, 235, 0.3);
}

.stat-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stat-info h3 {
    font-size: 36px;
    font-weight: 800;
    color: #4682B4;
    margin: 0 0 8px 0;
    text-shadow: 2px 2px 4px rgba(70, 130, 180, 0.1);
}

.stat-info p {
    color: #6c757d;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 15px;
    text-decoration: none;
    color: inherit;
    transition: all 0.4s ease;
    border: 2px solid rgba(70, 130, 180, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.2), transparent);
    transition: left 0.6s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-8px);
    border-color: #87CEEB;
    box-shadow: 0 20px 45px rgba(70, 130, 180, 0.25);
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.action-icon {
    font-size: 48px;
    opacity: 0.8;
}

.action-btn span:last-child {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

/* Admin Section Styling */
.admin-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.1);
    border: 2px solid rgba(70, 130, 180, 0.1);
}

.admin-section h2 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .admin-header {
        padding: 0 15px;
        height: 60px;
    }

    .admin-header .admin-logo h2 {
        font-size: 16px;
    }

    .admin-nav {
        padding: 0 15px;
    }

    .admin-nav ul {
        gap: 0;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .admin-nav ul::-webkit-scrollbar {
        display: none;
    }

    .admin-nav a {
        padding: 12px 20px;
        font-size: 14px;
    }

    .admin-main {
        padding: 20px 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }

    .stat-card {
        padding: 20px;
        border-radius: 15px;
    }

    .stat-icon {
        font-size: 36px;
    }

    .stat-info h3 {
        font-size: 28px;
    }

    .stat-info p {
        font-size: 14px;
    }

    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .action-btn {
        padding: 20px;
        border-radius: 12px;
        gap: 12px;
    }

    .action-icon {
        font-size: 36px;
    }

    .action-btn span:last-child {
        font-size: 14px;
    }

    .admin-section {
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 20px;
    }

    .admin-section h2 {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .logout-btn {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 20px;
    }
}

@media (max-width: 480px) {
    .admin-header {
        padding: 0 10px;
        height: 50px;
    }

    .admin-header .admin-logo h2 {
        font-size: 14px;
    }

    .admin-header .admin-user {
        font-size: 12px;
    }

    .admin-nav {
        padding: 0 10px;
    }

    .admin-nav a {
        padding: 10px 15px;
        font-size: 12px;
    }

    .admin-main {
        padding: 15px 10px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 25px;
    }

    .stat-card {
        padding: 15px;
        border-radius: 12px;
    }

    .stat-icon {
        font-size: 32px;
    }

    .stat-info h3 {
        font-size: 24px;
    }

    .stat-info p {
        font-size: 12px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .action-btn {
        padding: 15px;
        border-radius: 10px;
        gap: 10px;
    }

    .action-icon {
        font-size: 32px;
    }

    .action-btn span:last-child {
        font-size: 13px;
    }

    .admin-section {
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 15px;
    }

    .admin-section h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .logout-btn {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 15px;
    }
}

/* Very Small Mobile (320px) */
@media (max-width: 320px) {
    .admin-header {
        flex-direction: column;
        height: auto;
        padding: 10px;
        gap: 10px;
    }

    .admin-header .admin-header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .admin-nav a {
        padding: 8px 12px;
        font-size: 11px;
    }

    .admin-main {
        padding: 10px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-icon {
        font-size: 28px;
    }

    .stat-info h3 {
        font-size: 20px;
    }

    .action-btn {
        padding: 12px;
        gap: 8px;
    }

    .action-icon {
        font-size: 28px;
    }

    .action-btn span:last-child {
        font-size: 12px;
    }

    .admin-section {
        padding: 12px;
    }

    .admin-section h2 {
        font-size: 16px;
        margin-bottom: 12px;
    }
}

.logout-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.admin-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.admin-logo h2 {
    color: #333;
    font-size: 18px;
    margin: 0;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #666;
}

.admin-logout {
    color: #dc3545;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.admin-logout:hover {
    background: #f8f9fa;
}

/* Admin Sidebar */
.admin-sidebar {
    grid-area: sidebar;
    background: #f8f9fa;
    border-left: 1px solid #e9ecef;
    padding: 20px 0;
}

.admin-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.admin-nav li {
    margin-bottom: 5px;
}

.admin-nav a {
    display: block;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.admin-nav a:hover {
    background: white;
    color: #4682B4;
}

.admin-nav a.active {
    background: white;
    color: #4682B4;
    border-right-color: #4682B4;
    font-weight: 500;
}

/* Admin Main Content */
.admin-main {
    grid-area: main;
    padding: 30px;
    background: #f8f9fa;
    overflow-y: auto;
}

.admin-content h1 {
    color: #333;
    margin-bottom: 30px;
    font-size: 28px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 40px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 50%;
}

.stat-info h3 {
    font-size: 24px;
    color: #333;
    margin: 0 0 5px;
}

.stat-info p {
    color: #666;
    margin: 0;
    font-size: 14px;
}

/* Admin Sections */
.admin-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.admin-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.text-center {
    text-align: center;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.action-btn:hover {
    border-color: #4682B4;
    color: #4682B4;
    transform: translateY(-2px);
}

.action-icon {
    font-size: 20px;
}

/* Forms */
.admin-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #4682B4;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Alerts */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Responsive Design */
@media (max-width: 1920px) {
    .admin-layout {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 70px 1fr;
    }
    
    .admin-sidebar {
        display: none;
    }
    
    .admin-main {
        padding: 20px 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
