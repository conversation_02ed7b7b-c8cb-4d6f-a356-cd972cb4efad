<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/MessageManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$messageManager = new MessageManager();

// Get user info directly from database (in case UserManager doesn't exist)
try {
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();

    if (!$user) {
        echo "خطأ: المستخدم غير موجود";
        exit;
    }
} catch (Exception $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
    exit;
}

// Get pagination parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = 10;

// Get user messages
try {
    $messagesData = $messageManager->getUserMessages($_SESSION['user_id'], $page, $perPage);
    $messages = $messagesData['messages'];
    $totalPages = $messagesData['pages'];
    $totalMessages = $messagesData['total'];
} catch (Exception $e) {
    echo "خطأ في جلب الرسائل: " . $e->getMessage();
    $messages = [];
    $totalPages = 0;
    $totalMessages = 0;
}

// Get specific message details if ID is provided
$selectedMessage = null;
$messageReplies = [];
if (isset($_GET['id'])) {
    $messageId = (int)$_GET['id'];
    foreach ($messages as $msg) {
        if ($msg['id'] == $messageId) {
            $selectedMessage = $msg;
            // Get replies for this message
            $messageReplies = $messageManager->getMessageReplies($messageId);
            break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رسائلي - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-layout">
        <?php include __DIR__ . '/../includes/header.php'; ?>
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            <div class="page-container">
                <div class="page-header">
                    <h1><i class="fas fa-inbox"></i> رسائلي</h1>
                    <p>جميع الرسائل التي أرسلتها والردود عليها</p>
                </div>

                <div class="messages-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $totalMessages; ?></h3>
                            <p>إجمالي الرسائل</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo count(array_filter($messages, function($m) { return $m['status'] === 'pending'; })); ?></h3>
                            <p>في الانتظار</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-reply"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo count(array_filter($messages, function($m) { return $m['status'] === 'replied'; })); ?></h3>
                            <p>تم الرد</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="stat-info">
                            <a href="../page/ask_teacher.php" class="new-message-btn">
                                <i class="fas fa-plus"></i>
                                رسالة جديدة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (empty($messages)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h3>لا توجد رسائل</h3>
                        <p>لم ترسل أي رسائل بعد</p>
                        <a href="../page/ask_teacher.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إرسال رسالة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="messages-container">
                        <div class="messages-list">
                            <?php foreach ($messages as $message): ?>
                                <div class="message-item <?php echo $selectedMessage && $selectedMessage['id'] == $message['id'] ? 'active' : ''; ?>">
                                    <a href="?id=<?php echo $message['id']; ?>" class="message-link">
                                        <div class="message-header">
                                            <div class="message-subject">
                                                <?php echo htmlspecialchars($message['subject']); ?>
                                            </div>
                                            <div class="message-status status-<?php echo $message['status']; ?>">
                                                <?php 
                                                $statusLabels = [
                                                    'pending' => 'في الانتظار',
                                                    'read' => 'تم القراءة',
                                                    'replied' => 'تم الرد',
                                                    'closed' => 'مغلقة'
                                                ];
                                                echo $statusLabels[$message['status']] ?? $message['status'];
                                                ?>
                                            </div>
                                        </div>
                                        
                                        <div class="message-meta">
                                            <span class="message-date">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo date('Y/m/d H:i', strtotime($message['created_at'])); ?>
                                            </span>
                                            <span class="message-priority priority-<?php echo $message['priority']; ?>">
                                                <i class="fas fa-flag"></i>
                                                <?php 
                                                $priorityLabels = [
                                                    'low' => 'منخفضة',
                                                    'medium' => 'متوسطة',
                                                    'high' => 'عالية',
                                                    'urgent' => 'عاجلة'
                                                ];
                                                echo $priorityLabels[$message['priority']] ?? $message['priority'];
                                                ?>
                                            </span>
                                            <?php if ($message['reply_count'] > 0): ?>
                                                <span class="reply-count">
                                                    <i class="fas fa-reply"></i>
                                                    <?php echo $message['reply_count']; ?> رد
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="message-preview">
                                            <?php echo htmlspecialchars(mb_substr($message['message'], 0, 100)) . (mb_strlen($message['message']) > 100 ? '...' : ''); ?>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if ($selectedMessage): ?>
                            <div class="message-details">
                                <div class="message-detail-header">
                                    <h2><?php echo htmlspecialchars($selectedMessage['subject']); ?></h2>
                                    <div class="message-detail-meta">
                                        <span class="message-status status-<?php echo $selectedMessage['status']; ?>">
                                            <?php 
                                            $statusLabels = [
                                                'pending' => 'في الانتظار',
                                                'read' => 'تم القراءة',
                                                'replied' => 'تم الرد',
                                                'closed' => 'مغلقة'
                                            ];
                                            echo $statusLabels[$selectedMessage['status']] ?? $selectedMessage['status'];
                                            ?>
                                        </span>
                                        <span class="message-date">
                                            <i class="fas fa-calendar"></i>
                                            <?php echo date('Y/m/d H:i', strtotime($selectedMessage['created_at'])); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="message-content">
                                    <div class="original-message">
                                        <h4><i class="fas fa-user"></i> رسالتك:</h4>
                                        <div class="message-text">
                                            <?php echo nl2br(htmlspecialchars($selectedMessage['message'])); ?>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($messageReplies)): ?>
                                        <div class="replies-section">
                                            <h4><i class="fas fa-reply"></i> الردود:</h4>
                                            <?php foreach ($messageReplies as $reply): ?>
                                                <div class="reply-item">
                                                    <div class="reply-header">
                                                        <span class="reply-author">
                                                            <i class="fas fa-user-tie"></i>
                                                            المعلم
                                                        </span>
                                                        <span class="reply-date">
                                                            <?php echo date('Y/m/d H:i', strtotime($reply['created_at'])); ?>
                                                        </span>
                                                    </div>
                                                    <div class="reply-text">
                                                        <?php echo nl2br(htmlspecialchars($reply['reply_text'])); ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-replies">
                                            <i class="fas fa-clock"></i>
                                            <p>لم يتم الرد على هذه الرسالة بعد</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>" class="pagination-btn">
                                    <i class="fas fa-chevron-right"></i>
                                    السابق
                                </a>
                            <?php endif; ?>
                            
                            <span class="pagination-info">
                                صفحة <?php echo $page; ?> من <?php echo $totalPages; ?>
                            </span>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>" class="pagination-btn">
                                    التالي
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            border-radius: 20px;
            color: white;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.3);
        }
        
        .page-header h1 {
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .page-header p {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .messages-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .stat-info h3 {
            margin: 0 0 5px 0;
            font-size: 2em;
            color: #333;
            font-weight: 700;
        }
        
        .stat-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .new-message-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .new-message-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .empty-icon {
            font-size: 80px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .empty-state h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.8em;
        }
        
        .empty-state p {
            color: #666;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(70, 130, 180, 0.3);
        }

        .messages-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .messages-list {
            background: #f8f9fa;
            padding: 0;
            max-height: 600px;
            overflow-y: auto;
        }

        .message-item {
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .message-item:hover {
            background: #e9ecef;
        }

        .message-item.active {
            background: #4682B4;
            color: white;
        }

        .message-item.active .message-subject,
        .message-item.active .message-meta,
        .message-item.active .message-preview {
            color: white;
        }

        .message-link {
            display: block;
            padding: 20px;
            text-decoration: none;
            color: inherit;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .message-subject {
            font-weight: 700;
            font-size: 16px;
            color: #333;
            flex: 1;
            margin-left: 15px;
        }

        .message-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #ffc107; color: #856404; }
        .status-read { background: #17a2b8; color: white; }
        .status-replied { background: #28a745; color: white; }
        .status-closed { background: #6c757d; color: white; }

        .message-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 12px;
            color: #666;
        }

        .message-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .priority-low { color: #28a745; }
        .priority-medium { color: #ffc107; }
        .priority-high { color: #fd7e14; }
        .priority-urgent { color: #dc3545; }

        .reply-count {
            color: #4682B4;
            font-weight: 600;
        }

        .message-preview {
            color: #666;
            line-height: 1.4;
            font-size: 14px;
        }

        .message-details {
            padding: 30px;
        }

        .message-detail-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .message-detail-header h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.8em;
            font-weight: 700;
        }

        .message-detail-meta {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .original-message {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-right: 4px solid #4682B4;
        }

        .original-message h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .message-text {
            color: #555;
            line-height: 1.6;
            font-size: 16px;
        }

        .replies-section h4 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: 600;
        }

        .reply-item {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;
            border-right: 4px solid #28a745;
        }

        .reply-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .reply-author {
            font-weight: 600;
            color: #28a745;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reply-date {
            color: #666;
            font-size: 14px;
        }

        .reply-text {
            color: #333;
            line-height: 1.6;
            font-size: 15px;
        }

        .no-replies {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-replies i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .pagination-btn {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .pagination-info {
            color: #666;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .messages-container {
                grid-template-columns: 1fr;
            }

            .message-details {
                border-top: 2px solid #f8f9fa;
            }
        }

        @media (max-width: 768px) {
            .page-container {
                padding: 15px;
            }

            .page-header {
                padding: 20px;
            }

            .page-header h1 {
                font-size: 2em;
            }

            .messages-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .stat-card {
                padding: 20px;
                flex-direction: column;
                text-align: center;
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .message-link {
                padding: 15px;
            }

            .message-details {
                padding: 20px;
            }

            .message-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .message-meta {
                flex-wrap: wrap;
                gap: 10px;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }
    </script>
</body>
</html>
