<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Get lesson ID
$lesson_id = isset($_GET['lesson_id']) ? (int)$_GET['lesson_id'] : 0;

if (!$lesson_id) {
    header('Location: lessons.php');
    exit;
}

// Get lesson info
$stmt = $db->prepare("
    SELECT l.*, cs.name as subject_name, cs.color 
    FROM lessons l 
    JOIN curriculum_subjects cs ON l.subject_id = cs.id 
    WHERE l.id = ?
");
$stmt->execute([$lesson_id]);
$lesson = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$lesson) {
    header('Location: lessons.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_video':
                $title = $_POST['title'];
                $description = $_POST['description'];
                $youtube_url = $_POST['youtube_url'];
                $video_order = $_POST['video_order'];
                
                // Extract YouTube ID from URL
                $youtube_id = '';
                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $youtube_url, $matches)) {
                    $youtube_id = $matches[1];
                }
                
                $stmt = $db->prepare("INSERT INTO lesson_videos (lesson_id, title, description, youtube_url, youtube_id, video_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$lesson_id, $title, $description, $youtube_url, $youtube_id, $video_order, $_SESSION['admin_id']]);
                
                $success = "تم إضافة الفيديو بنجاح";
                break;
                
            case 'add_exercise':
                $title = $_POST['title'];
                $description = $_POST['description'];
                $exercise_order = $_POST['exercise_order'];
                
                $stmt = $db->prepare("INSERT INTO lesson_exercises (lesson_id, title, description, exercise_order, created_by) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$lesson_id, $title, $description, $exercise_order, $_SESSION['admin_id']]);
                
                $success = "تم إضافة التدريب بنجاح";
                break;
                
            case 'add_exam':
                $title = $_POST['title'];
                $description = $_POST['description'];
                $duration_minutes = $_POST['duration_minutes'];
                $exam_order = $_POST['exam_order'];
                
                $stmt = $db->prepare("INSERT INTO lesson_exams (lesson_id, title, description, duration_minutes, exam_order) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$lesson_id, $title, $description, $duration_minutes, $exam_order]);
                
                $success = "تم إضافة الامتحان بنجاح";
                break;
                
            case 'add_summary':
                $title = $_POST['title'];
                $description = $_POST['description'];
                $summary_order = $_POST['summary_order'];
                
                // Handle file upload
                if (isset($_FILES['pdf_file']) && $_FILES['pdf_file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = __DIR__ . '/../uploads/summaries/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $file_name = time() . '_' . $_FILES['pdf_file']['name'];
                    $file_path = $upload_dir . $file_name;
                    
                    if (move_uploaded_file($_FILES['pdf_file']['tmp_name'], $file_path)) {
                        $relative_path = 'uploads/summaries/' . $file_name;
                        $file_size = $_FILES['pdf_file']['size'];
                        
                        $stmt = $db->prepare("INSERT INTO lesson_summaries (lesson_id, title, description, file_path, file_name, file_size, summary_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$lesson_id, $title, $description, $relative_path, $file_name, $file_size, $summary_order, $_SESSION['admin_id']]);
                        
                        $success = "تم إضافة الملخص بنجاح";
                    } else {
                        $error = "فشل في رفع الملف";
                    }
                } else {
                    $error = "يرجى اختيار ملف PDF";
                }
                break;
        }
    }
}

// Get lesson content
$videos_stmt = $db->prepare("SELECT * FROM lesson_videos WHERE lesson_id = ? ORDER BY video_order");
$videos_stmt->execute([$lesson_id]);
$videos = $videos_stmt->fetchAll(PDO::FETCH_ASSOC);

$exercises_stmt = $db->prepare("SELECT * FROM lesson_exercises WHERE lesson_id = ? ORDER BY exercise_order");
$exercises_stmt->execute([$lesson_id]);
$exercises = $exercises_stmt->fetchAll(PDO::FETCH_ASSOC);

$exams_stmt = $db->prepare("SELECT * FROM lesson_exams WHERE lesson_id = ? ORDER BY exam_order");
$exams_stmt->execute([$lesson_id]);
$exams = $exams_stmt->fetchAll(PDO::FETCH_ASSOC);

$summaries_stmt = $db->prepare("SELECT * FROM lesson_summaries WHERE lesson_id = ? ORDER BY summary_order");
$summaries_stmt->execute([$lesson_id]);
$summaries = $summaries_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محتوى الدرس - <?php echo htmlspecialchars($lesson['title']); ?> - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="lesson-info">
                            <h1><i class="fas fa-book-open"></i> محتوى الدرس</h1>
                            <div class="lesson-details">
                                <h2><?php echo htmlspecialchars($lesson['title']); ?></h2>
                                <span class="subject-badge" style="background-color: <?php echo $lesson['color']; ?>20; color: <?php echo $lesson['color']; ?>;">
                                    <?php echo htmlspecialchars($lesson['subject_name']); ?>
                                </span>
                                <span class="lesson-number">الدرس رقم <?php echo $lesson['lesson_number']; ?></span>
                            </div>
                        </div>
                        <div class="header-actions">
                            <a href="lessons.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة للدروس
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Content Tabs -->
                <div class="content-tabs">
                    <div class="tab-buttons">
                        <button class="tab-btn active" onclick="showTab('videos')">
                            <i class="fas fa-video"></i>
                            الفيديوهات (<?php echo count($videos); ?>)
                        </button>
                        <button class="tab-btn" onclick="showTab('exercises')">
                            <i class="fas fa-dumbbell"></i>
                            التدريبات (<?php echo count($exercises); ?>)
                        </button>
                        <button class="tab-btn" onclick="showTab('exams')">
                            <i class="fas fa-clipboard-check"></i>
                            الامتحانات (<?php echo count($exams); ?>)
                        </button>
                        <button class="tab-btn" onclick="showTab('summaries')">
                            <i class="fas fa-file-pdf"></i>
                            الملخصات (<?php echo count($summaries); ?>)
                        </button>
                    </div>

                    <!-- Videos Tab -->
                    <div id="videos-tab" class="tab-content active">
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-plus-circle"></i> إضافة فيديو جديد</h3>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="admin-form">
                                    <input type="hidden" name="action" value="add_video">
                                    
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="video_title">عنوان الفيديو</label>
                                            <input type="text" name="title" id="video_title" class="form-control" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="video_order">ترتيب العرض</label>
                                            <input type="number" name="video_order" id="video_order" class="form-control" min="0" value="0">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="youtube_url">رابط اليوتيوب</label>
                                        <input type="url" name="youtube_url" id="youtube_url" class="form-control" required
                                               placeholder="https://www.youtube.com/watch?v=...">
                                    </div>

                                    <div class="form-group">
                                        <label for="video_description">وصف الفيديو</label>
                                        <textarea name="description" id="video_description" class="form-control" rows="3"></textarea>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            إضافة الفيديو
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Videos List -->
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-video"></i> قائمة الفيديوهات</h3>
                            </div>
                            <div class="card-body">
                                <?php if (empty($videos)): ?>
                                    <div class="empty-state">
                                        <i class="fas fa-video"></i>
                                        <h3>لا توجد فيديوهات</h3>
                                        <p>لم يتم إضافة أي فيديوهات لهذا الدرس بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="content-grid">
                                        <?php foreach ($videos as $video): ?>
                                            <div class="content-card">
                                                <div class="content-header">
                                                    <h4><?php echo htmlspecialchars($video['title']); ?></h4>
                                                    <div class="content-actions">
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="content-body">
                                                    <?php if ($video['youtube_id']): ?>
                                                        <div class="video-preview">
                                                            <img src="https://img.youtube.com/vi/<?php echo $video['youtube_id']; ?>/mqdefault.jpg" 
                                                                 alt="Video thumbnail" class="video-thumbnail">
                                                            <div class="play-overlay">
                                                                <i class="fas fa-play"></i>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if ($video['description']): ?>
                                                        <p class="content-description"><?php echo htmlspecialchars($video['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="content-meta">
                                                        <span class="order-badge">ترتيب: <?php echo $video['video_order']; ?></span>
                                                        <span class="status-badge <?php echo $video['is_active'] ? 'active' : 'inactive'; ?>">
                                                            <?php echo $video['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Exercises Tab -->
                    <div id="exercises-tab" class="tab-content">
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-plus-circle"></i> إضافة تدريب جديد</h3>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="admin-form">
                                    <input type="hidden" name="action" value="add_exercise">

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="exercise_title">عنوان التدريب</label>
                                            <input type="text" name="title" id="exercise_title" class="form-control" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="exercise_order">ترتيب العرض</label>
                                            <input type="number" name="exercise_order" id="exercise_order" class="form-control" min="0" value="0">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="exercise_description">وصف التدريب</label>
                                        <textarea name="description" id="exercise_description" class="form-control" rows="3"></textarea>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            إضافة التدريب
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Exercises List -->
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-dumbbell"></i> قائمة التدريبات</h3>
                            </div>
                            <div class="card-body">
                                <?php if (empty($exercises)): ?>
                                    <div class="empty-state">
                                        <i class="fas fa-dumbbell"></i>
                                        <h3>لا توجد تدريبات</h3>
                                        <p>لم يتم إضافة أي تدريبات لهذا الدرس بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="content-grid">
                                        <?php foreach ($exercises as $exercise): ?>
                                            <div class="content-card">
                                                <div class="content-header">
                                                    <h4><?php echo htmlspecialchars($exercise['title']); ?></h4>
                                                    <div class="content-actions">
                                                        <a href="exercise_questions.php?exercise_id=<?php echo $exercise['id']; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-question-circle"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="content-body">
                                                    <?php if ($exercise['description']): ?>
                                                        <p class="content-description"><?php echo htmlspecialchars($exercise['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="content-meta">
                                                        <span class="order-badge">ترتيب: <?php echo $exercise['exercise_order']; ?></span>
                                                        <span class="status-badge <?php echo $exercise['is_active'] ? 'active' : 'inactive'; ?>">
                                                            <?php echo $exercise['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Exams Tab -->
                    <div id="exams-tab" class="tab-content">
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-plus-circle"></i> إضافة امتحان جديد</h3>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="admin-form">
                                    <input type="hidden" name="action" value="add_exam">

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="exam_title">عنوان الامتحان</label>
                                            <input type="text" name="title" id="exam_title" class="form-control" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="exam_order">ترتيب العرض</label>
                                            <input type="number" name="exam_order" id="exam_order" class="form-control" min="0" value="0">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="duration_minutes">مدة الامتحان (دقيقة)</label>
                                            <input type="number" name="duration_minutes" id="duration_minutes" class="form-control" min="1" value="60">
                                        </div>

                                    </div>

                                    <div class="form-group">
                                        <label for="exam_description">وصف الامتحان</label>
                                        <textarea name="description" id="exam_description" class="form-control" rows="3"></textarea>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            إضافة الامتحان
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Exams List -->
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-clipboard-check"></i> قائمة الامتحانات</h3>
                            </div>
                            <div class="card-body">
                                <?php if (empty($exams)): ?>
                                    <div class="empty-state">
                                        <i class="fas fa-clipboard-check"></i>
                                        <h3>لا توجد امتحانات</h3>
                                        <p>لم يتم إضافة أي امتحانات لهذا الدرس بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="content-grid">
                                        <?php foreach ($exams as $exam): ?>
                                            <div class="content-card">
                                                <div class="content-header">
                                                    <h4><?php echo htmlspecialchars($exam['title']); ?></h4>
                                                    <div class="content-actions">
                                                        <a href="exam_questions.php?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-question-circle"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="content-body">
                                                    <?php if ($exam['description']): ?>
                                                        <p class="content-description"><?php echo htmlspecialchars($exam['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="exam-details">
                                                        <span class="detail-item">
                                                            <i class="fas fa-clock"></i>
                                                            <?php echo $exam['duration_minutes']; ?> دقيقة
                                                        </span>

                                                    </div>
                                                    <div class="content-meta">
                                                        <span class="order-badge">ترتيب: <?php echo $exam['exam_order']; ?></span>
                                                        <span class="status-badge <?php echo $exam['is_active'] ? 'active' : 'inactive'; ?>">
                                                            <?php echo $exam['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Summaries Tab -->
                    <div id="summaries-tab" class="tab-content">
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-plus-circle"></i> إضافة ملخص جديد</h3>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="admin-form" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="add_summary">

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="summary_title">عنوان الملخص</label>
                                            <input type="text" name="title" id="summary_title" class="form-control" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="summary_order">ترتيب العرض</label>
                                            <input type="number" name="summary_order" id="summary_order" class="form-control" min="0" value="0">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="pdf_file">ملف PDF</label>
                                        <input type="file" name="pdf_file" id="pdf_file" class="form-control" accept=".pdf" required>
                                        <small class="form-text">يجب أن يكون الملف بصيغة PDF</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="summary_description">وصف الملخص</label>
                                        <textarea name="description" id="summary_description" class="form-control" rows="3"></textarea>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            إضافة الملخص
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Summaries List -->
                        <div class="admin-card">
                            <div class="card-header">
                                <h3><i class="fas fa-file-pdf"></i> قائمة الملخصات</h3>
                            </div>
                            <div class="card-body">
                                <?php if (empty($summaries)): ?>
                                    <div class="empty-state">
                                        <i class="fas fa-file-pdf"></i>
                                        <h3>لا توجد ملخصات</h3>
                                        <p>لم يتم إضافة أي ملخصات لهذا الدرس بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="content-grid">
                                        <?php foreach ($summaries as $summary): ?>
                                            <div class="content-card">
                                                <div class="content-header">
                                                    <h4><?php echo htmlspecialchars($summary['title']); ?></h4>
                                                    <div class="content-actions">
                                                        <a href="../<?php echo $summary['file_path']; ?>" target="_blank" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="content-body">
                                                    <div class="pdf-preview">
                                                        <i class="fas fa-file-pdf"></i>
                                                        <span class="file-name"><?php echo htmlspecialchars($summary['file_name']); ?></span>
                                                        <span class="file-size"><?php echo round($summary['file_size'] / 1024, 2); ?> KB</span>
                                                    </div>
                                                    <?php if ($summary['description']): ?>
                                                        <p class="content-description"><?php echo htmlspecialchars($summary['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="content-meta">
                                                        <span class="order-badge">ترتيب: <?php echo $summary['summary_order']; ?></span>
                                                        <span class="status-badge <?php echo $summary['is_active'] ? 'active' : 'inactive'; ?>">
                                                            <?php echo $summary['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .admin-container {
            display: flex;
            flex: 1;
        }

        .admin-main {
            flex: 1;
            padding: 20px;
            margin-left: 280px;
        }

        .admin-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid #4682B4;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lesson-info h1 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .lesson-details {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .lesson-details h2 {
            color: #4682B4;
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .subject-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .lesson-number {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .content-tabs {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab-buttons {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            color: #6c757d;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab-btn:hover {
            background: rgba(70, 130, 180, 0.1);
            color: #4682B4;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .card-body {
            padding: 30px;
        }

        .admin-form {
            max-width: 800px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }

        .form-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .content-card {
            background: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .content-header {
            background: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
        }

        .content-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }

        .content-actions {
            display: flex;
            gap: 5px;
        }

        .content-body {
            padding: 20px;
        }

        .video-preview {
            position: relative;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .video-thumbnail {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.7);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .pdf-preview {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #fff5f5;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .pdf-preview i {
            font-size: 24px;
            color: #dc3545;
        }

        .file-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .file-size {
            font-size: 12px;
            color: #6c757d;
        }

        .content-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .exam-details {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #6c757d;
        }

        .content-meta {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .order-badge {
            background: #e9ecef;
            color: #6c757d;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-badge.active {
            background: #28a745;
            color: white;
        }

        .status-badge.inactive {
            background: #6c757d;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .admin-main {
                margin-left: 0;
                padding: 10px;
            }

            .header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .lesson-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .tab-buttons {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script>
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }
    </script>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
