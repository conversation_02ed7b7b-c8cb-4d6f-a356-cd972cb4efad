/**
 * Enhanced Notification System
 * Provides beautiful, responsive notifications throughout the application
 */

class NotificationSystem {
    constructor() {
        this.toastContainer = null;
        this.lastCheckTimestamp = Math.floor(Date.now() / 1000);
        this.pollingInterval = null;
        this.init();
    }

    init() {
        // Create toast container if it doesn't exist
        if (!document.querySelector('.toast-container')) {
            this.createToastContainer();
        }

        // Start real-time notification checking
        this.startRealtimeNotifications();

        // Auto-refresh notifications every 30 seconds (fallback)
        setInterval(() => {
            this.refreshNotificationBadge();
        }, 30000);
    }

    createToastContainer() {
        this.toastContainer = document.createElement('div');
        this.toastContainer.className = 'toast-container';
        this.toastContainer.innerHTML = `
            <style>
                .toast-container {
                    position: fixed;
                    top: 100px;
                    right: 30px;
                    z-index: 10000;
                    pointer-events: none;
                }
                
                .toast-container .notification-toast {
                    pointer-events: all;
                    margin-bottom: 15px;
                }
                
                @media (max-width: 768px) {
                    .toast-container {
                        right: 15px;
                        left: 15px;
                        top: 80px;
                    }
                    
                    .toast-container .notification-toast {
                        min-width: auto;
                        max-width: none;
                    }
                }
            </style>
        `;
        document.body.appendChild(this.toastContainer);
    }

    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - Type: success, error, warning, info
     * @param {number} duration - Duration in milliseconds (default: 4000)
     */
    showToast(message, type = 'info', duration = 4000) {
        const toast = document.createElement('div');
        toast.className = `notification-toast notification-toast-${type}`;
        
        const iconMap = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">${iconMap[type] || iconMap.info}</div>
                <span>${this.escapeHtml(message)}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        // Add to container
        if (!this.toastContainer) {
            this.createToastContainer();
        }
        this.toastContainer.appendChild(toast);
        
        // Auto remove
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }
        }, duration);
        
        return toast;
    }

    /**
     * Show success notification
     */
    success(message, duration = 4000) {
        return this.showToast(message, 'success', duration);
    }

    /**
     * Show error notification
     */
    error(message, duration = 5000) {
        return this.showToast(message, 'error', duration);
    }

    /**
     * Show warning notification
     */
    warning(message, duration = 4500) {
        return this.showToast(message, 'warning', duration);
    }

    /**
     * Show info notification
     */
    info(message, duration = 4000) {
        return this.showToast(message, 'info', duration);
    }

    /**
     * Show confirmation dialog with custom styling
     */
    confirm(message, title = 'تأكيد') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'notification-confirm-modal';
            modal.innerHTML = `
                <div class="confirm-overlay"></div>
                <div class="confirm-dialog">
                    <div class="confirm-header">
                        <h3>${this.escapeHtml(title)}</h3>
                    </div>
                    <div class="confirm-body">
                        <p>${this.escapeHtml(message)}</p>
                    </div>
                    <div class="confirm-actions">
                        <button class="confirm-btn confirm-cancel">إلغاء</button>
                        <button class="confirm-btn confirm-ok">موافق</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Add event listeners
            modal.querySelector('.confirm-cancel').onclick = () => {
                modal.remove();
                resolve(false);
            };
            
            modal.querySelector('.confirm-ok').onclick = () => {
                modal.remove();
                resolve(true);
            };
            
            modal.querySelector('.confirm-overlay').onclick = () => {
                modal.remove();
                resolve(false);
            };
        });
    }

    /**
     * Refresh notification badge count
     */
    refreshNotificationBadge() {
        fetch('/manash/includes/get_notifications.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const unreadCount = data.notifications.filter(n => !n.is_read).length;
                    this.updateBadgeCount(unreadCount);
                }
            })
            .catch(error => {
                console.error('Error refreshing notification badge:', error);
            });
    }

    /**
     * Update badge count
     */
    updateBadgeCount(count) {
        const badge = document.getElementById('notificationBadge');
        const notificationBtn = document.querySelector('.notification-btn');
        
        if (count > 0) {
            if (badge) {
                badge.textContent = count;
            } else if (notificationBtn) {
                const newBadge = document.createElement('span');
                newBadge.className = 'notification-badge';
                newBadge.id = 'notificationBadge';
                newBadge.textContent = count;
                notificationBtn.appendChild(newBadge);
            }
        } else {
            if (badge) {
                badge.remove();
            }
        }
    }

    /**
     * Start real-time notification checking
     */
    startRealtimeNotifications() {
        // Check for new notifications every 10 seconds
        this.pollingInterval = setInterval(() => {
            this.checkForNewNotifications();
        }, 10000);

        // Initial check
        this.checkForNewNotifications();
    }

    /**
     * Stop real-time notification checking
     */
    stopRealtimeNotifications() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }

    /**
     * Check for new notifications
     */
    checkForNewNotifications() {
        // Only check if we're on a page that supports notifications
        if (!document.querySelector('.notification-btn')) {
            return;
        }

        fetch(`includes/check_new_notifications.php?last_check=${this.lastCheckTimestamp}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.lastCheckTimestamp = data.current_timestamp;

                    if (data.has_new && data.notifications.length > 0) {
                        // Show new notifications as toasts
                        data.notifications.forEach(notification => {
                            this.showNotificationToast(notification);
                        });

                        // Update badge count
                        this.updateBadgeCount(data.unread_count);

                        // Update dropdown if it's open
                        const dropdown = document.getElementById('notificationsDropdown');
                        if (dropdown && dropdown.classList.contains('show')) {
                            this.refreshDropdownContent();
                        }
                    } else if (data.unread_count !== undefined) {
                        // Just update badge count
                        this.updateBadgeCount(data.unread_count);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking for new notifications:', error);
            });
    }

    /**
     * Show notification as toast
     */
    showNotificationToast(notification) {
        const iconMap = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        const toast = document.createElement('div');
        toast.className = `notification-toast notification-toast-${notification.type} notification-realtime`;

        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">${iconMap[notification.type] || iconMap.info}</div>
                <div class="toast-text">
                    <div class="toast-title">${this.escapeHtml(notification.title)}</div>
                    <div class="toast-message">${this.escapeHtml(notification.message)}</div>
                </div>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add click handler to mark as read
        toast.addEventListener('click', () => {
            if (!notification.is_read) {
                this.markNotificationAsRead(notification.id);
            }
        });

        // Add to container
        if (!this.toastContainer) {
            this.createToastContainer();
        }
        this.toastContainer.appendChild(toast);

        // Auto remove after 6 seconds (longer for real-time notifications)
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }
        }, 6000);

        return toast;
    }

    /**
     * Refresh dropdown content
     */
    refreshDropdownContent() {
        if (window.loadNotifications && typeof window.loadNotifications === 'function') {
            window.loadNotifications();
        }
    }

    /**
     * Mark notification as read
     */
    markNotificationAsRead(notificationId) {
        fetch('includes/mark_notifications_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ notification_id: notificationId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update badge count
                this.refreshNotificationBadge();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Show loading notification
     */
    showLoading(message = 'جاري التحميل...') {
        const toast = document.createElement('div');
        toast.className = 'notification-toast notification-toast-loading';
        toast.innerHTML = `
            <div class="toast-content">
                <div class="loading-spinner-small"></div>
                <span>${this.escapeHtml(message)}</span>
            </div>
        `;
        
        if (!this.toastContainer) {
            this.createToastContainer();
        }
        this.toastContainer.appendChild(toast);
        
        return {
            close: () => toast.remove(),
            update: (newMessage) => {
                const span = toast.querySelector('span');
                if (span) span.textContent = newMessage;
            }
        };
    }
}

// Create global instance
window.notifications = new NotificationSystem();

// Backward compatibility
window.showNotificationToast = (message, type, duration) => {
    window.notifications.showToast(message, type, duration);
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationSystem;
}
