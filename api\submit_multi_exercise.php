<?php
require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');


if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    $userId = $_SESSION['user_id'];
    
    $weekNumber = $_POST['week_number'] ?? null;
    $courseId = $_POST['course_id'] ?? null;
    $answers = $_POST['answers'] ?? [];
    
    if (!$weekNumber || !$courseId || empty($answers)) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }
    
    // Get all exercises for this week
    $stmt = $db->prepare("
        SELECT * FROM course_exercises 
        WHERE course_id = ? AND week_number = ? 
        ORDER BY id ASC
    ");
    $stmt->execute([$courseId, $weekNumber]);
    $exercises = $stmt->fetchAll();
    
    if (empty($exercises)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد تمارين لهذا الأسبوع']);
        exit;
    }
    
    // Debug: Log received answers
    error_log("Received answers: " . print_r($answers, true));
    error_log("Exercises count: " . count($exercises));

    // Validate that all questions are answered
    $unansweredQuestions = [];
    foreach ($exercises as $index => $exercise) {
        if (!isset($answers[$index]) || trim($answers[$index]) === '') {
            $unansweredQuestions[] = $index + 1;
            error_log("Missing answer for question index: $index");
        }
    }
    
    if (!empty($unansweredQuestions)) {
        echo json_encode([
            'success' => false, 
            'message' => 'يرجى الإجابة على جميع الأسئلة',
            'unanswered_questions' => $unansweredQuestions
        ]);
        exit;
    }
    
    // Get current attempt number
    $stmt = $db->prepare("
        SELECT COALESCE(MAX(attempt_number), 0) + 1 as next_attempt
        FROM user_exercise_attempts 
        WHERE user_id = ? AND exercise_id IN (" . implode(',', array_column($exercises, 'id')) . ")
    ");
    $stmt->execute([$userId]);
    $attemptNumber = $stmt->fetchColumn();
    
    $db->beginTransaction();
    
    $results = [];
    $totalQuestions = count($exercises);
    $correctAnswers = 0;
    
    // Process each answer
    foreach ($exercises as $index => $exercise) {
        if (!isset($answers[$index])) {
            error_log("Answer not found for index: $index");
            continue;
        }

        $userAnswer = trim($answers[$index]);
        $correctAnswer = trim($exercise['correct_answer']);

        error_log("Processing question $index: user_answer='$userAnswer', correct_answer='$correctAnswer'");
        
        // Normalize answers for true/false questions
        if ($exercise['question_type'] === 'true_false') {
            // Normalize user answer
            if (in_array(strtolower($userAnswer), ['true', 'صح', '1', 'نعم'])) {
                $userAnswer = 'true';
            } elseif (in_array(strtolower($userAnswer), ['false', 'خطأ', '0', 'لا'])) {
                $userAnswer = 'false';
            }
            
            // Normalize correct answer
            if (in_array(strtolower($correctAnswer), ['true', 'صح', '1', 'نعم'])) {
                $correctAnswer = 'true';
            } elseif (in_array(strtolower($correctAnswer), ['false', 'خطأ', '0', 'لا'])) {
                $correctAnswer = 'false';
            }
        }
        
        $isCorrect = (strcasecmp($userAnswer, $correctAnswer) === 0);
        $isCorrectInt = $isCorrect ? 1 : 0; // Convert boolean to integer
        if ($isCorrect) {
            $correctAnswers++;
        }
        
        // Format correct answer for display
        $displayCorrectAnswer = $exercise['correct_answer'] ?? 'غير محدد';
        if ($exercise['question_type'] === 'true_false') {
            if ($correctAnswer === 'true') {
                $displayCorrectAnswer = 'صح';
            } elseif ($correctAnswer === 'false') {
                $displayCorrectAnswer = 'خطأ';
            }
        }
        
        // Format user answer for display
        $displayUserAnswer = $answers[$index];
        if ($exercise['question_type'] === 'true_false') {
            if ($userAnswer === 'true') {
                $displayUserAnswer = 'صح';
            } elseif ($userAnswer === 'false') {
                $displayUserAnswer = 'خطأ';
            }
        }

        // Save attempt to database
        $stmt = $db->prepare("
            INSERT INTO user_exercise_attempts (user_id, exercise_id, user_answer, is_correct, attempt_number)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $exercise['id'], $answers[$index], $isCorrectInt, $attemptNumber]);

        error_log("Saved answer for exercise {$exercise['id']}: user_answer='{$answers[$index]}', is_correct=" . ($isCorrect ? 'true' : 'false'));
        
        $results[] = [
            'question_number' => $index + 1,
            'question_text' => $exercise['question_text'],
            'user_answer' => $displayUserAnswer,
            'correct_answer' => $displayCorrectAnswer,
            'is_correct' => $isCorrect,
            'explanation' => $exercise['explanation'] ?? '',
            'question_type' => $exercise['question_type']
        ];
    }
    
    $db->commit();
    
    // Get course passing grade (default to 60% if not set)
    $stmt = $db->prepare("SELECT passing_grade FROM courses WHERE id = ?");
    $stmt->execute([$courseId]);
    $rawPassingGrade = $stmt->fetchColumn();
    $passingGrade = floatval($rawPassingGrade ?: 60);

    // Calculate score
    $score = round(($correctAnswers / $totalQuestions) * 100, 1);
    $scoreFloat = floatval($score);
    $passed = $scoreFloat >= $passingGrade;

    // Detailed logging for debugging
    error_log("=== Exercise Results Debug ===");
    error_log("Course ID: $courseId");
    error_log("Raw Passing Grade from DB: " . ($rawPassingGrade ?? 'NULL'));
    error_log("Final Passing Grade: $passingGrade%");
    error_log("Correct Answers: $correctAnswers");
    error_log("Total Questions: $totalQuestions");
    error_log("Calculated Score: $score% (float: $scoreFloat)");
    error_log("Score >= Passing Grade? $scoreFloat >= $passingGrade = " . ($scoreFloat >= $passingGrade ? 'true' : 'false'));
    error_log("Final Passed Status: " . ($passed ? 'Yes' : 'No'));
    error_log("==============================");

    echo json_encode([
        'success' => true,
        'results' => $results,
        'total_questions' => $totalQuestions,
        'correct_answers' => $correctAnswers,
        'score' => $score,
        'attempt_number' => $attemptNumber,
        'passed' => $passed,
        'passing_grade' => $passingGrade,
        'week_number' => $weekNumber
    ]);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error submitting multi exercise: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
