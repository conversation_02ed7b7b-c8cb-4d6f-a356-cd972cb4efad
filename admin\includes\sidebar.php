<?php
// Get current page name
$currentPage = basename($_SERVER['PHP_SELF']);

// Get admin information
if (isset($_SESSION['admin_id'])) {
    $adminManager = new AdminManager();
    $adminData = $adminManager->getAdminById($_SESSION['admin_id']);
    $adminName = $adminData['full_name'] ?? 'المدير';
} else {
    $adminName = $_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'الإدارة';
}
?>

<aside class="modern-sidebar" id="modernSidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-brand">
        <div class="brand-logo">
            <img src="../img/logo.png" alt="<?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?>" class="logo-img">
        </div>
        <div class="brand-info">
            <h3><?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></h3>
            <span>لوحة التحكم</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 12h18m-9-9l9 9-9 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>

    <!-- Admin Profile Section -->
    <div class="sidebar-profile">
        <div class="profile-avatar">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
        </div>
        <div class="profile-info">
            <h4><?php echo htmlspecialchars($adminName); ?></h4>
            <span>مدير النظام</span>
        </div>
        <div class="profile-status">
            <span class="status-indicator online"></span>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-navigation">
        <!-- Dashboard Section -->
        <div class="nav-section">
            <h5 class="section-title">الرئيسية</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'index.php' ? 'active' : ''; ?>">
                    <a href="index.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">لوحة التحكم</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'users.php' ? 'active' : ''; ?>">
                    <a href="users.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">المستخدمين</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Notifications Section -->
        <div class="nav-section">
            <h5 class="section-title">الإشعارات</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'manage_notifications.php' ? 'active' : ''; ?>">
                    <a href="manage_notifications.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2"/>
                                <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">إدارة الإشعارات</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'send_notifications.php' ? 'active' : ''; ?>">
                    <a href="send_notifications.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">إرسال إشعار</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Content Section -->
        <div class="nav-section">
            <h5 class="section-title">المحتوى</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'news.php' ? 'active' : ''; ?>">
                    <a href="news.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3.5" stroke="currentColor" stroke-width="2"/>
                                <path d="M6 12h4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">الأخبار</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'exams.php' ? 'active' : ''; ?>">
                    <a href="exams.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">الامتحانات</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>




                <li class="nav-item <?php echo $currentPage === 'honor_board.php' ? 'active' : ''; ?>">
                    <a href="honor_board.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">لوحة الشرف</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'curriculum_subjects.php' ? 'active' : ''; ?>">
                    <a href="curriculum_subjects.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" stroke-width="2"/>
                                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">مواد المنهج</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Courses Section -->
        <div class="nav-section">
            <h5 class="section-title">الكورسات</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'courses.php' ? 'active' : ''; ?>">
                    <a href="courses.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="currentColor" stroke-width="2"/>
                                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">إدارة الكورسات</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'activation_codes.php' ? 'active' : ''; ?>">
                    <a href="activation_codes.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">أكواد التفعيل</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>


                <li class="nav-item <?php echo $currentPage === 'course_subscribers.php' ? 'active' : ''; ?>">
                    <a href="course_subscribers.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="2"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">مشتركي الكورسات</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                
            </ul>
        </div>

        <!-- Subscriptions & Payments Section -->
        <div class="nav-section">
            <h5 class="section-title">الاشتراكات والمدفوعات</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'subscribers.php' ? 'active' : ''; ?>">
                    <a href="subscribers.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                <path d="M20 8v6" stroke="currentColor" stroke-width="2"/>
                                <path d="M23 11h-6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">المشتركين</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'activation_codes.php' ? 'active' : ''; ?>">
                    <a href="activation_codes.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">أكواد التفعيل</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'subscription_plans.php' ? 'active' : ''; ?>">
                    <a href="subscription_plans.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">خطط الاشتراك</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'subscription_logs.php' ? 'active' : ''; ?>">
                    <a href="subscription_logs.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                                <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">سجلات الاشتراك</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>


            </ul>
        </div>

        <!-- Curriculum Section -->
        <div class="nav-section">
            <h5 class="section-title">المناهج والدروس</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'curriculum_subjects.php' ? 'active' : ''; ?>">
                    <a href="curriculum_subjects.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" stroke-width="2"/>
                                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">مواد المنهج</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>


                <li class="nav-item <?php echo $currentPage === 'lessons.php' ? 'active' : ''; ?>">
                    <a href="lessons.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" stroke="currentColor" stroke-width="2"/>
                                <line x1="12" y1="8" x2="12" y2="16" stroke="currentColor" stroke-width="2"/>
                                <line x1="8" y1="12" x2="16" y2="12" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text"> إضافة درس جديد و اضافة محتوي الدروس</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>



            </ul>
        </div>

        <!-- Students Section -->
        

        <!-- Administration Section -->
        <div class="nav-section">
            <h5 class="section-title">الإدارة</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'add_admin.php' ? 'active' : ''; ?>">
                    <a href="add_admin.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                <line x1="20" y1="8" x2="20" y2="14" stroke="currentColor" stroke-width="2"/>
                                <line x1="23" y1="11" x2="17" y2="11" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">إضافة إدارة</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'messages.php' ? 'active' : ''; ?>">
                    <a href="messages.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">الرسائل</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>

                <li class="nav-item <?php echo $currentPage === 'reset_admin.php' ? 'active' : ''; ?>">
                    <a href="reset_admin.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 4v6h6" stroke="currentColor" stroke-width="2"/>
                                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">إعادة تعيين الإدارة</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Testing & Development Section -->
        <div class="nav-section">
            <h5 class="section-title">الاختبار والتطوير</h5>
            <ul class="nav-list">
                <li class="nav-item <?php echo $currentPage === 'create_test_admin.php' ? 'active' : ''; ?>">
                    <a href="create_test_admin.php" class="nav-link">
                        <div class="nav-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <span class="nav-text">إنشاء مدير تجريبي</span>
                        <div class="nav-indicator"></div>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Sidebar Footer -->
    
</aside>


