<?php
session_start();
require_once '../includes/database.php';

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = Database::getInstance()->getConnection();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['user_id'];

// Get parameters
$contentType = isset($_GET['type']) ? $_GET['type'] : '';
$contentId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (empty($contentType) || $contentId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
    exit;
}

try {
    $result = [
        'success' => true,
        'completed' => false,
        'score' => 0,
        'percentage' => 0,
        'passed' => false,
        'attempt_count' => 0,
        'last_attempt_date' => null,
        'details' => []
    ];
    
    switch ($contentType) {
        case 'video':
            $stmt = $db->prepare("
                SELECT completed, progress_percentage, completed_at 
                FROM user_video_progress 
                WHERE user_id = ? AND video_id = ?
            ");
            $stmt->execute([$userId, $contentId]);
            $progress = $stmt->fetch();
            
            if ($progress) {
                $result['completed'] = (bool)$progress['completed'];
                $result['percentage'] = floatval($progress['progress_percentage']);
                $result['last_attempt_date'] = $progress['completed_at'];
            }
            break;
            
        case 'exercise':
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempt_count,
                       MAX(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as completed,
                       MAX(created_at) as last_attempt_date
                FROM user_exercise_attempts 
                WHERE user_id = ? AND exercise_id = ?
            ");
            $stmt->execute([$userId, $contentId]);
            $exercise = $stmt->fetch();
            
            if ($exercise) {
                $result['completed'] = (bool)$exercise['completed'];
                $result['attempt_count'] = intval($exercise['attempt_count']);
                $result['last_attempt_date'] = $exercise['last_attempt_date'];
                $result['passed'] = $result['completed'];
                $result['percentage'] = $result['completed'] ? 100 : 0;
                
                // Get detailed attempts
                $stmt = $db->prepare("
                    SELECT attempt_number, is_correct, created_at 
                    FROM user_exercise_attempts 
                    WHERE user_id = ? AND exercise_id = ? 
                    ORDER BY created_at DESC
                ");
                $stmt->execute([$userId, $contentId]);
                $result['details'] = $stmt->fetchAll();
            }
            break;
            
        case 'exam':
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempt_count,
                       MAX(percentage) as best_percentage,
                       MAX(passed) as passed,
                       MAX(completed_at) as last_attempt_date,
                       MAX(total_score) as best_score,
                       MAX(max_score) as max_score
                FROM user_exam_attempts 
                WHERE user_id = ? AND exam_id = ? AND completed_at IS NOT NULL
            ");
            $stmt->execute([$userId, $contentId]);
            $exam = $stmt->fetch();
            
            if ($exam && $exam['attempt_count'] > 0) {
                $result['completed'] = true;
                $result['attempt_count'] = intval($exam['attempt_count']);
                $result['percentage'] = floatval($exam['best_percentage']);
                $result['passed'] = (bool)$exam['passed'];
                $result['score'] = floatval($exam['best_score']);
                $result['last_attempt_date'] = $exam['last_attempt_date'];
                
                // Get detailed attempts
                $stmt = $db->prepare("
                    SELECT attempt_number, percentage, passed, total_score, max_score, completed_at 
                    FROM user_exam_attempts 
                    WHERE user_id = ? AND exam_id = ? AND completed_at IS NOT NULL
                    ORDER BY completed_at DESC
                ");
                $stmt->execute([$userId, $contentId]);
                $result['details'] = $stmt->fetchAll();
            }
            break;
            
        case 'weekly_test':
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempt_count,
                       MAX(percentage) as best_percentage,
                       MAX(completed_at) as last_attempt_date,
                       MAX(total_score) as best_score,
                       MAX(max_score) as max_score,
                       MAX(passed) as passed
                FROM user_weekly_test_attempts
                WHERE user_id = ? AND test_id = ? AND completed_at IS NOT NULL
            ");
            $stmt->execute([$userId, $contentId]);
            $test = $stmt->fetch();
            
            if ($test && $test['attempt_count'] > 0) {
                $result['completed'] = true;
                $result['attempt_count'] = intval($test['attempt_count']);
                $result['percentage'] = floatval($test['best_percentage']);
                $result['passed'] = (bool)$test['passed'] || $result['percentage'] >= 60; // Use saved passed status or 60% threshold
                $result['score'] = floatval($test['best_score']);
                $result['last_attempt_date'] = $test['last_attempt_date'];
                
                // Get detailed attempts
                $stmt = $db->prepare("
                    SELECT attempt_number, percentage, total_score, max_score, passed, completed_at
                    FROM user_weekly_test_attempts
                    WHERE user_id = ? AND test_id = ? AND completed_at IS NOT NULL
                    ORDER BY completed_at DESC
                ");
                $stmt->execute([$userId, $contentId]);
                $result['details'] = $stmt->fetchAll();
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid content type']);
            exit;
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
