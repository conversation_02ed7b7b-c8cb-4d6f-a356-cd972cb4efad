<?php
require_once '../config/config.php';
require_once '../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Get summary ID
$summary_id = isset($_GET['summary_id']) ? (int)$_GET['summary_id'] : 0;

if (!$summary_id) {
    header('Location: ' . SITE_URL . '/page/curriculum.php');
    exit;
}

// Get summary info with lesson and subject
$stmt = $db->prepare("
    SELECT ls.*, l.title as lesson_title, l.id as lesson_id, l.subject_id, l.is_free,
           cs.name as subject_name, cs.color as subject_color
    FROM lesson_summaries ls
    JOIN lessons l ON ls.lesson_id = l.id
    JOIN curriculum_subjects cs ON l.subject_id = cs.id
    WHERE ls.id = ? AND ls.is_active = 1 AND l.is_active = 1
");
$stmt->execute([$summary_id]);
$summary = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$summary) {
    header('Location: curriculum.php');
    exit;
}

// Check if user can access this summary
$user_id = $_SESSION['user_id'];
$subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date
                     FROM users u WHERE u.id = ?";
$stmt = $db->prepare($subscriptionQuery);
$stmt->execute([$user_id]);
$userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

$has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                   $userSubscription['subscription_end_date'] &&
                   strtotime($userSubscription['subscription_end_date']) > time();

$can_access = $summary['is_free'] || $has_subscription;

if (!$can_access) {
    header('Location: lesson_content.php?lesson_id=' . $summary['lesson_id']);
    exit;
}

// Get user's summary progress
$stmt = $db->prepare("SELECT * FROM user_summary_progress WHERE user_id = ? AND summary_id = ?");
$stmt->execute([$user_id, $summary_id]);
$progress = $stmt->fetch(PDO::FETCH_ASSOC);

// Handle completion update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['mark_completed'])) {
    if ($progress) {
        $stmt = $db->prepare("UPDATE user_summary_progress SET is_completed = 1, completed_at = NOW() WHERE user_id = ? AND summary_id = ?");
        $stmt->execute([$user_id, $summary_id]);
    } else {
        $stmt = $db->prepare("INSERT INTO user_summary_progress (user_id, summary_id, is_completed, completed_at) VALUES (?, ?, 1, NOW())");
        $stmt->execute([$user_id, $summary_id]);
    }
    
    // Update lesson progress
    updateLessonProgress($db, $user_id, $summary['lesson_id']);
    
    $success = "تم تسجيل إكمال مشاهدة الملخص بنجاح!";
    
    // Refresh progress
    $stmt = $db->prepare("SELECT * FROM user_summary_progress WHERE user_id = ? AND summary_id = ?");
    $stmt->execute([$user_id, $summary_id]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);
}

function updateLessonProgress($db, $user_id, $lesson_id) {
    // Get total content count
    $total_query = "
        SELECT 
            (SELECT COUNT(*) FROM lesson_videos WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exercises WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exams WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_summaries WHERE lesson_id = ? AND is_active = 1) as total_count
    ";
    $stmt = $db->prepare($total_query);
    $stmt->execute([$lesson_id, $lesson_id, $lesson_id, $lesson_id]);
    $total_count = $stmt->fetchColumn();
    
    // Get completed content count
    $completed_count = 0;

    try {
        $completed_query = "
            SELECT
                (SELECT COUNT(*) FROM user_video_progress uvp
                 JOIN lesson_videos lv ON uvp.video_id = lv.id
                 WHERE uvp.user_id = ? AND lv.lesson_id = ? AND uvp.is_completed = 1) +
                (SELECT COUNT(*) FROM user_exercise_progress uep
                 JOIN lesson_exercises le ON uep.exercise_id = le.id
                 WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
                (SELECT COUNT(*) FROM user_exam_progress uep
                 JOIN lesson_exams le ON uep.exam_id = le.id
                 WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
                (SELECT COUNT(*) FROM user_summary_progress usp
                 JOIN lesson_summaries ls ON usp.summary_id = ls.id
                 WHERE usp.user_id = ? AND ls.lesson_id = ? AND usp.is_completed = 1) as completed_count
        ";
        $stmt = $db->prepare($completed_query);
        $stmt->execute([$user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id]);
        $completed_count = $stmt->fetchColumn();
    } catch (Exception $e) {
        // If tables don't exist, use 0
        $completed_count = 0;
    }

    
    $completion_percentage = $total_count > 0 ? round(($completed_count / $total_count) * 100, 2) : 0;
    $is_completed = $completion_percentage >= 100;
    
    // Update or insert lesson progress
    $stmt = $db->prepare("
        INSERT INTO user_lesson_progress (user_id, lesson_id, completion_percentage, is_completed, last_accessed_at, completed_at)
        VALUES (?, ?, ?, ?, NOW(), ?)
        ON DUPLICATE KEY UPDATE 
            completion_percentage = VALUES(completion_percentage),
            is_completed = VALUES(is_completed),
            last_accessed_at = VALUES(last_accessed_at),
            completed_at = CASE WHEN VALUES(is_completed) = 1 AND is_completed = 0 THEN NOW() ELSE completed_at END
    ");
    $stmt->execute([$user_id, $lesson_id, $completion_percentage, $is_completed, $is_completed ? date('Y-m-d H:i:s') : null]);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($summary['title']); ?> - عارض الملخص - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="summary-container">
                <!-- Summary Header -->
                <div class="summary-header">
                    <div class="summary-info">
                        <div class="summary-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="summary-details">
                            <h1><?php echo htmlspecialchars($summary['title']); ?></h1>
                            <div class="summary-meta">
                                <span class="lesson-badge">
                                    <?php echo htmlspecialchars($summary['lesson_title']); ?>
                                </span>
                                <span class="subject-badge" style="background-color: <?php echo $summary['subject_color']; ?>20; color: <?php echo $summary['subject_color']; ?>;">
                                    <?php echo htmlspecialchars($summary['subject_name']); ?>
                                </span>
                                <?php if ($progress && $progress['is_completed']): ?>
                                    <span class="completion-badge">
                                        <i class="fas fa-check-circle"></i>
                                        مكتمل
                                    </span>
                                <?php endif; ?>
                            </div>
                            <?php if ($summary['description']): ?>
                                <p class="summary-description"><?php echo htmlspecialchars($summary['description']); ?></p>
                            <?php endif; ?>
                            <div class="file-info">
                                <span class="file-name">
                                    <i class="fas fa-file-pdf"></i>
                                    <?php echo htmlspecialchars($summary['file_name']); ?>
                                </span>
                                <span class="file-size">
                                    <?php echo round($summary['file_size'] / 1024, 2); ?> KB
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="summary-actions">
                        <a href="lesson_content.php?lesson_id=<?php echo $summary['lesson_id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للدرس
                        </a>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <!-- PDF Viewer -->
                <div class="pdf-viewer-wrapper">
                    <div class="pdf-viewer">
                        <iframe 
                            src="../<?php echo $summary['file_path']; ?>#toolbar=1&navpanes=1&scrollbar=1"
                            type="application/pdf"
                            width="100%" 
                            height="600px">
                            <p>متصفحك لا يدعم عرض ملفات PDF. يمكنك 
                                <a href="../<?php echo $summary['file_path']; ?>" target="_blank">تحميل الملف</a>
                                لعرضه.
                            </p>
                        </iframe>
                    </div>
                    
                    <div class="pdf-controls">
                        <a href="../<?php echo $summary['file_path']; ?>" target="_blank" class="btn btn-info">
                            <i class="fas fa-external-link-alt"></i>
                            فتح في نافذة جديدة
                        </a>
                        <a href="../<?php echo $summary['file_path']; ?>" download class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            تحميل الملف
                        </a>
                    </div>
                </div>

                <!-- Summary Controls -->
                <div class="summary-controls">
                    <?php if (!$progress || !$progress['is_completed']): ?>
                        <form method="POST" class="completion-form">
                            <button type="submit" name="mark_completed" class="btn btn-success btn-large">
                                <i class="fas fa-check"></i>
                                هل أكملت مشاهدة الملخص؟
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="completed-message">
                            <i class="fas fa-check-circle"></i>
                            <span>تم إكمال مشاهدة الملخص</span>
                            <small>تم الإكمال في: <?php echo date('d/m/Y H:i', strtotime($progress['completed_at'])); ?></small>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Other Summaries -->
                <div class="summary-navigation">
                    <h3>ملخصات أخرى في هذا الدرس</h3>
                    <?php
                    // Get other summaries in this lesson
                    $stmt = $db->prepare("
                        SELECT ls.*, COALESCE(usp.is_completed, 0) as is_completed
                        FROM lesson_summaries ls
                        LEFT JOIN user_summary_progress usp ON ls.id = usp.summary_id AND usp.user_id = ?
                        WHERE ls.lesson_id = ? AND ls.is_active = 1
                        ORDER BY ls.summary_order
                    ");
                    $stmt->execute([$user_id, $summary['lesson_id']]);
                    $other_summaries = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    ?>
                    
                    <?php if (count($other_summaries) > 1): ?>
                        <div class="summary-list">
                            <?php foreach ($other_summaries as $other_summary): ?>
                                <div class="summary-item <?php echo $other_summary['id'] == $summary_id ? 'current' : ''; ?> <?php echo $other_summary['is_completed'] ? 'completed' : ''; ?>">
                                    <div class="summary-icon-small">
                                        <i class="fas fa-file-pdf"></i>
                                        <?php if ($other_summary['is_completed']): ?>
                                            <div class="completion-overlay">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="summary-details-small">
                                        <h4><?php echo htmlspecialchars($other_summary['title']); ?></h4>
                                        <?php if ($other_summary['description']): ?>
                                            <p><?php echo htmlspecialchars(substr($other_summary['description'], 0, 60)) . '...'; ?></p>
                                        <?php endif; ?>
                                        <span class="file-size-small"><?php echo round($other_summary['file_size'] / 1024, 2); ?> KB</span>
                                    </div>
                                    <?php if ($other_summary['id'] != $summary_id): ?>
                                        <a href="summary_viewer.php?summary_id=<?php echo $other_summary['id']; ?>" class="summary-link">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="no-other-summaries">هذا هو الملخص الوحيد في هذا الدرس</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <style>
        .summary-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg,
                rgba(135, 206, 235, 0.02) 0%,
                rgba(70, 130, 180, 0.05) 50%,
                rgba(32, 178, 170, 0.02) 100%);
            min-height: 100vh;
        }

        .summary-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .summary-info {
            display: flex;
            align-items: flex-start;
            gap: 25px;
        }

        .summary-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
            flex-shrink: 0;
        }

        .summary-details h1 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 2rem;
            font-weight: 700;
            line-height: 1.3;
        }

        .summary-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .lesson-badge, .subject-badge, .completion-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .lesson-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .completion-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .summary-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.6;
            margin: 0 0 15px 0;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px 15px;
            background: rgba(255, 193, 7, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .file-name {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #2c3e50;
            font-weight: 600;
            font-size: 14px;
        }

        .file-name i {
            color: #ffc107;
        }

        .file-size {
            color: #6c757d;
            font-size: 12px;
        }

        .pdf-viewer-wrapper {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .pdf-viewer {
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
        }

        .pdf-viewer iframe {
            border: none;
            border-radius: 15px;
        }

        .pdf-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .summary-controls {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            text-align: center;
        }

        .completion-form {
            margin: 0;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            color: white;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .completed-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: #28a745;
        }

        .completed-message i {
            font-size: 48px;
        }

        .completed-message span {
            font-size: 18px;
            font-weight: 600;
        }

        .completed-message small {
            color: #6c757d;
            font-size: 12px;
        }

        .summary-navigation {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .summary-navigation h3 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .summary-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .summary-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }

        .summary-item.current {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(253, 126, 20, 0.1));
            border: 2px solid #ffc107;
        }

        .summary-item.completed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 2px;
        }

        .summary-icon-small {
            position: relative;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .completion-overlay {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        .summary-details-small {
            flex: 1;
            min-width: 0;
        }

        .summary-details-small h4 {
            color: #2c3e50;
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .summary-details-small p {
            color: #6c757d;
            margin: 0 0 5px 0;
            font-size: 12px;
            line-height: 1.4;
        }

        .file-size-small {
            color: #6c757d;
            font-size: 11px;
        }

        .summary-link {
            color: #ffc107;
            font-size: 20px;
            text-decoration: none;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .summary-link:hover {
            background: rgba(255, 193, 7, 0.1);
            transform: scale(1.1);
        }

        .no-other-summaries {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .summary-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .summary-info {
                flex-direction: column;
                text-align: center;
            }

            .summary-details h1 {
                font-size: 1.5rem;
            }

            .summary-meta {
                justify-content: center;
            }

            .pdf-controls {
                flex-direction: column;
                align-items: center;
            }

            .summary-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
        }
    </style>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
