<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set admin session for testing
$_SESSION['admin_id'] = 1;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار AJAX</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .result { margin: 20px 0; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h2>اختبار AJAX لإضافة خطة الاشتراك</h2>
    
    <form id="testForm">
        <div class="form-group">
            <label>اسم الخطة:</label>
            <input type="text" name="name" value="خطة اختبار AJAX" required>
        </div>
        
        <div class="form-group">
            <label>اسم الخطة (إنجليزي):</label>
            <input type="text" name="name_en" value="AJAX Test Plan">
        </div>
        
        <div class="form-group">
            <label>الوصف:</label>
            <textarea name="description">هذه خطة اختبار AJAX</textarea>
        </div>
        
        <div class="form-group">
            <label>السعر:</label>
            <input type="number" name="price" step="0.01" value="50.00" required>
        </div>
        
        <div class="form-group">
            <label>نسبة الخصم (%):</label>
            <input type="number" name="discount_percentage" min="0" max="100" value="20">
        </div>
        
        <div class="form-group">
            <label>مدة الاشتراك (أيام):</label>
            <input type="number" name="duration_days" value="30" required>
        </div>
        
        <div class="form-group">
            <label>المميزات:</label>
            <textarea name="features" rows="4">ميزة اختبار 1
ميزة اختبار 2
ميزة اختبار 3</textarea>
        </div>
        
        <div class="form-group">
            <label>الأيقونة:</label>
            <input type="text" name="icon" value="🧪">
        </div>
        
        <div class="form-group">
            <label>اللون:</label>
            <input type="color" name="color" value="#ff6b6b">
        </div>
        
        <div class="form-group">
            <label>ترتيب العرض:</label>
            <input type="number" name="sort_order" value="999">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" checked> نشطة
            </label>
        </div>
        
        <div class="form-group">
            <button type="button" class="btn" onclick="testAjax()">اختبار AJAX</button>
        </div>
    </form>
    
    <div id="result"></div>
    
    <script>
        function testAjax() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            formData.append('action', 'add');
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>جاري الإرسال...</p>';
            
            console.log('Sending data:', Object.fromEntries(formData));
            
            fetch('ajax_subscription_plans.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        <h3>نجح الاختبار! ✅</h3>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>معرف الخطة:</strong> ${data.plan_id || 'غير محدد'}</p>
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">
                        <h3>فشل الاختبار! ❌</h3>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        ${data.error_details ? `<p><strong>تفاصيل الخطأ:</strong> ${JSON.stringify(data.error_details, null, 2)}</p>` : ''}
                    </div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="result error">
                    <h3>خطأ في الشبكة! ❌</h3>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                </div>`;
            });
        }
    </script>
</body>
</html>
