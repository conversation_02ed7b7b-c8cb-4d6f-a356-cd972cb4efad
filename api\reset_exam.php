<?php
header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['exam_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الامتحان مطلوب']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    $courseManager = new CourseManager();
    $userId = $_SESSION['user_id'];
    $examId = $input['exam_id'];
    
    // Get exam details and check access
    $stmt = $db->prepare("
        SELECT ce.*, c.id as course_id 
        FROM course_exams ce
        JOIN courses c ON ce.course_id = c.id
        WHERE ce.id = ? AND ce.is_active = 1
    ");
    $stmt->execute([$examId]);
    $exam = $stmt->fetch();
    
    if (!$exam) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
        exit;
    }
    
    // Check if user has access to this course
    if (!$courseManager->userHasAccess($userId, $exam['course_id'])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول لهذا الامتحان']);
        exit;
    }
    
    // Get all questions for this exam
    $stmt = $db->prepare("
        SELECT id FROM course_exam_questions
        WHERE exam_id = ?
    ");
    $stmt->execute([$examId]);
    $questions = $stmt->fetchAll();
    
    if (empty($questions)) {
        echo json_encode([
            'success' => true,
            'message' => 'لا توجد أسئلة لهذا الامتحان أو تم إعادة التعيين بالفعل',
            'questions_reset' => 0
        ]);
        exit;
    }
    
    // Ensure the simple answers table exists
    try {
        $db->query("SELECT 1 FROM user_exam_answers_simple LIMIT 1");
    } catch (Exception $e) {
        // Create the table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS user_exam_answers_simple (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                question_id INT NOT NULL,
                user_answer TEXT NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                score DECIMAL(5,2) DEFAULT 0,
                attempt_number INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_question (user_id, question_id),
                INDEX idx_attempt (user_id, question_id, attempt_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        error_log("Created user_exam_answers_simple table");
    }

    $db->beginTransaction();

    // Delete all previous answers for this exam
    $questionIds = array_column($questions, 'id');
    $placeholders = str_repeat('?,', count($questionIds) - 1) . '?';

    // Delete from simple answers table
    $stmt = $db->prepare("
        DELETE FROM user_exam_answers_simple
        WHERE user_id = ? AND question_id IN ($placeholders)
    ");
    $deletedRows1 = $stmt->execute(array_merge([$userId], $questionIds));
    $deletedCount1 = $stmt->rowCount();

    // Also delete from old exam answers table if exists
    try {
        $stmt = $db->prepare("
            DELETE FROM user_exam_answers
            WHERE user_id = ? AND exam_id = ?
        ");
        $stmt->execute([$userId, $examId]);
        $deletedCount2 = $stmt->rowCount();
    } catch (Exception $e) {
        // Table might not exist, continue
        $deletedCount2 = 0;
        error_log("Old exam answers table not found: " . $e->getMessage());
    }

    $db->commit();

    error_log("Reset exam for user $userId, exam $examId: deleted $deletedCount1 + $deletedCount2 rows");
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إعادة تعيين الامتحان بنجاح',
        'questions_reset' => count($questions),
        'deleted_rows' => $deletedCount1 + $deletedCount2
    ]);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error resetting exam: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
