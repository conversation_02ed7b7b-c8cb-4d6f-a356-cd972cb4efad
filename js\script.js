// Global variables
let currentStep = 1;
let totalSteps = 5;
let formData = {};

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Show loading screen
    showLoadingScreen();

    // Initialize form validation
    initializeValidation();

    // Initialize multi-step form
    initializeMultiStepForm();

    // Initialize modals
    initializeModals();
});

// Loading Screen Functions
function showLoadingScreen() {
    const loadingScreen = document.querySelector('.loading-screen');
    if (loadingScreen) {
        setTimeout(() => {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                showWelcomeModal();
            }, 500);
        }, 2000);
    }
}

function showWelcomeModal() {
    const welcomeModal = document.getElementById('welcomeModal');
    if (welcomeModal) {
        showModal('welcomeModal');
    }
}

// Modal Functions
function initializeModals() {
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            hideModal(e.target.id);
        }
    });
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
    }
}

// Validation Functions
function initializeValidation() {
    // Real-time validation for all inputs
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            clearFieldError(this);
            if (this.type === 'password') {
                updatePasswordStrength(this);
            }
        });
    });

    // Username uniqueness check (only for signup page)
    const usernameInput = document.getElementById('username');
    if (usernameInput) {
        usernameInput.addEventListener('blur', function() {
            checkUsernameUniqueness(this.value);
        });
    }

    // Email uniqueness check (only for signup page)
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            checkEmailUniqueness(this.value);
        });
    }

    // Login form specific validation
    const loginUsernameInput = document.getElementById('username_email');
    if (loginUsernameInput) {
        loginUsernameInput.addEventListener('blur', function() {
            validateLoginField(this);
        });
    }

    const loginPasswordInput = document.querySelector('#loginForm #password');
    if (loginPasswordInput) {
        loginPasswordInput.addEventListener('blur', function() {
            validateLoginField(this);
        });
    }

    // Password confirmation
    const passwordConfirmInput = document.getElementById('password_confirm');
    if (passwordConfirmInput) {
        passwordConfirmInput.addEventListener('input', function() {
            validatePasswordConfirmation();
        });
    }
}

function validateField(field) {
    const fieldName = field.name;
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }

    // Specific field validations
    switch (fieldName) {
        case 'email':
            if (value && !isValidEmail(value)) {
                isValid = false;
                errorMessage = 'البريد الإلكتروني غير صحيح';
            }
            break;

        case 'password':
            if (value) {
                const passwordErrors = validatePassword(value);
                if (passwordErrors.length > 0) {
                    isValid = false;
                    errorMessage = passwordErrors[0];
                }
            }
            break;

        case 'first_name':
        case 'second_name':
        case 'third_name':
        case 'fourth_name':
            if (value && !isValidArabicName(value)) {
                isValid = false;
                errorMessage = 'يجب أن يحتوي على أحرف عربية فقط';
            }
            break;

        case 'birth_date':
            if (value && !isValidAge(value)) {
                isValid = false;
                errorMessage = 'العمر يجب أن يكون 7 سنوات على الأقل';
            }
            break;

        case 'personal_phone':
        case 'father_phone':
        case 'mother_phone':
            if (value && !isValidPhone(value)) {
                isValid = false;
                errorMessage = 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 010، 011، 012، أو 015 ويكون 11 رقم)';
            }
            break;
    }

    // Update field appearance
    if (isValid) {
        showFieldSuccess(field);
    } else {
        showFieldError(field, errorMessage);
    }

    return isValid;
}

function showFieldError(field, message) {
    field.classList.remove('success');
    field.classList.add('error');

    let errorElement = field.parentNode.querySelector('.error-message');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        field.parentNode.appendChild(errorElement);
    }

    errorElement.textContent = message;
    errorElement.classList.add('show');

    // Hide success message if exists
    const successElement = field.parentNode.querySelector('.success-message');
    if (successElement) {
        successElement.classList.remove('show');
    }
}

function showFieldSuccess(field, message = '') {
    field.classList.remove('error');
    field.classList.add('success');

    // Hide error message
    const errorElement = field.parentNode.querySelector('.error-message');
    if (errorElement) {
        errorElement.classList.remove('show');
    }

    // Show success message if provided
    if (message) {
        let successElement = field.parentNode.querySelector('.success-message');
        if (!successElement) {
            successElement = document.createElement('div');
            successElement.className = 'success-message';
            field.parentNode.appendChild(successElement);
        }

        successElement.textContent = message;
        successElement.classList.add('show');
    }
}

function clearFieldError(field) {
    field.classList.remove('error', 'success');

    const errorElement = field.parentNode.querySelector('.error-message');
    if (errorElement) {
        errorElement.classList.remove('show');
    }

    const successElement = field.parentNode.querySelector('.success-message');
    if (successElement) {
        successElement.classList.remove('show');
    }
}

// Login form specific validation
function validateLoginField(field) {
    const fieldName = field.name;
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }

    // Specific field validations for login
    switch (fieldName) {
        case 'username_email':
            if (value && value.length < 3) {
                isValid = false;
                errorMessage = 'اسم المستخدم أو البريد الإلكتروني قصير جداً';
            }
            break;
        case 'password':
            if (value && value.length < 6) {
                isValid = false;
                errorMessage = 'كلمة المرور قصيرة جداً';
            }
            break;
    }

    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        clearFieldError(field);
    }

    return isValid;
}

// Validation Helper Functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidArabicName(name) {
    const arabicRegex = /^[\u0600-\u06FF\s]+$/;
    return arabicRegex.test(name);
}

function isValidPhone(phone) {
    // Remove spaces and dashes
    phone = phone.replace(/[\s-]/g, '');

    // Check if it's exactly 11 digits and starts with valid prefix
    if (phone.length !== 11) return false;

    const validPrefixes = ['010', '011', '012', '015'];
    const prefix = phone.substring(0, 3);

    return validPrefixes.includes(prefix) && /^\d+$/.test(phone);
}

function isValidAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age >= 7;
}

function validatePassword(password) {
    const errors = [];

    if (password.length < 8) {
        errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }

    if (!/[A-Z]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
    }

    if (!/[a-z]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
    }

    if (!/[0-9]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
    }

    return errors;
}

function updatePasswordStrength(passwordField) {
    const password = passwordField.value;
    const strengthContainer = passwordField.parentNode.querySelector('.password-strength');

    if (!strengthContainer) return;

    let score = 0;
    let strengthText = '';
    let strengthClass = '';

    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;

    if (score <= 2) {
        strengthClass = 'strength-weak';
        strengthText = 'ضعيفة';
    } else if (score <= 4) {
        strengthClass = 'strength-medium';
        strengthText = 'متوسطة';
    } else {
        strengthClass = 'strength-strong';
        strengthText = 'قوية';
    }

    strengthContainer.className = `password-strength ${strengthClass}`;
    strengthContainer.querySelector('.strength-text').textContent = `قوة كلمة المرور: ${strengthText}`;
}

function validatePasswordConfirmation() {
    const password = document.getElementById('password').value;
    const passwordConfirm = document.getElementById('password_confirm').value;
    const confirmField = document.getElementById('password_confirm');

    if (passwordConfirm && password !== passwordConfirm) {
        showFieldError(confirmField, 'كلمات المرور غير متطابقة');
        return false;
    } else if (passwordConfirm && password === passwordConfirm) {
        showFieldSuccess(confirmField, 'كلمات المرور متطابقة');
        return true;
    }

    return true;
}

// AJAX Functions for uniqueness checks
function checkUsernameUniqueness(username) {
    if (!username) return;

    fetch('includes/check_uniqueness.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: 'username',
            value: username
        })
    })
    .then(response => response.json())
    .then(data => {
        const usernameField = document.getElementById('username');
        if (data.exists) {
            showFieldError(usernameField, 'اسم المستخدم موجود بالفعل');
        } else {
            showFieldSuccess(usernameField, 'اسم المستخدم متاح');
        }
    })
    .catch(error => {
        console.error('Error checking username:', error);
    });
}

function checkEmailUniqueness(email) {
    if (!email || !isValidEmail(email)) return;

    fetch('includes/check_uniqueness.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: 'email',
            value: email
        })
    })
    .then(response => response.json())
    .then(data => {
        const emailField = document.getElementById('email');
        if (data.exists) {
            showFieldError(emailField, 'البريد الإلكتروني موجود بالفعل');
        } else {
            showFieldSuccess(emailField, 'البريد الإلكتروني متاح');
        }
    })
    .catch(error => {
        console.error('Error checking email:', error);
    });
}

// Multi-step Form Functions
function initializeMultiStepForm() {
    // Initialize step indicators
    updateStepIndicator();

    // Show first step
    showStep(1);

    // Initialize education level change handler
    const educationLevelSelect = document.getElementById('education_level');
    if (educationLevelSelect) {
        educationLevelSelect.addEventListener('change', function() {
            updateGradeOptions();
            updateSpecializationOptions();
        });
    }

    // Initialize education type change handler
    const educationTypeSelect = document.getElementById('education_type');
    if (educationTypeSelect) {
        educationTypeSelect.addEventListener('change', function() {
            updateGradeOptions();
        });
    }

    // Initialize grade change handler
    const gradeSelect = document.getElementById('grade');
    if (gradeSelect) {
        gradeSelect.addEventListener('change', function() {
            updatePreview();
        });
    }

    // Initialize name change handlers for preview
    const nameFields = ['first_name', 'second_name', 'third_name', 'fourth_name'];
    nameFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('input', function() {
                updateNamePreview();
            });
        }
    });
}

function showStep(step) {
    // Update current step first
    currentStep = step;

    // Hide all steps
    const steps = document.querySelectorAll('.form-step');
    steps.forEach(stepElement => {
        stepElement.classList.remove('active');
    });

    // Show current step
    const currentStepElement = document.getElementById(`step-${step}`);
    if (currentStepElement) {
        currentStepElement.classList.add('active');
    }

    // Update step indicator
    updateStepIndicator();

    // Update navigation buttons
    updateNavigationButtons();
}

function updateStepIndicator() {
    // Update main step indicator
    const stepElements = document.querySelectorAll('.step');
    stepElements.forEach((stepElement, index) => {
        const stepNumber = index + 1;

        stepElement.classList.remove('active', 'completed');

        if (stepNumber < currentStep) {
            stepElement.classList.add('completed');
        } else if (stepNumber === currentStep) {
            stepElement.classList.add('active');
        }

        // Update step number display
        stepElement.textContent = stepNumber;
    });

    // Update right side steps progress
    const stepsProgress = document.querySelectorAll('#steps-progress li');
    stepsProgress.forEach((stepElement, index) => {
        const stepNumber = index + 1;

        stepElement.classList.remove('active', 'completed');

        if (stepNumber < currentStep) {
            stepElement.classList.add('completed');
        } else if (stepNumber === currentStep) {
            stepElement.classList.add('active');
        }
    });
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');

    if (prevBtn) {
        prevBtn.style.display = currentStep === 1 ? 'none' : 'block';
    }

    if (nextBtn) {
        nextBtn.style.display = currentStep === totalSteps ? 'none' : 'block';
    }

    if (submitBtn) {
        submitBtn.style.display = currentStep === totalSteps ? 'block' : 'none';
    }
}

function nextStep() {
    if (validateCurrentStep()) {
        saveCurrentStepData();
        if (currentStep < totalSteps) {
            showStep(currentStep + 1);
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
    }
}

function validateCurrentStep() {
    const currentStepElement = document.getElementById(`step-${currentStep}`);
    if (!currentStepElement) return false;

    const inputs = currentStepElement.querySelectorAll('.form-input[required], .form-select[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });

    // Additional step-specific validations
    switch (currentStep) {
        case 1:
            // Check password confirmation
            if (!validatePasswordConfirmation()) {
                isValid = false;
            }
            break;

        case 2:
            // Check if all name fields are filled and valid
            const nameFields = ['first_name', 'second_name', 'third_name', 'fourth_name'];
            nameFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && !validateField(field)) {
                    isValid = false;
                }
            });
            break;

        case 3:
            // Check phone numbers
            const phoneFields = ['personal_phone', 'father_phone', 'mother_phone'];
            phoneFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && !validateField(field)) {
                    isValid = false;
                }
            });
            break;

        case 4:
            // Check education information
            const educationFields = ['education_level', 'education_type', 'grade'];
            educationFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && !field.value) {
                    showFieldError(field, 'هذا الحقل مطلوب');
                    isValid = false;
                }
            });
            break;

        case 5:
            // Check terms agreement
            const termsCheckbox = document.getElementById('terms_agreement');
            if (termsCheckbox && !termsCheckbox.checked) {
                alert('يجب الموافقة على الشروط والأحكام');
                isValid = false;
            }
            break;
    }

    return isValid;
}

function saveCurrentStepData() {
    const currentStepElement = document.getElementById(`step-${currentStep}`);
    if (!currentStepElement) return;

    const inputs = currentStepElement.querySelectorAll('.form-input, .form-select, input[type="radio"]:checked, input[type="checkbox"]:checked');

    inputs.forEach(input => {
        if (input.type === 'radio' || input.type === 'checkbox') {
            if (input.checked) {
                formData[input.name] = input.value;
            }
        } else {
            formData[input.name] = input.value;
        }
    });
}

// Education Options Functions
function updateGradeOptions() {
    const educationLevel = document.getElementById('education_level').value;
    const educationType = document.getElementById('education_type').value;
    const gradeSelect = document.getElementById('grade');

    if (!gradeSelect || !educationLevel) return;

    // Clear current options
    gradeSelect.innerHTML = '<option value="">اختر الصف</option>';

    const grades = {
        'primary': {
            '5': 'الخامس الابتدائي',
            '6': 'السادس الابتدائي'
        },
        'preparatory': {
            '1': 'الأول الإعدادي',
            '2': 'الثاني الإعدادي',
            '3': 'الثالث الإعدادي'
        },
        'secondary': {
            '1': 'الأول الثانوي',
            '2': 'الثاني الثانوي',
            '3': 'الثالث الثانوي'
        }
    };

    if (grades[educationLevel]) {
        Object.entries(grades[educationLevel]).forEach(([value, text]) => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            gradeSelect.appendChild(option);
        });
    }

    // Update preview
    updatePreview();
}

function updateSpecializationOptions() {
    const educationLevel = document.getElementById('education_level').value;
    const specializationContainer = document.getElementById('specialization_container');

    if (!specializationContainer) return;

    if (educationLevel === 'secondary') {
        specializationContainer.style.display = 'block';
    } else {
        specializationContainer.style.display = 'none';
        // Clear specialization value
        const specializationRadios = document.querySelectorAll('input[name="specialization"]');
        specializationRadios.forEach(radio => radio.checked = false);
    }
}

function updatePreview() {
    const educationLevel = document.getElementById('education_level').value;
    const educationType = document.getElementById('education_type').value;
    const grade = document.getElementById('grade').value;
    const specialization = document.querySelector('input[name="specialization"]:checked');

    const previewElement = document.getElementById('education_preview');
    if (!previewElement) return;

    let previewText = '';

    if (educationLevel && educationType && grade) {
        const levelNames = {
            'primary': 'ابتدائي',
            'preparatory': 'إعدادي',
            'secondary': 'ثانوي'
        };

        const typeNames = {
            'azhari': 'أزهري',
            'general': 'عام'
        };

        const gradeNames = {
            'primary': {
                '5': 'الخامس الابتدائي',
                '6': 'السادس الابتدائي'
            },
            'preparatory': {
                '1': 'الأول الإعدادي',
                '2': 'الثاني الإعدادي',
                '3': 'الثالث الإعدادي'
            },
            'secondary': {
                '1': 'الأول الثانوي',
                '2': 'الثاني الثانوي',
                '3': 'الثالث الثانوي'
            }
        };

        previewText = `${gradeNames[educationLevel][grade]} - ${typeNames[educationType]}`;

        if (educationLevel === 'secondary' && specialization) {
            const specializationNames = {
                'scientific': 'علمي',
                'literary': 'أدبي'
            };
            previewText += ` - ${specializationNames[specialization.value]}`;
        }
    }

    previewElement.textContent = previewText;
}

function updateNamePreview() {
    const firstName = document.getElementById('first_name').value;
    const secondName = document.getElementById('second_name').value;
    const thirdName = document.getElementById('third_name').value;
    const fourthName = document.getElementById('fourth_name').value;

    const previewElement = document.getElementById('name_preview');
    if (!previewElement) return;

    const fullName = [firstName, secondName, thirdName, fourthName]
        .filter(name => name.trim())
        .join(' ');

    previewElement.textContent = fullName || 'لم يتم إدخال الاسم بعد';
}

// Form Submission Functions
function submitRegistrationForm() {
    // Show confirmation modal
    showModal('confirmationModal');
}

function confirmRegistration() {
    // Hide confirmation modal
    hideModal('confirmationModal');

    // Collect all form data
    saveCurrentStepData();

    // Show loading
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'جاري إنشاء الحساب...';
    submitBtn.disabled = true;

    // Submit form data
    fetch('signup.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'register',
            ...formData
        })
    })
    .then(response => {
        // Check if response is ok
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        // Check content type
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            // If not JSON, get text and show error
            return response.text().then(text => {
                console.error('Expected JSON but got:', text.substring(0, 200));
                throw new Error('الخادم لم يرجع استجابة صحيحة');
            });
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            showSuccessModal(data.message);
        } else {
            showErrorModal(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorModal('حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

function showSuccessModal(message) {
    const modal = document.getElementById('successModal');
    const messageElement = modal.querySelector('.modal-text');
    messageElement.textContent = message;
    showModal('successModal');
}

function showErrorModal(message) {
    const modal = document.getElementById('errorModal');
    const messageElement = modal.querySelector('.modal-text');
    messageElement.textContent = message;
    showModal('errorModal');
}

// Login Form Functions
function submitLoginForm() {
    const form = document.getElementById('loginForm');

    // Validate form fields first
    const usernameField = document.getElementById('username_email');
    const passwordField = document.querySelector('#loginForm #password');

    let isValid = true;
    if (!validateLoginField(usernameField)) isValid = false;
    if (!validateLoginField(passwordField)) isValid = false;

    if (!isValid) {
        if (window.notifications) {
            window.notifications.error('يرجى تصحيح الأخطاء في النموذج');
        } else {
            showAlert('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
        return;
    }

    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Show loading state
    let loadingNotification = null;
    if (window.notifications) {
        loadingNotification = window.notifications.showLoading('جاري تسجيل الدخول...');
    }

    submitBtn.textContent = 'جاري تسجيل الدخول...';
    submitBtn.disabled = true;

    fetch('login.php', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            return response.text().then(text => {
                console.error('Expected JSON but got:', text.substring(0, 200));
                throw new Error('الخادم لم يرجع استجابة صحيحة');
            });
        }

        return response.json();
    })
    .then(data => {
        if (loadingNotification) {
            loadingNotification.close();
        }

        if (data.success) {
            if (window.notifications) {
                window.notifications.success(data.message || 'تم تسجيل الدخول بنجاح');
            }

            // Redirect after a short delay
            setTimeout(() => {
                window.location.href = data.redirect || 'page/dashboard';
            }, 1000);
        } else {
            if (window.notifications) {
                window.notifications.error(data.message || 'فشل في تسجيل الدخول');
            } else {
                showAlert(data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (loadingNotification) {
            loadingNotification.close();
        }

        if (window.notifications) {
            window.notifications.error('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
        } else {
            showAlert('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
        }
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} fade-in`;
    alert.textContent = message;

    // Insert at top of form container
    const container = document.querySelector('.form-container');
    container.insertBefore(alert, container.firstChild);

    // Auto remove after 5 seconds
    setTimeout(() => {
        alert.remove();
    }, 5000);
}