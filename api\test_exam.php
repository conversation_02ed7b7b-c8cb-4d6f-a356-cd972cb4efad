<?php
// Simple test for exam API
header('Content-Type: application/json');

try {
    // Set the correct path
    $basePath = dirname(__DIR__);
    require_once $basePath . '/config/config.php';
    require_once $basePath . '/includes/database.php';

    // Get database connection
    $db = DatabaseConnection::getInstance()->getConnection();

    // Test database connection
    $stmt = $db->query("SELECT 1");

    // Check if tables exist
    $tables = ['course_exams', 'course_exam_questions', 'course_weekly_tests', 'course_weekly_test_questions'];
    $tableStatus = [];

    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            $tableStatus[$table] = "exists with $count records";
        } catch (Exception $e) {
            $tableStatus[$table] = "does not exist";
        }
    }

    echo json_encode([
        'success' => true,
        'message' => 'Database connection successful',
        'tables' => $tableStatus,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
