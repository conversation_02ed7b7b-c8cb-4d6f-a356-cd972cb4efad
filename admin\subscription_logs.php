<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

echo "<h2>سجل انتهاء الاشتراكات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
table { width: 100%; border-collapse: collapse; margin: 20px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
th { background: #f8f9fa; }
.btn { padding: 8px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; color: white; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; }
.log-content { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
</style>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Handle manual expiry check
    if (isset($_POST['check_expired'])) {
        $current_time = date('Y-m-d H:i:s');
        
        // Find expired subscriptions
        $stmt = $db->prepare("SELECT id, username, subscription_end_date, current_plan_id 
                             FROM users 
                             WHERE subscription_status = 'active' 
                             AND subscription_end_date <= ?");
        $stmt->execute([$current_time]);
        $expired_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($expired_users) > 0) {
            // Expire the subscriptions
            $stmt = $db->prepare("UPDATE users SET 
                                 subscription_status = 'expired',
                                 current_plan_id = NULL
                                 WHERE subscription_status = 'active' 
                                 AND subscription_end_date <= ?");
            $result = $stmt->execute([$current_time]);
            
            if ($result) {
                echo "<div class='success'>تم انتهاء " . count($expired_users) . " اشتراك بنجاح</div>";
            }
        } else {
            echo "<div class='info'>لا توجد اشتراكات منتهية حالياً</div>";
        }
    }
    
    // Run cron job manually
    if (isset($_POST['run_cron'])) {
        $cron_file = __DIR__ . '/../cron/check_expired_subscriptions.php';
        if (file_exists($cron_file)) {
            ob_start();
            include $cron_file;
            $output = ob_get_clean();
            echo "<div class='success'>تم تشغيل مهمة فحص الاشتراكات</div>";
        } else {
            echo "<div class='error'>ملف المهمة غير موجود</div>";
        }
    }
    
    echo "<div class='info'>⏰ الوقت الحالي: " . date('Y-m-d H:i:s') . "</div>";
    
    // Manual actions
    echo "<div style='margin: 20px 0;'>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<button type='submit' name='check_expired' class='btn btn-warning'>فحص الاشتراكات المنتهية يدوياً</button>";
    echo "</form>";
    
    echo "<form method='POST' style='display: inline;'>";
    echo "<button type='submit' name='run_cron' class='btn btn-primary'>تشغيل مهمة الفحص التلقائي</button>";
    echo "</form>";
    echo "</div>";
    
    // Show subscription statistics
    echo "<h3>إحصائيات الاشتراكات:</h3>";
    
    $stats = [];
    
    // Active subscriptions
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE subscription_status = 'active'");
    $stats['active'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Expired subscriptions
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE subscription_status = 'expired'");
    $stats['expired'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Cancelled subscriptions
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE subscription_status = 'cancelled'");
    $stats['cancelled'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Expiring soon (within 24 hours)
    $tomorrow = date('Y-m-d H:i:s', time() + (24 * 60 * 60));
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users 
                         WHERE subscription_status = 'active' 
                         AND subscription_end_date BETWEEN NOW() AND ?");
    $stmt->execute([$tomorrow]);
    $stats['expiring_soon'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<table>";
    echo "<tr><th>الحالة</th><th>العدد</th></tr>";
    echo "<tr><td>اشتراكات نشطة</td><td style='color: green; font-weight: bold;'>{$stats['active']}</td></tr>";
    echo "<tr><td>اشتراكات منتهية</td><td style='color: red; font-weight: bold;'>{$stats['expired']}</td></tr>";
    echo "<tr><td>اشتراكات ملغية</td><td style='color: orange; font-weight: bold;'>{$stats['cancelled']}</td></tr>";
    echo "<tr><td>تنتهي خلال 24 ساعة</td><td style='color: orange; font-weight: bold;'>{$stats['expiring_soon']}</td></tr>";
    echo "</table>";
    
    // Show recent subscription history
    echo "<h3>سجل الاشتراكات الأخير:</h3>";
    
    try {
        $stmt = $db->query("SELECT sh.*, u.username 
                           FROM subscription_history sh
                           LEFT JOIN users u ON sh.user_id = u.id
                           ORDER BY sh.action_date DESC 
                           LIMIT 20");
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($history) > 0) {
            echo "<table>";
            echo "<tr><th>التاريخ</th><th>المستخدم</th><th>الخطة</th><th>الإجراء</th><th>الملاحظات</th></tr>";
            
            foreach ($history as $record) {
                $action_color = '';
                switch ($record['action']) {
                    case 'activated': $action_color = 'color: green;'; break;
                    case 'expired': $action_color = 'color: red;'; break;
                    case 'cancelled': $action_color = 'color: orange;'; break;
                    case 'renewed': $action_color = 'color: blue;'; break;
                }
                
                echo "<tr>";
                echo "<td>" . date('Y-m-d H:i', strtotime($record['action_date'])) . "</td>";
                echo "<td>{$record['username']}</td>";
                echo "<td>{$record['plan_name']}</td>";
                echo "<td style='$action_color'>{$record['action']}</td>";
                echo "<td>" . substr($record['notes'], 0, 50) . "...</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='info'>لا يوجد سجل للاشتراكات</div>";
        }
    } catch (Exception $e) {
        echo "<div class='info'>جدول سجل الاشتراكات غير موجود بعد</div>";
    }
    
    // Show users with active subscriptions
    echo "<h3>المستخدمين ذوي الاشتراكات النشطة:</h3>";
    
    $stmt = $db->query("SELECT u.id, u.username, u.subscription_end_date, sp.name as plan_name,
                       TIMESTAMPDIFF(MINUTE, NOW(), u.subscription_end_date) as minutes_remaining
                       FROM users u
                       LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                       WHERE u.subscription_status = 'active'
                       ORDER BY u.subscription_end_date ASC");
    $active_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($active_users) > 0) {
        echo "<table>";
        echo "<tr><th>المستخدم</th><th>الخطة</th><th>تاريخ الانتهاء</th><th>الوقت المتبقي</th><th>الحالة</th></tr>";
        
        foreach ($active_users as $user) {
            $remaining_class = '';
            $remaining_text = '';
            
            if ($user['minutes_remaining'] <= 0) {
                $remaining_class = 'style="background: #ffcccc;"';
                $remaining_text = 'منتهي (يحتاج تحديث)';
            } elseif ($user['minutes_remaining'] <= 60) {
                $remaining_class = 'style="background: #fff3cd;"';
                $remaining_text = $user['minutes_remaining'] . ' دقيقة';
            } elseif ($user['minutes_remaining'] <= 1440) { // 24 hours
                $remaining_class = 'style="background: #fff3cd;"';
                $remaining_text = floor($user['minutes_remaining'] / 60) . ' ساعة';
            } else {
                $remaining_text = floor($user['minutes_remaining'] / 1440) . ' يوم';
            }
            
            echo "<tr $remaining_class>";
            echo "<td>{$user['username']} (ID: {$user['id']})</td>";
            echo "<td>{$user['plan_name']}</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($user['subscription_end_date'])) . "</td>";
            echo "<td>$remaining_text</td>";
            echo "<td>" . ($user['minutes_remaining'] <= 0 ? 'منتهي' : 'نشط') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='info'>لا توجد اشتراكات نشطة حالياً</div>";
    }
    
    // Show cron log if exists
    $log_file = __DIR__ . '/../cron/logs/subscription_expiry.log';
    if (file_exists($log_file)) {
        echo "<h3>سجل مهمة فحص الاشتراكات:</h3>";
        $log_content = file_get_contents($log_file);
        
        // Show only last 50 lines
        $lines = explode("\n", $log_content);
        $recent_lines = array_slice($lines, -50);
        $recent_content = implode("\n", $recent_lines);
        
        echo "<div class='log-content'>$recent_content</div>";
        echo "<p><small>عرض آخر 50 سطر من السجل</small></p>";
    } else {
        echo "<div class='info'>ملف سجل المهام غير موجود بعد</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<p>
    <a href="subscription_plans.php" class="btn btn-primary">إدارة الخطط</a>
    <a href="test_subscription_expiry.php" class="btn btn-success">اختبار انتهاء الاشتراك</a>
</p>
