<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set admin session for testing
$_SESSION['admin_id'] = 1;

echo "<h2>اختبار انتهاء الاشتراكات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
.warning { color: orange; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
table { width: 100%; border-collapse: collapse; margin: 20px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
th { background: #f8f9fa; }
.btn { padding: 8px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; color: white; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; }
</style>";

$message = '';
$error = '';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // Handle actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_test_subscription':
                    // Create a test subscription that expires in 1 minute
                    $test_user_id = $_POST['user_id'];
                    $plan_id = $_POST['plan_id'];
                    $expiry_minutes = intval($_POST['expiry_minutes']);
                    
                    $end_date = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));
                    
                    // Update user subscription
                    $stmt = $db->prepare("UPDATE users SET 
                                         subscription_status = 'active',
                                         current_plan_id = ?,
                                         subscription_end_date = ?
                                         WHERE id = ?");
                    $result = $stmt->execute([$plan_id, $end_date, $test_user_id]);
                    
                    if ($result) {
                        $message = "تم إنشاء اشتراك تجريبي للمستخدم $test_user_id ينتهي في $expiry_minutes دقيقة (في $end_date)";
                    } else {
                        $error = "فشل في إنشاء الاشتراك التجريبي";
                    }
                    break;
                    
                case 'check_expired':
                    // Check and expire subscriptions
                    $current_time = date('Y-m-d H:i:s');
                    
                    // Find expired subscriptions
                    $stmt = $db->prepare("SELECT id, username, subscription_end_date, current_plan_id 
                                         FROM users 
                                         WHERE subscription_status = 'active' 
                                         AND subscription_end_date <= ?");
                    $stmt->execute([$current_time]);
                    $expired_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (count($expired_users) > 0) {
                        // Expire the subscriptions
                        $stmt = $db->prepare("UPDATE users SET 
                                             subscription_status = 'expired',
                                             current_plan_id = NULL
                                             WHERE subscription_status = 'active' 
                                             AND subscription_end_date <= ?");
                        $result = $stmt->execute([$current_time]);
                        
                        if ($result) {
                            $message = "تم انتهاء " . count($expired_users) . " اشتراك";
                            foreach ($expired_users as $user) {
                                $message .= "<br>- المستخدم: {$user['username']} (ID: {$user['id']}) - انتهى في: {$user['subscription_end_date']}";
                            }
                        } else {
                            $error = "فشل في تحديث الاشتراكات المنتهية";
                        }
                    } else {
                        $message = "لا توجد اشتراكات منتهية حالياً";
                    }
                    break;
                    
                case 'reset_user':
                    // Reset user subscription
                    $user_id = $_POST['user_id'];
                    $stmt = $db->prepare("UPDATE users SET 
                                         subscription_status = NULL,
                                         current_plan_id = NULL,
                                         subscription_end_date = NULL
                                         WHERE id = ?");
                    $result = $stmt->execute([$user_id]);
                    
                    if ($result) {
                        $message = "تم إعادة تعيين اشتراك المستخدم $user_id";
                    } else {
                        $error = "فشل في إعادة تعيين الاشتراك";
                    }
                    break;
            }
        }
    }
    
    if ($message) {
        echo "<div class='success'>$message</div>";
    }
    
    if ($error) {
        echo "<div class='error'>$error</div>";
    }
    
    // Show current time
    echo "<div class='info'>⏰ الوقت الحالي: " . date('Y-m-d H:i:s') . "</div>";
    
    // Get all users with their subscription status
    $stmt = $db->query("SELECT u.id, u.username, u.subscription_status, u.subscription_end_date, u.current_plan_id,
                       sp.name as plan_name
                       FROM users u
                       LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                       ORDER BY u.id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get available plans
    $stmt = $db->query("SELECT id, name FROM subscription_plans WHERE is_active = 1");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>المستخدمين وحالة الاشتراك:</h3>";
    echo "<table>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>حالة الاشتراك</th><th>الخطة</th><th>تاريخ الانتهاء</th><th>الحالة</th><th>الإجراءات</th></tr>";
    
    foreach ($users as $user) {
        $status_class = '';
        $status_text = $user['subscription_status'] ?: 'غير مشترك';
        
        if ($user['subscription_status'] === 'active') {
            if ($user['subscription_end_date'] && strtotime($user['subscription_end_date']) <= time()) {
                $status_class = 'style="background: #ffcccc;"';
                $status_text = 'منتهي (يحتاج تحديث)';
            } else {
                $status_class = 'style="background: #ccffcc;"';
                $status_text = 'نشط';
            }
        } elseif ($user['subscription_status'] === 'expired') {
            $status_class = 'style="background: #ffcccc;"';
            $status_text = 'منتهي';
        } elseif ($user['subscription_status'] === 'cancelled') {
            $status_class = 'style="background: #ffffcc;"';
            $status_text = 'ملغي';
        }
        
        echo "<tr $status_class>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>$status_text</td>";
        echo "<td>" . ($user['plan_name'] ?: 'لا يوجد') . "</td>";
        echo "<td>" . ($user['subscription_end_date'] ?: 'لا يوجد') . "</td>";
        
        // Time remaining
        if ($user['subscription_end_date']) {
            $remaining = strtotime($user['subscription_end_date']) - time();
            if ($remaining > 0) {
                $remaining_text = floor($remaining / 60) . " دقيقة";
            } else {
                $remaining_text = "منتهي منذ " . abs(floor($remaining / 60)) . " دقيقة";
            }
        } else {
            $remaining_text = "لا يوجد";
        }
        echo "<td>$remaining_text</td>";
        
        // Actions
        echo "<td>";
        echo "<form method='POST' style='display: inline;'>";
        echo "<input type='hidden' name='action' value='reset_user'>";
        echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
        echo "<button type='submit' class='btn btn-warning'>إعادة تعيين</button>";
        echo "</form>";
        echo "</td>";
        
        echo "</tr>";
    }
    echo "</table>";
    
    // Create test subscription form
    echo "<h3>إنشاء اشتراك تجريبي:</h3>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='create_test_subscription'>";
    echo "<label>المستخدم: ";
    echo "<select name='user_id' required>";
    foreach ($users as $user) {
        echo "<option value='{$user['id']}'>{$user['username']} (ID: {$user['id']})</option>";
    }
    echo "</select></label><br><br>";
    
    echo "<label>الخطة: ";
    echo "<select name='plan_id' required>";
    foreach ($plans as $plan) {
        echo "<option value='{$plan['id']}'>{$plan['name']}</option>";
    }
    echo "</select></label><br><br>";
    
    echo "<label>ينتهي خلال (دقائق): ";
    echo "<input type='number' name='expiry_minutes' value='1' min='1' max='60' required>";
    echo "</label><br><br>";
    
    echo "<button type='submit' class='btn btn-primary'>إنشاء اشتراك تجريبي</button>";
    echo "</form>";
    
    // Check expired subscriptions
    echo "<h3>فحص الاشتراكات المنتهية:</h3>";
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='check_expired'>";
    echo "<button type='submit' class='btn btn-danger'>فحص وإنهاء الاشتراكات المنتهية</button>";
    echo "</form>";
    
    echo "<div class='info'>";
    echo "<h4>كيفية الاستخدام:</h4>";
    echo "<ol>";
    echo "<li>اختر مستخدم وخطة وحدد المدة (بالدقائق)</li>";
    echo "<li>اضغط 'إنشاء اشتراك تجريبي' لإنشاء اشتراك ينتهي خلال الوقت المحدد</li>";
    echo "<li>انتظر حتى انتهاء الوقت</li>";
    echo "<li>اضغط 'فحص وإنهاء الاشتراكات المنتهية' لتحديث حالة الاشتراكات</li>";
    echo "<li>تحقق من تغيير حالة المستخدم من 'نشط' إلى 'منتهي'</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div class='error'>الملف: " . $e->getFile() . "</div>";
    echo "<div class='error'>السطر: " . $e->getLine() . "</div>";
}
?>

<p><a href="subscription_plans.php">العودة لإدارة الخطط</a></p>
