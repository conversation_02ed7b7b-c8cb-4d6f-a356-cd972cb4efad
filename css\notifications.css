/**
 * Enhanced Notification System Styles
 * Beautiful, responsive notification components
 */

/* Toast Notifications */
.notification-toast {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.2);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(70, 130, 180, 0.2);
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.notification-toast-success {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.notification-toast-error {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, #fff8f8 0%, #ffeaea 100%);
}

.notification-toast-warning {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #fffdf5 0%, #fff3cd 100%);
}

.notification-toast-info {
    border-left: 4px solid #4682B4;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

.notification-toast-loading {
    border-left: 4px solid #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.toast-content {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.toast-content span {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    line-height: 1.4;
}

.toast-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.toast-title {
    font-weight: 600;
    font-size: 14px;
    color: #2c3e50;
    line-height: 1.3;
}

.toast-message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* Real-time notification styling */
.notification-realtime {
    border-left-width: 6px;
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1), pulse 0.6s ease 0.4s;
}

.notification-realtime .toast-content {
    padding: 18px 22px;
}

.notification-realtime .toast-icon {
    font-size: 20px;
    animation: bounce 0.6s ease 0.2s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-4px);
    }
    60% {
        transform: translateY(-2px);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 20px 40px rgba(70, 130, 180, 0.2);
    }
    50% {
        box-shadow: 0 25px 50px rgba(70, 130, 180, 0.4);
    }
    100% {
        box-shadow: 0 20px 40px rgba(70, 130, 180, 0.2);
    }
}

.toast-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    transform: scale(1.1);
}

/* Confirmation Modal */
.notification-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.confirm-dialog {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.3);
    min-width: 400px;
    max-width: 500px;
    position: relative;
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.confirm-header {
    padding: 25px 30px 15px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.confirm-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.confirm-body {
    padding: 25px 30px;
}

.confirm-body p {
    margin: 0;
    color: #555;
    font-size: 16px;
    line-height: 1.6;
}

.confirm-actions {
    padding: 20px 30px 30px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.confirm-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.confirm-cancel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    border: 2px solid rgba(108, 117, 125, 0.2);
}

.confirm-cancel:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    color: #495057;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.2);
}

.confirm-ok {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.confirm-ok:hover {
    background: linear-gradient(135deg, #4682B4 0%, #2c5282 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Loading spinner for toasts */
.loading-spinner-small {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(70, 130, 180, 0.2);
    border-top: 2px solid #4682B4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    flex-shrink: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .notification-toast {
        min-width: auto;
        max-width: none;
        margin: 0 15px;
    }
    
    .confirm-dialog {
        min-width: auto;
        margin: 0 20px;
        max-width: calc(100vw - 40px);
    }
    
    .confirm-header {
        padding: 20px 25px 12px;
    }
    
    .confirm-header h3 {
        font-size: 18px;
    }
    
    .confirm-body {
        padding: 20px 25px;
    }
    
    .confirm-body p {
        font-size: 15px;
    }
    
    .confirm-actions {
        padding: 15px 25px 25px;
        flex-direction: column-reverse;
    }
    
    .confirm-btn {
        width: 100%;
        padding: 14px 20px;
    }
}

@media (max-width: 480px) {
    .toast-content {
        padding: 12px 15px;
        gap: 10px;
    }
    
    .toast-content span {
        font-size: 13px;
    }
    
    .toast-icon {
        font-size: 16px;
    }
    
    .confirm-dialog {
        margin: 0 15px;
    }
}
