<?php
require_once __DIR__ . '/../config/config.php';

header('Content-Type: application/json');

try {
    $paymob_config = PAYMENT_GATEWAYS['paymob'];
    
    echo "<h2>🧪 اختبار إعدادات Paymob</h2>";
    
    // Test 1: Authentication
    echo "<h3>1. اختبار المصادقة</h3>";
    
    $auth_data = ['api_key' => $paymob_config['api_key']];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $paymob_config['base_url'] . '/auth/tokens');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($auth_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    
    $auth_response = curl_exec($ch);
    $auth_result = json_decode($auth_response, true);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ خطأ في الاتصال: $curl_error</p>";
    } elseif ($http_code !== 201 && $http_code !== 200) {
        echo "<p style='color: red;'>❌ خطأ HTTP: $http_code</p>";
        echo "<pre>" . htmlspecialchars($auth_response) . "</pre>";
    } elseif (!$auth_result || !isset($auth_result['token'])) {
        echo "<p style='color: red;'>❌ فشل في الحصول على token</p>";
        echo "<pre>" . htmlspecialchars($auth_response) . "</pre>";
    } else {
        echo "<p style='color: green;'>✅ تم الحصول على token بنجاح</p>";
        $auth_token = $auth_result['token'];
        
        // Test 2: Create test order
        echo "<h3>2. اختبار إنشاء طلب</h3>";
        
        $order_data = [
            'auth_token' => $auth_token,
            'delivery_needed' => false,
            'amount_cents' => 10000, // 100 EGP
            'currency' => 'EGP',
            'items' => [
                [
                    'name' => 'Test Subscription',
                    'amount_cents' => 10000,
                    'description' => 'Test subscription for Paymob integration',
                    'quantity' => 1
                ]
            ]
        ];
        
        curl_setopt($ch, CURLOPT_URL, $paymob_config['base_url'] . '/ecommerce/orders');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
        
        $order_response = curl_exec($ch);
        $order_result = json_decode($order_response, true);
        $curl_error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($curl_error) {
            echo "<p style='color: red;'>❌ خطأ في الاتصال: $curl_error</p>";
        } elseif ($http_code !== 201 && $http_code !== 200) {
            echo "<p style='color: red;'>❌ خطأ HTTP: $http_code</p>";
            echo "<pre>" . htmlspecialchars($order_response) . "</pre>";
        } elseif (!$order_result || !isset($order_result['id'])) {
            echo "<p style='color: red;'>❌ فشل في إنشاء الطلب</p>";
            echo "<pre>" . htmlspecialchars($order_response) . "</pre>";
        } else {
            echo "<p style='color: green;'>✅ تم إنشاء الطلب بنجاح - ID: " . $order_result['id'] . "</p>";
            $paymob_order_id = $order_result['id'];
            
            // Test 3: Create payment key
            echo "<h3>3. اختبار إنشاء مفتاح الدفع</h3>";
            
            $payment_key_data = [
                'auth_token' => $auth_token,
                'amount_cents' => 10000,
                'expiration' => 3600,
                'order_id' => $paymob_order_id,
                'billing_data' => [
                    'apartment' => 'NA',
                    'email' => '<EMAIL>',
                    'floor' => 'NA',
                    'first_name' => 'Test',
                    'street' => 'NA',
                    'building' => 'NA',
                    'phone_number' => '+201000000000',
                    'shipping_method' => 'NA',
                    'postal_code' => 'NA',
                    'city' => 'Cairo',
                    'country' => 'EG',
                    'last_name' => 'User',
                    'state' => 'Cairo'
                ],
                'currency' => 'EGP',
                'integration_id' => intval($paymob_config['integration_id']),
                'lock_order_when_paid' => true
            ];
            
            curl_setopt($ch, CURLOPT_URL, $paymob_config['base_url'] . '/acceptance/payment_keys');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_key_data));
            
            $payment_key_response = curl_exec($ch);
            $payment_key_result = json_decode($payment_key_response, true);
            $curl_error = curl_error($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($curl_error) {
                echo "<p style='color: red;'>❌ خطأ في الاتصال: $curl_error</p>";
            } elseif ($http_code !== 201 && $http_code !== 200) {
                echo "<p style='color: red;'>❌ خطأ HTTP: $http_code</p>";
                echo "<pre>" . htmlspecialchars($payment_key_response) . "</pre>";
            } elseif (!$payment_key_result || !isset($payment_key_result['token'])) {
                echo "<p style='color: red;'>❌ فشل في إنشاء مفتاح الدفع</p>";
                echo "<pre>" . htmlspecialchars($payment_key_response) . "</pre>";
            } else {
                echo "<p style='color: green;'>✅ تم إنشاء مفتاح الدفع بنجاح</p>";
                $payment_token = $payment_key_result['token'];
                
                // Generate test payment URL
                $payment_url = "https://accept.paymob.com/api/acceptance/iframes/{$paymob_config['iframe_id']}?payment_token={$payment_token}";
                
                echo "<h3>4. رابط الدفع التجريبي</h3>";
                echo "<p><a href='$payment_url' target='_blank' style='color: blue;'>اختبار الدفع (100 جنيه)</a></p>";
                echo "<p style='color: orange;'>⚠️ هذا رابط تجريبي - لا تستخدم بيانات حقيقية</p>";
            }
        }
    }
    
    curl_close($ch);
    
    // Display configuration
    echo "<h3>5. الإعدادات الحالية</h3>";
    echo "<ul>";
    echo "<li><strong>API Key:</strong> " . substr($paymob_config['api_key'], 0, 20) . "...</li>";
    echo "<li><strong>Integration ID:</strong> " . $paymob_config['integration_id'] . "</li>";
    echo "<li><strong>Iframe ID:</strong> " . $paymob_config['iframe_id'] . "</li>";
    echo "<li><strong>Base URL:</strong> " . $paymob_config['base_url'] . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    direction: rtl;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #4682B4;
    padding-bottom: 10px;
}

pre {
    background: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    max-height: 200px;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

a {
    background: #4682B4;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    display: inline-block;
}

a:hover {
    background: #20B2AA;
}
</style>
