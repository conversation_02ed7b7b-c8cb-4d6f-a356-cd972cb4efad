<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

try {
    $db = Database::getInstance()->getConnection();
    
    $courseId = $_GET['course_id'] ?? null;
    $userId = $_GET['user_id'] ?? $_SESSION['user_id'] ?? null;
    
    if (!$courseId || !$userId) {
        echo json_encode(['success' => false, 'message' => 'معرف الكورس أو المستخدم مفقود']);
        exit;
    }
    
    // Check if completion note exists
    $stmt = $db->prepare("
        SELECT completion_date, note 
        FROM course_completion_notes 
        WHERE user_id = ? AND course_id = ?
    ");
    $stmt->execute([$userId, $courseId]);
    $completionNote = $stmt->fetch();
    
    if ($completionNote) {
        echo json_encode([
            'success' => true,
            'is_completed' => true,
            'completion_date' => $completionNote['completion_date'],
            'note' => $completionNote['note']
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'is_completed' => false
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error checking course completion: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم'
    ]);
}
?>
