<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    echo "🔄 إنشاء جدول course_completion_notes...\n";
    
    $sql = "
        CREATE TABLE IF NOT EXISTS course_completion_notes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            completion_date DATETIME NOT NULL,
            note TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_completion (user_id, course_id),
            INDEX idx_user_id (user_id),
            INDEX idx_course_id (course_id),
            INDEX idx_completion_date (completion_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $result = $db->exec($sql);
    
    echo "✅ تم إنشاء الجدول بنجاح!\n";
    
    // التحقق من وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'course_completion_notes'");
    if ($stmt->rowCount() > 0) {
        echo "✅ تم التحقق من وجود الجدول!\n";
    } else {
        echo "❌ الجدول غير موجود!\n";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "🎉 انتهى!\n";
?>
