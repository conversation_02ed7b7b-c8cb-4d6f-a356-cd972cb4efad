-- Curriculum Subjects Table
-- This table stores the subjects/sections for each grade and education type

CREATE TABLE IF NOT EXISTS curriculum_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم القسم/المادة',
    name_en VARCHAR(100) NULL COMMENT 'اسم القسم بالإنجليزية',
    description TEXT NULL COMMENT 'وصف القسم',
    education_level ENUM('primary', 'preparatory', 'secondary') NOT NULL COMMENT 'المرحلة التعليمية',
    education_type ENUM('azhari', 'general') NOT NULL COMMENT 'نوع التعليم',
    grade VARCHAR(10) NOT NULL COMMENT 'الصف',
    specialization ENUM('scientific', 'literary', 'all') DEFAULT 'all' COMMENT 'التخصص للثانوي',
    icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة القسم',
    color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون القسم',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة القسم',
    created_by INT NULL COMMENT 'منشئ القسم',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_education (education_level, education_type, grade),
    INDEX idx_specialization (specialization),
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order),
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Lessons table for each subject
CREATE TABLE IF NOT EXISTS lessons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL COMMENT 'معرف القسم',
    title VARCHAR(255) NOT NULL COMMENT 'عنوان الدرس',
    description TEXT NULL COMMENT 'وصف الدرس',
    lesson_number INT NOT NULL COMMENT 'رقم الدرس',
    is_free BOOLEAN DEFAULT FALSE COMMENT 'هل الدرس مجاني',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الدرس',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    created_by INT NULL COMMENT 'منشئ الدرس',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (subject_id) REFERENCES curriculum_subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_subject_id (subject_id),
    INDEX idx_lesson_number (lesson_number),
    INDEX idx_is_free (is_free),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    UNIQUE KEY unique_subject_lesson (subject_id, lesson_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Lesson videos table
CREATE TABLE IF NOT EXISTS lesson_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lesson_id INT NOT NULL COMMENT 'معرف الدرس',
    title VARCHAR(255) NOT NULL COMMENT 'عنوان الفيديو',
    description TEXT NULL COMMENT 'وصف الفيديو',
    youtube_url VARCHAR(500) NOT NULL COMMENT 'رابط اليوتيوب',
    youtube_id VARCHAR(50) NULL COMMENT 'معرف الفيديو في اليوتيوب',
    duration_minutes INT DEFAULT 0 COMMENT 'مدة الفيديو بالدقائق',
    video_order INT DEFAULT 0 COMMENT 'ترتيب الفيديو',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الفيديو',
    created_by INT NULL COMMENT 'منشئ الفيديو',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_video_order (video_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Lesson exercises table
CREATE TABLE IF NOT EXISTS lesson_exercises (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lesson_id INT NOT NULL COMMENT 'معرف الدرس',
    title VARCHAR(255) NOT NULL COMMENT 'عنوان التدريب',
    description TEXT NULL COMMENT 'وصف التدريب',
    exercise_order INT DEFAULT 0 COMMENT 'ترتيب التدريب',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة التدريب',
    created_by INT NULL COMMENT 'منشئ التدريب',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_exercise_order (exercise_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Exercise questions table
CREATE TABLE IF NOT EXISTS exercise_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exercise_id INT NOT NULL COMMENT 'معرف التدريب',
    question_text TEXT NOT NULL COMMENT 'نص السؤال',
    question_type ENUM('true_false', 'multiple_choice') NOT NULL COMMENT 'نوع السؤال',
    options JSON NULL COMMENT 'خيارات السؤال للاختيار المتعدد',
    correct_answer TEXT NOT NULL COMMENT 'الإجابة الصحيحة',
    explanation TEXT NULL COMMENT 'شرح الإجابة',
    question_order INT DEFAULT 0 COMMENT 'ترتيب السؤال',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة السؤال',
    created_by INT NULL COMMENT 'منشئ السؤال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (exercise_id) REFERENCES lesson_exercises(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_exercise_id (exercise_id),
    INDEX idx_question_order (question_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Lesson exams table
CREATE TABLE IF NOT EXISTS lesson_exams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lesson_id INT NOT NULL COMMENT 'معرف الدرس',
    title VARCHAR(255) NOT NULL COMMENT 'عنوان الامتحان',
    description TEXT NULL COMMENT 'وصف الامتحان',
    duration_minutes INT DEFAULT 60 COMMENT 'مدة الامتحان بالدقائق',
    passing_score DECIMAL(5,2) DEFAULT 60.00 COMMENT 'درجة النجاح',
    exam_order INT DEFAULT 0 COMMENT 'ترتيب الامتحان',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الامتحان',
    created_by INT NULL COMMENT 'منشئ الامتحان',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_exam_order (exam_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Exam questions table
CREATE TABLE IF NOT EXISTS exam_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exam_id INT NOT NULL COMMENT 'معرف الامتحان',
    question_text TEXT NOT NULL COMMENT 'نص السؤال',
    question_type ENUM('true_false', 'multiple_choice') NOT NULL COMMENT 'نوع السؤال',
    options JSON NULL COMMENT 'خيارات السؤال للاختيار المتعدد',
    correct_answer TEXT NOT NULL COMMENT 'الإجابة الصحيحة',
    explanation TEXT NULL COMMENT 'شرح الإجابة',
    points DECIMAL(5,2) DEFAULT 1.00 COMMENT 'نقاط السؤال',
    question_order INT DEFAULT 0 COMMENT 'ترتيب السؤال',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة السؤال',
    created_by INT NULL COMMENT 'منشئ السؤال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (exam_id) REFERENCES lesson_exams(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_exam_id (exam_id),
    INDEX idx_question_order (question_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Lesson summaries table
CREATE TABLE IF NOT EXISTS lesson_summaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    lesson_id INT NOT NULL COMMENT 'معرف الدرس',
    title VARCHAR(255) NOT NULL COMMENT 'عنوان الملخص',
    description TEXT NULL COMMENT 'وصف الملخص',
    file_path VARCHAR(500) NOT NULL COMMENT 'مسار ملف PDF',
    file_name VARCHAR(255) NOT NULL COMMENT 'اسم الملف',
    file_size INT DEFAULT 0 COMMENT 'حجم الملف بالبايت',
    summary_order INT DEFAULT 0 COMMENT 'ترتيب الملخص',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الملخص',
    created_by INT NULL COMMENT 'منشئ الملخص',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_summary_order (summary_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User progress tracking tables
CREATE TABLE IF NOT EXISTS user_lesson_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    lesson_id INT NOT NULL COMMENT 'معرف الدرس',
    is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الدرس',
    completion_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الإكمال',
    last_accessed_at TIMESTAMP NULL COMMENT 'آخر وصول للدرس',
    completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_lesson (user_id, lesson_id),
    INDEX idx_user_id (user_id),
    INDEX idx_lesson_id (lesson_id),
    INDEX idx_is_completed (is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS user_video_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    video_id INT NOT NULL COMMENT 'معرف الفيديو',
    is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الفيديو',
    watch_time_seconds INT DEFAULT 0 COMMENT 'وقت المشاهدة بالثواني',
    completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES lesson_videos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_video (user_id, video_id),
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_is_completed (is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS user_exercise_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    exercise_id INT NOT NULL COMMENT 'معرف التدريب',
    is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال التدريب',
    score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'النتيجة',
    total_questions INT DEFAULT 0 COMMENT 'إجمالي الأسئلة',
    correct_answers INT DEFAULT 0 COMMENT 'الإجابات الصحيحة',
    completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (exercise_id) REFERENCES lesson_exercises(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_exercise (user_id, exercise_id),
    INDEX idx_user_id (user_id),
    INDEX idx_exercise_id (exercise_id),
    INDEX idx_is_completed (is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS user_exam_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    exam_id INT NOT NULL COMMENT 'معرف الامتحان',
    is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الامتحان',
    score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'النتيجة',
    total_questions INT DEFAULT 0 COMMENT 'إجمالي الأسئلة',
    correct_answers INT DEFAULT 0 COMMENT 'الإجابات الصحيحة',
    is_passed BOOLEAN DEFAULT FALSE COMMENT 'هل نجح في الامتحان',
    time_taken_minutes INT DEFAULT 0 COMMENT 'الوقت المستغرق بالدقائق',
    completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES lesson_exams(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_exam (user_id, exam_id),
    INDEX idx_user_id (user_id),
    INDEX idx_exam_id (exam_id),
    INDEX idx_is_completed (is_completed),
    INDEX idx_is_passed (is_passed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS user_summary_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    summary_id INT NOT NULL COMMENT 'معرف الملخص',
    is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الملخص',
    completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (summary_id) REFERENCES lesson_summaries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_summary (user_id, summary_id),
    INDEX idx_user_id (user_id),
    INDEX idx_summary_id (summary_id),
    INDEX idx_is_completed (is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default subjects for Primary Education (Azhari & General)
-- المرحلة الابتدائية - أزهري وعام: نحو، نصوص، قراءة، إملاء، خط، تعبير

INSERT INTO curriculum_subjects (name, name_en, education_level, education_type, grade, specialization, icon, color, sort_order) VALUES
-- الابتدائي الأزهري
('نحو', 'Grammar', 'primary', 'azhari', '4', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'azhari', '4', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'azhari', '4', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'azhari', '4', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'azhari', '4', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'azhari', '4', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'azhari', '5', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'azhari', '5', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'azhari', '5', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'azhari', '5', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'azhari', '5', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'azhari', '5', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'azhari', '6', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'azhari', '6', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'azhari', '6', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'azhari', '6', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'azhari', '6', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'azhari', '6', 'all', '💭', '#20B2AA', 6),

-- الابتدائي العام
('نحو', 'Grammar', 'primary', 'general', '1', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'general', '1', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'general', '1', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'general', '1', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'general', '1', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'general', '1', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'general', '2', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'general', '2', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'general', '2', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'general', '2', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'general', '2', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'general', '2', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'general', '3', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'general', '3', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'general', '3', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'general', '3', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'general', '3', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'general', '3', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'general', '4', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'general', '4', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'general', '4', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'general', '4', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'general', '4', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'general', '4', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'general', '5', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'general', '5', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'general', '5', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'general', '5', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'general', '5', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'general', '5', 'all', '💭', '#20B2AA', 6),

('نحو', 'Grammar', 'primary', 'general', '6', 'all', '📝', '#2E8B57', 1),
('نصوص', 'Texts', 'primary', 'general', '6', 'all', '📖', '#4682B4', 2),
('قراءة', 'Reading', 'primary', 'general', '6', 'all', '📚', '#FF6347', 3),
('إملاء', 'Dictation', 'primary', 'general', '6', 'all', '✍️', '#9370DB', 4),
('خط', 'Calligraphy', 'primary', 'general', '6', 'all', '🖋️', '#DAA520', 5),
('تعبير', 'Expression', 'primary', 'general', '6', 'all', '💭', '#20B2AA', 6),

-- المرحلة الإعدادية - أزهري وعام: نحو، مطالعة، نصوص، إملاء، إنشاء، خط
-- الإعدادي الأزهري
('نحو', 'Grammar', 'preparatory', 'azhari', '1', 'all', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'preparatory', 'azhari', '1', 'all', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'preparatory', 'azhari', '1', 'all', '📖', '#4682B4', 3),
('إملاء', 'Dictation', 'preparatory', 'azhari', '1', 'all', '✍️', '#9370DB', 4),
('إنشاء', 'Composition', 'preparatory', 'azhari', '1', 'all', '📄', '#FF8C00', 5),
('خط', 'Calligraphy', 'preparatory', 'azhari', '1', 'all', '🖋️', '#DAA520', 6),

-- الثاني والثالث الإعدادي الأزهري (مع إضافة الصرف)
('نحو', 'Grammar', 'preparatory', 'azhari', '2', 'all', '📝', '#2E8B57', 1),
('صرف', 'Morphology', 'preparatory', 'azhari', '2', 'all', '🔤', '#8B4513', 2),
('مطالعة', 'Reading', 'preparatory', 'azhari', '2', 'all', '📚', '#FF6347', 3),
('نصوص', 'Texts', 'preparatory', 'azhari', '2', 'all', '📖', '#4682B4', 4),
('إملاء', 'Dictation', 'preparatory', 'azhari', '2', 'all', '✍️', '#9370DB', 5),
('إنشاء', 'Composition', 'preparatory', 'azhari', '2', 'all', '📄', '#FF8C00', 6),
('خط', 'Calligraphy', 'preparatory', 'azhari', '2', 'all', '🖋️', '#DAA520', 7),

('نحو', 'Grammar', 'preparatory', 'azhari', '3', 'all', '📝', '#2E8B57', 1),
('صرف', 'Morphology', 'preparatory', 'azhari', '3', 'all', '🔤', '#8B4513', 2),
('مطالعة', 'Reading', 'preparatory', 'azhari', '3', 'all', '📚', '#FF6347', 3),
('نصوص', 'Texts', 'preparatory', 'azhari', '3', 'all', '📖', '#4682B4', 4),
('إملاء', 'Dictation', 'preparatory', 'azhari', '3', 'all', '✍️', '#9370DB', 5),
('إنشاء', 'Composition', 'preparatory', 'azhari', '3', 'all', '📄', '#FF8C00', 6),
('خط', 'Calligraphy', 'preparatory', 'azhari', '3', 'all', '🖋️', '#DAA520', 7),

-- الإعدادي العام
('نحو', 'Grammar', 'preparatory', 'general', '1', 'all', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'preparatory', 'general', '1', 'all', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'preparatory', 'general', '1', 'all', '📖', '#4682B4', 3),
('إملاء', 'Dictation', 'preparatory', 'general', '1', 'all', '✍️', '#9370DB', 4),
('إنشاء', 'Composition', 'preparatory', 'general', '1', 'all', '📄', '#FF8C00', 5),
('خط', 'Calligraphy', 'preparatory', 'general', '1', 'all', '🖋️', '#DAA520', 6),

('نحو', 'Grammar', 'preparatory', 'general', '2', 'all', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'preparatory', 'general', '2', 'all', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'preparatory', 'general', '2', 'all', '📖', '#4682B4', 3),
('إملاء', 'Dictation', 'preparatory', 'general', '2', 'all', '✍️', '#9370DB', 4),
('إنشاء', 'Composition', 'preparatory', 'general', '2', 'all', '📄', '#FF8C00', 5),
('خط', 'Calligraphy', 'preparatory', 'general', '2', 'all', '🖋️', '#DAA520', 6),

('نحو', 'Grammar', 'preparatory', 'general', '3', 'all', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'preparatory', 'general', '3', 'all', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'preparatory', 'general', '3', 'all', '📖', '#4682B4', 3),
('إملاء', 'Dictation', 'preparatory', 'general', '3', 'all', '✍️', '#9370DB', 4),
('إنشاء', 'Composition', 'preparatory', 'general', '3', 'all', '📄', '#FF8C00', 5),
('خط', 'Calligraphy', 'preparatory', 'general', '3', 'all', '🖋️', '#DAA520', 6),

-- المرحلة الثانوية العام والعلمي والأدبي: نحو، مطالعة، نصوص، إنشاء، قصة
-- الثانوي العام - علمي
('نحو', 'Grammar', 'secondary', 'general', '1', 'scientific', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'secondary', 'general', '1', 'scientific', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'secondary', 'general', '1', 'scientific', '📖', '#4682B4', 3),
('إنشاء', 'Composition', 'secondary', 'general', '1', 'scientific', '📄', '#FF8C00', 4),
('قصة', 'Story', 'secondary', 'general', '1', 'scientific', '📚', '#8A2BE2', 5),

('نحو', 'Grammar', 'secondary', 'general', '2', 'scientific', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'secondary', 'general', '2', 'scientific', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'secondary', 'general', '2', 'scientific', '📖', '#4682B4', 3),
('إنشاء', 'Composition', 'secondary', 'general', '2', 'scientific', '📄', '#FF8C00', 4),
('قصة', 'Story', 'secondary', 'general', '2', 'scientific', '📚', '#8A2BE2', 5),

('نحو', 'Grammar', 'secondary', 'general', '3', 'scientific', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'secondary', 'general', '3', 'scientific', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'secondary', 'general', '3', 'scientific', '📖', '#4682B4', 3),
('إنشاء', 'Composition', 'secondary', 'general', '3', 'scientific', '📄', '#FF8C00', 4),
('قصة', 'Story', 'secondary', 'general', '3', 'scientific', '📚', '#8A2BE2', 5),

-- الثانوي العام - أدبي
('نحو', 'Grammar', 'secondary', 'general', '1', 'literary', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'secondary', 'general', '1', 'literary', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'secondary', 'general', '1', 'literary', '📖', '#4682B4', 3),
('إنشاء', 'Composition', 'secondary', 'general', '1', 'literary', '📄', '#FF8C00', 4),
('قصة', 'Story', 'secondary', 'general', '1', 'literary', '📚', '#8A2BE2', 5),

('نحو', 'Grammar', 'secondary', 'general', '2', 'literary', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'secondary', 'general', '2', 'literary', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'secondary', 'general', '2', 'literary', '📖', '#4682B4', 3),
('إنشاء', 'Composition', 'secondary', 'general', '2', 'literary', '📄', '#FF8C00', 4),
('قصة', 'Story', 'secondary', 'general', '2', 'literary', '📚', '#8A2BE2', 5),

('نحو', 'Grammar', 'secondary', 'general', '3', 'literary', '📝', '#2E8B57', 1),
('مطالعة', 'Reading', 'secondary', 'general', '3', 'literary', '📚', '#FF6347', 2),
('نصوص', 'Texts', 'secondary', 'general', '3', 'literary', '📖', '#4682B4', 3),
('إنشاء', 'Composition', 'secondary', 'general', '3', 'literary', '📄', '#FF8C00', 4),
('قصة', 'Story', 'secondary', 'general', '3', 'literary', '📚', '#8A2BE2', 5),

-- المرحلة الثانوية الأزهري: نحو، صرف، مطالعة، بلاغة، أدب، نصوص، إنشاء
-- الثاني الثانوي أزهري يضاف له: عروض وقافية
-- الثانوي الأزهري
('نحو', 'Grammar', 'secondary', 'azhari', '1', 'all', '📝', '#2E8B57', 1),
('صرف', 'Morphology', 'secondary', 'azhari', '1', 'all', '🔤', '#8B4513', 2),
('مطالعة', 'Reading', 'secondary', 'azhari', '1', 'all', '📚', '#FF6347', 3),
('بلاغة', 'Rhetoric', 'secondary', 'azhari', '1', 'all', '🎭', '#DC143C', 4),
('أدب', 'Literature', 'secondary', 'azhari', '1', 'all', '📜', '#4B0082', 5),
('نصوص', 'Texts', 'secondary', 'azhari', '1', 'all', '📖', '#4682B4', 6),
('إنشاء', 'Composition', 'secondary', 'azhari', '1', 'all', '📄', '#FF8C00', 7),

-- الثاني الثانوي أزهري (مع عروض وقافية)
('نحو', 'Grammar', 'secondary', 'azhari', '2', 'all', '📝', '#2E8B57', 1),
('صرف', 'Morphology', 'secondary', 'azhari', '2', 'all', '🔤', '#8B4513', 2),
('مطالعة', 'Reading', 'secondary', 'azhari', '2', 'all', '📚', '#FF6347', 3),
('بلاغة', 'Rhetoric', 'secondary', 'azhari', '2', 'all', '🎭', '#DC143C', 4),
('أدب', 'Literature', 'secondary', 'azhari', '2', 'all', '📜', '#4B0082', 5),
('نصوص', 'Texts', 'secondary', 'azhari', '2', 'all', '📖', '#4682B4', 6),
('إنشاء', 'Composition', 'secondary', 'azhari', '2', 'all', '📄', '#FF8C00', 7),
('عروض', 'Prosody', 'secondary', 'azhari', '2', 'all', '🎵', '#228B22', 8),
('قافية', 'Rhyme', 'secondary', 'azhari', '2', 'all', '🎶', '#32CD32', 9),

('نحو', 'Grammar', 'secondary', 'azhari', '3', 'all', '📝', '#2E8B57', 1),
('صرف', 'Morphology', 'secondary', 'azhari', '3', 'all', '🔤', '#8B4513', 2),
('مطالعة', 'Reading', 'secondary', 'azhari', '3', 'all', '📚', '#FF6347', 3),
('بلاغة', 'Rhetoric', 'secondary', 'azhari', '3', 'all', '🎭', '#DC143C', 4),
('أدب', 'Literature', 'secondary', 'azhari', '3', 'all', '📜', '#4B0082', 5),
('نصوص', 'Texts', 'secondary', 'azhari', '3', 'all', '📖', '#4682B4', 6),
('إنشاء', 'Composition', 'secondary', 'azhari', '3', 'all', '📄', '#FF8C00', 7);
