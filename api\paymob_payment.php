<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مسموح']);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['plan_id']) || !isset($input['amount']) || !isset($input['payment_type'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات مفقودة']);
    exit;
}

$user_id = $_SESSION['user_id'];
$plan_id = intval($input['plan_id']);
$amount = floatval($input['amount']);
$payment_type = $input['payment_type']; // 'card' or 'wallet'
$wallet_type = $input['wallet_type'] ?? null; // for wallet payments

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user and plan details
    $stmt = $db->prepare("SELECT u.*, sp.* FROM users u, subscription_plans sp WHERE u.id = ? AND sp.id = ?");
    $stmt->execute([$user_id, $plan_id]);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$data) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit;
    }

    // Ensure we have required user data
    $full_name = !empty($data['full_name']) ? $data['full_name'] : $data['username'] . ' User';
    $email = $data['email'] ?: '<EMAIL>';
    $phone = $data['phone'] ?: '+201000000000';
    
    // Check if user has active subscription
    $stmt = $db->prepare("SELECT subscription_status, subscription_end_date FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_subscription = $stmt->fetch(PDO::FETCH_ASSOC);

    $has_active_subscription = $user_subscription &&
                              $user_subscription['subscription_status'] === 'active' &&
                              $user_subscription['subscription_end_date'] &&
                              strtotime($user_subscription['subscription_end_date']) > time();

    if ($has_active_subscription) {
        echo json_encode([
            'success' => false,
            'message' => 'لديك اشتراك نشط بالفعل. لا يمكن الاشتراك في خطة جديدة حتى انتهاء الاشتراك الحالي أو إلغاؤه.'
        ]);
        exit;
    }

    // Check if Paymob is enabled
    if (!PAYMENT_GATEWAYS['paymob']['enabled']) {
        echo json_encode(['success' => false, 'message' => 'الدفع الإلكتروني غير متاح حالياً']);
        exit;
    }
    
    $paymob_config = PAYMENT_GATEWAYS['paymob'];
    
    // Generate unique order ID
    $order_id = 'SUB_' . $user_id . '_' . time();
    
    // Create subscription record (without setting start_date and end_date until payment is confirmed)
    $payment_method = $payment_type === 'wallet' ? 'wallet' : 'visa';

    $stmt = $db->prepare("INSERT INTO user_subscriptions (user_id, plan_id, payment_method, payment_status, amount_paid, duration_days) VALUES (?, ?, ?, 'pending', ?, ?)");
    $stmt->execute([$user_id, $plan_id, $payment_method, $amount, $data['duration_days']]);
    $subscription_id = $db->lastInsertId();
    
    // Create payment record
    $stmt = $db->prepare("INSERT INTO payments (subscription_id, user_id, payment_method, payment_gateway, transaction_id, amount, currency, status) VALUES (?, ?, ?, 'paymob', ?, ?, 'EGP', 'pending')");
    $stmt->execute([$subscription_id, $user_id, $payment_method, $order_id, $amount]);
    
    // Step 1: Get authentication token
    $auth_data = ['api_key' => $paymob_config['api_key']];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $paymob_config['base_url'] . '/auth/tokens');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($auth_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    
    $auth_response = curl_exec($ch);
    $auth_result = json_decode($auth_response, true);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // Log authentication response
    if (!file_exists(__DIR__ . '/../logs')) {
        mkdir(__DIR__ . '/../logs', 0755, true);
    }
    file_put_contents(__DIR__ . '/../logs/paymob_debug.log',
        date('Y-m-d H:i:s') . " - Auth Response: " . $auth_response . "\n",
        FILE_APPEND);

    if ($curl_error) {
        curl_close($ch);
        echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بالمصادقة: ' . $curl_error]);
        exit;
    }

    if ($http_code !== 201 && $http_code !== 200) {
        curl_close($ch);
        echo json_encode(['success' => false, 'message' => 'خطأ في المصادقة (HTTP ' . $http_code . ')']);
        exit;
    }

    if (!$auth_result || !isset($auth_result['token'])) {
        curl_close($ch);
        $error_message = 'خطأ في المصادقة مع بوابة الدفع';
        if (isset($auth_result['detail'])) {
            $error_message .= ': ' . $auth_result['detail'];
        }
        echo json_encode(['success' => false, 'message' => $error_message, 'debug' => $auth_result]);
        exit;
    }
    
    $auth_token = $auth_result['token'];
    
    // Step 2: Create order
    $order_data = [
        'auth_token' => $auth_token,
        'delivery_needed' => false,
        'amount_cents' => $amount * 100, // Convert to cents
        'currency' => 'EGP',
        'items' => [
            [
                'name' => $data['name'],
                'amount_cents' => $amount * 100,
                'description' => 'اشتراك ' . ($data['name'] ?? 'خطة') . ' - ' . SITE_NAME,
                'quantity' => 1
            ]
        ]
    ];
    
    curl_setopt($ch, CURLOPT_URL, $paymob_config['base_url'] . '/ecommerce/orders');
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
    
    $order_response = curl_exec($ch);
    $order_result = json_decode($order_response, true);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // Log order response
    file_put_contents(__DIR__ . '/../logs/paymob_debug.log',
        date('Y-m-d H:i:s') . " - Order Response: " . $order_response . "\n",
        FILE_APPEND);

    if ($curl_error) {
        curl_close($ch);
        echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال لإنشاء الطلب: ' . $curl_error]);
        exit;
    }

    if ($http_code !== 201 && $http_code !== 200) {
        curl_close($ch);
        echo json_encode(['success' => false, 'message' => 'خطأ في إنشاء الطلب (HTTP ' . $http_code . ')']);
        exit;
    }

    if (!$order_result || !isset($order_result['id'])) {
        curl_close($ch);
        $error_message = 'خطأ في إنشاء الطلب';
        if (isset($order_result['detail'])) {
            $error_message .= ': ' . $order_result['detail'];
        }
        echo json_encode(['success' => false, 'message' => $error_message, 'debug' => $order_result]);
        exit;
    }
    
    $paymob_order_id = $order_result['id'];
    
    // Step 3: Get payment key
    $integration_id = $payment_type === 'wallet' && isset($paymob_config['wallet_integrations'][$wallet_type])
        ? $paymob_config['wallet_integrations'][$wallet_type]
        : $paymob_config['integration_id'];

    // Split full name safely
    $name_parts = explode(' ', trim($full_name));
    $first_name = !empty($name_parts[0]) ? $name_parts[0] : 'User';
    $last_name = isset($name_parts[1]) && !empty($name_parts[1]) ? implode(' ', array_slice($name_parts, 1)) : 'Name';

    // Ensure names are not empty and contain valid characters
    $first_name = preg_replace('/[^a-zA-Z\x{0600}-\x{06FF}\s]/u', '', $first_name) ?: 'User';
    $last_name = preg_replace('/[^a-zA-Z\x{0600}-\x{06FF}\s]/u', '', $last_name) ?: 'Name';

    // Validate required data
    if (empty($first_name) || empty($last_name) || empty($email)) {
        echo json_encode(['success' => false, 'message' => 'بيانات المستخدم غير مكتملة']);
        exit;
    }

    $payment_key_data = [
        'auth_token' => $auth_token,
        'amount_cents' => intval($amount * 100),
        'expiration' => 3600, // 1 hour
        'order_id' => $paymob_order_id,
        'billing_data' => [
            'apartment' => 'NA',
            'email' => $email,
            'floor' => 'NA',
            'first_name' => $first_name,
            'street' => 'NA',
            'building' => 'NA',
            'phone_number' => $phone,
            'shipping_method' => 'NA',
            'postal_code' => 'NA',
            'city' => 'Cairo',
            'country' => 'EG',
            'last_name' => $last_name,
            'state' => 'Cairo'
        ],
        'currency' => 'EGP',
        'integration_id' => intval($integration_id),
        'lock_order_when_paid' => true
    ];

    // Log the payment key data for debugging
    file_put_contents(__DIR__ . '/../logs/paymob_debug.log',
        date('Y-m-d H:i:s') . " - Payment Key Data: " . json_encode($payment_key_data, JSON_UNESCAPED_UNICODE) . "\n",
        FILE_APPEND);
    
    curl_setopt($ch, CURLOPT_URL, $paymob_config['base_url'] . '/acceptance/payment_keys');
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_key_data));
    
    $payment_key_response = curl_exec($ch);
    $payment_key_result = json_decode($payment_key_response, true);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Log the response for debugging
    if (!file_exists(__DIR__ . '/../logs')) {
        mkdir(__DIR__ . '/../logs', 0755, true);
    }
    file_put_contents(__DIR__ . '/../logs/paymob_debug.log',
        date('Y-m-d H:i:s') . " - Payment Key Response: " . $payment_key_response . "\n",
        FILE_APPEND);

    if ($curl_error) {
        echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال: ' . $curl_error]);
        exit;
    }

    if ($http_code !== 201 && $http_code !== 200) {
        $error_message = 'خطأ من بوابة الدفع (HTTP ' . $http_code . ')';
        if ($http_code === 401) {
            $error_message = 'خطأ في المصادقة - يرجى التحقق من إعدادات بوابة الدفع';
        }
        echo json_encode(['success' => false, 'message' => $error_message]);
        exit;
    }

    if (!$payment_key_result || !isset($payment_key_result['token'])) {
        $error_message = 'خطأ في إنشاء مفتاح الدفع';
        if (isset($payment_key_result['message'])) {
            if ($payment_key_result['message'] === 'unrelated payment integration') {
                $error_message = 'خطأ في إعدادات المحفظة الإلكترونية - يرجى المحاولة بطريقة دفع أخرى';
            } else {
                $error_message .= ': ' . $payment_key_result['message'];
            }
        } elseif (isset($payment_key_result['detail'])) {
            $error_message .= ': ' . $payment_key_result['detail'];
        }
        echo json_encode(['success' => false, 'message' => $error_message, 'debug' => $payment_key_result]);
        exit;
    }
    
    $payment_token = $payment_key_result['token'];
    
    // Update payment record with Paymob order ID
    $stmt = $db->prepare("UPDATE payments SET gateway_response = ? WHERE transaction_id = ?");
    $stmt->execute([json_encode(['paymob_order_id' => $paymob_order_id, 'payment_token' => $payment_token]), $order_id]);
    
    // Generate payment URL
    if ($payment_type === 'wallet') {
        $payment_url = "https://accept.paymob.com/api/acceptance/payments/pay?source[identifier]={$wallet_type}&source[subtype]=WALLET&payment_token={$payment_token}";
    } else {
        $payment_url = "https://accept.paymob.com/api/acceptance/iframes/{$paymob_config['iframe_id']}?payment_token={$payment_token}";
    }
    
    echo json_encode([
        'success' => true,
        'payment_url' => $payment_url,
        'payment_token' => $payment_token,
        'order_id' => $order_id,
        'paymob_order_id' => $paymob_order_id,
        'message' => 'تم إنشاء طلب الدفع بنجاح'
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()]);
}
?>
