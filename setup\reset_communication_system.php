<?php
/**
 * Reset Communication System Script
 * This script drops existing tables and recreates them properly
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

class CommunicationSystemReset {
    private $db;
    private $errors = [];
    private $success = [];
    
    public function __construct() {
        try {
            $this->db = Database::getInstance()->getConnection();
        } catch (Exception $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function reset() {
        echo "<h2>إعادة تعيين نظام التواصل بين الطلاب والمعلمين...</h2>\n";
        
        // Drop existing tables
        $this->dropTables();
        
        // Create tables fresh
        $this->createTables();
        
        // Insert default data
        $this->insertDefaultData();
        
        // Display results
        $this->displayResults();
        
        return empty($this->errors);
    }
    
    private function dropTables() {
        echo "<h3>حذف الجداول الموجودة...</h3>\n";
        
        // Order matters due to foreign key constraints
        $tables = [
            'message_notifications',
            'admin_replies', 
            'message_templates',
            'student_messages',
            'message_categories'
        ];
        
        // Disable foreign key checks temporarily
        try {
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            foreach ($tables as $table) {
                try {
                    $this->db->exec("DROP TABLE IF EXISTS {$table}");
                    $this->success[] = "تم حذف الجدول: {$table}";
                } catch (PDOException $e) {
                    $this->errors[] = "خطأ في حذف الجدول {$table}: " . $e->getMessage();
                }
            }
            
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 1");
            
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إعدادات قاعدة البيانات: " . $e->getMessage();
        }
    }
    
    private function createTables() {
        echo "<h3>إنشاء الجداول الجديدة...</h3>\n";
        
        // Create message categories table first
        $this->createMessageCategoriesTable();
        
        // Create student messages table
        $this->createStudentMessagesTable();
        
        // Create admin replies table
        $this->createAdminRepliesTable();
        
        // Create message notifications table
        $this->createMessageNotificationsTable();
        
        // Create message templates table
        $this->createMessageTemplatesTable();
    }
    
    private function createMessageCategoriesTable() {
        $sql = "
        CREATE TABLE message_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            name_ar VARCHAR(100) NOT NULL,
            description TEXT NULL,
            icon VARCHAR(50) DEFAULT '📝',
            color VARCHAR(7) DEFAULT '#4682B4',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_is_active (is_active),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        try {
            $this->db->exec($sql);
            $this->success[] = "تم إنشاء جدول: message_categories";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول message_categories: " . $e->getMessage();
        }
    }
    
    private function createStudentMessagesTable() {
        $sql = "
        CREATE TABLE student_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            message_type ENUM('question', 'help', 'complaint', 'suggestion', 'other') DEFAULT 'question',
            category_id INT NULL,
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            status ENUM('pending', 'read', 'replied', 'closed') DEFAULT 'pending',
            is_anonymous BOOLEAN DEFAULT FALSE,
            student_name VARCHAR(100) NULL,
            student_email VARCHAR(100) NULL,
            student_phone VARCHAR(15) NULL,
            attachment_path VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES message_categories(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_message_type (message_type),
            INDEX idx_category_id (category_id),
            INDEX idx_priority (priority),
            INDEX idx_created_at (created_at),
            INDEX idx_read_at (read_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        try {
            $this->db->exec($sql);
            $this->success[] = "تم إنشاء جدول: student_messages";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول student_messages: " . $e->getMessage();
        }
    }
    
    private function createAdminRepliesTable() {
        $sql = "
        CREATE TABLE admin_replies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            message_id INT NOT NULL,
            admin_id INT NOT NULL,
            reply_text TEXT NOT NULL,
            is_public BOOLEAN DEFAULT FALSE,
            attachment_path VARCHAR(255) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (message_id) REFERENCES student_messages(id) ON DELETE CASCADE,
            FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
            INDEX idx_message_id (message_id),
            INDEX idx_admin_id (admin_id),
            INDEX idx_created_at (created_at),
            INDEX idx_is_public (is_public)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        try {
            $this->db->exec($sql);
            $this->success[] = "تم إنشاء جدول: admin_replies";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول admin_replies: " . $e->getMessage();
        }
    }
    
    private function createMessageNotificationsTable() {
        $sql = "
        CREATE TABLE message_notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            message_id INT NOT NULL,
            recipient_type ENUM('admin', 'student') NOT NULL,
            recipient_id INT NOT NULL,
            notification_type ENUM('new_message', 'new_reply', 'status_change') NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            
            FOREIGN KEY (message_id) REFERENCES student_messages(id) ON DELETE CASCADE,
            INDEX idx_message_id (message_id),
            INDEX idx_recipient (recipient_type, recipient_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        try {
            $this->db->exec($sql);
            $this->success[] = "تم إنشاء جدول: message_notifications";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول message_notifications: " . $e->getMessage();
        }
    }
    
    private function createMessageTemplatesTable() {
        $sql = "
        CREATE TABLE message_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            category_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            usage_count INT DEFAULT 0,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (category_id) REFERENCES message_categories(id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
            INDEX idx_category_id (category_id),
            INDEX idx_is_active (is_active),
            INDEX idx_created_by (created_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        try {
            $this->db->exec($sql);
            $this->success[] = "تم إنشاء جدول: message_templates";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول message_templates: " . $e->getMessage();
        }
    }
    
    private function insertDefaultData() {
        echo "<h3>إدراج البيانات الافتراضية...</h3>\n";
        
        // Insert default categories
        $categories = [
            ['General Question', 'سؤال عام', 'General questions about courses or platform', '❓', '#4682B4', 1],
            ['Technical Support', 'الدعم الفني', 'Technical issues and platform problems', '🔧', '#e74c3c', 2],
            ['Course Content', 'محتوى الكورس', 'Questions about specific course content', '📚', '#27ae60', 3],
            ['Payment Issues', 'مشاكل الدفع', 'Payment and subscription related issues', '💳', '#f39c12', 4],
            ['Account Issues', 'مشاكل الحساب', 'Account access and profile issues', '👤', '#9b59b6', 5],
            ['Suggestions', 'اقتراحات', 'Suggestions for platform improvement', '💡', '#1abc9c', 6],
            ['Complaints', 'شكاوى', 'Complaints and feedback', '⚠️', '#e67e22', 7],
            ['Other', 'أخرى', 'Other types of messages', '📝', '#95a5a6', 8]
        ];
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO message_categories (name, name_ar, description, icon, color, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($categories as $category) {
                $stmt->execute($category);
            }
            
            $this->success[] = "تم إدراج التصنيفات الافتراضية";
            
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إدراج التصنيفات: " . $e->getMessage();
        }
        
        // Insert default templates
        $templates = [
            ['شكر على التواصل', 'شكراً لك على تواصلك معنا. تم استلام رسالتك وسيتم الرد عليك في أقرب وقت ممكن.', 1],
            ['طلب معلومات إضافية', 'نشكرك على رسالتك. نحتاج إلى معلومات إضافية لمساعدتك بشكل أفضل. يرجى تقديم المزيد من التفاصيل حول مشكلتك.', 1],
            ['حل مشكلة فنية', 'تم حل المشكلة الفنية التي واجهتك. يرجى المحاولة مرة أخرى والتواصل معنا إذا استمرت المشكلة.', 2],
            ['توضيح محتوى الكورس', 'بخصوص استفسارك حول محتوى الكورس، يمكنك مراجعة الدروس المتاحة في قسم المحتوى أو مشاهدة الفيديوهات التوضيحية.', 3],
            ['مساعدة في الدفع', 'بخصوص مشكلة الدفع، يرجى التواصل معنا عبر الواتساب مع إرفاق صورة من إيصال التحويل للمساعدة السريعة.', 4]
        ];
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO message_templates (title, content, category_id, created_by) 
                VALUES (?, ?, ?, 1)
            ");
            
            foreach ($templates as $template) {
                $stmt->execute($template);
            }
            
            $this->success[] = "تم إدراج قوالب الردود الافتراضية";
            
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إدراج قوالب الردود: " . $e->getMessage();
        }
    }
    
    private function displayResults() {
        echo "<h3>نتائج العملية:</h3>\n";
        
        if (!empty($this->success)) {
            echo "<div style='color: green; background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>✅ نجح:</h4>\n";
            foreach ($this->success as $message) {
                echo "<p>• {$message}</p>\n";
            }
            echo "</div>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<div style='color: red; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>❌ أخطاء:</h4>\n";
            foreach ($this->errors as $error) {
                echo "<p>• {$error}</p>\n";
            }
            echo "</div>\n";
        }
        
        if (empty($this->errors)) {
            echo "<div style='color: blue; background: #cce7ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>🎉 تم إعادة تعيين نظام التواصل بنجاح!</h4>\n";
            echo "<p>تم حذف الجداول القديمة وإنشاء جداول جديدة بالبيانات الافتراضية.</p>\n";
            echo "<p><strong>الخطوات التالية:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>يمكن للطلاب الآن إرسال رسائل عبر صفحة 'اسأل معلم'</li>\n";
            echo "<li>يمكن للإدارة عرض والرد على الرسائل في لوحة الإدارة</li>\n";
            echo "<li>نظام الإشعارات نشط للرسائل الجديدة</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }
    }
}

// Run the reset if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'reset_communication_system.php') {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إعادة تعيين نظام التواصل - <?php echo SITE_NAME; ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .btn { background: #4682B4; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
            .btn:hover { background: #357abd; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; color: #856404; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>إعادة تعيين نظام التواصل بين الطلاب والمعلمين</h1>
            
            <div class="warning">
                <strong>تحذير:</strong> هذه العملية ستحذف جميع الرسائل والردود الموجودة وتعيد إنشاء الجداول من جديد.
            </div>
            
            <?php
            $reset = new CommunicationSystemReset();
            $success = $reset->reset();
            
            if ($success) {
                echo "<p style='text-align: center; margin-top: 20px;'>";
                echo "<a href='../admin/student_messages.php' class='btn'>الذهاب إلى إدارة الرسائل</a> ";
                echo "<a href='../page/ask_teacher.php' class='btn'>اختبار صفحة اسأل معلم</a>";
                echo "</p>";
            }
            ?>
        </div>
    </body>
    </html>
    <?php
}
?>
