<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Check if subscription_plans table exists, create if not
        $stmt = $db->query("SHOW TABLES LIKE 'subscription_plans'");
        if ($stmt->rowCount() == 0) {
            // Create subscription_plans table
            $createTableSQL = "CREATE TABLE subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة',
                name_en VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية',
                description TEXT NULL COMMENT 'وصف الخطة',
                price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر الأصلي',
                discount_percentage INT DEFAULT 0 COMMENT 'نسبة الخصم',
                discounted_price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر بعد الخصم',
                duration_days INT NOT NULL DEFAULT 30 COMMENT 'مدة الاشتراك بالأيام',
                features JSON NULL COMMENT 'مميزات الخطة',
                icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة الخطة',
                color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة',
                is_popular BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة شائعة',
                is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الخطة نشطة',
                sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
                created_by INT NOT NULL DEFAULT 1 COMMENT 'منشئ الخطة',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $db->exec($createTableSQL);
        }
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    // Calculate discounted price
                    $price = floatval($_POST['price']);
                    $discount = intval($_POST['discount_percentage']);
                    $discounted_price = $price - ($price * $discount / 100);
                    
                    // Process features
                    $features = array_filter(array_map('trim', explode("\n", $_POST['features'])));
                    $features_json = json_encode($features, JSON_UNESCAPED_UNICODE);
                    
                    $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, is_active, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([
                        $_POST['name'],
                        $_POST['name_en'] ?? '',
                        $_POST['description'] ?? '',
                        $price,
                        $discount,
                        $discounted_price,
                        $_POST['duration_days'],
                        $features_json,
                        $_POST['icon'] ?? '📚',
                        $_POST['color'] ?? '#4682B4',
                        isset($_POST['is_popular']) ? 1 : 0,
                        isset($_POST['is_active']) ? 1 : 0,
                        $_POST['sort_order'] ?? 0,
                        $_SESSION['admin_id']
                    ]);

                    if ($result) {
                        $planId = $db->lastInsertId();
                        $message = 'تم إضافة الخطة بنجاح - ID: ' . $planId;

                        // Verify the plan was actually added
                        $verifyStmt = $db->prepare("SELECT name FROM subscription_plans WHERE id = ?");
                        $verifyStmt->execute([$planId]);
                        $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);

                        if ($verifyResult) {
                            $message .= ' - اسم الخطة: ' . $verifyResult['name'];
                        }
                    } else {
                        $error = 'فشل في إضافة الخطة - لم يتم تنفيذ الاستعلام';
                    }
                    break;
                    
                case 'edit':
                    // Calculate discounted price
                    $price = floatval($_POST['price']);
                    $discount = intval($_POST['discount_percentage']);
                    $discounted_price = $price - ($price * $discount / 100);
                    
                    // Process features
                    $features = array_filter(array_map('trim', explode("\n", $_POST['features'])));
                    $features_json = json_encode($features, JSON_UNESCAPED_UNICODE);
                    
                    $stmt = $db->prepare("UPDATE subscription_plans SET name = ?, name_en = ?, description = ?, price = ?, discount_percentage = ?, discounted_price = ?, duration_days = ?, features = ?, icon = ?, color = ?, is_popular = ?, is_active = ?, sort_order = ? WHERE id = ?");
                    $stmt->execute([
                        $_POST['name'],
                        $_POST['name_en'],
                        $_POST['description'],
                        $price,
                        $discount,
                        $discounted_price,
                        $_POST['duration_days'],
                        $features_json,
                        $_POST['icon'],
                        $_POST['color'],
                        isset($_POST['is_popular']) ? 1 : 0,
                        isset($_POST['is_active']) ? 1 : 0,
                        $_POST['sort_order'],
                        $_POST['id']
                    ]);
                    $message = 'تم تحديث الخطة بنجاح';
                    break;
                    
                case 'delete':
                    $stmt = $db->prepare("DELETE FROM subscription_plans WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم حذف الخطة بنجاح';
                    break;
                    
                case 'toggle_status':
                    $stmt = $db->prepare("UPDATE subscription_plans SET is_active = !is_active WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم تغيير حالة الخطة بنجاح';
                    break;
                    
                case 'toggle_popular':
                    // First, remove popular status from all plans
                    $db->exec("UPDATE subscription_plans SET is_popular = FALSE");
                    // Then set this plan as popular
                    $stmt = $db->prepare("UPDATE subscription_plans SET is_popular = TRUE WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $message = 'تم تعيين الخطة كالأكثر شيوعاً';
                    break;
            }
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
        error_log("Subscription Plans Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

        // Add more detailed error information for debugging
        if (isset($_POST['action']) && $_POST['action'] === 'add') {
            $error .= '<br><strong>تفاصيل إضافية:</strong>';
            $error .= '<br>- الملف: ' . $e->getFile();
            $error .= '<br>- السطر: ' . $e->getLine();
            $error .= '<br>- البيانات المرسلة: ' . json_encode($_POST, JSON_UNESCAPED_UNICODE);
        }
    }
}

// Get all plans
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->query("SELECT sp.*, a.username as created_by_name,
                       (SELECT COUNT(*) FROM user_subscriptions us WHERE us.plan_id = sp.id AND us.end_date > NOW()) as active_subscribers
                       FROM subscription_plans sp
                       LEFT JOIN admins a ON sp.created_by = a.id
                       ORDER BY sp.sort_order, sp.created_at DESC");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $plans = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة خطط الاشتراك - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>إدارة خطط الاشتراك</h1>
                <p>إنشاء وإدارة خطط الاشتراك المختلفة</p>
            </div>

            <!-- Action Buttons -->
            <div class="content-card">
                <div class="d-flex justify-between items-center">
                    <h2>خطط الاشتراك</h2>
                    <button class="btn btn-primary" onclick="openModal('addPlanModal')">
                        <i class="fas fa-plus"></i>
                        إضافة خطة جديدة
                    </button>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="content-card">
                    <div class="alert alert-success" style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <i class="fas fa-check-circle" style="margin-left: 8px;"></i>
                        <?php echo $message; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="content-card">
                    <div class="alert alert-danger" style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
                        <?php echo $error; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Debug Information (only in development) -->
            <?php if (defined('DEBUG') && DEBUG): ?>
                <div class="content-card">
                    <div class="alert alert-info" style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4>معلومات التشخيص:</h4>
                        <p><strong>POST Data:</strong> <?php echo json_encode($_POST, JSON_UNESCAPED_UNICODE); ?></p>
                        <p><strong>Session Admin ID:</strong> <?php echo $_SESSION['admin_id'] ?? 'غير محدد'; ?></p>
                        <p><strong>Database Connection:</strong>
                            <?php
                            try {
                                $testDb = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
                                echo "✅ متصل";
                            } catch (Exception $e) {
                                echo "❌ غير متصل: " . $e->getMessage();
                            }
                            ?>
                        </p>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="content-card">
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                </div>
            <?php endif; ?>

                <!-- Plans Grid -->
                <div class="plans-grid">
                    <?php foreach ($plans as $plan): ?>
                        <div class="plan-card <?php echo $plan['is_popular'] ? 'popular' : ''; ?> <?php echo !$plan['is_active'] ? 'inactive' : ''; ?>" style="border-top: 4px solid <?php echo $plan['color']; ?>">
                            <?php if ($plan['is_popular']): ?>
                                <div class="popular-badge">
                                    <i class="fas fa-star"></i>
                                    الأكثر شيوعاً
                                </div>
                            <?php endif; ?>
                            
                            <div class="plan-header">
                                <div class="plan-icon" style="background-color: <?php echo $plan['color']; ?>20; color: <?php echo $plan['color']; ?>">
                                    <?php echo $plan['icon']; ?>
                                </div>
                                <h3><?php echo htmlspecialchars($plan['name']); ?></h3>
                                <?php if ($plan['name_en']): ?>
                                    <span class="plan-subtitle"><?php echo htmlspecialchars($plan['name_en']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="plan-pricing">
                                <?php if ($plan['discount_percentage'] > 0): ?>
                                    <div class="original-price"><?php echo number_format($plan['price'], 0); ?> جنيه</div>
                                    <div class="discount-badge"><?php echo $plan['discount_percentage']; ?>% خصم</div>
                                <?php endif; ?>
                                <div class="current-price"><?php echo number_format($plan['discounted_price'], 0); ?> جنيه</div>
                                <div class="duration"><?php echo $plan['duration_days']; ?> يوم</div>
                            </div>
                            
                            <?php if ($plan['description']): ?>
                                <div class="plan-description">
                                    <p><?php echo htmlspecialchars($plan['description']); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($plan['features']): ?>
                                <div class="plan-features">
                                    <h4>المميزات:</h4>
                                    <ul>
                                        <?php 
                                        $features = json_decode($plan['features'], true);
                                        foreach ($features as $feature): 
                                        ?>
                                            <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="plan-stats">
                                <div class="stat">
                                    <i class="fas fa-users"></i>
                                    <span><?php echo $plan['active_subscribers']; ?> مشترك نشط</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-sort-numeric-up"></i>
                                    <span>ترتيب: <?php echo $plan['sort_order']; ?></span>
                                </div>
                            </div>
                            
                            <div class="plan-status">
                                <span class="status-badge <?php echo $plan['is_active'] ? 'active' : 'inactive'; ?>">
                                    <?php echo $plan['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                            
                            <div class="plan-actions">
                                <button class="btn btn-sm btn-primary" onclick="editPlan(<?php echo htmlspecialchars(json_encode($plan)); ?>)">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </button>
                                
                                <?php if (!$plan['is_popular']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_popular">
                                        <input type="hidden" name="id" value="<?php echo $plan['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-warning">
                                            <i class="fas fa-star"></i>
                                            جعل شائع
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="id" value="<?php echo $plan['id']; ?>">
                                    <button type="submit" class="btn btn-sm <?php echo $plan['is_active'] ? 'btn-secondary' : 'btn-success'; ?>">
                                        <i class="fas fa-<?php echo $plan['is_active'] ? 'pause' : 'play'; ?>"></i>
                                        <?php echo $plan['is_active'] ? 'إيقاف' : 'تفعيل'; ?>
                                    </button>
                                </form>
                                
                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه الخطة؟\nسيتم حذف جميع الاشتراكات المرتبطة بها!')">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="id" value="<?php echo $plan['id']; ?>">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Plan Modal -->
    <div class="modal" id="addPlanModal">
        <div class="modal-header">
            <h3 class="modal-title">إضافة خطة اشتراك جديدة</h3>
            <button class="modal-close" onclick="closeModal('addPlanModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="addPlanForm" method="POST">
                <input type="hidden" name="action" value="add">
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">اسم الخطة (عربي)</label>
                        <input type="text" class="form-input" name="name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اسم الخطة (إنجليزي)</label>
                        <input type="text" class="form-input" name="name_en" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-textarea" name="description" required></textarea>
                </div>
                <div class="grid-3">
                    <div class="form-group">
                        <label class="form-label">السعر</label>
                        <input type="number" class="form-input" name="price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">نسبة الخصم (%)</label>
                        <input type="number" class="form-input" name="discount_percentage" min="0" max="100" value="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">مدة الاشتراك (أيام)</label>
                        <input type="number" class="form-input" name="duration_days" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">المميزات (كل ميزة في سطر منفصل)</label>
                    <textarea class="form-textarea" name="features" rows="5" required></textarea>
                </div>
                <div class="grid-3">
                    <div class="form-group">
                        <label class="form-label">الأيقونة</label>
                        <input type="text" class="form-input" name="icon" placeholder="fas fa-star">
                    </div>
                    <div class="form-group">
                        <label class="form-label">اللون</label>
                        <input type="color" class="form-input" name="color" value="#4f46e5">
                    </div>
                    <div class="form-group">
                        <label class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-input" name="sort_order" value="1">
                    </div>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="is_popular"> خطة مميزة
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="is_active" checked> نشطة
                        </label>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitAddPlan()">إضافة الخطة</button>
            <button class="btn btn-secondary" onclick="closeModal('addPlanModal')">إلغاء</button>
        </div>
    </div>

    <!-- Edit Plan Modal -->
    <div class="modal" id="editPlanModal">
        <div class="modal-header">
            <h3 class="modal-title">تعديل خطة الاشتراك</h3>
            <button class="modal-close" onclick="closeModal('editPlanModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="editPlanForm" method="POST">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="editPlanId">
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">اسم الخطة (عربي)</label>
                        <input type="text" class="form-input" name="name" id="editName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اسم الخطة (إنجليزي)</label>
                        <input type="text" class="form-input" name="name_en" id="editNameEn" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-textarea" name="description" id="editDescription" required></textarea>
                </div>
                <div class="grid-3">
                    <div class="form-group">
                        <label class="form-label">السعر</label>
                        <input type="number" class="form-input" name="price" id="editPrice" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">نسبة الخصم (%)</label>
                        <input type="number" class="form-input" name="discount_percentage" id="editDiscount" min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label class="form-label">مدة الاشتراك (أيام)</label>
                        <input type="number" class="form-input" name="duration_days" id="editDuration" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">المميزات (كل ميزة في سطر منفصل)</label>
                    <textarea class="form-textarea" name="features" id="editFeatures" rows="5" required></textarea>
                </div>
                <div class="grid-3">
                    <div class="form-group">
                        <label class="form-label">الأيقونة</label>
                        <input type="text" class="form-input" name="icon" id="editIcon">
                    </div>
                    <div class="form-group">
                        <label class="form-label">اللون</label>
                        <input type="color" class="form-input" name="color" id="editColor">
                    </div>
                    <div class="form-group">
                        <label class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-input" name="sort_order" id="editSortOrder">
                    </div>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="is_popular" id="editIsPopular"> خطة مميزة
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="is_active" id="editIsActive"> نشطة
                        </label>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitEditPlan()">حفظ التغييرات</button>
            <button class="btn btn-secondary" onclick="closeModal('editPlanModal')">إلغاء</button>
        </div>

    <style>
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .plan-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .plan-card.popular {
            border: 2px solid #ffc107;
            transform: scale(1.05);
        }

        .plan-card.inactive {
            opacity: 0.6;
            filter: grayscale(50%);
        }

        .popular-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .plan-header {
            padding: 25px 25px 15px 25px;
            text-align: center;
        }

        .plan-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin: 0 auto 15px auto;
        }

        .plan-header h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.4rem;
        }

        .plan-subtitle {
            color: #666;
            font-size: 14px;
        }

        .plan-pricing {
            padding: 0 25px 15px 25px;
            text-align: center;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }

        .discount-badge {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin: 5px 0;
            display: inline-block;
        }

        .current-price {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
            margin: 5px 0;
        }

        .duration {
            color: #666;
            font-size: 14px;
        }

        .plan-description {
            padding: 0 25px 15px 25px;
        }

        .plan-description p {
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        .plan-features {
            padding: 0 25px 15px 25px;
        }

        .plan-features h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        .plan-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .plan-features li {
            padding: 5px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plan-features li i {
            color: #28a745;
            font-size: 12px;
        }

        .plan-stats {
            padding: 15px 25px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            background: #f8f9fa;
        }

        .stat {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #666;
        }

        .plan-status {
            padding: 15px 25px;
            text-align: center;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .plan-actions {
            padding: 20px 25px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        /* Fix modal display issues */
        .modal {
            display: none !important;
            position: fixed !important;
            z-index: 2001 !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            width: auto !important;
            height: auto !important;
            background-color: #ffffff !important;
            border-radius: 1rem !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
            max-width: 90vw !important;
            max-height: 90vh !important;
            overflow-y: auto !important;
            min-width: 600px !important;
            border: 1px solid #e2e8f0 !important;
        }

        .modal.show {
            display: block !important;
        }

        /* Create modal overlay */
        .modal-overlay {
            display: none !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 2000 !important;
        }

        .modal-overlay.show {
            display: block !important;
        }

        /* Ensure modal content is visible */
        .modal-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 1.5rem !important;
            border-bottom: 1px solid #e2e8f0 !important;
            background: #f8fafc !important;
            border-radius: 1rem 1rem 0 0 !important;
        }

        .modal-title {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            color: #1e293b !important;
            margin: 0 !important;
        }

        .modal-close {
            background: none !important;
            border: none !important;
            font-size: 1.5rem !important;
            color: #64748b !important;
            cursor: pointer !important;
            padding: 0.25rem !important;
            border-radius: 0.375rem !important;
            transition: all 0.3s ease !important;
            width: 32px !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .modal-close:hover {
            background: #f1f5f9 !important;
            color: #1e293b !important;
        }

        .modal-body {
            padding: 1.5rem !important;
            max-height: 60vh !important;
            overflow-y: auto !important;
        }

        .modal-footer {
            display: flex !important;
            justify-content: flex-end !important;
            gap: 1rem !important;
            padding: 1.5rem !important;
            border-top: 1px solid #e2e8f0 !important;
            background: #f8fafc !important;
            border-radius: 0 0 1rem 1rem !important;
        }

        /* Form styling in modals */
        .modal .form-group {
            margin-bottom: 1rem !important;
        }

        .modal .form-label {
            display: block !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            color: #1e293b !important;
            margin-bottom: 0.5rem !important;
        }

        .modal .form-input,
        .modal .form-select,
        .modal .form-textarea {
            width: 100% !important;
            padding: 0.75rem !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            font-size: 0.875rem !important;
            transition: all 0.3s ease !important;
            background: #ffffff !important;
            color: #1e293b !important;
        }

        .modal .form-input:focus,
        .modal .form-select:focus,
        .modal .form-textarea:focus {
            outline: none !important;
            border-color: #4f46e5 !important;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
        }

        .modal .grid-2 {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 1rem !important;
        }

        .modal .grid-3 {
            display: grid !important;
            grid-template-columns: 1fr 1fr 1fr !important;
            gap: 1rem !important;
        }

        .modal .btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 0.5rem !important;
            padding: 0.75rem 1.5rem !important;
            border: none !important;
            border-radius: 0.5rem !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
        }

        .modal .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #6366f1) !important;
            color: white !important;
        }

        .modal .btn-secondary {
            background: #f1f5f9 !important;
            color: #475569 !important;
            border: 1px solid #d1d5db !important;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .form-group {
            margin-bottom: 15px;
            padding: 0 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .form-actions {
            padding: 20px;
            border-top: 1px solid #ddd;
            text-align: left;
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .plans-grid {
                grid-template-columns: 1fr;
            }

            .plan-actions {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
    <script>
        // Create modal overlay if it doesn't exist
        function createModalOverlay() {
            let overlay = document.querySelector('.modal-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'modal-overlay';
                overlay.onclick = function() {
                    closeAllModals();
                };
                document.body.appendChild(overlay);
            }
            return overlay;
        }

        // Override openModal function to work with our modals
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            const overlay = createModalOverlay();

            if (modal) {
                overlay.classList.add('show');
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        // Override closeModal function
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            const overlay = document.querySelector('.modal-overlay');

            if (modal) {
                modal.classList.remove('show');
            }
            if (overlay) {
                overlay.classList.remove('show');
            }
            document.body.style.overflow = '';
        }

        function closeAllModals() {
            const modals = document.querySelectorAll('.modal.show');
            const overlay = document.querySelector('.modal-overlay');

            modals.forEach(modal => modal.classList.remove('show'));
            if (overlay) {
                overlay.classList.remove('show');
            }
            document.body.style.overflow = '';
        }

        // Update modal function names to match new system
        function showAddModal() {
            openModal('addPlanModal');
        }

        function showEditModal(id) {
            openModal('editPlanModal');
            // Load plan data for editing
            loadPlanData(id);
        }

        function loadPlanData(id) {
            // For now, we'll get the data from the page since we don't have the API yet
            // In a real implementation, you would fetch from an API
            console.log('Loading plan data for ID:', id);
        }

        // Function to populate edit form with plan data (called from PHP)
        function editPlan(planData) {
            // Populate the edit form
            document.getElementById('editPlanId').value = planData.id;
            document.getElementById('editName').value = planData.name;
            document.getElementById('editNameEn').value = planData.name_en || '';
            document.getElementById('editDescription').value = planData.description || '';
            document.getElementById('editPrice').value = planData.price;
            document.getElementById('editDiscount').value = planData.discount_percentage || 0;
            document.getElementById('editDuration').value = planData.duration_days;
            document.getElementById('editIcon').value = planData.icon || '';
            document.getElementById('editColor').value = planData.color || '#4f46e5';
            document.getElementById('editSortOrder').value = planData.sort_order || 1;
            document.getElementById('editIsPopular').checked = planData.is_popular == 1;
            document.getElementById('editIsActive').checked = planData.is_active == 1;

            // Handle features
            if (planData.features) {
                try {
                    const features = JSON.parse(planData.features);
                    document.getElementById('editFeatures').value = features.join('\n');
                } catch (e) {
                    document.getElementById('editFeatures').value = planData.features;
                }
            }

            // Open the edit modal
            openModal('editPlanModal');
        }

        function submitAddPlan() {
            const form = document.getElementById('addPlanForm');
            const formData = new FormData(form);

            // Add action parameter
            formData.append('action', 'add');

            // Validate required fields
            const name = formData.get('name');
            const price = formData.get('price');
            const duration_days = formData.get('duration_days');
            const features = formData.get('features');

            console.log('Form data validation:', {
                name: name,
                price: price,
                duration_days: duration_days,
                features: features
            });

            if (!name || name.trim() === '') {
                window.adminPanel.showToast('يرجى إدخال اسم الخطة', 'error');
                return;
            }

            if (!price || isNaN(price) || parseFloat(price) <= 0) {
                window.adminPanel.showToast('يرجى إدخال سعر صحيح', 'error');
                return;
            }

            if (!duration_days || isNaN(duration_days) || parseInt(duration_days) <= 0) {
                window.adminPanel.showToast('يرجى إدخال مدة صحيحة بالأيام', 'error');
                return;
            }

            if (!features || features.trim() === '') {
                window.adminPanel.showToast('يرجى إدخال مميزات الخطة', 'error');
                return;
            }

            // Show loading message
            if (window.adminPanel && window.adminPanel.showToast) {
                window.adminPanel.showToast('جاري إضافة الخطة...', 'info');
            } else {
                console.log('جاري إضافة الخطة...');
            }

            fetch('ajax_subscription_plans.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response:', data);

                if (data.success) {
                    if (window.adminPanel && window.adminPanel.showToast) {
                        window.adminPanel.showToast(data.message, 'success');
                    } else {
                        alert('✅ ' + data.message);
                    }
                    closeModal('addPlanModal');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    if (window.adminPanel && window.adminPanel.showToast) {
                        window.adminPanel.showToast(data.message || 'حدث خطأ في إضافة الخطة', 'error');
                    } else {
                        alert('❌ ' + (data.message || 'حدث خطأ في إضافة الخطة'));
                    }
                    if (data.error_details) {
                        console.error('Error details:', data.error_details);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في الاتصال بالخادم', 'error');
            });
        }

        function submitEditPlan() {
            const form = document.getElementById('editPlanForm');
            const formData = new FormData(form);

            fetch('ajax_subscription_plans.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.adminPanel.showToast(data.message, 'success');
                    closeModal('editPlanModal');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    window.adminPanel.showToast(data.message || 'خطأ في تحديث الخطة', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في الاتصال بالخادم', 'error');
            });
        }

        function deletePlan(id) {
            if (confirm('هل أنت متأكد من حذف هذه الخطة؟')) {
                const formData = new FormData();
                formData.append('action', 'delete');
                formData.append('id', id);

                fetch('ajax_subscription_plans.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.adminPanel.showToast(data.message, 'success');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        window.adminPanel.showToast(data.message || 'خطأ في حذف الخطة', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.adminPanel.showToast('خطأ في الاتصال بالخادم', 'error');
                });
            }
        }
    </script>
</body>
</html>
