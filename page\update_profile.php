<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $phone = trim($_POST['phone']);
    
    if (empty($full_name)) {
        $error = 'الاسم الكامل مطلوب';
    } elseif (empty($phone)) {
        $error = 'رقم الهاتف مطلوب';
    } else {
        try {
            $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
            $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $stmt = $db->prepare("UPDATE users SET full_name = ?, phone = ? WHERE id = ?");
            $stmt->execute([$full_name, $phone, $user_id]);
            
            $message = 'تم تحديث البيانات بنجاح';
            
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء التحديث';
        }
    }
}

// Get current user data
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث الملف الشخصي - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="page-container">
        <div class="form-container">
            <div class="form-header">
                <h1>تحديث الملف الشخصي</h1>
                <p>يرجى تحديث بياناتك لتتمكن من استخدام جميع خدمات الدفع</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <form method="POST" class="profile-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" disabled>
                    <small>لا يمكن تغيير اسم المستخدم</small>
                </div>

                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" disabled>
                    <small>لا يمكن تغيير البريد الإلكتروني</small>
                </div>

                <div class="form-group">
                    <label for="full_name">الاسم الكامل *</label>
                    <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name'] ?: ''); ?>" required>
                    <small>مطلوب لعمليات الدفع الإلكتروني</small>
                </div>

                <div class="form-group">
                    <label for="phone">رقم الهاتف *</label>
                    <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?: ''); ?>" required>
                    <small>مطلوب لعمليات الدفع الإلكتروني</small>
                </div>

                <div class="form-group">
                    <label for="grade">الصف الدراسي</label>
                    <input type="text" id="grade" value="<?php echo htmlspecialchars($user['grade']); ?>" disabled>
                    <small>لا يمكن تغيير الصف الدراسي</small>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </form>

            <!-- Payment Readiness Check -->
            <div class="payment-check">
                <h3>جاهزية البيانات للدفع الإلكتروني</h3>
                <div class="check-items">
                    <div class="check-item <?php echo !empty($user['full_name']) ? 'valid' : 'invalid'; ?>">
                        <i class="fas <?php echo !empty($user['full_name']) ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        <span>الاسم الكامل</span>
                    </div>
                    <div class="check-item <?php echo !empty($user['phone']) ? 'valid' : 'invalid'; ?>">
                        <i class="fas <?php echo !empty($user['phone']) ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        <span>رقم الهاتف</span>
                    </div>
                    <div class="check-item <?php echo !empty($user['email']) ? 'valid' : 'invalid'; ?>">
                        <i class="fas <?php echo !empty($user['email']) ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        <span>البريد الإلكتروني</span>
                    </div>
                </div>
                
                <?php if (!empty($user['full_name']) && !empty($user['phone']) && !empty($user['email'])): ?>
                    <div class="payment-ready">
                        <i class="fas fa-shield-alt"></i>
                        <span>بياناتك جاهزة للدفع الإلكتروني</span>
                    </div>
                <?php else: ?>
                    <div class="payment-not-ready">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>يرجى إكمال البيانات المطلوبة للدفع الإلكتروني</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
        .page-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .form-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-header h1 {
            color: #4682B4;
            margin-bottom: 10px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }

        .form-group input:disabled {
            background: #f8f9fa;
            color: #6c757d;
        }

        .form-group small {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4682B4, #20B2AA);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .payment-check {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .payment-check h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .check-items {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .check-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .check-item.valid {
            color: #28a745;
        }

        .check-item.invalid {
            color: #dc3545;
        }

        .payment-ready {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .payment-not-ready {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .form-actions {
                flex-direction: column;
            }
        }
    </style>

    <?php include '../includes/footer.php'; ?>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
