<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>اختبار بسيط للإشعارات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
.btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; color: white; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-danger { background: #dc3545; }
</style>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات</div>";
    
    // Create tables if needed
    if (isset($_POST['create_tables'])) {
        try {
            $createNotificationsSQL = "CREATE TABLE IF NOT EXISTS user_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                notification_type ENUM('subscription_expired', 'subscription_expiring', 'subscription_renewed', 'subscription_cancelled') NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                is_dismissed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                metadata JSON NULL,
                INDEX idx_user_id (user_id),
                INDEX idx_type (notification_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($createNotificationsSQL);
            
            $createSettingsSQL = "CREATE TABLE IF NOT EXISTS notification_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                notification_type VARCHAR(50) NOT NULL,
                is_enabled BOOLEAN DEFAULT TRUE,
                last_shown TIMESTAMP NULL,
                show_count INT DEFAULT 0,
                max_show_count INT DEFAULT 1,
                UNIQUE KEY unique_user_type (user_id, notification_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($createSettingsSQL);
            
            echo "<div class='success'>✅ تم إنشاء جداول الإشعارات</div>";
        } catch (Exception $e) {
            echo "<div class='error'>خطأ في إنشاء الجداول: " . $e->getMessage() . "</div>";
        }
    }
    
    // Get first user
    $stmt = $db->query("SELECT id, username FROM users ORDER BY id LIMIT 1");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<div class='error'>لا يوجد مستخدمين</div>";
        exit;
    }
    
    $user_id = $user['id'];
    $username = $user['username'];
    
    echo "<div class='info'>المستخدم للاختبار: $username (ID: $user_id)</div>";
    
    // Expire user subscription
    if (isset($_POST['expire_user'])) {
        $stmt = $db->prepare("UPDATE users SET subscription_status = 'expired', current_plan_id = NULL WHERE id = ?");
        $result = $stmt->execute([$user_id]);
        
        if ($result) {
            echo "<div class='success'>✅ تم انتهاء اشتراك المستخدم</div>";
        } else {
            echo "<div class='error'>فشل في انتهاء الاشتراك</div>";
        }
    }
    
    // Reset notifications
    if (isset($_POST['reset_notifications'])) {
        $stmt = $db->prepare("DELETE FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
        $result = $stmt->execute([$user_id]);
        echo "<div class='success'>✅ تم إعادة تعيين الإشعارات</div>";
    }
    
    // Check current status
    $stmt = $db->prepare("SELECT subscription_status FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $userStatus = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='info'>حالة الاشتراك: " . ($userStatus['subscription_status'] ?: 'غير محدد') . "</div>";
    
    // Check notification settings
    try {
        $stmt = $db->prepare("SELECT * FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
        $stmt->execute([$user_id]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings) {
            echo "<div class='info'>إعدادات الإشعار: عُرض {$settings['show_count']} مرة</div>";
            $shouldShow = ($settings['show_count'] < $settings['max_show_count']);
        } else {
            echo "<div class='info'>لا توجد إعدادات إشعار - سيظهر الإشعار</div>";
            $shouldShow = true;
        }
    } catch (Exception $e) {
        echo "<div class='info'>جدول الإعدادات غير موجود</div>";
        $shouldShow = false;
    }
    
    // Check if should show notification
    $canShow = ($userStatus['subscription_status'] === 'expired') && $shouldShow;
    echo "<div class='info'>يجب إظهار الإشعار: " . ($canShow ? 'نعم ✅' : 'لا ❌') . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
}
?>

<h3>خطوات الاختبار:</h3>

<form method="POST">
    <button type="submit" name="create_tables" class="btn btn-primary">1. إنشاء جداول الإشعارات</button>
</form>

<form method="POST">
    <button type="submit" name="expire_user" class="btn btn-danger">2. انتهاء اشتراك المستخدم</button>
</form>

<div class="info">
    <strong>3. افتح الداشبورد:</strong>
    <a href="../page/dashboard.php" class="btn btn-success" target="_blank">فتح الداشبورد</a>
</div>

<form method="POST">
    <button type="submit" name="reset_notifications" class="btn btn-primary">4. إعادة تعيين الإشعارات</button>
</form>

<div class="info">
    <h4>الملفات المطلوبة:</h4>
    <ul>
        <li>✅ جداول قاعدة البيانات</li>
        <li>✅ ملف الإشعار: includes/subscription_notification.php</li>
        <li>✅ تم إضافة الإشعار في الداشبورد والمنهج والاشتراكات</li>
    </ul>
</div>

<p><a href="../page/dashboard.php" class="btn btn-success">الداشبورد</a></p>
