<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/PaymentGateway.php';

class PaymobGateway extends PaymentGateway {
    private $config;
    private $authToken;
    
    public function __construct() {
        parent::__construct();
        $this->config = $this->getGatewayConfig('paymob');
    }
    
    /**
     * Get authentication token
     */
    private function getAuthToken() {
        if ($this->authToken) {
            return $this->authToken;
        }
        
        $authData = [
            'api_key' => $this->config['api_key']
        ];
        
        $response = $this->sendPaymobRequest('/auth/tokens', $authData);
        
        if ($response && isset($response['token'])) {
            $this->authToken = $response['token'];
            return $this->authToken;
        }
        
        throw new Exception('Failed to get Paymob auth token');
    }
    
    /**
     * Create Paymob payment request
     */
    public function createPaymentRequest($userId, $courseId, $amount, $customerInfo) {
        try {
            // Create transaction record
            $transactionId = $this->createTransaction($userId, $courseId, 'paymob', $amount);
            if (!$transactionId) {
                throw new Exception('Failed to create transaction record');
            }
            
            // Get auth token
            $authToken = $this->getAuthToken();
            
            // Step 1: Create order
            $orderData = [
                'auth_token' => $authToken,
                'delivery_needed' => false,
                'amount_cents' => $amount * 100, // Convert to cents
                'currency' => 'EGP',
                'merchant_order_id' => $transactionId,
                'items' => [
                    [
                        'name' => $customerInfo['course_title'],
                        'amount_cents' => $amount * 100,
                        'description' => 'Course: ' . $customerInfo['course_title'],
                        'quantity' => 1
                    ]
                ]
            ];
            
            $orderResponse = $this->sendPaymobRequest('/ecommerce/orders', $orderData);
            
            if (!$orderResponse || !isset($orderResponse['id'])) {
                throw new Exception('Failed to create Paymob order');
            }
            
            $orderId = $orderResponse['id'];
            
            // Step 2: Create payment key
            $paymentKeyData = [
                'auth_token' => $authToken,
                'amount_cents' => $amount * 100,
                'expiration' => 3600, // 1 hour
                'order_id' => $orderId,
                'billing_data' => [
                    'apartment' => 'NA',
                    'email' => $customerInfo['email'],
                    'floor' => 'NA',
                    'first_name' => explode(' ', $customerInfo['name'])[0] ?? $customerInfo['name'],
                    'street' => 'NA',
                    'building' => 'NA',
                    'phone_number' => $customerInfo['mobile'] ?? '01000000000',
                    'shipping_method' => 'NA',
                    'postal_code' => 'NA',
                    'city' => 'Cairo',
                    'country' => 'EG',
                    'last_name' => explode(' ', $customerInfo['name'], 2)[1] ?? 'User',
                    'state' => 'Cairo'
                ],
                'currency' => 'EGP',
                'integration_id' => $this->config['integration_id']
            ];
            
            $paymentKeyResponse = $this->sendPaymobRequest('/acceptance/payment_keys', $paymentKeyData);
            
            if (!$paymentKeyResponse || !isset($paymentKeyResponse['token'])) {
                throw new Exception('Failed to create Paymob payment key');
            }
            
            $paymentToken = $paymentKeyResponse['token'];
            
            // Update transaction with Paymob order ID
            $this->updateTransactionStatus($transactionId, 'pending', $orderId, [
                'order_id' => $orderId,
                'payment_token' => $paymentToken
            ]);
            
            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'payment_url' => 'https://accept.paymob.com/api/acceptance/iframes/' . $this->config['iframe_id'] . '?payment_token=' . $paymentToken,
                'payment_token' => $paymentToken,
                'order_id' => $orderId
            ];
            
        } catch (Exception $e) {
            error_log("Paymob payment error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment initialization failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Send request to Paymob API
     */
    private function sendPaymobRequest($endpoint, $data) {
        $url = $this->config['base_url'] . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("Paymob API cURL error: " . $error);
            return false;
        }
        
        if ($httpCode !== 200 && $httpCode !== 201) {
            error_log("Paymob API HTTP error: " . $httpCode . " - " . $response);
            return false;
        }
        
        return json_decode($response, true);
    }
    
    /**
     * Handle Paymob callback
     */
    public function handleCallback($callbackData) {
        try {
            // Verify HMAC signature
            $hmacSignature = $callbackData['hmac'] ?? '';
            if (!$this->verifyHmacSignature($callbackData, $hmacSignature)) {
                error_log("Paymob callback HMAC verification failed");
                return false;
            }
            
            $orderId = $callbackData['order']['id'];
            $transactionId = $callbackData['order']['merchant_order_id'];
            $success = $callbackData['success'] === 'true' || $callbackData['success'] === true;
            $paymobTransactionId = $callbackData['id'];
            
            // Get transaction
            $transaction = $this->getTransaction($transactionId);
            if (!$transaction) {
                error_log("Transaction not found: " . $transactionId);
                return false;
            }
            
            // Process based on payment status
            if ($success && !$callbackData['error_occured']) {
                // Payment successful
                $success = $this->processSuccessfulPayment($transactionId, $paymobTransactionId, $callbackData);
                if ($success) {
                    error_log("Paymob payment successful: " . $transactionId);
                    return true;
                } else {
                    error_log("Failed to process successful Paymob payment: " . $transactionId);
                    return false;
                }
            } else {
                // Payment failed
                $this->updateTransactionStatus($transactionId, 'failed', $paymobTransactionId, $callbackData);
                error_log("Paymob payment failed: " . $transactionId);
                return true;
            }
            
        } catch (Exception $e) {
            error_log("Paymob callback error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Verify HMAC signature
     */
    private function verifyHmacSignature($data, $signature) {
        // Build the signature string according to Paymob documentation
        $signatureString = $data['amount_cents'] . $data['created_at'] . 
                          $data['currency'] . $data['error_occured'] . 
                          $data['has_parent_transaction'] . $data['id'] . 
                          $data['integration_id'] . $data['is_3d_secure'] . 
                          $data['is_auth'] . $data['is_capture'] . 
                          $data['is_refunded'] . $data['is_standalone_payment'] . 
                          $data['is_voided'] . $data['order']['id'] . 
                          $data['owner'] . $data['pending'] . 
                          $data['source_data']['pan'] . $data['source_data']['sub_type'] . 
                          $data['source_data']['type'] . $data['success'];
        
        $expectedSignature = hash_hmac('sha512', $signatureString, $this->config['hmac_secret']);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Check payment status
     */
    public function checkPaymentStatus($transactionId) {
        try {
            $transaction = $this->getTransaction($transactionId);
            if (!$transaction) {
                return ['success' => false, 'error' => 'Transaction not found'];
            }
            
            $gatewayResponse = json_decode($transaction['gateway_response'], true);
            $orderId = $gatewayResponse['order_id'] ?? null;
            
            if (!$orderId) {
                return ['success' => false, 'error' => 'Order ID not found'];
            }
            
            // Get auth token
            $authToken = $this->getAuthToken();
            
            // Query Paymob for order status
            $url = $this->config['base_url'] . '/ecommerce/orders/' . $orderId;
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $authToken,
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $orderData = json_decode($response, true);
                return [
                    'success' => true,
                    'status' => $orderData['paid_amount_cents'] > 0 ? 'PAID' : 'PENDING',
                    'order_id' => $orderId,
                    'amount' => $orderData['amount_cents'] / 100
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to check payment status'];
            
        } catch (Exception $e) {
            error_log("Paymob status check error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Status check failed'];
        }
    }
}
?>
