<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إصلاح عمود الأيقونة في جدول subscription_plans</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
</style>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // Check if subscription_plans table exists
    $stmt = $db->query("SHOW TABLES LIKE 'subscription_plans'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='error'>❌ جدول subscription_plans غير موجود</div>";
        exit;
    }
    
    echo "<div class='info'>📋 جدول subscription_plans موجود</div>";
    
    // Check current structure of icon column
    $stmt = $db->query("SHOW COLUMNS FROM subscription_plans LIKE 'icon'");
    $iconColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($iconColumn) {
        echo "<div class='info'>📊 هيكل عمود icon الحالي:</div>";
        echo "<ul>";
        echo "<li><strong>النوع:</strong> {$iconColumn['Type']}</li>";
        echo "<li><strong>Null:</strong> {$iconColumn['Null']}</li>";
        echo "<li><strong>Default:</strong> {$iconColumn['Default']}</li>";
        echo "</ul>";
        
        // Check if it needs to be updated
        if (strpos($iconColumn['Type'], 'varchar(10)') !== false) {
            echo "<div class='info'>🔧 يحتاج عمود icon للتحديث من VARCHAR(10) إلى VARCHAR(50)</div>";
            
            // Update the column
            $alterSQL = "ALTER TABLE subscription_plans MODIFY COLUMN icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة الخطة'";
            $db->exec($alterSQL);
            
            echo "<div class='success'>✅ تم تحديث عمود icon بنجاح إلى VARCHAR(50)</div>";
            
            // Verify the change
            $stmt = $db->query("SHOW COLUMNS FROM subscription_plans LIKE 'icon'");
            $updatedColumn = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<div class='info'>📊 هيكل عمود icon بعد التحديث:</div>";
            echo "<ul>";
            echo "<li><strong>النوع:</strong> {$updatedColumn['Type']}</li>";
            echo "<li><strong>Null:</strong> {$updatedColumn['Null']}</li>";
            echo "<li><strong>Default:</strong> {$updatedColumn['Default']}</li>";
            echo "</ul>";
            
        } else {
            echo "<div class='success'>✅ عمود icon بالحجم الصحيح بالفعل</div>";
        }
    } else {
        echo "<div class='error'>❌ عمود icon غير موجود في الجدول</div>";
        
        // Add the icon column
        $alterSQL = "ALTER TABLE subscription_plans ADD COLUMN icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة الخطة'";
        $db->exec($alterSQL);
        
        echo "<div class='success'>✅ تم إضافة عمود icon بنجاح</div>";
    }
    
    // Test adding a plan with a long icon
    echo "<div class='info'>🧪 اختبار إضافة خطة بأيقونة طويلة...</div>";
    
    $testIcon = "fas fa-graduation-cap"; // This is longer than 10 characters
    echo "<div class='info'>📝 الأيقونة المختبرة: '$testIcon' (الطول: " . strlen($testIcon) . " حرف)</div>";
    
    $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discounted_price, duration_days, features, icon, color, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        'خطة اختبار الأيقونة',
        'Icon Test Plan',
        'هذه خطة لاختبار الأيقونة الطويلة',
        100.00,
        100.00,
        30,
        '["اختبار الأيقونة"]',
        $testIcon,
        '#28a745',
        1
    ]);
    
    if ($result) {
        $planId = $db->lastInsertId();
        echo "<div class='success'>✅ تم إضافة خطة الاختبار بنجاح - ID: $planId</div>";
        
        // Verify the icon was saved correctly
        $stmt = $db->prepare("SELECT icon FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        $savedIcon = $stmt->fetchColumn();
        
        echo "<div class='info'>💾 الأيقونة المحفوظة: '$savedIcon'</div>";
        
        if ($savedIcon === $testIcon) {
            echo "<div class='success'>✅ تم حفظ الأيقونة بشكل صحيح!</div>";
        } else {
            echo "<div class='error'>❌ الأيقونة لم تُحفظ بشكل صحيح</div>";
        }
        
        // Clean up - delete the test plan
        $stmt = $db->prepare("DELETE FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        echo "<div class='info'>🗑️ تم حذف خطة الاختبار</div>";
        
    } else {
        echo "<div class='error'>❌ فشل في إضافة خطة الاختبار</div>";
    }
    
    // Show current plans count
    $stmt = $db->query("SELECT COUNT(*) as count FROM subscription_plans");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>📊 عدد الخطط الحالية: $count</div>";
    
    echo "<div class='success'>🎉 تم إصلاح المشكلة بنجاح! يمكنك الآن إضافة خطط بأيقونات طويلة.</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div class='error'>الملف: " . $e->getFile() . "</div>";
    echo "<div class='error'>السطر: " . $e->getLine() . "</div>";
}
?>

<p><a href="subscription_plans.php">العودة لصفحة إدارة الخطط</a></p>
<p><a href="test_ajax.php">اختبار AJAX</a></p>
