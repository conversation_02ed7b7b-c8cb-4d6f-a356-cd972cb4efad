<?php
header('Content-Type: application/json');
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access'
    ]);
    exit;
}

try {
    $userManager = new UserManager();
    
    // Get parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 20))); // Max 100 records per page
    $search = trim($_GET['search'] ?? '');
    $status = $_GET['status'] ?? 'all'; // all, active, inactive
    $education_level = $_GET['education_level'] ?? 'all';
    $sort_by = $_GET['sort_by'] ?? 'created_at';
    $sort_order = strtoupper($_GET['sort_order'] ?? 'DESC');
    
    // Validate sort order
    if (!in_array($sort_order, ['ASC', 'DESC'])) {
        $sort_order = 'DESC';
    }
    
    // Validate sort field
    $allowed_sort_fields = ['id', 'username', 'email', 'first_name', 'created_at', 'last_login'];
    if (!in_array($sort_by, $allowed_sort_fields)) {
        $sort_by = 'created_at';
    }
    
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(username LIKE ? OR email LIKE ? OR first_name LIKE ? OR second_name LIKE ? OR third_name LIKE ? OR fourth_name LIKE ?)";
        $search_param = "%{$search}%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param, $search_param]);
    }
    
    if ($status === 'active') {
        $where_conditions[] = "is_active = 1";
    } elseif ($status === 'inactive') {
        $where_conditions[] = "is_active = 0";
    }
    
    if ($education_level !== 'all') {
        $where_conditions[] = "education_level = ?";
        $params[] = $education_level;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM users {$where_clause}";
    $db = Database::getInstance()->getConnection();
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute($params);
    $total_users = $count_stmt->fetch()['total'];
    
    // Get users data
    $query = "
        SELECT 
            id, username, email, 
            first_name, second_name, third_name, fourth_name,
            personal_phone, father_phone, mother_phone,
            education_level, education_type, grade, specialization,
            is_active, created_at, last_login
        FROM users 
        {$where_clause}
        ORDER BY {$sort_by} {$sort_order}
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
    // Format user data
    $formatted_users = [];
    foreach ($users as $user) {
        $full_name = trim(implode(' ', array_filter([
            $user['first_name'],
            $user['second_name'],
            $user['third_name'],
            $user['fourth_name']
        ])));
        
        $formatted_users[] = [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'full_name' => $full_name,
            'first_name' => $user['first_name'],
            'second_name' => $user['second_name'],
            'third_name' => $user['third_name'],
            'fourth_name' => $user['fourth_name'],
            'personal_phone' => $user['personal_phone'],
            'father_phone' => $user['father_phone'],
            'mother_phone' => $user['mother_phone'],
            'education_level' => $user['education_level'],
            'education_type' => $user['education_type'],
            'grade' => $user['grade'],
            'specialization' => $user['specialization'],
            'is_active' => (bool)$user['is_active'],
            'created_at' => $user['created_at'],
            'last_login' => $user['last_login'],
            'formatted_created_at' => $user['created_at'] ? date('Y-m-d H:i', strtotime($user['created_at'])) : null,
            'formatted_last_login' => $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول'
        ];
    }
    
    // Calculate pagination info
    $total_pages = ceil($total_users / $limit);
    
    // Get statistics
    $stats_query = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_this_month,
            SUM(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as active_this_week
        FROM users
    ";
    $stats_stmt = $db->query($stats_query);
    $stats = $stats_stmt->fetch();
    
    // Return response
    echo json_encode([
        'success' => true,
        'data' => [
            'users' => $formatted_users,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $total_pages,
                'total_users' => (int)$total_users,
                'per_page' => $limit,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            ],
            'filters' => [
                'search' => $search,
                'status' => $status,
                'education_level' => $education_level,
                'sort_by' => $sort_by,
                'sort_order' => $sort_order
            ],
            'statistics' => [
                'total' => (int)$stats['total'],
                'active' => (int)$stats['active'],
                'inactive' => (int)$stats['inactive'],
                'new_this_month' => (int)$stats['new_this_month'],
                'active_this_week' => (int)$stats['active_this_week']
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("API Error in get_users.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => 'حدث خطأ في الخادم'
    ], JSON_UNESCAPED_UNICODE);
}
?>
