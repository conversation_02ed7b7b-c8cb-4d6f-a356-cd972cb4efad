<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'generate_course_code':
                    // Generate course activation code
                    $code = strtoupper($_POST['custom_code'] ?: generateRandomCode());
                    $expires_at = $_POST['expires_at'] ? $_POST['expires_at'] : null;
                    
                    $stmt = $db->prepare("INSERT INTO course_activation_codes (code, course_id, expires_at, created_by, notes) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $code,
                        $_POST['course_id'],
                        $expires_at,
                        $_SESSION['admin_id'],
                        $_POST['notes']
                    ]);
                    $message = "تم إنشاء كود تفعيل الكورس: $code";
                    break;
                    
                case 'generate_subscription_code':
                    // Generate subscription activation code
                    $code = strtoupper($_POST['custom_code'] ?: generateRandomCode());
                    $expires_at = $_POST['expires_at'] ? $_POST['expires_at'] : null;
                    
                    $stmt = $db->prepare("INSERT INTO activation_codes (code, plan_id, expires_at, created_by, notes) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $code,
                        $_POST['plan_id'],
                        $expires_at,
                        $_SESSION['admin_id'],
                        $_POST['notes']
                    ]);
                    $message = "تم إنشاء كود تفعيل الاشتراك: $code";
                    break;
                    
                case 'generate_bulk':
                    $count = intval($_POST['count']);
                    $type = $_POST['type']; // 'course' or 'subscription'
                    $target_id = $_POST['target_id'];
                    $expires_at = $_POST['expires_at'] ? $_POST['expires_at'] : null;
                    $prefix = $_POST['prefix'] ?: '';
                    
                    $generated = [];
                    for ($i = 0; $i < $count; $i++) {
                        $code = strtoupper($prefix . generateRandomCode());
                        try {
                            if ($type === 'course') {
                                $stmt = $db->prepare("INSERT INTO course_activation_codes (code, course_id, expires_at, created_by, notes) VALUES (?, ?, ?, ?, ?)");
                                $stmt->execute([
                                    $code,
                                    $target_id,
                                    $expires_at,
                                    $_SESSION['admin_id'],
                                    "كود كورس مُولد تلقائياً - دفعة " . date('Y-m-d H:i')
                                ]);
                            } else {
                                $stmt = $db->prepare("INSERT INTO activation_codes (code, plan_id, expires_at, created_by, notes) VALUES (?, ?, ?, ?, ?)");
                                $stmt->execute([
                                    $code,
                                    $target_id,
                                    $expires_at,
                                    $_SESSION['admin_id'],
                                    "كود اشتراك مُولد تلقائياً - دفعة " . date('Y-m-d H:i')
                                ]);
                            }
                            $generated[] = $code;
                        } catch (Exception $e) {
                            // Skip duplicate codes
                            $i--;
                        }
                    }
                    $message = "تم إنشاء " . count($generated) . " كود تفعيل";
                    break;
                    
                case 'delete_course_code':
                    $stmt = $db->prepare("DELETE FROM course_activation_codes WHERE id = ? AND is_used = 0");
                    $stmt->execute([$_POST['id']]);
                    if ($stmt->rowCount() > 0) {
                        $message = 'تم حذف كود تفعيل الكورس بنجاح';
                    } else {
                        $error = 'لا يمكن حذف كود مستخدم';
                    }
                    break;
                    
                case 'delete_subscription_code':
                    $stmt = $db->prepare("DELETE FROM activation_codes WHERE id = ? AND is_used = 0");
                    $stmt->execute([$_POST['id']]);
                    if ($stmt->rowCount() > 0) {
                        $message = 'تم حذف كود تفعيل الاشتراك بنجاح';
                    } else {
                        $error = 'لا يمكن حذف كود مستخدم';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// Get course activation codes
$course_codes = [];
try {
    // تأكد من وجود اتصال قاعدة البيانات
    if (!isset($db)) {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }

    $stmt = $db->query("SELECT cac.*, c.title as course_title, u.username as used_by_name, a.username as created_by_name
                       FROM course_activation_codes cac
                       JOIN courses c ON cac.course_id = c.id
                       LEFT JOIN users u ON cac.used_by = u.id
                       LEFT JOIN admins a ON cac.created_by = a.id
                       ORDER BY cac.created_at DESC");
    $course_codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Table might not exist yet
}

// Get subscription activation codes
$subscription_codes = [];
try {
    $stmt = $db->query("SELECT ac.*, sp.name as plan_name, sp.color as plan_color, sp.icon as plan_icon,
                       u.username as used_by_name, a.username as created_by_name
                       FROM activation_codes ac 
                       JOIN subscription_plans sp ON ac.plan_id = sp.id
                       LEFT JOIN users u ON ac.used_by = u.id
                       LEFT JOIN admins a ON ac.created_by = a.id
                       ORDER BY ac.created_at DESC");
    $subscription_codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Table might not exist yet
}

// Get courses for dropdown
try {
    if (!isset($db)) {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    $stmt = $db->query("SELECT id, title FROM courses WHERE is_active = 1 ORDER BY title");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $courses = [];
}

// Get plans for dropdown
try {
    $stmt = $db->query("SELECT id, name, color, icon FROM subscription_plans WHERE is_active = 1 ORDER BY sort_order");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $plans = [];
}

function generateRandomCode($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أكواد التفعيل - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>إدارة أكواد التفعيل</h1>
                <p>إنشاء وإدارة أكواد تفعيل الكورسات والاشتراكات</p>
            </div>

            <!-- Action Buttons -->
            <div class="content-card">
                <div class="d-flex justify-between items-center mb-4">
                    <h2>أكواد التفعيل</h2>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="openModal('generateCourseCodeModal')">
                            <i class="fas fa-plus"></i>
                            كود تفعيل كورس
                        </button>
                        <button class="btn btn-success" onclick="openModal('generateSubscriptionCodeModal')">
                            <i class="fas fa-plus"></i>
                            كود تفعيل اشتراك
                        </button>
                        <button class="btn btn-warning" onclick="openModal('bulkGenerateModal')">
                            <i class="fas fa-layer-group"></i>
                            إنشاء أكواد متعددة
                        </button>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="content-card">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $message; ?>
                    </div>
                </div>
            <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Tabs -->
                <div class="tabs">
                    <button class="tab-btn active" onclick="showTab('course-codes')">أكواد الكورسات</button>
                    <button class="tab-btn" onclick="showTab('subscription-codes')">أكواد الاشتراكات</button>
                </div>

                <!-- Course Codes Tab -->
                <div id="course-codes" class="tab-content active">
                    <h3>أكواد تفعيل الكورسات</h3>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>الكورس</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>مستخدم بواسطة</th>
                                    <th>تاريخ الاستخدام</th>
                                    <th>منشئ الكود</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($course_codes as $code): ?>
                                    <?php
                                    $is_expired = $code['expires_at'] && strtotime($code['expires_at']) < time();
                                    $status_class = $code['is_used'] ? 'used' : ($is_expired ? 'expired' : 'available');
                                    $status_text = $code['is_used'] ? 'مستخدم' : ($is_expired ? 'منتهي' : 'متاح');
                                    ?>
                                    <tr class="code-row <?php echo $status_class; ?>">
                                        <td>
                                            <div class="code-display">
                                                <span class="code-text"><?php echo htmlspecialchars($code['code']); ?></span>
                                                <button class="copy-btn" onclick="copyCode('<?php echo $code['code']; ?>')" title="نسخ الكود">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($code['course_title']); ?></td>
                                        <td>
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($code['expires_at']): ?>
                                                <span class="<?php echo $is_expired ? 'text-danger' : ''; ?>">
                                                    <?php echo date('Y-m-d H:i', strtotime($code['expires_at'])); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">بلا انتهاء</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($code['used_by_name']): ?>
                                                <span class="user-name"><?php echo htmlspecialchars($code['used_by_name']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($code['used_at']): ?>
                                                <?php echo date('Y-m-d H:i', strtotime($code['used_at'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="admin-name"><?php echo htmlspecialchars($code['created_by_name']); ?></span>
                                        </td>
                                        <td>
                                            <?php if (!$code['is_used']): ?>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الكود؟')">
                                                    <input type="hidden" name="action" value="delete_course_code">
                                                    <input type="hidden" name="id" value="<?php echo $code['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                        حذف
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="text-muted">مستخدم</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Subscription Codes Tab -->
                <div id="subscription-codes" class="tab-content">
                    <h3>أكواد تفعيل الاشتراكات</h3>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>الخطة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>مستخدم بواسطة</th>
                                    <th>تاريخ الاستخدام</th>
                                    <th>منشئ الكود</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subscription_codes as $code): ?>
                                    <?php
                                    $is_expired = $code['expires_at'] && strtotime($code['expires_at']) < time();
                                    $status_class = $code['is_used'] ? 'used' : ($is_expired ? 'expired' : 'available');
                                    $status_text = $code['is_used'] ? 'مستخدم' : ($is_expired ? 'منتهي' : 'متاح');
                                    ?>
                                    <tr class="code-row <?php echo $status_class; ?>">
                                        <td>
                                            <div class="code-display">
                                                <span class="code-text"><?php echo htmlspecialchars($code['code']); ?></span>
                                                <button class="copy-btn" onclick="copyCode('<?php echo $code['code']; ?>')" title="نسخ الكود">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="plan-info">
                                                <span class="plan-icon" style="color: <?php echo $code['plan_color']; ?>">
                                                    <?php echo $code['plan_icon']; ?>
                                                </span>
                                                <?php echo htmlspecialchars($code['plan_name']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($code['expires_at']): ?>
                                                <span class="<?php echo $is_expired ? 'text-danger' : ''; ?>">
                                                    <?php echo date('Y-m-d H:i', strtotime($code['expires_at'])); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">بلا انتهاء</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($code['used_by_name']): ?>
                                                <span class="user-name"><?php echo htmlspecialchars($code['used_by_name']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($code['used_at']): ?>
                                                <?php echo date('Y-m-d H:i', strtotime($code['used_at'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="admin-name"><?php echo htmlspecialchars($code['created_by_name']); ?></span>
                                        </td>
                                        <td>
                                            <?php if (!$code['is_used']): ?>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الكود؟')">
                                                    <input type="hidden" name="action" value="delete_subscription_code">
                                                    <input type="hidden" name="id" value="<?php echo $code['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                        حذف
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="text-muted">مستخدم</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Generate Course Code Modal -->
    <div class="modal" id="generateCourseCodeModal">
        <div class="modal-header">
            <h3 class="modal-title">إنشاء كود تفعيل كورس</h3>
            <button class="modal-close" onclick="closeModal('generateCourseCodeModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="generateCourseCodeForm" method="POST">
                <input type="hidden" name="action" value="generate_course_code">
                <div class="form-group">
                    <label class="form-label">اختر الكورس</label>
                    <select class="form-select" name="course_id" required>
                        <option value="">اختر كورس</option>
                        <?php
                        try {
                            $db = Database::getInstance()->getConnection();
                            $stmt = $db->query("SELECT id, title FROM courses WHERE is_active = 1 ORDER BY title");
                            while ($course = $stmt->fetch()) {
                                echo "<option value='{$course['id']}'>{$course['title']}</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>خطأ في تحميل الكورسات</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">كود مخصص (اختياري)</label>
                    <input type="text" class="form-input" name="custom_code" placeholder="اتركه فارغاً لإنشاء كود تلقائي">
                </div>
                <div class="form-group">
                    <label class="form-label">تاريخ انتهاء الصلاحية (اختياري)</label>
                    <input type="datetime-local" class="form-input" name="expires_at">
                </div>
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-textarea" name="notes" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitGenerateCourseCode()">إنشاء الكود</button>
            <button class="btn btn-secondary" onclick="closeModal('generateCourseCodeModal')">إلغاء</button>
        </div>
    </div>

    <!-- Generate Subscription Code Modal -->
    <div class="modal" id="generateSubscriptionCodeModal">
        <div class="modal-header">
            <h3 class="modal-title">إنشاء كود تفعيل اشتراك</h3>
            <button class="modal-close" onclick="closeModal('generateSubscriptionCodeModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="generateSubscriptionCodeForm" method="POST">
                <input type="hidden" name="action" value="generate_subscription_code">
                <div class="form-group">
                    <label class="form-label">اختر خطة الاشتراك</label>
                    <select class="form-select" name="plan_id" required>
                        <option value="">اختر خطة</option>
                        <?php
                        try {
                            $db = Database::getInstance()->getConnection();
                            $stmt = $db->query("SELECT id, name FROM subscription_plans WHERE is_active = 1 ORDER BY name");
                            while ($plan = $stmt->fetch()) {
                                echo "<option value='{$plan['id']}'>{$plan['name']}</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>خطأ في تحميل الخطط</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">كود مخصص (اختياري)</label>
                    <input type="text" class="form-input" name="custom_code" placeholder="اتركه فارغاً لإنشاء كود تلقائي">
                </div>
                <div class="form-group">
                    <label class="form-label">تاريخ انتهاء الصلاحية (اختياري)</label>
                    <input type="datetime-local" class="form-input" name="expires_at">
                </div>
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-textarea" name="notes" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitGenerateSubscriptionCode()">إنشاء الكود</button>
            <button class="btn btn-secondary" onclick="closeModal('generateSubscriptionCodeModal')">إلغاء</button>
        </div>
    </div>

    <!-- Bulk Generate Modal -->
    <div class="modal" id="bulkGenerateModal">
        <div class="modal-header">
            <h3 class="modal-title">إنشاء أكواد متعددة</h3>
            <button class="modal-close" onclick="closeModal('bulkGenerateModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="bulkGenerateForm" method="POST">
                <input type="hidden" name="action" value="generate_bulk">
                <div class="form-group">
                    <label class="form-label">نوع الكود</label>
                    <select class="form-select" name="code_type" id="bulkCodeType" onchange="updateBulkOptions()" required>
                        <option value="">اختر نوع الكود</option>
                        <option value="course">كود تفعيل كورس</option>
                        <option value="subscription">كود تفعيل اشتراك</option>
                    </select>
                </div>
                <div class="form-group" id="bulkCourseSelect" style="display: none;">
                    <label class="form-label">اختر الكورس</label>
                    <select class="form-select" name="course_id">
                        <option value="">اختر كورس</option>
                        <?php
                        try {
                            $db = Database::getInstance()->getConnection();
                            $stmt = $db->query("SELECT id, title FROM courses WHERE is_active = 1 ORDER BY title");
                            while ($course = $stmt->fetch()) {
                                echo "<option value='{$course['id']}'>{$course['title']}</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>خطأ في تحميل الكورسات</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="form-group" id="bulkPlanSelect" style="display: none;">
                    <label class="form-label">اختر خطة الاشتراك</label>
                    <select class="form-select" name="plan_id">
                        <option value="">اختر خطة</option>
                        <?php
                        try {
                            $db = Database::getInstance()->getConnection();
                            $stmt = $db->query("SELECT id, name FROM subscription_plans WHERE is_active = 1 ORDER BY name");
                            while ($plan = $stmt->fetch()) {
                                echo "<option value='{$plan['id']}'>{$plan['name']}</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>خطأ في تحميل الخطط</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label class="form-label">عدد الأكواد</label>
                        <input type="number" class="form-input" name="count" min="1" max="100" value="10" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">بادئة الكود (اختياري)</label>
                        <input type="text" class="form-input" name="prefix" placeholder="مثال: BULK">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">تاريخ انتهاء الصلاحية (اختياري)</label>
                    <input type="datetime-local" class="form-input" name="expires_at">
                </div>
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-textarea" name="notes" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitBulkGenerate()">إنشاء الأكواد</button>
            <button class="btn btn-secondary" onclick="closeModal('bulkGenerateModal')">إلغاء</button>
        </div>
    </div>
            <div class="modal-header">
                <h3 id="generateModalTitle">إنشاء كود تفعيل</h3>
                <span class="close" onclick="closeModal('generateModal')">&times;</span>
            </div>
            <form method="POST" id="generateForm">
                <input type="hidden" name="action" id="generateAction">
                
                <div class="form-group" id="courseGroup" style="display: none;">
                    <label for="course_id">الكورس *</label>
                    <select id="course_id" name="course_id">
                        <option value="">اختر الكورس</option>
                        <?php foreach ($courses as $course): ?>
                            <option value="<?php echo $course['id']; ?>">
                                <?php echo htmlspecialchars($course['title']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group" id="planGroup" style="display: none;">
                    <label for="plan_id">الخطة *</label>
                    <select id="plan_id" name="plan_id">
                        <option value="">اختر الخطة</option>
                        <?php foreach ($plans as $plan): ?>
                            <option value="<?php echo $plan['id']; ?>">
                                <?php echo $plan['icon']; ?> <?php echo htmlspecialchars($plan['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="custom_code">كود مخصص (اختياري)</label>
                    <input type="text" id="custom_code" name="custom_code" placeholder="اتركه فارغاً للتوليد التلقائي">
                </div>
                
                <div class="form-group">
                    <label for="expires_at">تاريخ الانتهاء (اختياري)</label>
                    <input type="datetime-local" id="expires_at" name="expires_at">
                </div>
                
                <div class="form-group">
                    <label for="notes">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="3"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إنشاء الكود
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('generateModal')">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Generate Bulk Codes Modal -->
    <div id="bulkModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إنشاء أكواد متعددة</h3>
                <span class="close" onclick="closeModal('bulkModal')">&times;</span>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="generate_bulk">
                
                <div class="form-group">
                    <label for="bulk_type">نوع الكود *</label>
                    <select id="bulk_type" name="type" required onchange="toggleBulkTarget()">
                        <option value="">اختر النوع</option>
                        <option value="course">كود كورس</option>
                        <option value="subscription">كود اشتراك</option>
                    </select>
                </div>
                
                <div class="form-group" id="bulkCourseGroup" style="display: none;">
                    <label for="bulk_course_id">الكورس *</label>
                    <select id="bulk_course_id" name="target_id">
                        <option value="">اختر الكورس</option>
                        <?php foreach ($courses as $course): ?>
                            <option value="<?php echo $course['id']; ?>">
                                <?php echo htmlspecialchars($course['title']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group" id="bulkPlanGroup" style="display: none;">
                    <label for="bulk_plan_id">الخطة *</label>
                    <select id="bulk_plan_id" name="target_id">
                        <option value="">اختر الخطة</option>
                        <?php foreach ($plans as $plan): ?>
                            <option value="<?php echo $plan['id']; ?>">
                                <?php echo $plan['icon']; ?> <?php echo htmlspecialchars($plan['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="count">عدد الأكواد *</label>
                        <input type="number" id="count" name="count" min="1" max="100" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="prefix">بادئة الكود (اختياري)</label>
                        <input type="text" id="prefix" name="prefix" maxlength="5" placeholder="مثل: VIP">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="bulk_expires_at">تاريخ الانتهاء (اختياري)</label>
                    <input type="datetime-local" id="bulk_expires_at" name="expires_at">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-layer-group"></i>
                        إنشاء الأكواد
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('bulkModal')">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function showGenerateModal(type) {
            if (type === 'course') {
                document.getElementById('generateModalTitle').textContent = 'إنشاء كود تفعيل كورس';
                document.getElementById('generateAction').value = 'generate_course_code';
                document.getElementById('courseGroup').style.display = 'block';
                document.getElementById('planGroup').style.display = 'none';
                document.getElementById('course_id').required = true;
                document.getElementById('plan_id').required = false;
            } else {
                document.getElementById('generateModalTitle').textContent = 'إنشاء كود تفعيل اشتراك';
                document.getElementById('generateAction').value = 'generate_subscription_code';
                document.getElementById('courseGroup').style.display = 'none';
                document.getElementById('planGroup').style.display = 'block';
                document.getElementById('course_id').required = false;
                document.getElementById('plan_id').required = true;
            }
            document.getElementById('generateModal').style.display = 'block';
        }

        function showBulkModal() {
            document.getElementById('bulkModal').style.display = 'block';
        }

        function toggleBulkTarget() {
            const type = document.getElementById('bulk_type').value;
            const courseGroup = document.getElementById('bulkCourseGroup');
            const planGroup = document.getElementById('bulkPlanGroup');
            
            if (type === 'course') {
                courseGroup.style.display = 'block';
                planGroup.style.display = 'none';
                document.getElementById('bulk_course_id').required = true;
                document.getElementById('bulk_plan_id').required = false;
            } else if (type === 'subscription') {
                courseGroup.style.display = 'none';
                planGroup.style.display = 'block';
                document.getElementById('bulk_course_id').required = false;
                document.getElementById('bulk_plan_id').required = true;
            } else {
                courseGroup.style.display = 'none';
                planGroup.style.display = 'none';
                document.getElementById('bulk_course_id').required = false;
                document.getElementById('bulk_plan_id').required = false;
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function copyCode(code) {
            navigator.clipboard.writeText(code).then(function() {
                // Show success message
                const btn = event.target.closest('.copy-btn');
                const originalIcon = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.style.color = '#28a745';
                
                setTimeout(() => {
                    btn.innerHTML = originalIcon;
                    btn.style.color = '';
                }, 2000);
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>

    <style>
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-bottom-color: #4682B4;
            color: #4682B4;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .code-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .code-text {
            font-family: monospace;
            font-weight: bold;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .copy-btn {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-badge.available {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.used {
            background: #cce5ff;
            color: #004085;
        }

        .status-badge.expired {
            background: #f8d7da;
            color: #721c24;
        }

        .plan-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plan-icon {
            font-size: 1.2rem;
        }

        /* Fix modal display issues */
        .modal {
            display: none !important;
            position: fixed !important;
            z-index: 2001 !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            width: auto !important;
            height: auto !important;
            background-color: #ffffff !important;
            border-radius: 1rem !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
            max-width: 90vw !important;
            max-height: 90vh !important;
            overflow-y: auto !important;
            min-width: 600px !important;
            border: 1px solid #e2e8f0 !important;
        }

        .modal.show {
            display: block !important;
        }

        /* Create modal overlay */
        .modal-overlay {
            display: none !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0,0,0,0.5) !important;
            z-index: 2000 !important;
        }

        .modal-overlay.show {
            display: block !important;
        }

        /* Ensure modal content is visible */
        .modal-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 1.5rem !important;
            border-bottom: 1px solid #e2e8f0 !important;
            background: #f8fafc !important;
            border-radius: 1rem 1rem 0 0 !important;
        }

        .modal-title {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            color: #1e293b !important;
            margin: 0 !important;
        }

        .modal-close {
            background: none !important;
            border: none !important;
            font-size: 1.5rem !important;
            color: #64748b !important;
            cursor: pointer !important;
            padding: 0.25rem !important;
            border-radius: 0.375rem !important;
            transition: all 0.3s ease !important;
            width: 32px !important;
            height: 32px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .modal-close:hover {
            background: #f1f5f9 !important;
            color: #1e293b !important;
        }

        .modal-body {
            padding: 1.5rem !important;
            max-height: 60vh !important;
            overflow-y: auto !important;
        }

        .modal-footer {
            display: flex !important;
            justify-content: flex-end !important;
            gap: 1rem !important;
            padding: 1.5rem !important;
            border-top: 1px solid #e2e8f0 !important;
            background: #f8fafc !important;
            border-radius: 0 0 1rem 1rem !important;
        }

        /* Form styling in modals */
        .modal .form-group {
            margin-bottom: 1rem !important;
        }

        .modal .form-label {
            display: block !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            color: #1e293b !important;
            margin-bottom: 0.5rem !important;
        }

        .modal .form-input,
        .modal .form-select,
        .modal .form-textarea {
            width: 100% !important;
            padding: 0.75rem !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            font-size: 0.875rem !important;
            transition: all 0.3s ease !important;
            background: #ffffff !important;
            color: #1e293b !important;
        }

        .modal .form-input:focus,
        .modal .form-select:focus,
        .modal .form-textarea:focus {
            outline: none !important;
            border-color: #4f46e5 !important;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
        }

        .modal .grid-2 {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 1rem !important;
        }

        .modal .grid-3 {
            display: grid !important;
            grid-template-columns: 1fr 1fr 1fr !important;
            gap: 1rem !important;
        }

        .modal .btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 0.5rem !important;
            padding: 0.75rem 1.5rem !important;
            border: none !important;
            border-radius: 0.5rem !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
        }

        .modal .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #6366f1) !important;
            color: white !important;
        }

        .modal .btn-secondary {
            background: #f1f5f9 !important;
            color: #475569 !important;
            border: 1px solid #d1d5db !important;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group {
            margin-bottom: 15px;
            padding: 0 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-actions {
            padding: 20px;
            border-top: 1px solid #ddd;
            text-align: left;
            display: flex;
            gap: 10px;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .header-actions {
                flex-direction: column;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
    <script>
        // Create modal overlay if it doesn't exist
        function createModalOverlay() {
            let overlay = document.querySelector('.modal-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'modal-overlay';
                overlay.onclick = function() {
                    closeAllModals();
                };
                document.body.appendChild(overlay);
            }
            return overlay;
        }

        // Override openModal function to work with our modals
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            const overlay = createModalOverlay();

            if (modal) {
                overlay.classList.add('show');
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        // Override closeModal function
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            const overlay = document.querySelector('.modal-overlay');

            if (modal) {
                modal.classList.remove('show');
            }
            if (overlay) {
                overlay.classList.remove('show');
            }
            document.body.style.overflow = '';
        }

        function closeAllModals() {
            const modals = document.querySelectorAll('.modal.show');
            const overlay = document.querySelector('.modal-overlay');

            modals.forEach(modal => modal.classList.remove('show'));
            if (overlay) {
                overlay.classList.remove('show');
            }
            document.body.style.overflow = '';
        }

        // Update modal function names to match new system
        function showGenerateModal(type) {
            if (type === 'course') {
                openModal('generateCourseCodeModal');
            } else if (type === 'subscription') {
                openModal('generateSubscriptionCodeModal');
            }
        }

        function showBulkModal() {
            openModal('bulkGenerateModal');
        }

        function deleteCode(id, type) {
            if (confirm('هل أنت متأكد من حذف هذا الكود؟')) {
                const formData = new FormData();
                formData.append('action', 'delete');
                formData.append('id', id);
                formData.append('type', type);

                fetch('activation_codes.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.text())
                .then(data => {
                    window.adminPanel.showToast('تم حذف الكود بنجاح', 'success');
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.adminPanel.showToast('خطأ في حذف الكود', 'error');
                });
            }
        }

        function submitGenerateCourseCode() {
            const form = document.getElementById('generateCourseCodeForm');
            const formData = new FormData(form);

            fetch('activation_codes.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                window.adminPanel.showToast('تم إنشاء كود التفعيل بنجاح', 'success');
                closeModal('generateCourseCodeModal');
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في إنشاء الكود', 'error');
            });
        }

        function submitGenerateSubscriptionCode() {
            const form = document.getElementById('generateSubscriptionCodeForm');
            const formData = new FormData(form);

            fetch('activation_codes.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                window.adminPanel.showToast('تم إنشاء كود التفعيل بنجاح', 'success');
                closeModal('generateSubscriptionCodeModal');
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في إنشاء الكود', 'error');
            });
        }

        function submitBulkGenerate() {
            const form = document.getElementById('bulkGenerateForm');
            const formData = new FormData(form);

            fetch('activation_codes.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                window.adminPanel.showToast('تم إنشاء الأكواد بنجاح', 'success');
                closeModal('bulkGenerateModal');
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في إنشاء الأكواد', 'error');
            });
        }

        function updateBulkOptions() {
            const codeType = document.getElementById('bulkCodeType').value;
            const courseSelect = document.getElementById('bulkCourseSelect');
            const planSelect = document.getElementById('bulkPlanSelect');

            if (codeType === 'course') {
                courseSelect.style.display = 'block';
                planSelect.style.display = 'none';
            } else if (codeType === 'subscription') {
                courseSelect.style.display = 'none';
                planSelect.style.display = 'block';
            } else {
                courseSelect.style.display = 'none';
                planSelect.style.display = 'none';
            }
        }

        function copyCode(code) {
            navigator.clipboard.writeText(code).then(() => {
                window.adminPanel.showToast('تم نسخ الكود', 'success');
            }).catch(err => {
                console.error('Error copying code:', err);
                window.adminPanel.showToast('خطأ في نسخ الكود', 'error');
            });
        }
    </script>
</body>
</html>
