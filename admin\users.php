<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle user actions (activate/deactivate/delete)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $userManager = new UserManager();
    $userId = $_POST['user_id'] ?? 0;
    $action = $_POST['action'];
    
    try {
        switch ($action) {
            case 'activate':
                $result = $userManager->updateUserStatus($userId, 1);
                $message = $result ? 'تم تفعيل المستخدم بنجاح' : 'فشل في تفعيل المستخدم';
                break;
            case 'deactivate':
                $result = $userManager->updateUserStatus($userId, 0);
                $message = $result ? 'تم إلغاء تفعيل المستخدم بنجاح' : 'فشل في إلغاء تفعيل المستخدم';
                break;
            case 'delete':
                $result = $userManager->deleteUser($userId);
                $message = $result ? 'تم حذف المستخدم بنجاح' : 'فشل في حذف المستخدم';
                break;
        }
    } catch (Exception $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="admin-content">
                <h1>إدارة المستخدمين</h1>
                
                <?php if (isset($message)): ?>
                    <div class="alert alert-info"><?php echo htmlspecialchars($message); ?></div>
                <?php endif; ?>

                <!-- Search and Filter -->
                <div class="admin-section">
                    <div class="search-filters">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">البحث</label>
                                <input type="text" id="searchInput" class="form-input" placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني">
                            </div>
                            <div class="form-group">
                                <label class="form-label">المرحلة التعليمية</label>
                                <select id="educationFilter" class="form-select">
                                    <option value="">جميع المراحل</option>
                                    <option value="primary">ابتدائي</option>
                                    <option value="preparatory">إعدادي</option>
                                    <option value="secondary">ثانوي</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">نوع التعليم</label>
                                <select id="typeFilter" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="azhari">أزهري</option>
                                    <option value="general">عام</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">الحالة</label>
                                <select id="statusFilter" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <button onclick="loadUsers()" class="btn btn-primary">بحث</button>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="admin-section">
                    <h2>قائمة المستخدمين</h2>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>المرحلة التعليمية</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTable">
                                <!-- Users will be loaded here via AJAX -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div id="pagination" class="pagination">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Action Modal -->
    <div id="userActionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3 id="modalTitle">تأكيد الإجراء</h3>
            <p id="modalMessage">هل أنت متأكد من هذا الإجراء؟</p>
            <div class="modal-actions">
                <button id="confirmAction" class="btn btn-danger">تأكيد</button>
                <button onclick="closeModal()" class="btn btn-secondary">إلغاء</button>
            </div>
        </div>
    </div>

    <style>
        .search-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button.active {
            background: #4682B4;
            color: white;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .action-btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-activate {
            background: #28a745;
            color: white;
        }
        
        .btn-deactivate {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
    </style>

    <script>
        let currentPage = 1;
        let currentAction = null;
        let currentUserId = null;

        // Load users with filters
        function loadUsers(page = 1) {
            currentPage = page;
            const search = document.getElementById('searchInput').value;
            const education = document.getElementById('educationFilter').value;
            const type = document.getElementById('typeFilter').value;
            const status = document.getElementById('statusFilter').value;

            const params = new URLSearchParams({
                page: page,
                search: search,
                education: education,
                type: type,
                status: status
            });

            fetch(`../api/get_users.php?${params}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        displayUsers(data.data.users);
                        displayPagination(data.data.pagination);
                        displayStatistics(data.data.statistics);
                    } else {
                        document.getElementById('usersTable').innerHTML = '<tr><td colspan="8" class="text-center">حدث خطأ في تحميل البيانات: ' + (data.message || data.error) + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    document.getElementById('usersTable').innerHTML = '<tr><td colspan="8" class="text-center">حدث خطأ في الاتصال بالخادم</td></tr>';
                });
        }

        // Display users in table
        function displayUsers(users) {
            const tbody = document.getElementById('usersTable');
            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد بيانات</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => {
                const educationDisplay = getEducationDisplay(user.education_level, user.education_type, user.grade);
                return `
                <tr>
                    <td>${user.full_name || 'غير محدد'}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td>${user.personal_phone || 'غير محدد'}</td>
                    <td>${educationDisplay}</td>
                    <td>${user.formatted_created_at}</td>
                    <td>${user.formatted_last_login}</td>
                    <td>
                        <span class="status-badge ${user.is_active ? 'active' : 'inactive'}">
                            ${user.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            ${user.is_active ?
                                `<button onclick="showActionModal('deactivate', ${user.id}, '${user.username}')" class="action-btn-small btn-deactivate">إلغاء تفعيل</button>` :
                                `<button onclick="showActionModal('activate', ${user.id}, '${user.username}')" class="action-btn-small btn-activate">تفعيل</button>`
                            }
                            <button onclick="showActionModal('delete', ${user.id}, '${user.username}')" class="action-btn-small btn-delete">حذف</button>
                        </div>
                    </td>
                </tr>
                `;
            }).join('');
        }

        // Display pagination
        function displayPagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            if (pagination.total_pages <= 1) {
                paginationDiv.innerHTML = '';
                return;
            }

            let html = '';
            for (let i = 1; i <= pagination.total_pages; i++) {
                html += `<button onclick="loadUsers(${i})" class="${i === currentPage ? 'active' : ''}">${i}</button>`;
            }
            paginationDiv.innerHTML = html;
        }

        // Show action confirmation modal
        function showActionModal(action, userId, username) {
            currentAction = action;
            currentUserId = userId;
            
            const modal = document.getElementById('userActionModal');
            const title = document.getElementById('modalTitle');
            const message = document.getElementById('modalMessage');
            
            switch (action) {
                case 'activate':
                    title.textContent = 'تفعيل المستخدم';
                    message.textContent = `هل تريد تفعيل المستخدم "${username}"؟`;
                    break;
                case 'deactivate':
                    title.textContent = 'إلغاء تفعيل المستخدم';
                    message.textContent = `هل تريد إلغاء تفعيل المستخدم "${username}"؟`;
                    break;
                case 'delete':
                    title.textContent = 'حذف المستخدم';
                    message.textContent = `هل تريد حذف المستخدم "${username}" نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.`;
                    break;
            }
            
            modal.style.display = 'flex';
        }

        // Close modal
        function closeModal() {
            document.getElementById('userActionModal').style.display = 'none';
            currentAction = null;
            currentUserId = null;
        }

        // Confirm action
        document.getElementById('confirmAction').addEventListener('click', function() {
            if (currentAction && currentUserId) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="${currentAction}">
                    <input type="hidden" name="user_id" value="${currentUserId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });

        // Load users on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        // Search on Enter key
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadUsers();
            }
        });

        // Helper function to format education display
        function getEducationDisplay(level, type, grade) {
            const levels = {
                'primary': 'ابتدائي',
                'preparatory': 'إعدادي',
                'secondary': 'ثانوي'
            };
            const types = {
                'azhari': 'أزهري',
                'general': 'عام'
            };
            const grades = {
                '1': 'الأول', '2': 'الثاني', '3': 'الثالث',
                '4': 'الرابع', '5': 'الخامس', '6': 'السادس'
            };

            const levelText = levels[level] || level;
            const typeText = types[type] || type;
            const gradeText = grades[grade] || grade;

            return `${gradeText} ${levelText} ${typeText}`;
        }

        // Display statistics
        function displayStatistics(stats) {
            // Update statistics if elements exist
            const totalElement = document.getElementById('totalUsers');
            const activeElement = document.getElementById('activeUsers');
            const inactiveElement = document.getElementById('inactiveUsers');

            if (totalElement) totalElement.textContent = stats.total;
            if (activeElement) activeElement.textContent = stats.active;
            if (inactiveElement) inactiveElement.textContent = stats.inactive;
        }
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
