-- نظام الاشتراكات الكامل
-- إ<PERSON><PERSON><PERSON><PERSON> جداول النظام

-- جدول الخطط
CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة',
    name_en VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية',
    description TEXT NULL COMMENT 'وصف الخطة',
    price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'سعر الخطة',
    discount_percentage INT DEFAULT 0 COMMENT 'نسبة الخصم',
    discounted_price DECIMAL(10,2) NULL COMMENT 'السعر بعد الخصم',
    duration_days INT NOT NULL DEFAULT 30 COMMENT 'مدة الخطة بالأيام',
    features JSON NULL COMMENT 'مميزات الخطة',
    icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة الخطة',
    color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة',
    is_popular BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة الأكثر شيوعاً',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الخطة',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    created_by INT NULL COMMENT 'منشئ الخطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_active (is_active),
    INDEX idx_popular (is_popular),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول اشتراكات المستخدمين
CREATE TABLE user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    plan_id INT NOT NULL COMMENT 'معرف الخطة',
    activation_code VARCHAR(50) NULL COMMENT 'كود التفعيل المستخدم',
    payment_method ENUM('code', 'fawry', 'visa', 'wallet') NOT NULL COMMENT 'طريقة الدفع',
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending' COMMENT 'حالة الدفع',
    amount_paid DECIMAL(10,2) NOT NULL COMMENT 'المبلغ المدفوع',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ بداية الاشتراك',
    end_date TIMESTAMP NULL COMMENT 'تاريخ انتهاء الاشتراك',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الاشتراك',
    auto_renew BOOLEAN DEFAULT FALSE COMMENT 'التجديد التلقائي',
    cancelled_at TIMESTAMP NULL COMMENT 'تاريخ الإلغاء',
    cancellation_reason TEXT NULL COMMENT 'سبب الإلغاء',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_end_date (end_date),
    INDEX idx_payment_status (payment_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول أكواد التفعيل
CREATE TABLE activation_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود التفعيل',
    plan_id INT NOT NULL COMMENT 'معرف الخطة',
    is_used BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود',
    used_by INT NULL COMMENT 'المستخدم الذي استخدم الكود',
    used_at TIMESTAMP NULL COMMENT 'تاريخ الاستخدام',
    expires_at TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
    created_by INT NOT NULL COMMENT 'منشئ الكود',
    notes TEXT NULL COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_code (code),
    INDEX idx_plan_unused (plan_id, is_used),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المدفوعات
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subscription_id INT NOT NULL COMMENT 'معرف الاشتراك',
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    payment_method ENUM('code', 'fawry', 'visa', 'wallet') NOT NULL COMMENT 'طريقة الدفع',
    payment_gateway VARCHAR(50) NULL COMMENT 'بوابة الدفع',
    transaction_id VARCHAR(100) NULL COMMENT 'معرف المعاملة',
    gateway_response JSON NULL COMMENT 'استجابة البوابة',
    amount DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
    currency VARCHAR(3) DEFAULT 'EGP' COMMENT 'العملة',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending' COMMENT 'حالة الدفع',
    payment_date TIMESTAMP NULL COMMENT 'تاريخ الدفع',
    refund_date TIMESTAMP NULL COMMENT 'تاريخ الاسترداد',
    refund_amount DECIMAL(10,2) NULL COMMENT 'مبلغ الاسترداد',
    notes TEXT NULL COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_payments (user_id),
    INDEX idx_status (status),
    INDEX idx_transaction (transaction_id),
    INDEX idx_payment_date (payment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات الاشتراكات
CREATE TABLE subscription_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL COMMENT 'التاريخ',
    total_subscriptions INT DEFAULT 0 COMMENT 'إجمالي الاشتراكات',
    new_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات الجديدة',
    cancelled_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات الملغاة',
    expired_subscriptions INT DEFAULT 0 COMMENT 'الاشتراكات المنتهية',
    total_revenue DECIMAL(10,2) DEFAULT 0 COMMENT 'إجمالي الإيرادات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_date (date),
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج خطط افتراضية
INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, sort_order) VALUES
('الخطة الأساسية', 'Basic Plan', 'خطة مناسبة للمبتدئين', 99.00, 0, 99.00, 30, '["الوصول لجميع الأقسام", "دعم فني أساسي", "تحديثات مجانية"]', '📚', '#4682B4', FALSE, 1),
('الخطة المتقدمة', 'Advanced Plan', 'خطة شاملة للطلاب الجادين', 199.00, 20, 159.20, 60, '["الوصول لجميع الأقسام", "دعم فني متقدم", "تحديثات مجانية", "اختبارات إضافية", "تقارير مفصلة"]', '🎓', '#28a745', TRUE, 2),
('الخطة المميزة', 'Premium Plan', 'خطة متكاملة مع جميع المميزات', 299.00, 25, 224.25, 90, '["الوصول لجميع الأقسام", "دعم فني مميز", "تحديثات مجانية", "اختبارات إضافية", "تقارير مفصلة", "جلسات فردية", "محتوى حصري"]', '👑', '#ffc107', FALSE, 3);

-- جدول أكواد تفعيل الكورسات (إضافة جديدة)
CREATE TABLE course_activation_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود التفعيل',
    course_id INT NOT NULL COMMENT 'معرف الكورس',
    is_used BOOLEAN DEFAULT FALSE COMMENT 'هل تم استخدام الكود',
    used_by INT NULL COMMENT 'المستخدم الذي استخدم الكود',
    used_at TIMESTAMP NULL COMMENT 'تاريخ الاستخدام',
    expires_at TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الكود',
    created_by INT NOT NULL COMMENT 'منشئ الكود',
    notes TEXT NULL COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_code (code),
    INDEX idx_course_unused (course_id, is_used),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بعض أكواد التفعيل التجريبية
INSERT INTO activation_codes (code, plan_id, expires_at, created_by, notes) VALUES
('BASIC2024', 1, DATE_ADD(NOW(), INTERVAL 30 DAY), 1, 'كود تجريبي للخطة الأساسية'),
('ADVANCED2024', 2, DATE_ADD(NOW(), INTERVAL 30 DAY), 1, 'كود تجريبي للخطة المتقدمة'),
('PREMIUM2024', 3, DATE_ADD(NOW(), INTERVAL 30 DAY), 1, 'كود تجريبي للخطة المميزة');
