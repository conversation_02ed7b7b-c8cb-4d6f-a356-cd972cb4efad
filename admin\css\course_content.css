        .content-tabs {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            border-radius: 50px;
            border: 5px solid #dee2e6;
            padding: 10px 20px;
        }

        .tab-btn {
            display: inline-flex;
            gap: 10px;
            padding: 15px 25px;
            border: 3px dashed rgb(162, 179, 236);
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 1s ease;
            position: relative;
            overflow: hidden;
            text-align: center;
            justify-content: center;
            align-items: center;
            min-width: 120px;
            margin: 0 auto;
            transition: all .3s ease;

        }


        .tab-btn::before {
            content: '';
            position: absolute;
            top: 100%;
            left: 100%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.7s ease;
        }

        .tab-btn:hover::before {
            width: 400px;
            height: 400px;
        }

        .as{
            width: 3px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #0056b3;
        }

        .tab-btn:hover{
            color: #ff0000;
            background-color: rgb(119, 175, 192);
            border: 3px solid rgb(0, 0, 0);
        }

        .tab-btn.active {
            color: #e6e6e6;
            background-color: #0069c5;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .content-form {
            background: #ffffff;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group{
            padding: 10px 20px;
            margin-bottom: 40px;
        }

        .form-group label{
            padding: 10px 30px;
            margin: 0 auto;
            font-size: 18px;
            color: #0683ff;
        }

        .form-group input{
            margin-top: 20px;
            padding: 10px 20px;
            border-radius: 30px;
            border: 3px solid rgb(185, 217, 223);
            font-size: 18px;
            width: 200px;
            height: 50px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group #video_platform{
            margin-top: 20px;
            padding: 10px 20px;
            border-radius: 30px;
            border: 3px solid rgb(185, 217, 223);
            font-size: 18px;
            width: 200px;
            height: 50px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: all .7s ease;
        }

        .form-group #video_platform option{
            background-color: #a0bfda;
            color: #007bff;
            border-radius: 3px;
        }

        .form-group #video_description{
            margin-top: 20px;
            padding: 10px 20px;
            border-radius: 30px;
            border: 3px solid rgb(185, 217, 223);
            font-size: 18px;
            width: 200px;
            height: 50px;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
        }


        .empty-message {
            text-align: center;
            color: #0069c5;
            padding: 40px;
            font-style: italic;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 8px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .questions-section {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }

        .questions-section h4 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .question-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .question-item:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
            transform: translateY(-2px);
        }

        .enhanced-question {
            border-left: 4px solid #007bff;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .question-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .question-number {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
        }

        .question-badge {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .question-actions {
            display: flex;
            gap: 10px;
        }

        .questions-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 25px;
        }

        .info-card {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .info-icon {
            font-size: 20px;
        }

        .info-text {
            color: #1565c0;
            font-weight: 600;
            font-size: 14px;
        }

        .questions-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px dashed #dee2e6;
        }

        .btn-icon {
            margin-left: 8px;
        }

        .question-header h5 {
            color: #4682B4;
            margin: 0;
        }

        .question-options {
            margin: 15px 0;
        }

        .question-options input {
            margin-bottom: 8px;
        }

        #options_container input {
            margin-bottom: 8px;
        }

        /* Radio Group Styles */
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
            color: #495057;
        }

        .radio-option input[type="radio"] {
            display: none;
        }

        .radio-custom {
            width: 20px;
            height: 20px;
            border: 2px solid #dee2e6;
            border-radius: 50%;
            position: relative;
            transition: all 0.3s ease;
        }

        .radio-option input[type="radio"]:checked + .radio-custom {
            border-color: #007bff;
            background: #007bff;
        }

        .radio-option input[type="radio"]:checked + .radio-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }

        /* Option Item Styles */
        .option-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .option-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }

        .option-item input[type="radio"] {
            margin: 0;
        }

        .option-item input[type="text"] {
            flex: 1;
            border: none;
            background: transparent;
            padding: 5px;
            font-size: 14px;
        }

        .option-item input[type="text"]:focus {
            outline: none;
            background: #f8f9fa;
        }



