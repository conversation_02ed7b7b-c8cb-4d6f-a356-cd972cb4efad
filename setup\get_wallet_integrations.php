<?php
require_once __DIR__ . '/../config/config.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<title>الحصول على Integration IDs للمحافظ الإلكترونية</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n";
echo ".success { color: green; }\n";
echo ".error { color: red; }\n";
echo ".info { color: blue; }\n";
echo ".integration { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }\n";
echo ".wallet { background: #e8f5e9; border-left-color: #28a745; }\n";
echo ".card { background: #fff3cd; border-left-color: #ffc107; }\n";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>🏦 الحصول على Integration IDs للمحافظ الإلكترونية</h1>\n";

try {
    $config = PAYMENT_GATEWAYS['paymob'];
    $apiKey = $config['api_key'];
    
    echo "<div class='info'>📡 جاري الحصول على Auth Token...</div><br>\n";
    
    // Get auth token
    $authData = ['api_key' => $apiKey];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://accept.paymob.com/api/auth/tokens');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($authData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception("HTTP Error: $httpCode - $response");
    }
    
    $authResponse = json_decode($response, true);
    
    if (!$authResponse || !isset($authResponse['token'])) {
        throw new Exception("Failed to get auth token: " . $response);
    }
    
    $authToken = $authResponse['token'];
    echo "<div class='success'>✅ تم الحصول على Auth Token بنجاح</div><br>\n";
    
    // Get integrations
    echo "<div class='info'>📋 جاري الحصول على جميع Integration IDs...</div><br>\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://accept.paymob.com/api/ecommerce/integrations');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $authToken,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception("HTTP Error getting integrations: $httpCode - $response");
    }
    
    $integrations = json_decode($response, true);
    
    if (!$integrations) {
        throw new Exception("Failed to get integrations: " . $response);
    }
    
    echo "<div class='success'>✅ تم الحصول على Integration IDs بنجاح</div><br>\n";
    echo "<h2>📱 Integration IDs المتاحة:</h2>\n";
    
    $walletIntegrations = [];
    $cardIntegrations = [];
    $otherIntegrations = [];
    
    foreach ($integrations as $integration) {
        $name = strtolower($integration['name'] ?? '');
        $type = strtolower($integration['type'] ?? '');
        
        // Categorize integrations
        if (strpos($name, 'vodafone') !== false || strpos($name, 'cash') !== false || 
            strpos($type, 'wallet') !== false || strpos($type, 'mobile') !== false) {
            $walletIntegrations[] = $integration;
        } elseif (strpos($name, 'card') !== false || strpos($type, 'card') !== false ||
                  strpos($name, 'visa') !== false || strpos($name, 'master') !== false) {
            $cardIntegrations[] = $integration;
        } else {
            $otherIntegrations[] = $integration;
        }
    }
    
    // Display wallet integrations
    if (!empty($walletIntegrations)) {
        echo "<h3>📱 المحافظ الإلكترونية:</h3>\n";
        foreach ($walletIntegrations as $integration) {
            echo "<div class='integration wallet'>\n";
            echo "<strong>🆔 ID:</strong> " . $integration['id'] . "<br>\n";
            echo "<strong>📛 Name:</strong> " . ($integration['name'] ?? 'N/A') . "<br>\n";
            echo "<strong>🔧 Type:</strong> " . ($integration['type'] ?? 'N/A') . "<br>\n";
            echo "<strong>💰 Currency:</strong> " . ($integration['currency'] ?? 'N/A') . "<br>\n";
            echo "<strong>🔴 Status:</strong> " . ($integration['is_live'] ? '🟢 Live' : '🟡 Test') . "<br>\n";
            echo "</div>\n";
        }
    } else {
        echo "<div class='error'>⚠️ لم يتم العثور على integration IDs للمحافظ الإلكترونية</div>\n";
        echo "<div class='info'>💡 تحتاج إلى إنشاء integration جديد للمحافظ من لوحة تحكم Paymob</div>\n";
    }
    
    // Display card integrations
    if (!empty($cardIntegrations)) {
        echo "<h3>💳 البطاقات الائتمانية:</h3>\n";
        foreach ($cardIntegrations as $integration) {
            echo "<div class='integration card'>\n";
            echo "<strong>🆔 ID:</strong> " . $integration['id'] . "<br>\n";
            echo "<strong>📛 Name:</strong> " . ($integration['name'] ?? 'N/A') . "<br>\n";
            echo "<strong>🔧 Type:</strong> " . ($integration['type'] ?? 'N/A') . "<br>\n";
            echo "<strong>💰 Currency:</strong> " . ($integration['currency'] ?? 'N/A') . "<br>\n";
            echo "<strong>🔴 Status:</strong> " . ($integration['is_live'] ? '🟢 Live' : '🟡 Test') . "<br>\n";
            echo "</div>\n";
        }
    }
    
    // Display other integrations
    if (!empty($otherIntegrations)) {
        echo "<h3>🔧 Integration IDs أخرى:</h3>\n";
        foreach ($otherIntegrations as $integration) {
            echo "<div class='integration'>\n";
            echo "<strong>🆔 ID:</strong> " . $integration['id'] . "<br>\n";
            echo "<strong>📛 Name:</strong> " . ($integration['name'] ?? 'N/A') . "<br>\n";
            echo "<strong>🔧 Type:</strong> " . ($integration['type'] ?? 'N/A') . "<br>\n";
            echo "<strong>💰 Currency:</strong> " . ($integration['currency'] ?? 'N/A') . "<br>\n";
            echo "<strong>🔴 Status:</strong> " . ($integration['is_live'] ? '🟢 Live' : '🟡 Test') . "<br>\n";
            echo "</div>\n";
        }
    }
    
    // Generate configuration code
    echo "<br><h2>⚙️ كود الإعدادات المقترح:</h2>\n";
    echo "<div class='code'>\n";
    echo "// أضف هذا إلى ملف config/config.php<br>\n";
    echo "'wallet_integrations' => [<br>\n";
    
    if (!empty($walletIntegrations)) {
        foreach ($walletIntegrations as $integration) {
            $name = strtolower($integration['name'] ?? '');
            if (strpos($name, 'vodafone') !== false) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;'vodafone_cash' => '" . $integration['id'] . "',<br>\n";
            } elseif (strpos($name, 'etisalat') !== false) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;'etisalat_cash' => '" . $integration['id'] . "',<br>\n";
            } elseif (strpos($name, 'we') !== false) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;'we_cash' => '" . $integration['id'] . "',<br>\n";
            } elseif (strpos($name, 'orange') !== false) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;'orange_cash' => '" . $integration['id'] . "',<br>\n";
            } else {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;'wallet_" . $integration['id'] . "' => '" . $integration['id'] . "', // " . ($integration['name'] ?? 'Unknown') . "<br>\n";
            }
        }
    } else {
        echo "&nbsp;&nbsp;&nbsp;&nbsp;// تحتاج إلى إنشاء integration IDs للمحافظ من لوحة تحكم Paymob<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;'vodafone_cash' => 'YOUR_VODAFONE_INTEGRATION_ID',<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;'etisalat_cash' => 'YOUR_ETISALAT_INTEGRATION_ID',<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;'we_cash' => 'YOUR_WE_INTEGRATION_ID',<br>\n";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;'orange_cash' => 'YOUR_ORANGE_INTEGRATION_ID',<br>\n";
    }
    
    echo "]<br>\n";
    echo "</div>\n";
    
    echo "<br><h2>📋 الخطوات التالية:</h2>\n";
    echo "<ol>\n";
    echo "<li>🔑 إذا لم تجد integration IDs للمحافظ، اذهب إلى <a href='https://accept.paymob.com/portal2/en/PaymentIntegrations' target='_blank'>لوحة تحكم Paymob</a></li>\n";
    echo "<li>➕ أنشئ integration جديد لكل محفظة إلكترونية</li>\n";
    echo "<li>📝 انسخ Integration IDs وأضفها للإعدادات</li>\n";
    echo "<li>🧪 اختبر كل محفظة للتأكد من عملها</li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "<div class='error'><h2>❌ خطأ: " . $e->getMessage() . "</h2></div>\n";
    echo "<div class='info'>💡 تأكد من صحة API Key في ملف الإعدادات</div>\n";
}

echo "<br><a href='../page/dashboard.php'>🏠 العودة للوحة التحكم</a>\n";
echo "<br><a href='update_payment_system.php'>🔄 تحديث نظام الدفع</a>\n";
echo "</body>\n";
echo "</html>\n";
?>
