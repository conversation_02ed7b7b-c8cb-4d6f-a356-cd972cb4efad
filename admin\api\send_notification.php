<?php
session_start();
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    // Validate required fields
    if (!isset($_POST['title']) || empty(trim($_POST['title']))) {
        echo json_encode(['success' => false, 'message' => 'عنوان الإشعار مطلوب']);
        exit;
    }
    
    if (!isset($_POST['message']) || empty(trim($_POST['message']))) {
        echo json_encode(['success' => false, 'message' => 'محتوى الإشعار مطلوب']);
        exit;
    }
    
    $title = trim($_POST['title']);
    $message = trim($_POST['message']);
    $type = isset($_POST['type']) ? trim($_POST['type']) : 'info';
    
    // Validate type
    $validTypes = ['info', 'success', 'warning', 'error'];
    if (!in_array($type, $validTypes)) {
        $type = 'info';
    }
    
    $db = Database::getInstance()->getConnection();
    
    // Insert notification
    $insertStmt = $db->prepare("
        INSERT INTO notifications (title, message, type, created_by, created_at) 
        VALUES (:title, :message, :type, :created_by, NOW())
    ");
    
    $insertStmt->bindParam(':title', $title);
    $insertStmt->bindParam(':message', $message);
    $insertStmt->bindParam(':type', $type);
    $insertStmt->bindParam(':created_by', $_SESSION['admin_id']);
    
    if ($insertStmt->execute()) {
        $notificationId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إرسال الإشعار بنجاح',
            'notification_id' => $notificationId
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في إرسال الإشعار']);
    }
    
} catch (Exception $e) {
    error_log("Error in send_notification.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في إرسال الإشعار'
    ]);
}
?>
