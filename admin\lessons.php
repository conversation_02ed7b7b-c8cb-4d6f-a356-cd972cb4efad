<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_lesson':
                $subject_id = $_POST['subject_id'];
                $title = $_POST['title'];
                $description = $_POST['description'];
                $lesson_number = $_POST['lesson_number'];
                $is_free = isset($_POST['is_free']) ? 1 : 0;
                $sort_order = $_POST['sort_order'];
                
                $stmt = $db->prepare("INSERT INTO lessons (subject_id, title, description, lesson_number, is_free, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$subject_id, $title, $description, $lesson_number, $is_free, $sort_order, $_SESSION['admin_id']]);
                
                $success = "تم إضافة الدرس بنجاح";
                break;
                
            case 'edit_lesson':
                $lesson_id = $_POST['lesson_id'];
                $subject_id = $_POST['subject_id'];
                $title = $_POST['title'];
                $description = $_POST['description'];
                $lesson_number = $_POST['lesson_number'];
                $is_free = isset($_POST['is_free']) ? 1 : 0;
                $sort_order = $_POST['sort_order'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                $stmt = $db->prepare("UPDATE lessons SET subject_id = ?, title = ?, description = ?, lesson_number = ?, is_free = ?, sort_order = ?, is_active = ? WHERE id = ?");
                $stmt->execute([$subject_id, $title, $description, $lesson_number, $is_free, $sort_order, $is_active, $lesson_id]);
                
                $success = "تم تحديث الدرس بنجاح";
                break;
                
            case 'delete_lesson':
                $lesson_id = $_POST['lesson_id'];
                $stmt = $db->prepare("DELETE FROM lessons WHERE id = ?");
                $stmt->execute([$lesson_id]);
                
                $success = "تم حذف الدرس بنجاح";
                break;
        }
    }
}

// Get subjects for dropdown
$subjects_stmt = $db->query("SELECT * FROM curriculum_subjects WHERE is_active = 1 ORDER BY education_level, education_type, grade, sort_order");
$subjects = $subjects_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get lessons with subject info
$lessons_query = "
    SELECT l.*, cs.name as subject_name, cs.education_level, cs.education_type, cs.grade, cs.color
    FROM lessons l
    JOIN curriculum_subjects cs ON l.subject_id = cs.id
    ORDER BY cs.education_level, cs.education_type, cs.grade, cs.sort_order, l.sort_order
";
$lessons_stmt = $db->query($lessons_query);
$lessons = $lessons_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get lesson for editing if requested
$edit_lesson = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $db->prepare("SELECT * FROM lessons WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_lesson = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدروس - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>إدارة الدروس</h1>
                <p>إضافة وإدارة دروس الأقسام الدراسية</p>
            </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <!-- Add/Edit Lesson Form -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3>
                            <i class="fas fa-plus-circle"></i>
                            <?php echo $edit_lesson ? 'تعديل الدرس' : 'إضافة درس جديد'; ?>
                        </h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="admin-form">
                            <input type="hidden" name="action" value="<?php echo $edit_lesson ? 'edit_lesson' : 'add_lesson'; ?>">
                            <?php if ($edit_lesson): ?>
                                <input type="hidden" name="lesson_id" value="<?php echo $edit_lesson['id']; ?>">
                            <?php endif; ?>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="subject_id">القسم الدراسي</label>
                                    <select name="subject_id" id="subject_id" class="form-control" required>
                                        <option value="">اختر القسم</option>
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>" 
                                                    <?php echo ($edit_lesson && $edit_lesson['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                                                <?php echo $subject['name']; ?> - 
                                                <?php echo $subject['education_level']; ?> 
                                                <?php echo $subject['education_type']; ?> 
                                                الصف <?php echo $subject['grade']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="lesson_number">رقم الدرس</label>
                                    <input type="number" name="lesson_number" id="lesson_number" 
                                           class="form-control" min="1" required
                                           value="<?php echo $edit_lesson ? $edit_lesson['lesson_number'] : ''; ?>">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="title">عنوان الدرس</label>
                                <input type="text" name="title" id="title" class="form-control" required
                                       value="<?php echo $edit_lesson ? htmlspecialchars($edit_lesson['title']) : ''; ?>">
                            </div>

                            <div class="form-group">
                                <label for="description">وصف الدرس</label>
                                <textarea name="description" id="description" class="form-control" rows="3"><?php echo $edit_lesson ? htmlspecialchars($edit_lesson['description']) : ''; ?></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="sort_order">ترتيب العرض</label>
                                    <input type="number" name="sort_order" id="sort_order" 
                                           class="form-control" min="0"
                                           value="<?php echo $edit_lesson ? $edit_lesson['sort_order'] : '0'; ?>">
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="is_free" value="1" 
                                                   <?php echo ($edit_lesson && $edit_lesson['is_free']) ? 'checked' : ''; ?>>
                                            <span class="checkmark"></span>
                                            درس مجاني
                                        </label>
                                        
                                        <?php if ($edit_lesson): ?>
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="is_active" value="1" 
                                                       <?php echo ($edit_lesson && $edit_lesson['is_active']) ? 'checked' : ''; ?>>
                                                <span class="checkmark"></span>
                                                نشط
                                            </label>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    <?php echo $edit_lesson ? 'تحديث الدرس' : 'إضافة الدرس'; ?>
                                </button>
                                
                                <?php if ($edit_lesson): ?>
                                    <a href="lessons.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Lessons List -->
                <div class="admin-card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> قائمة الدروس</h3>
                        <div class="card-actions">
                            <span class="badge badge-primary"><?php echo count($lessons); ?> درس</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($lessons)): ?>
                            <div class="empty-state">
                                <i class="fas fa-book-open"></i>
                                <h3>لا توجد دروس</h3>
                                <p>لم يتم إضافة أي دروس بعد</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="admin-table">
                                    <thead>
                                        <tr>
                                            <th>رقم الدرس</th>
                                            <th>العنوان</th>
                                            <th>القسم</th>
                                            <th>المرحلة</th>
                                            <th>النوع</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($lessons as $lesson): ?>
                                            <tr>
                                                <td>
                                                    <span class="lesson-number"><?php echo $lesson['lesson_number']; ?></span>
                                                </td>
                                                <td>
                                                    <div class="lesson-info">
                                                        <strong><?php echo htmlspecialchars($lesson['title']); ?></strong>
                                                        <?php if ($lesson['description']): ?>
                                                            <small><?php echo htmlspecialchars(substr($lesson['description'], 0, 50)) . '...'; ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="subject-badge" style="background-color: <?php echo $lesson['color']; ?>20; color: <?php echo $lesson['color']; ?>;">
                                                        <?php echo htmlspecialchars($lesson['subject_name']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $lesson['education_level']; ?></td>
                                                <td><?php echo $lesson['education_type']; ?></td>
                                                <td>
                                                    <div class="status-badges">
                                                        <?php if ($lesson['is_free']): ?>
                                                            <span class="badge badge-success">مجاني</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">مدفوع</span>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($lesson['is_active']): ?>
                                                            <span class="badge badge-primary">نشط</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-secondary">غير نشط</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="lesson_content.php?lesson_id=<?php echo $lesson['id']; ?>" 
                                                           class="btn btn-sm btn-info" title="إدارة المحتوى">
                                                            <i class="fas fa-folder-open"></i>
                                                        </a>
                                                        <a href="lessons.php?edit=<?php echo $lesson['id']; ?>" 
                                                           class="btn btn-sm btn-warning" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button onclick="deleteLesson(<?php echo $lesson['id']; ?>)" 
                                                                class="btn btn-sm btn-danger" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <h3>تأكيد الحذف</h3>
            <p>هل أنت متأكد من حذف هذا الدرس؟ سيتم حذف جميع المحتويات المرتبطة به.</p>
            <div class="modal-actions">
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="action" value="delete_lesson">
                    <input type="hidden" name="lesson_id" id="deleteLessonId">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">إلغاء</button>
                </form>
            </div>
        </div>
    </div>

    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .admin-container {
            display: flex;
            flex: 1;
        }

        .admin-main {
            flex: 1;
            padding: 20px;
            margin-left: 280px;
        }

        .admin-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid #4682B4;
        }

        .admin-header h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .admin-header p {
            color: #6c757d;
            margin: 0;
            font-size: 16px;
        }

        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .card-body {
            padding: 30px;
        }

        .admin-form {
            max-width: 800px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .admin-table th,
        .admin-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .lesson-number {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 12px;
        }

        .lesson-info strong {
            display: block;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .lesson-info small {
            color: #6c757d;
            font-size: 12px;
        }

        .subject-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-badges {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-primary {
            background: #007bff;
            color: white;
        }

        .badge-secondary {
            background: #6c757d;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .modal-content h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .modal-content p {
            color: #6c757d;
            margin-bottom: 25px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .admin-main {
                margin-left: 0;
                padding: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .admin-table {
                font-size: 12px;
            }

            .admin-table th,
            .admin-table td {
                padding: 8px;
            }
        }
    </style>

    <script>
        function deleteLesson(lessonId) {
            document.getElementById('deleteLessonId').value = lessonId;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('deleteModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
