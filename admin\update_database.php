<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

try {
    // Create lessons table
    $db->exec("
        CREATE TABLE IF NOT EXISTS lessons (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subject_id INT NOT NULL COMMENT 'معرف القسم',
            title VARCHAR(255) NOT NULL COMMENT 'عنوان الدرس',
            description TEXT NULL COMMENT 'وصف الدرس',
            lesson_number INT NOT NULL COMMENT 'رقم الدرس',
            is_free BOOLEAN DEFAULT FALSE COMMENT 'هل الدرس مجاني',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الدرس',
            sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
            created_by INT NULL COMMENT 'منشئ الدرس',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (subject_id) REFERENCES curriculum_subjects(id) ON DELETE CASCADE,
            INDEX idx_subject_id (subject_id),
            INDEX idx_lesson_number (lesson_number),
            INDEX idx_is_free (is_free),
            INDEX idx_is_active (is_active),
            INDEX idx_sort_order (sort_order),
            UNIQUE KEY unique_subject_lesson (subject_id, lesson_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create lesson_videos table
    $db->exec("
        CREATE TABLE IF NOT EXISTS lesson_videos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lesson_id INT NOT NULL COMMENT 'معرف الدرس',
            title VARCHAR(255) NOT NULL COMMENT 'عنوان الفيديو',
            description TEXT NULL COMMENT 'وصف الفيديو',
            youtube_url VARCHAR(500) NOT NULL COMMENT 'رابط اليوتيوب',
            youtube_id VARCHAR(50) NULL COMMENT 'معرف الفيديو في اليوتيوب',
            duration_minutes INT DEFAULT 0 COMMENT 'مدة الفيديو بالدقائق',
            video_order INT DEFAULT 0 COMMENT 'ترتيب الفيديو',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الفيديو',
            created_by INT NULL COMMENT 'منشئ الفيديو',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
            INDEX idx_lesson_id (lesson_id),
            INDEX idx_video_order (video_order),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create lesson_exercises table
    $db->exec("
        CREATE TABLE IF NOT EXISTS lesson_exercises (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lesson_id INT NOT NULL COMMENT 'معرف الدرس',
            title VARCHAR(255) NOT NULL COMMENT 'عنوان التدريب',
            description TEXT NULL COMMENT 'وصف التدريب',
            exercise_order INT DEFAULT 0 COMMENT 'ترتيب التدريب',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة التدريب',
            created_by INT NULL COMMENT 'منشئ التدريب',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
            INDEX idx_lesson_id (lesson_id),
            INDEX idx_exercise_order (exercise_order),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create exercise_questions table
    $db->exec("
        CREATE TABLE IF NOT EXISTS exercise_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            exercise_id INT NOT NULL COMMENT 'معرف التدريب',
            question_text TEXT NOT NULL COMMENT 'نص السؤال',
            question_type ENUM('true_false', 'multiple_choice') NOT NULL COMMENT 'نوع السؤال',
            options JSON NULL COMMENT 'خيارات السؤال للاختيار المتعدد',
            correct_answer TEXT NOT NULL COMMENT 'الإجابة الصحيحة',
            explanation TEXT NULL COMMENT 'شرح الإجابة',
            question_order INT DEFAULT 0 COMMENT 'ترتيب السؤال',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة السؤال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (exercise_id) REFERENCES lesson_exercises(id) ON DELETE CASCADE,
            INDEX idx_exercise_id (exercise_id),
            INDEX idx_question_order (question_order),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create lesson_exams table
    $db->exec("
        CREATE TABLE IF NOT EXISTS lesson_exams (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lesson_id INT NOT NULL COMMENT 'معرف الدرس',
            title VARCHAR(255) NOT NULL COMMENT 'عنوان الامتحان',
            description TEXT NULL COMMENT 'وصف الامتحان',
            duration_minutes INT DEFAULT 60 COMMENT 'مدة الامتحان بالدقائق',
            exam_order INT DEFAULT 0 COMMENT 'ترتيب الامتحان',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الامتحان',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
            INDEX idx_lesson_id (lesson_id),
            INDEX idx_exam_order (exam_order),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create exam_questions table
    $db->exec("
        CREATE TABLE IF NOT EXISTS exam_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            exam_id INT NOT NULL COMMENT 'معرف الامتحان',
            question_text TEXT NOT NULL COMMENT 'نص السؤال',
            question_type ENUM('true_false', 'multiple_choice') NOT NULL COMMENT 'نوع السؤال',
            options JSON NULL COMMENT 'خيارات السؤال للاختيار المتعدد',
            correct_answer TEXT NOT NULL COMMENT 'الإجابة الصحيحة',
            explanation TEXT NULL COMMENT 'شرح الإجابة',
            points DECIMAL(5,2) DEFAULT 1.00 COMMENT 'نقاط السؤال',
            question_order INT DEFAULT 0 COMMENT 'ترتيب السؤال',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة السؤال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (exam_id) REFERENCES lesson_exams(id) ON DELETE CASCADE,
            INDEX idx_exam_id (exam_id),
            INDEX idx_question_order (question_order),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create lesson_summaries table
    $db->exec("
        CREATE TABLE IF NOT EXISTS lesson_summaries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lesson_id INT NOT NULL COMMENT 'معرف الدرس',
            title VARCHAR(255) NOT NULL COMMENT 'عنوان الملخص',
            description TEXT NULL COMMENT 'وصف الملخص',
            file_path VARCHAR(500) NOT NULL COMMENT 'مسار ملف PDF',
            file_name VARCHAR(255) NOT NULL COMMENT 'اسم الملف',
            file_size INT DEFAULT 0 COMMENT 'حجم الملف بالبايت',
            summary_order INT DEFAULT 0 COMMENT 'ترتيب الملخص',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة الملخص',
            created_by INT NULL COMMENT 'منشئ الملخص',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
            INDEX idx_lesson_id (lesson_id),
            INDEX idx_summary_order (summary_order),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    echo "تم إنشاء جداول الدروس بنجاح!<br>";

} catch (Exception $e) {
    echo "خطأ في إنشاء الجداول: " . $e->getMessage() . "<br>";
}

try {
    // Create progress tracking tables
    $db->exec("
        CREATE TABLE IF NOT EXISTS user_lesson_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL COMMENT 'معرف المستخدم',
            lesson_id INT NOT NULL COMMENT 'معرف الدرس',
            is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الدرس',
            completion_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الإكمال',
            last_accessed_at TIMESTAMP NULL COMMENT 'آخر وصول للدرس',
            completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_lesson (user_id, lesson_id),
            INDEX idx_user_id (user_id),
            INDEX idx_lesson_id (lesson_id),
            INDEX idx_is_completed (is_completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    $db->exec("
        CREATE TABLE IF NOT EXISTS user_video_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL COMMENT 'معرف المستخدم',
            video_id INT NOT NULL COMMENT 'معرف الفيديو',
            is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الفيديو',
            watch_time_seconds INT DEFAULT 0 COMMENT 'وقت المشاهدة بالثواني',
            completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (video_id) REFERENCES lesson_videos(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_video (user_id, video_id),
            INDEX idx_user_id (user_id),
            INDEX idx_video_id (video_id),
            INDEX idx_is_completed (is_completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    $db->exec("
        CREATE TABLE IF NOT EXISTS user_exercise_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL COMMENT 'معرف المستخدم',
            exercise_id INT NOT NULL COMMENT 'معرف التدريب',
            is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال التدريب',
            score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'النتيجة',
            total_questions INT DEFAULT 0 COMMENT 'إجمالي الأسئلة',
            correct_answers INT DEFAULT 0 COMMENT 'الإجابات الصحيحة',
            completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (exercise_id) REFERENCES lesson_exercises(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_exercise (user_id, exercise_id),
            INDEX idx_user_id (user_id),
            INDEX idx_exercise_id (exercise_id),
            INDEX idx_is_completed (is_completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    $db->exec("
        CREATE TABLE IF NOT EXISTS user_exam_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL COMMENT 'معرف المستخدم',
            exam_id INT NOT NULL COMMENT 'معرف الامتحان',
            is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الامتحان',
            score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'النتيجة',
            total_questions INT DEFAULT 0 COMMENT 'إجمالي الأسئلة',
            correct_answers INT DEFAULT 0 COMMENT 'الإجابات الصحيحة',
            is_passed BOOLEAN DEFAULT FALSE COMMENT 'هل نجح في الامتحان',
            time_taken_minutes INT DEFAULT 0 COMMENT 'الوقت المستغرق بالدقائق',
            completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (exam_id) REFERENCES lesson_exams(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_exam (user_id, exam_id),
            INDEX idx_user_id (user_id),
            INDEX idx_exam_id (exam_id),
            INDEX idx_is_completed (is_completed),
            INDEX idx_is_passed (is_passed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    $db->exec("
        CREATE TABLE IF NOT EXISTS user_summary_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL COMMENT 'معرف المستخدم',
            summary_id INT NOT NULL COMMENT 'معرف الملخص',
            is_completed BOOLEAN DEFAULT FALSE COMMENT 'هل تم إكمال الملخص',
            completed_at TIMESTAMP NULL COMMENT 'تاريخ الإكمال',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (summary_id) REFERENCES lesson_summaries(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_summary (user_id, summary_id),
            INDEX idx_user_id (user_id),
            INDEX idx_summary_id (summary_id),
            INDEX idx_is_completed (is_completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    echo "تم إنشاء جداول تتبع التقدم بنجاح!<br>";

} catch (Exception $e) {
    echo "خطأ في إنشاء جداول التتبع: " . $e->getMessage() . "<br>";
}

// Create uploads directory
$upload_dir = __DIR__ . '/../uploads/summaries/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "تم إنشاء مجلد الملخصات بنجاح!<br>";
    } else {
        echo "فشل في إنشاء مجلد الملخصات<br>";
    }
}

// Remove passing_score column if it exists
try {
    $db->exec("ALTER TABLE lesson_exams DROP COLUMN passing_score");
    echo "تم حذف عمود passing_score من جدول lesson_exams<br>";
} catch (Exception $e) {
    // Column might not exist, ignore error
}

// Remove created_by column if it exists
try {
    $db->exec("ALTER TABLE lesson_exams DROP COLUMN created_by");
    echo "تم حذف عمود created_by من جدول lesson_exams<br>";
} catch (Exception $e) {
    // Column might not exist, ignore error
}

echo "<br><strong>تم الانتهاء من تحديث قاعدة البيانات!</strong><br>";
echo "<a href='lessons.php'>الذهاب لإدارة الدروس</a>";
?>
