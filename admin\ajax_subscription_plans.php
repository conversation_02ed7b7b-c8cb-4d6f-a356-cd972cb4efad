<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json; charset=utf-8');

$response = ['success' => false, 'message' => ''];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if subscription_plans table exists, create if not
        $stmt = $db->query("SHOW TABLES LIKE 'subscription_plans'");
        if ($stmt->rowCount() == 0) {
            // Create subscription_plans table
            $createTableSQL = "CREATE TABLE subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL COMMENT 'اسم الخطة',
                name_en VARCHAR(100) NULL COMMENT 'اسم الخطة بالإنجليزية',
                description TEXT NULL COMMENT 'وصف الخطة',
                price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر الأصلي',
                discount_percentage INT DEFAULT 0 COMMENT 'نسبة الخصم',
                discounted_price DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر بعد الخصم',
                duration_days INT NOT NULL DEFAULT 30 COMMENT 'مدة الاشتراك بالأيام',
                features JSON NULL COMMENT 'مميزات الخطة',
                icon VARCHAR(50) DEFAULT '📚' COMMENT 'أيقونة الخطة',
                color VARCHAR(7) DEFAULT '#4682B4' COMMENT 'لون الخطة',
                is_popular BOOLEAN DEFAULT FALSE COMMENT 'هل الخطة شائعة',
                is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الخطة نشطة',
                sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
                created_by INT NOT NULL DEFAULT 1 COMMENT 'منشئ الخطة',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $db->exec($createTableSQL);
        }
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    // Validate required fields
                    if (empty($_POST['name'])) {
                        throw new Exception('اسم الخطة مطلوب');
                    }
                    if (empty($_POST['price']) || !is_numeric($_POST['price'])) {
                        throw new Exception('السعر مطلوب ويجب أن يكون رقماً');
                    }
                    if (empty($_POST['duration_days']) || !is_numeric($_POST['duration_days'])) {
                        throw new Exception('مدة الاشتراك مطلوبة ويجب أن تكون رقماً');
                    }
                    if (empty($_POST['features'])) {
                        throw new Exception('مميزات الخطة مطلوبة');
                    }
                    
                    // Calculate discounted price
                    $price = floatval($_POST['price']);
                    $discount = intval($_POST['discount_percentage'] ?? 0);
                    $discounted_price = $price - ($price * $discount / 100);
                    
                    // Process features
                    $features = array_filter(array_map('trim', explode("\n", $_POST['features'])));
                    $features_json = json_encode($features, JSON_UNESCAPED_UNICODE);
                    
                    $stmt = $db->prepare("INSERT INTO subscription_plans (name, name_en, description, price, discount_percentage, discounted_price, duration_days, features, icon, color, is_popular, is_active, sort_order, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([
                        $_POST['name'],
                        $_POST['name_en'] ?? '',
                        $_POST['description'] ?? '',
                        $price,
                        $discount,
                        $discounted_price,
                        $_POST['duration_days'],
                        $features_json,
                        $_POST['icon'] ?? '📚',
                        $_POST['color'] ?? '#4682B4',
                        isset($_POST['is_popular']) ? 1 : 0,
                        isset($_POST['is_active']) ? 1 : 0,
                        $_POST['sort_order'] ?? 0,
                        $_SESSION['admin_id']
                    ]);
                    
                    if ($result) {
                        $planId = $db->lastInsertId();
                        
                        // Verify the plan was actually added
                        $verifyStmt = $db->prepare("SELECT name FROM subscription_plans WHERE id = ?");
                        $verifyStmt->execute([$planId]);
                        $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($verifyResult) {
                            $response['success'] = true;
                            $response['message'] = 'تم إضافة الخطة بنجاح - ID: ' . $planId . ' - اسم الخطة: ' . $verifyResult['name'];
                            $response['plan_id'] = $planId;
                        } else {
                            throw new Exception('تم إدراج الخطة لكن لا يمكن التحقق منها');
                        }
                    } else {
                        throw new Exception('فشل في إضافة الخطة - لم يتم تنفيذ الاستعلام');
                    }
                    break;
                    
                case 'edit':
                    // Similar validation and processing for edit
                    if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
                        throw new Exception('معرف الخطة مطلوب');
                    }
                    if (empty($_POST['name'])) {
                        throw new Exception('اسم الخطة مطلوب');
                    }
                    
                    // Calculate discounted price
                    $price = floatval($_POST['price']);
                    $discount = intval($_POST['discount_percentage'] ?? 0);
                    $discounted_price = $price - ($price * $discount / 100);
                    
                    // Process features
                    $features = array_filter(array_map('trim', explode("\n", $_POST['features'])));
                    $features_json = json_encode($features, JSON_UNESCAPED_UNICODE);
                    
                    $stmt = $db->prepare("UPDATE subscription_plans SET name = ?, name_en = ?, description = ?, price = ?, discount_percentage = ?, discounted_price = ?, duration_days = ?, features = ?, icon = ?, color = ?, is_popular = ?, is_active = ?, sort_order = ? WHERE id = ?");
                    $result = $stmt->execute([
                        $_POST['name'],
                        $_POST['name_en'] ?? '',
                        $_POST['description'] ?? '',
                        $price,
                        $discount,
                        $discounted_price,
                        $_POST['duration_days'],
                        $features_json,
                        $_POST['icon'] ?? '📚',
                        $_POST['color'] ?? '#4682B4',
                        isset($_POST['is_popular']) ? 1 : 0,
                        isset($_POST['is_active']) ? 1 : 0,
                        $_POST['sort_order'] ?? 0,
                        $_POST['id']
                    ]);
                    
                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'تم تحديث الخطة بنجاح';
                    } else {
                        throw new Exception('فشل في تحديث الخطة');
                    }
                    break;
                    
                case 'delete':
                    if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
                        throw new Exception('معرف الخطة مطلوب');
                    }
                    
                    $stmt = $db->prepare("DELETE FROM subscription_plans WHERE id = ?");
                    $result = $stmt->execute([$_POST['id']]);
                    
                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'تم حذف الخطة بنجاح';
                    } else {
                        throw new Exception('فشل في حذف الخطة');
                    }
                    break;
                    
                case 'toggle_status':
                    if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
                        throw new Exception('معرف الخطة مطلوب');
                    }
                    
                    $stmt = $db->prepare("UPDATE subscription_plans SET is_active = !is_active WHERE id = ?");
                    $result = $stmt->execute([$_POST['id']]);
                    
                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'تم تغيير حالة الخطة بنجاح';
                    } else {
                        throw new Exception('فشل في تغيير حالة الخطة');
                    }
                    break;
                    
                case 'toggle_popular':
                    if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
                        throw new Exception('معرف الخطة مطلوب');
                    }
                    
                    // First, remove popular status from all plans
                    $db->exec("UPDATE subscription_plans SET is_popular = FALSE");
                    // Then set this plan as popular
                    $stmt = $db->prepare("UPDATE subscription_plans SET is_popular = TRUE WHERE id = ?");
                    $result = $stmt->execute([$_POST['id']]);
                    
                    if ($result) {
                        $response['success'] = true;
                        $response['message'] = 'تم تعيين الخطة كالأكثر شيوعاً';
                    } else {
                        throw new Exception('فشل في تعيين الخطة كشائعة');
                    }
                    break;
                    
                default:
                    throw new Exception('إجراء غير صحيح');
            }
        } else {
            throw new Exception('لم يتم تحديد إجراء');
        }
        
    } catch (Exception $e) {
        $response['success'] = false;
        $response['message'] = $e->getMessage();
        $response['error_details'] = [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'post_data' => $_POST
        ];
        
        // Log the error
        error_log("Subscription Plans AJAX Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    }
} else {
    $response['message'] = 'طريقة الطلب غير صحيحة';
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
