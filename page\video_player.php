<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Get video ID
$video_id = isset($_GET['video_id']) ? (int)$_GET['video_id'] : 0;

if (!$video_id) {
    header('Location: curriculum.php');
    exit;
}

// Get video info with lesson and subject
$stmt = $db->prepare("
    SELECT lv.*, l.title as lesson_title, l.id as lesson_id, l.subject_id, l.is_free,
           cs.name as subject_name, cs.color as subject_color
    FROM lesson_videos lv
    JOIN lessons l ON lv.lesson_id = l.id
    JOIN curriculum_subjects cs ON l.subject_id = cs.id
    WHERE lv.id = ? AND lv.is_active = 1 AND l.is_active = 1
");
$stmt->execute([$video_id]);
$video = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$video) {
    header('Location: curriculum.php');
    exit;
}

// Check if user can access this video
$user_id = $_SESSION['user_id'];
$subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date
                     FROM users u WHERE u.id = ?";
$stmt = $db->prepare($subscriptionQuery);
$stmt->execute([$user_id]);
$userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

$has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                   $userSubscription['subscription_end_date'] &&
                   strtotime($userSubscription['subscription_end_date']) > time();

$can_access = $video['is_free'] || $has_subscription;

if (!$can_access) {
    header('Location: lesson_content.php?lesson_id=' . $video['lesson_id']);
    exit;
}

// Get user's video progress
$stmt = $db->prepare("SELECT * FROM user_video_progress WHERE user_id = ? AND video_id = ?");
$stmt->execute([$user_id, $video_id]);
$progress = $stmt->fetch(PDO::FETCH_ASSOC);

// Handle completion update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['mark_completed'])) {
    if ($progress) {
        $stmt = $db->prepare("UPDATE user_video_progress SET is_completed = 1, completed_at = NOW() WHERE user_id = ? AND video_id = ?");
        $stmt->execute([$user_id, $video_id]);
    } else {
        $stmt = $db->prepare("INSERT INTO user_video_progress (user_id, video_id, is_completed, completed_at) VALUES (?, ?, 1, NOW())");
        $stmt->execute([$user_id, $video_id]);
    }
    
    // Update lesson progress
    updateLessonProgress($db, $user_id, $video['lesson_id']);
    
    $success = "تم تسجيل إكمال الفيديو بنجاح!";
    
    // Refresh progress
    $stmt = $db->prepare("SELECT * FROM user_video_progress WHERE user_id = ? AND video_id = ?");
    $stmt->execute([$user_id, $video_id]);
    $progress = $stmt->fetch(PDO::FETCH_ASSOC);
}

function updateLessonProgress($db, $user_id, $lesson_id) {
    // Get total content count
    $total_query = "
        SELECT 
            (SELECT COUNT(*) FROM lesson_videos WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exercises WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_exams WHERE lesson_id = ? AND is_active = 1) +
            (SELECT COUNT(*) FROM lesson_summaries WHERE lesson_id = ? AND is_active = 1) as total_count
    ";
    $stmt = $db->prepare($total_query);
    $stmt->execute([$lesson_id, $lesson_id, $lesson_id, $lesson_id]);
    $total_count = $stmt->fetchColumn();
    
    // Get completed content count
    $completed_query = "
        SELECT 
            (SELECT COUNT(*) FROM user_video_progress uvp 
             JOIN lesson_videos lv ON uvp.video_id = lv.id 
             WHERE uvp.user_id = ? AND lv.lesson_id = ? AND uvp.is_completed = 1) +
            (SELECT COUNT(*) FROM user_exercise_progress uep 
             JOIN lesson_exercises le ON uep.exercise_id = le.id 
             WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
            (SELECT COUNT(*) FROM user_exam_progress uep 
             JOIN lesson_exams le ON uep.exam_id = le.id 
             WHERE uep.user_id = ? AND le.lesson_id = ? AND uep.is_completed = 1) +
            (SELECT COUNT(*) FROM user_summary_progress usp 
             JOIN lesson_summaries ls ON usp.summary_id = ls.id 
             WHERE usp.user_id = ? AND ls.lesson_id = ? AND usp.is_completed = 1) as completed_count
    ";
    $stmt = $db->prepare($completed_query);
    $stmt->execute([$user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id, $user_id, $lesson_id]);
    $completed_count = $stmt->fetchColumn();
    
    $completion_percentage = $total_count > 0 ? round(($completed_count / $total_count) * 100, 2) : 0;
    $is_completed = $completion_percentage >= 100 ? 1 : 0; // Convert to integer

    // Update or insert lesson progress
    $stmt = $db->prepare("
        INSERT INTO user_lesson_progress (user_id, lesson_id, completion_percentage, is_completed, last_accessed_at, completed_at)
        VALUES (?, ?, ?, ?, NOW(), ?)
        ON DUPLICATE KEY UPDATE
            completion_percentage = VALUES(completion_percentage),
            is_completed = VALUES(is_completed),
            last_accessed_at = VALUES(last_accessed_at),
            completed_at = CASE WHEN VALUES(is_completed) = 1 AND is_completed = 0 THEN NOW() ELSE completed_at END
    ");
    $stmt->execute([$user_id, $lesson_id, $completion_percentage, $is_completed, $is_completed ? date('Y-m-d H:i:s') : null]);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($video['title']); ?> - مشغل الفيديو - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="video-container">
                <!-- Video Header -->
                <div class="video-header">
                    <div class="video-info">
                        <h1><?php echo htmlspecialchars($video['title']); ?></h1>
                        <div class="video-meta">
                            <span class="lesson-badge">
                                <?php echo htmlspecialchars($video['lesson_title']); ?>
                            </span>
                            <span class="subject-badge" style="background-color: <?php echo $video['subject_color']; ?>20; color: <?php echo $video['subject_color']; ?>;">
                                <?php echo htmlspecialchars($video['subject_name']); ?>
                            </span>
                            <?php if ($progress && $progress['is_completed']): ?>
                                <span class="completion-badge">
                                    <i class="fas fa-check-circle"></i>
                                    مكتمل
                                </span>
                            <?php endif; ?>
                        </div>
                        <?php if ($video['description']): ?>
                            <p class="video-description"><?php echo htmlspecialchars($video['description']); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="video-actions">
                        <a href="lesson_content.php?lesson_id=<?php echo $video['lesson_id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للدرس
                        </a>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <!-- Video Player -->
                <div class="video-player-wrapper">
                    <div class="video-player">
                        <?php if ($video['youtube_id']): ?>
                            <iframe 
                                src="https://www.youtube.com/embed/<?php echo $video['youtube_id']; ?>?rel=0&modestbranding=1&showinfo=0"
                                frameborder="0" 
                                allowfullscreen
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                            </iframe>
                        <?php else: ?>
                            <div class="video-placeholder">
                                <i class="fas fa-video"></i>
                                <p>فيديو غير متاح</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Video Controls -->
                <div class="video-controls">
                    <?php if (!$progress || !$progress['is_completed']): ?>
                        <form method="POST" class="completion-form">
                            <button type="submit" name="mark_completed" class="btn btn-success btn-large">
                                <i class="fas fa-check"></i>
                                هل أكملت مشاهدة الفيديو؟
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="completed-message">
                            <i class="fas fa-check-circle"></i>
                            <span>تم إكمال مشاهدة الفيديو</span>
                            <small>تم الإكمال في: <?php echo date('d/m/Y H:i', strtotime($progress['completed_at'])); ?></small>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Video Navigation -->
                <div class="video-navigation">
                    <h3>فيديوهات أخرى في هذا الدرس</h3>
                    <?php
                    // Get other videos in this lesson
                    $stmt = $db->prepare("
                        SELECT lv.*, COALESCE(uvp.is_completed, 0) as is_completed
                        FROM lesson_videos lv
                        LEFT JOIN user_video_progress uvp ON lv.id = uvp.video_id AND uvp.user_id = ?
                        WHERE lv.lesson_id = ? AND lv.is_active = 1
                        ORDER BY lv.video_order
                    ");
                    $stmt->execute([$user_id, $video['lesson_id']]);
                    $other_videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    ?>
                    
                    <?php if (count($other_videos) > 1): ?>
                        <div class="video-list">
                            <?php foreach ($other_videos as $other_video): ?>
                                <div class="video-item <?php echo $other_video['id'] == $video_id ? 'current' : ''; ?> <?php echo $other_video['is_completed'] ? 'completed' : ''; ?>">
                                    <div class="video-thumbnail-small">
                                        <?php if ($other_video['youtube_id']): ?>
                                            <img src="https://img.youtube.com/vi/<?php echo $other_video['youtube_id']; ?>/mqdefault.jpg" alt="Video thumbnail">
                                        <?php endif; ?>
                                        <div class="video-overlay">
                                            <?php if ($other_video['is_completed']): ?>
                                                <i class="fas fa-check-circle"></i>
                                            <?php elseif ($other_video['id'] == $video_id): ?>
                                                <i class="fas fa-play"></i>
                                            <?php else: ?>
                                                <i class="far fa-play-circle"></i>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="video-details">
                                        <h4><?php echo htmlspecialchars($other_video['title']); ?></h4>
                                        <?php if ($other_video['description']): ?>
                                            <p><?php echo htmlspecialchars(substr($other_video['description'], 0, 60)) . '...'; ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($other_video['id'] != $video_id): ?>
                                        <a href="video_player.php?video_id=<?php echo $other_video['id']; ?>" class="video-link">
                                            <i class="fas fa-play"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="no-other-videos">هذا هو الفيديو الوحيد في هذا الدرس</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <style>
        .video-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg,
                rgba(135, 206, 235, 0.02) 0%,
                rgba(70, 130, 180, 0.05) 50%,
                rgba(32, 178, 170, 0.02) 100%);
            min-height: 100vh;
        }

        .video-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .video-info h1 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 2rem;
            font-weight: 700;
            line-height: 1.3;
        }

        .video-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .lesson-badge, .subject-badge, .completion-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .lesson-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .completion-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .video-description {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        .video-player-wrapper {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .video-player {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            border-radius: 15px;
            overflow: hidden;
            background: #000;
        }

        .video-player iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .video-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            background: #f8f9fa;
        }

        .video-placeholder i {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .video-controls {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            text-align: center;
        }

        .completion-form {
            margin: 0;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .completed-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: #28a745;
        }

        .completed-message i {
            font-size: 48px;
        }

        .completed-message span {
            font-size: 18px;
            font-weight: 600;
        }

        .completed-message small {
            color: #6c757d;
            font-size: 12px;
        }

        .video-navigation {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .video-navigation h3 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .video-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .video-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .video-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }

        .video-item.current {
            background: linear-gradient(135deg, rgba(135, 206, 235, 0.2), rgba(70, 130, 180, 0.1));
            border: 2px solid #87CEEB;
        }

        .video-item.completed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 2px;
        }

        .video-thumbnail-small {
            position: relative;
            width: 80px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .video-thumbnail-small img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .video-details {
            flex: 1;
            min-width: 0;
        }

        .video-details h4 {
            color: #2c3e50;
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .video-details p {
            color: #6c757d;
            margin: 0;
            font-size: 12px;
            line-height: 1.4;
        }

        .video-link {
            color: #4682B4;
            font-size: 20px;
            text-decoration: none;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .video-link:hover {
            background: rgba(70, 130, 180, 0.1);
            transform: scale(1.1);
        }

        .no-other-videos {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .video-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .video-info h1 {
                font-size: 1.5rem;
            }

            .video-meta {
                justify-content: center;
            }

            .video-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .video-thumbnail-small {
                width: 120px;
                height: 90px;
            }
        }
    </style>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
