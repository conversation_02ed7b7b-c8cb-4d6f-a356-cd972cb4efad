<?php
require_once 'database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['type']) || !isset($input['value'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid input']);
    exit;
}

$type = $input['type'];
$value = trim($input['value']);

if (empty($value)) {
    echo json_encode(['exists' => false]);
    exit;
}

try {
    $userManager = new UserManager();
    $exists = false;
    
    switch ($type) {
        case 'username':
            $exists = $userManager->usernameExists($value);
            break;
            
        case 'email':
            $exists = $userManager->emailExists($value);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid type']);
            exit;
    }
    
    echo json_encode(['exists' => $exists]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error']);
}
?>
