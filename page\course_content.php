<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Check if user has access to this course
if (!$courseManager->userHasAccess($userId, $courseId)) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=no_access');
    exit;
}

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

// Get all course content organized by weeks
$allContent = [];

// Get videos (if table exists)
try {
    $stmt = $db->prepare("
        SELECT 'video' as type, id, week_number, title, description, 1 as content_order, '' as timing_info, video_url, video_platform, NULL as duration_minutes
        FROM course_videos
        WHERE course_id = ? AND is_active = 1
    ");
    $stmt->execute([$courseId]);
    $videos = $stmt->fetchAll();
    $allContent = array_merge($allContent, $videos);
} catch (Exception $e) {
    // Table doesn't exist, skip videos
}

// Get exercises
try {
    $stmt = $db->prepare("
        SELECT 'exercise' as type, id, week_number, question_text as title, explanation as description, 2 as content_order, '' as timing_info, NULL as video_url, NULL as video_platform, NULL as duration_minutes
        FROM course_exercises
        WHERE course_id = ?
    ");
    $stmt->execute([$courseId]);
    $exercises = $stmt->fetchAll();
    $allContent = array_merge($allContent, $exercises);
} catch (Exception $e) {
    // Table doesn't exist or error, skip exercises
}

// Get exams
try {
    $stmt = $db->prepare("
        SELECT 'exam' as type, id, week_number, title, description, 3 as content_order, '' as timing_info, NULL as video_url, NULL as video_platform, duration_minutes
        FROM course_exams
        WHERE course_id = ? AND is_active = 1
    ");
    $stmt->execute([$courseId]);
    $exams = $stmt->fetchAll();
    $allContent = array_merge($allContent, $exams);
} catch (Exception $e) {
    // Table doesn't exist or error, skip exams
}

// Get weekly tests
try {
    $stmt = $db->prepare("
        SELECT 'weekly_test' as type, id, week_number, title, description, 4 as content_order, '' as timing_info, NULL as video_url, NULL as video_platform, duration_minutes
        FROM course_weekly_tests
        WHERE course_id = ? AND is_active = 1
    ");
    $stmt->execute([$courseId]);
    $weeklyTests = $stmt->fetchAll();
    $allContent = array_merge($allContent, $weeklyTests);
} catch (Exception $e) {
    // Table doesn't exist or error, skip weekly tests
}

// Sort by week_number and content_order
usort($allContent, function($a, $b) {
    if ($a['week_number'] == $b['week_number']) {
        return $a['content_order'] - $b['content_order'];
    }
    return $a['week_number'] - $b['week_number'];
});

// Add exercises as week-based content
$exerciseStmt = $db->prepare("
    SELECT DISTINCT week_number, COUNT(*) as exercise_count
    FROM course_exercises
    WHERE course_id = ? AND week_number IS NOT NULL
    GROUP BY week_number
    ORDER BY week_number
");
$exerciseStmt->execute([$courseId]);
$exerciseWeeks = $exerciseStmt->fetchAll();

// Add exercise entries to content
foreach ($exerciseWeeks as $exerciseWeek) {
    $allContent[] = [
        'type' => 'exercise',
        'id' => 'week_' . $exerciseWeek['week_number'], // Special ID format for week exercises
        'week_number' => $exerciseWeek['week_number'],
        'title' => 'تمارين الأسبوع (' . $exerciseWeek['exercise_count'] . ' أسئلة)',
        'description' => null,
        'content_order' => 2, // After videos, before exams
        'timing_info' => null,
        'video_url' => null,
        'video_platform' => null,
        'duration_minutes' => null
    ];
}

// Organize content by weeks
$weeklyContent = [];
foreach ($allContent as $content) {
    $weeklyContent[$content['week_number']][] = $content;
}

// Get user progress
$stmt = $db->prepare("SELECT video_id, progress_percentage, completed FROM user_video_progress WHERE user_id = ?");
$stmt->execute([$userId]);
$videoProgress = [];
foreach ($stmt->fetchAll() as $progress) {
    $videoProgress[$progress['video_id']] = $progress;
}

// Get exercise completion status
$exerciseCompletion = [];
try {
    $stmt = $db->prepare("
        SELECT DISTINCT exercise_id, 1 as completed
        FROM user_exercise_attempts
        WHERE user_id = ? AND is_correct = 1
    ");
    $stmt->execute([$userId]);
    while ($row = $stmt->fetch()) {
        $exerciseCompletion[$row['exercise_id']] = true;
    }
} catch (Exception $e) {
    // Table might not exist
}

// Get week-based exercise completion
$weekExerciseCompletion = [];
try {
    $stmt = $db->prepare("
        SELECT ce.week_number, COUNT(DISTINCT ce.id) as total_exercises,
               COUNT(DISTINCT uea.exercise_id) as completed_exercises
        FROM course_exercises ce
        LEFT JOIN user_exercise_attempts uea ON ce.id = uea.exercise_id AND uea.user_id = ? AND uea.is_correct = 1
        WHERE ce.course_id = ?
        GROUP BY ce.week_number
    ");
    $stmt->execute([$userId, $courseId]);
    while ($row = $stmt->fetch()) {
        $weekExerciseCompletion['week_' . $row['week_number']] =
            $row['completed_exercises'] >= $row['total_exercises'] && $row['total_exercises'] > 0;
    }
} catch (Exception $e) {
    // Table might not exist
}

// Get exam completion status with scores
$examCompletion = [];
$examScores = [];
try {
    $stmt = $db->prepare("
        SELECT uea.exam_id, uea.percentage, uea.passed, 1 as completed
        FROM user_exam_attempts uea
        WHERE uea.user_id = ? AND uea.completed_at IS NOT NULL
        ORDER BY uea.completed_at DESC
    ");
    $stmt->execute([$userId]);
    while ($row = $stmt->fetch()) {
        $examCompletion[$row['exam_id']] = true;
        $examScores[$row['exam_id']] = $row['percentage'];
    }
} catch (Exception $e) {
    // Table might not exist - fallback to old method
    try {
        $stmt = $db->prepare("
            SELECT DISTINCT exam_id, 1 as completed
            FROM user_exam_answers_simple ueas
            JOIN course_exam_questions ceq ON ueas.question_id = ceq.id
            WHERE ueas.user_id = ?
            GROUP BY exam_id
            HAVING COUNT(DISTINCT ueas.question_id) = (
                SELECT COUNT(*) FROM course_exam_questions WHERE exam_id = exam_id
            )
        ");
        $stmt->execute([$userId]);
        while ($row = $stmt->fetch()) {
            $examCompletion[$row['exam_id']] = true;
            $examScores[$row['exam_id']] = 100; // Assume 100% for old data
        }
    } catch (Exception $e2) {
        // Table might not exist
    }
}

// Get weekly test completion status with scores
$weeklyTestCompletion = [];
$weeklyTestScores = [];
try {
    $stmt = $db->prepare("
        SELECT uwta.test_id, uwta.percentage, 1 as completed
        FROM user_weekly_test_attempts uwta
        WHERE uwta.user_id = ? AND uwta.completed_at IS NOT NULL
        ORDER BY uwta.completed_at DESC
    ");
    $stmt->execute([$userId]);
    while ($row = $stmt->fetch()) {
        $weeklyTestCompletion[$row['test_id']] = true;
        $weeklyTestScores[$row['test_id']] = $row['percentage'];
    }
} catch (Exception $e) {
    // Table might not exist - fallback to old method
    try {
        $stmt = $db->prepare("
            SELECT DISTINCT test_id, 1 as completed
            FROM user_weekly_test_answers_simple uwtas
            JOIN course_weekly_test_questions cwtq ON uwtas.question_id = cwtq.id
            WHERE uwtas.user_id = ?
            GROUP BY test_id
            HAVING COUNT(DISTINCT uwtas.question_id) = (
                SELECT COUNT(*) FROM course_weekly_test_questions WHERE test_id = test_id
            )
        ");
        $stmt->execute([$userId]);
        while ($row = $stmt->fetch()) {
            $weeklyTestCompletion[$row['test_id']] = true;
            $weeklyTestScores[$row['test_id']] = 100; // Assume 100% for old data
        }
    } catch (Exception $e2) {
        // Table might not exist
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="modern-course-layout">
        <!-- Main Content Area (Full Screen) -->
        <div class="course-main-fullscreen">
            <div class="course-header-modern">
                <div class="course-info">
                    <h1 class="course-title-modern"><?php echo htmlspecialchars($course['title']); ?></h1>
                    <p class="course-subject-modern"><?php echo htmlspecialchars($course['subject']); ?></p>
                </div>
                <div class="course-actions">
                    <button class="btn-back" onclick="window.location.href='courses.php'"
                        <i class="fas fa-arrow-right"></i>
                        العودة للكورسات
                    </button>
                </div>
            </div>

            <div class="content-display-modern" id="contentDisplay">
                <div class="welcome-content-modern">
                    <div class="welcome-animation">
                        <div class="welcome-icon-modern">🎓</div>
                        <div class="floating-elements">
                            <div class="float-element">📚</div>
                            <div class="float-element">✨</div>
                            <div class="float-element">🌟</div>
                        </div>
                    </div>
                    <h2>مرحباً بك في رحلة التعلم</h2>
                    <p class="welcome-description"><?php echo htmlspecialchars($course['description']); ?></p>

                    <?php if ($course['features']): ?>
                        <div class="course-features-modern">
                            <h3><i class="fas fa-star"></i> مميزات الكورس</h3>
                            <div class="features-grid">
                                <?php
                                $features = explode("\n", $course['features']);
                                foreach ($features as $feature):
                                    if (trim($feature)):
                                ?>
                                    <div class="feature-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span><?php echo htmlspecialchars(trim($feature)); ?></span>
                                    </div>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="start-learning">
                        <p class="start-instruction">اضغط على "إظهار المحتوى" أدناه لبدء رحلة التعلم</p>
                        <div class="arrow-down">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Content Navigation -->
        <div class="bottom-content-bar">
            <div class="content-navigation">
                <button class="nav-btn prev-btn" id="prevContentBtn" onclick="navigateContent('prev')" disabled>
                    <i class="fas fa-chevron-right"></i>
                    <span>المحتوى السابق</span>
                </button>

                <button class="show-content-btn" id="showContentBtn" onclick="toggleContentModal()">
                    <i class="fas fa-list"></i>
                    <span>إظهار المحتوى</span>
                    <div class="content-summary" id="contentSummary">
                        <span class="completed-count">0</span>/<span class="total-count">0</span>
                    </div>
                </button>

                <button class="nav-btn next-btn" id="nextContentBtn" onclick="navigateContent('next')" disabled>
                    <span>المحتوى التالي</span>
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        </div>

        <!-- Full-Screen Content Modal -->
        <div class="content-modal" id="contentModal">
            <div class="modal-header">
                <h3>محتوى الكورس</h3>
                <button class="modal-close" onclick="toggleContentModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-content-area">
                <!-- Completion Summary Section -->
                <div class="completion-summary-section">
                    <div class="summary-cards">
                        <div class="summary-card completed-card">
                            <div class="card-icon">✅</div>
                            <div class="card-content">
                                <div class="card-number" id="completedItemsCount">0</div>
                                <div class="card-label">مكتمل</div>
                            </div>
                        </div>
                        <div class="summary-card perfect-card">
                            <div class="card-icon">👑</div>
                            <div class="card-content">
                                <div class="card-number" id="perfectScoreCount">0</div>
                                <div class="card-label">نتيجة مثالية</div>
                            </div>
                        </div>
                        <div class="summary-card pending-card">
                            <div class="card-icon">⏳</div>
                            <div class="card-content">
                                <div class="card-number" id="pendingItemsCount">0</div>
                                <div class="card-label">في الانتظار</div>
                            </div>
                        </div>
                        <div class="summary-card progress-card">
                            <div class="card-icon">📊</div>
                            <div class="card-content">
                                <div class="card-number" id="overallProgress">0%</div>
                                <div class="card-label">التقدم الإجمالي</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-sidebar-horizontal">
                    <?php if (!empty($weeklyContent)): ?>
                        <?php foreach ($weeklyContent as $weekNumber => $weekContent): ?>
                            <div class="week-section-modern">
                                <div class="week-header-modern" onclick="toggleWeekModern(<?php echo $weekNumber; ?>)">
                                    <div class="week-info">
                                        <span class="week-number"><?php echo $weekNumber; ?></span>
                                        <span class="week-title-modern">الأسبوع <?php echo $weekNumber; ?></span>
                                    </div>
                                    <span class="week-toggle-modern">
                                        <i class="fas fa-chevron-down"></i>
                                    </span>
                                </div>
                                <div class="week-content-modern" id="week-modern-<?php echo $weekNumber; ?>">
                                    <div class="content-grid">
                                        <?php foreach ($weekContent as $content): ?>
                                            <?php
                                            // Check completion status for all content types
                                            $isCompleted = false;
                                            $completionScore = 0;
                                            $isPerfectScore = false;

                                            switch ($content['type']) {
                                                case 'video':
                                                    $isCompleted = isset($videoProgress[$content['id']]) && $videoProgress[$content['id']]['completed'];
                                                    $completionScore = isset($videoProgress[$content['id']]) ? $videoProgress[$content['id']]['progress_percentage'] : 0;
                                                    $isPerfectScore = $completionScore >= 100;
                                                    break;
                                                case 'exercise':
                                                    if (strpos($content['id'], 'week_') === 0) {
                                                        // Week-based exercises - check for 100% completion
                                                        $isCompleted = isset($weekExerciseCompletion[$content['id']]) && $weekExerciseCompletion[$content['id']];
                                                        $isPerfectScore = $isCompleted; // For exercises, completion means 100%
                                                        $completionScore = $isCompleted ? 100 : 0;
                                                    } else {
                                                        // Individual exercises
                                                        $isCompleted = isset($exerciseCompletion[$content['id']]);
                                                        $isPerfectScore = $isCompleted;
                                                        $completionScore = $isCompleted ? 100 : 0;
                                                    }
                                                    break;
                                                case 'exam':
                                                    $isCompleted = isset($examCompletion[$content['id']]);
                                                    $completionScore = isset($examScores[$content['id']]) ? $examScores[$content['id']] : 0;
                                                    $isPerfectScore = $completionScore >= 100;
                                                    break;
                                                case 'weekly_test':
                                                    $isCompleted = isset($weeklyTestCompletion[$content['id']]);
                                                    $completionScore = isset($weeklyTestScores[$content['id']]) ? $weeklyTestScores[$content['id']] : 0;
                                                    $isPerfectScore = $completionScore >= 100;
                                                    break;
                                            }
                                            ?>
                                            <div class="content-item-modern <?php echo $isCompleted ? 'completed' : ''; ?> <?php echo $isPerfectScore ? 'perfect-score' : ''; ?>"
                                                 onclick="<?php echo $isPerfectScore ? 'showPerfectScoreMessage(\'' . $content['type'] . '\', ' . $content['id'] . ')' : 'loadContentModern(\'' . $content['type'] . '\', ' . $content['id'] . ')'; ?>"
                                                 data-type="<?php echo $content['type']; ?>"
                                                 data-id="<?php echo $content['id']; ?>"
                                                 data-completed="<?php echo $isCompleted ? 'true' : 'false'; ?>"
                                                 data-score="<?php echo $completionScore; ?>"
                                                 data-perfect="<?php echo $isPerfectScore ? 'true' : 'false'; ?>">
                                                <div class="content-icon-modern">
                                                    <?php
                                                    $icons = [
                                                        'video' => '<i class="fas fa-play-circle"></i>',
                                                        'exercise' => '<i class="fas fa-edit"></i>',
                                                        'exam' => '<i class="fas fa-clipboard-check"></i>',
                                                        'weekly_test' => '<i class="fas fa-flask"></i>'
                                                    ];
                                                    echo $icons[$content['type']];
                                                    ?>
                                                </div>
                                                <div class="content-info-modern">
                                                    <div class="content-title-modern"><?php echo htmlspecialchars($content['title']); ?></div>
                                                    <div class="content-type-badge">
                                                        <?php
                                                        $typeNames = [
                                                            'video' => 'فيديو',
                                                            'exercise' => 'تمرين',
                                                            'exam' => 'امتحان',
                                                            'weekly_test' => 'اختبار أسبوعي'
                                                        ];
                                                        echo $typeNames[$content['type']];
                                                        ?>
                                                    </div>
                                                    <?php if ($content['type'] === 'video' && isset($videoProgress[$content['id']])): ?>
                                                        <div class="progress-bar-modern">
                                                            <div class="progress-fill-modern" style="width: <?php echo $videoProgress[$content['id']]['progress_percentage']; ?>%"></div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($isCompleted): ?>
                                                    <div class="completion-badge-modern <?php echo $isPerfectScore ? 'perfect-completion' : ''; ?>">
                                                        <i class="fas fa-<?php echo $isPerfectScore ? 'crown' : 'check'; ?>"></i>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($isPerfectScore): ?>
                                                    <div class="perfect-score-overlay">
                                                        <div class="perfect-score-badge">
                                                            <i class="fas fa-star"></i>
                                                            <span>100%</span>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-content-modern">
                            <div class="empty-icon-modern">📚</div>
                            <h3>لا يوجد محتوى متاح حالياً</h3>
                            <p>سيتم إضافة المحتوى قريباً</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Modal Overlay -->
        <div class="modal-overlay" id="modalOverlay" onclick="toggleContentModal()"></div>
    </div>

    <style>
        /* Modern Course Layout - Enhanced Dashboard Color Scheme */
        .modern-course-layout {
            min-height: 100vh;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            overflow-x: hidden;
        }

        /* Enhanced Background Animation */
        .modern-course-layout::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(70, 130, 180, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(135, 206, 235, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(70, 130, 180, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        /* Course Header - Dashboard Style */
        .course-header-modern {
            padding: 30px 40px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid rgba(135, 206, 235, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.15);
            border-radius: 0 0 20px 20px;
        }

        .course-info h1.course-title-modern {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 8px 0;
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(70, 130, 180, 0.1);
        }

        .course-subject-modern {
            color: #6c757d;
            font-size: 16px;
            margin: 0;
            font-weight: 500;
            background: rgba(135, 206, 235, 0.05);
            padding: 8px 15px;
            border-radius: 15px;
            border-left: 3px solid #87CEEB;
        }

        .btn-back {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
        }

        /* Main Content Area */
        .course-main-fullscreen {
            min-height: calc(100vh - 120px);
            padding-bottom: 120px;
        }

        .content-display-modern {
            padding: 40px;
            min-height: calc(100vh - 200px);
        }

        /* Welcome Content */
        .welcome-content-modern {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
            padding: 60px 0;
        }

        .welcome-animation {
            position: relative;
            margin-bottom: 40px;
        }

        .welcome-icon-modern {
            font-size: 120px;
            margin-bottom: 30px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 300px;
            pointer-events: none;
        }

        .float-element {
            position: absolute;
            font-size: 30px;
            animation: floatAround 6s ease-in-out infinite;
        }

        .float-element:nth-child(1) {
            top: 20%;
            right: 10%;
            animation-delay: 0s;
        }

        .float-element:nth-child(2) {
            top: 60%;
            left: 15%;
            animation-delay: 2s;
        }

        .float-element:nth-child(3) {
            top: 30%;
            left: 80%;
            animation-delay: 4s;
        }

        @keyframes floatAround {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            33% { transform: translateY(-30px) rotate(120deg); opacity: 1; }
            66% { transform: translateY(15px) rotate(240deg); opacity: 0.8; }
        }

        .welcome-content-modern h2 {
            font-size: 36px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .welcome-description {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            margin-bottom: 40px;
            text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
        }

        /* Enhanced Progress Bar */
        .course-progress-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 40px;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(135, 206, 235, 0.2);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .progress-title {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .progress-percentage {
            font-size: 24px;
            font-weight: 800;
            color: #4682B4;
            text-shadow: 2px 2px 4px rgba(70, 130, 180, 0.1);
        }

        .progress-bar-container {
            position: relative;
            height: 12px;
            background: rgba(135, 206, 235, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #4682B4, #87CEEB);
            border-radius: 10px;
            transition: width 1s ease;
            position: relative;
        }

        .progress-bar-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #6c757d;
        }

        .progress-stat {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .progress-stat i {
            color: #4682B4;
        }

        /* Smart Notifications */
        .smart-notification {
            position: fixed;
            top: 100px;
            right: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.2);
            border-left: 4px solid #4682B4;
            max-width: 350px;
            z-index: 1500;
            transform: translateX(400px);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .smart-notification.show {
            transform: translateX(0);
        }

        .notification-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .notification-icon {
            font-size: 20px;
            color: #4682B4;
        }

        .notification-title {
            font-weight: 700;
            color: #2c3e50;
            font-size: 16px;
        }

        .notification-message {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
        }

        .notification-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .notification-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-btn.primary {
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            color: white;
        }

        .notification-btn.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        /* Course Features */
        .course-features-modern {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .course-features-modern h3 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateX(-5px);
        }

        .feature-item i {
            color: #4682B4;
            font-size: 18px;
        }

        .feature-item span {
            color: #2c3e50;
            font-weight: 500;
        }

        /* Start Learning Section */
        .start-learning {
            margin-top: 50px;
        }

        .start-instruction {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 20px;
            text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
        }

        .arrow-down {
            font-size: 30px;
            color: rgba(255, 255, 255, 0.8);
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* Bottom Content Bar - Dashboard Style */
        .bottom-content-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(70, 130, 180, 0.1);
            padding: 20px 40px;
            box-shadow: 0 -10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .content-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-btn {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 150px;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .nav-btn:disabled {
            background: linear-gradient(135deg, #ccc 0%, #999 100%);
            cursor: not-allowed;
            opacity: 0.6;
            box-shadow: none;
        }

        .nav-btn:not(:disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
        }

        .show-content-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            position: relative;
        }

        .content-summary {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .show-content-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        /* Content Modal */
        .content-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: 2000;
            transform: translateY(100%);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow-y: auto;
        }

        .content-modal.show {
            transform: translateY(0);
        }

        .modal-header {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .modal-header h3 {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
        }

        .modal-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .modal-content-area {
            padding: 40px;
            min-height: calc(100vh - 100px);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        /* Completion Summary Section */
        .completion-summary-section {
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.1);
            border: 2px solid rgba(135, 206, 235, 0.2);
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .summary-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .completed-card {
            border-color: #28a745;
        }

        .completed-card .card-icon {
            color: #28a745;
            font-size: 24px;
        }

        .perfect-card {
            border-color: #ffd700;
        }

        .perfect-card .card-icon {
            color: #ffd700;
            font-size: 24px;
        }

        .pending-card {
            border-color: #ffc107;
        }

        .pending-card .card-icon {
            color: #ffc107;
            font-size: 24px;
        }

        .progress-card {
            border-color: #4682B4;
        }

        .progress-card .card-icon {
            color: #4682B4;
            font-size: 24px;
        }

        .card-content {
            flex: 1;
        }

        .card-number {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
        }

        .card-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
            margin-top: 5px;
        }

        /* Content Sidebar Horizontal */
        .content-sidebar-horizontal {
            max-width: 1200px;
            margin: 0 auto;
        }

        .week-section-modern {
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .week-header-modern {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px 30px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-bottom: 1px solid #dee2e6;
        }

        .week-header-modern:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .week-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .week-number {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .week-title-modern {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
        }

        .week-toggle-modern {
            color: #4682B4;
            transition: transform 0.3s ease;
            font-size: 18px;
        }

        .week-section-modern.collapsed .week-toggle-modern {
            transform: rotate(-90deg);
        }

        .week-content-modern {
            padding: 30px;
            max-height: 1000px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .week-section-modern.collapsed .week-content-modern {
            max-height: 0;
            padding: 0 30px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .content-item-modern {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid rgba(135, 206, 235, 0.2);
            border-radius: 20px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.1);
            backdrop-filter: blur(10px);
        }

        /* Distinct Content Type Styles */
        .content-item-modern[data-type="video"] {
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border-color: #ff6b6b;
        }

        .content-item-modern[data-type="video"]:hover {
            border-color: #ff5252;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.3);
            background: linear-gradient(135deg, #fff0f0 0%, #ffe0e0 100%);
        }

        .content-item-modern[data-type="exercise"] {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
            border-color: #4dabf7;
        }

        .content-item-modern[data-type="exercise"]:hover {
            border-color: #339af0;
            box-shadow: 0 15px 35px rgba(77, 171, 247, 0.3);
            background: linear-gradient(135deg, #e8f4ff 0%, #d6edff 100%);
        }

        .content-item-modern[data-type="exam"] {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            border-color: #ffb74d;
        }

        .content-item-modern[data-type="exam"]:hover {
            border-color: #ffa726;
            box-shadow: 0 15px 35px rgba(255, 183, 77, 0.3);
            background: linear-gradient(135deg, #fff3c4 0%, #ffe082 100%);
        }

        .content-item-modern[data-type="weekly_test"] {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-color: #ba68c8;
        }

        .content-item-modern[data-type="weekly_test"]:hover {
            border-color: #ab47bc;
            box-shadow: 0 15px 35px rgba(186, 104, 200, 0.3);
            background: linear-gradient(135deg, #ede7f6 0%, #d1c4e9 100%);
        }

        .content-item-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4682B4, #87CEEB);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .content-item-modern:hover::before {
            transform: scaleX(1);
        }

        .content-item-modern:hover {
            border-color: #87CEEB;
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(70, 130, 180, 0.2);
            background: linear-gradient(135deg, #ffffff 0%, rgba(135, 206, 235, 0.05) 100%);
        }

        .content-item-modern.active {
            border-color: #4682B4;
            background: linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(135, 206, 235, 0.1) 100%);
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.3);
        }

        .content-icon-modern {
            font-size: 36px;
            color: #4682B4;
            margin-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, rgba(70, 130, 180, 0.1), rgba(135, 206, 235, 0.1));
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            transition: all 0.3s ease;
        }

        /* Content Type Specific Icons */
        .content-item-modern[data-type="video"] .content-icon-modern {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 82, 82, 0.1));
            color: #ff5252;
        }

        .content-item-modern[data-type="exercise"] .content-icon-modern {
            background: linear-gradient(135deg, rgba(77, 171, 247, 0.2), rgba(51, 154, 240, 0.1));
            color: #339af0;
        }

        .content-item-modern[data-type="exam"] .content-icon-modern {
            background: linear-gradient(135deg, rgba(255, 183, 77, 0.2), rgba(255, 167, 38, 0.1));
            color: #ffa726;
        }

        .content-item-modern[data-type="weekly_test"] .content-icon-modern {
            background: linear-gradient(135deg, rgba(186, 104, 200, 0.2), rgba(171, 71, 188, 0.1));
            color: #ab47bc;
        }

        .content-item-modern:hover .content-icon-modern {
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            color: white;
            transform: scale(1.1) rotate(5deg);
        }

        .content-info-modern {
            text-align: center;
        }

        .content-title-modern {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .content-type-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 10px;
        }

        /* Content Type Specific Badges */
        .content-item-modern[data-type="video"] .content-type-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
        }

        .content-item-modern[data-type="exercise"] .content-type-badge {
            background: linear-gradient(135deg, #4dabf7 0%, #339af0 100%);
        }

        .content-item-modern[data-type="exam"] .content-type-badge {
            background: linear-gradient(135deg, #ffb74d 0%, #ffa726 100%);
        }

        .content-item-modern[data-type="weekly_test"] .content-type-badge {
            background: linear-gradient(135deg, #ba68c8 0%, #ab47bc 100%);
        }

        .progress-bar-modern {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill-modern {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transition: width 0.3s ease;
        }

        .completion-badge-modern {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            animation: completionPulse 2s ease-in-out infinite;
            z-index: 2;
        }

        .completion-badge-modern.perfect-completion {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #333;
            animation: perfectScorePulse 2s ease-in-out infinite;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.5);
        }

        .perfect-score-overlay {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 179, 0, 0.05));
            border-radius: 20px;
            pointer-events: none;
            z-index: 1;
        }

        .perfect-score-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 3px;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
            animation: sparkle 3s ease-in-out infinite;
        }

        @keyframes perfectScorePulse {
            0%, 100% { transform: scale(1); box-shadow: 0 4px 15px rgba(255, 215, 0, 0.5); }
            50% { transform: scale(1.1); box-shadow: 0 6px 20px rgba(255, 215, 0, 0.7); }
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.05) rotate(5deg); }
            75% { transform: scale(1.05) rotate(-5deg); }
        }

        .content-item-modern.perfect-score {
            border-color: #ffd700 !important;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3) !important;
        }

        .content-item-modern.perfect-score:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4) !important;
        }

        @keyframes completionPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .content-item-modern.completed {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
            border-color: #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
        }

        .content-item-modern.completed .content-icon-modern {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .content-item-modern.completed .content-type-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        /* Action buttons for exercise results */
        .action-buttons-container {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            min-width: 140px;
            justify-content: center;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-action.btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-action.btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-action.btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        /* Enhanced celebration animation with dashboard colors */
        .celebration-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
            z-index: 1000;
        }

        .celebration-animation .confetti {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: confettiFall 4s ease-out infinite;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .celebration-animation .confetti:nth-child(1) {
            left: 10%;
            animation-delay: 0s;
            background: linear-gradient(135deg, #4682B4, #87CEEB);
        }

        .celebration-animation .confetti:nth-child(2) {
            left: 30%;
            animation-delay: 0.3s;
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .celebration-animation .confetti:nth-child(3) {
            left: 50%;
            animation-delay: 0.6s;
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .celebration-animation .confetti:nth-child(4) {
            left: 70%;
            animation-delay: 0.9s;
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .celebration-animation .confetti:nth-child(5) {
            left: 90%;
            animation-delay: 1.2s;
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
        }

        /* Floating particles */
        .celebration-animation::before,
        .celebration-animation::after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            background: linear-gradient(135deg, #87CEEB, #4682B4);
            border-radius: 50%;
            animation: floatUp 3s ease-out infinite;
        }

        .celebration-animation::before {
            left: 25%;
            animation-delay: 0.5s;
        }

        .celebration-animation::after {
            left: 75%;
            animation-delay: 1.5s;
        }

        @keyframes confettiFall {
            0% {
                transform: translateY(-100px) rotate(0deg) scale(0);
                opacity: 1;
            }
            10% {
                transform: translateY(-80px) rotate(36deg) scale(1);
                opacity: 1;
            }
            100% {
                transform: translateY(400px) rotate(720deg) scale(0.5);
                opacity: 0;
            }
        }

        @keyframes floatUp {
            0% {
                transform: translateY(100px) scale(0);
                opacity: 0;
            }
            50% {
                transform: translateY(-50px) scale(1);
                opacity: 1;
            }
            100% {
                transform: translateY(-200px) scale(0);
                opacity: 0;
            }
        }

        /* Success pulse animation */
        @keyframes successPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
            }
        }

        /* Smooth content transitions */
        .content-item-modern {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .content-item-modern:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.2);
        }

        .content-item-modern.completed {
            animation: completedGlow 2s ease-in-out infinite alternate;
        }

        @keyframes completedGlow {
            0% {
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            }
            100% {
                box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
            }
        }

        .empty-content-modern {
            text-align: center;
            padding: 80px 20px;
            color: #6c757d;
        }

        .empty-icon-modern {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .empty-content-modern h3 {
            font-size: 24px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Motivational Modal Styles */
        .motivational-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 3000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .motivational-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .motivational-modal {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            transform: scale(0.7);
            transition: transform 0.3s ease;
            border: 3px solid #4682B4;
        }

        .motivational-modal-overlay.show .motivational-modal {
            transform: scale(1);
        }

        .motivational-header {
            margin-bottom: 25px;
        }

        .motivational-icon {
            font-size: 60px;
            margin-bottom: 15px;
            animation: bounce 2s infinite;
        }

        .motivational-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .motivational-content p {
            font-size: 16px;
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .motivational-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .motivational-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .motivational-btn.btn-primary {
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            color: white;
        }

        .motivational-btn.btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .motivational-btn.btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #212529;
        }

        .motivational-btn.btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
        }

        .motivational-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* Note-taking Modal Styles */
        .note-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 3500;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .note-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .note-modal {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            transform: scale(0.7);
            transition: transform 0.3s ease;
        }

        .note-modal-overlay.show .note-modal {
            transform: scale(1);
        }

        .note-header {
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            color: white;
            padding: 20px 25px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .note-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
        }

        .note-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .note-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .note-content {
            padding: 25px;
            flex: 1;
        }

        #noteTextarea {
            width: 100%;
            min-height: 200px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        #noteTextarea:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }

        .note-actions {
            padding: 20px 25px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        /* Break Timer Styles */
        .break-timer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 4000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .break-timer-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .break-timer-modal {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 40px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 25px 70px rgba(0,0,0,0.4);
            transform: scale(0.7);
            transition: transform 0.3s ease;
            border: 3px solid #ffc107;
        }

        .break-timer-overlay.show .break-timer-modal {
            transform: scale(1);
        }

        .break-header {
            margin-bottom: 30px;
        }

        .break-icon {
            font-size: 60px;
            margin-bottom: 15px;
            animation: steam 2s ease-in-out infinite;
        }

        @keyframes steam {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .break-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }

        .timer-display {
            margin: 25px 0;
        }

        .timer-circle {
            width: 120px;
            height: 120px;
            border: 6px solid #ffc107;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }

        .timer-text {
            font-size: 24px;
            font-weight: 700;
            color: #856404;
        }

        .break-tips {
            display: flex;
            justify-content: space-around;
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
        }

        .tip {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .break-actions {
            margin-top: 25px;
        }

        /* Enhanced Accessibility Features */
        .content-item-modern:focus {
            outline: 3px solid #4682B4;
            outline-offset: 2px;
        }

        .btn:focus, .nav-btn:focus, .show-content-btn:focus {
            outline: 3px solid #4682B4;
            outline-offset: 2px;
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            .content-item-modern {
                border-width: 3px;
            }

            .content-type-badge {
                border: 2px solid currentColor;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            .modern-course-layout {
                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            }

            .content-item-modern {
                background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);
                color: #e0e0e0;
            }

            .course-header-modern {
                background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
            }

            .course-title-modern {
                color: #e0e0e0 !important;
            }
        }

        /* Mobile Responsive - Enhanced */
        @media (max-width: 768px) {
            .course-header-modern {
                padding: 20px;
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .course-info h1.course-title-modern {
                font-size: 24px;
            }

            .content-display-modern {
                padding: 20px;
            }

            .welcome-content-modern {
                padding: 40px 0;
            }

            .welcome-icon-modern {
                font-size: 80px;
            }

            .welcome-content-modern h2 {
                font-size: 28px;
            }

            .course-features-modern {
                padding: 20px;
                margin: 30px 0;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            /* Enhanced Mobile Summary Cards */
            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .summary-card {
                padding: 15px;
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .card-number {
                font-size: 24px;
            }

            /* Mobile Video Player */
            .video-learning-tools {
                flex-direction: column;
                gap: 10px;
            }

            .tool-btn {
                padding: 12px 15px;
            }

            .tool-btn i {
                font-size: 18px;
            }

            /* Mobile Achievement Notifications */
            .achievement-notification {
                right: 10px;
                left: 10px;
                transform: translateY(-100px);
                max-width: none;
            }

            .achievement-notification.show {
                transform: translateY(0);
            }

            /* Enhanced Mobile Navigation */
            .bottom-content-bar {
                padding: 15px 20px;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(15px);
            }

            .content-navigation {
                display: grid;
                grid-template-columns: 1fr 1.5fr 1fr;
                gap: 10px;
                align-items: center;
                max-width: 100%;
            }

            .nav-btn {
                min-width: auto;
                padding: 12px 8px;
                font-size: 12px;
                border-radius: 20px;
                flex-direction: column;
                gap: 4px;
                height: 60px;
                box-shadow: 0 2px 8px rgba(70, 130, 180, 0.2);
            }

            .nav-btn i {
                font-size: 16px;
            }

            .nav-btn span {
                font-size: 10px;
                line-height: 1.2;
                text-align: center;
            }

            .show-content-btn {
                padding: 15px 20px;
                font-size: 14px;
                border-radius: 25px;
                height: 60px;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            }

            .show-content-btn i {
                font-size: 18px;
            }

            .show-content-btn span {
                font-size: 14px;
                font-weight: 700;
            }

            /* Touch-friendly button sizing */
            .nav-btn:not(:disabled):active {
                transform: scale(0.95);
            }

            .show-content-btn:active {
                transform: scale(0.95);
            }

            .modal-header {
                padding: 20px;
            }

            .modal-content-area {
                padding: 20px;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .action-buttons-container {
                flex-direction: column;
                gap: 10px;
            }

            .btn-action {
                width: 100%;
                min-width: auto;
                padding: 15px 20px;
                font-size: 16px;
            }

            .completion-badge-modern {
                width: 25px;
                height: 25px;
                font-size: 12px;
                top: 10px;
                left: 10px;
            }

            .week-header-modern {
                padding: 15px 20px;
            }

            .week-content-modern {
                padding: 20px;
            }

            .week-section-modern.collapsed .week-content-modern {
                padding: 0 20px;
            }

            .content-item-modern {
                padding: 15px;
            }

            .floating-elements {
                width: 200px;
                height: 200px;
            }

            .float-element {
                font-size: 20px;
            }
        }

        /* Extra small mobile devices */
        @media (max-width: 480px) {
            .bottom-content-bar {
                padding: 10px 15px;
            }

            .content-navigation {
                gap: 8px;
            }

            .nav-btn {
                padding: 10px 6px;
                height: 55px;
            }

            .nav-btn span {
                font-size: 9px;
            }

            .show-content-btn {
                padding: 12px 16px;
                height: 55px;
            }

            .show-content-btn span {
                font-size: 13px;
            }

            /* Enhanced Mobile Features */
            .exam-timer-container {
                padding: 8px 12px;
                top: 5px;
                left: 5px;
                right: 5px;
                border-radius: 10px;
            }

            .timer-text {
                font-size: 12px;
            }

            .timer-time {
                font-size: 16px;
            }

            .course-progress-container {
                margin: 8px 12px;
                padding: 12px;
                border-radius: 10px;
            }

            .progress-title {
                font-size: 14px;
            }

            .progress-percentage {
                font-size: 18px;
            }

            .smart-notification {
                top: 70px;
                right: 5px;
                left: 5px;
                padding: 12px;
                border-radius: 10px;
            }

            .notification-title {
                font-size: 13px;
            }

            .notification-message {
                font-size: 12px;
            }

            .notification-btn {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* Content Display Styles */
        .content-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .content-header h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 28px;
        }

        .content-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .content-type {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .duration, .timing {
            background: #f8f9fa;
            color: #6c757d;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 14px;
        }

        .content-description {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #4682B4;
        }

        .custom-video-player {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        .custom-video-player iframe,
        .custom-video-player video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Enhanced Video Player Controls */
        .video-player-wrapper {
            position: relative;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .video-player-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(180deg, rgba(0,0,0,0.7) 0%, transparent 100%);
            padding: 15px 20px;
            z-index: 10;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .video-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 3px rgba(0,0,0,0.5);
        }

        .video-controls {
            display: flex;
            gap: 10px;
        }

        .video-control-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .video-control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .video-progress-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(0deg, rgba(0,0,0,0.7) 0%, transparent 100%);
            padding: 15px 20px;
            z-index: 10;
        }

        .video-progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .video-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ff5252);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .video-time-info {
            color: white;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Enhanced Video Content Styles */
        .enhanced-video-content {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .video-header-enhanced {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
        }

        .video-type-badge {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
        }

        .video-description {
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border-left: 4px solid #ff6b6b;
        }

        /* Video Learning Tools */
        .video-learning-tools {
            display: flex;
            gap: 15px;
            margin-top: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px solid rgba(255, 107, 107, 0.2);
        }

        .learning-tool {
            flex: 1;
        }

        .tool-btn {
            width: 100%;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #ff6b6b;
            color: #ff5252;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .tool-btn:hover {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .tool-btn i {
            font-size: 20px;
        }

        .tool-btn span {
            font-size: 12px;
        }

        .question-container {
            margin: 30px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px solid #dee2e6;
        }

        .question-container h3 {
            color: #4682B4;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .question-text {
            color: #2c3e50;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border-right: 4px solid #4682B4;
        }

        .options-container {
            margin: 25px 0;
        }

        .option-label {
            display: block;
            padding: 15px 20px;
            margin-bottom: 10px;
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .option-label:hover {
            border-color: #87CEEB;
            background: rgba(135, 206, 235, 0.05);
        }

        .option-label input[type="radio"] {
            margin-left: 15px;
            transform: scale(1.2);
        }

        .option-label span {
            color: #2c3e50;
            font-size: 16px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
        }

        .result-container {
            margin-top: 30px;
            padding: 25px;
            border-radius: 15px;
            animation: slideIn 0.3s ease;
        }

        .result-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            color: #155724;
        }

        .result-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            color: #721c24;
        }

        /* Enhanced Result Styles */
        .enhanced-success {
            background: linear-gradient(135deg, #d1f2eb 0%, #a3e4d7 100%);
            border: 3px solid #28a745;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .enhanced-error {
            background: linear-gradient(135deg, #fdeaea 0%, #fadbd8 100%);
            border: 3px solid #e74c3c;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .success-icon-large, .error-icon-large {
            font-size: 60px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .success-message, .error-message {
            font-size: 18px;
            font-weight: 600;
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.7);
        }

        .answer-details {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .correct-answer-highlight {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-weight: 600;
        }

        .wrong-answer-highlight {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-weight: 600;
        }

        .explanation-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-right: 4px solid #1976d2;
        }

        .attempt-info {
            color: #6c757d;
            font-style: italic;
            margin-top: 10px;
        }

        .btn-action.btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        /* Enhanced Action Buttons */
        .enhanced-action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px solid rgba(70, 130, 180, 0.1);
        }

        .btn-action.btn-large {
            padding: 18px 25px;
            font-size: 16px;
            font-weight: 700;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            min-height: 80px;
            justify-content: center;
            transition: all 0.4s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-action.btn-large:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-action.btn-large i {
            font-size: 24px;
        }

        .btn-action.btn-large span {
            font-size: 14px;
            text-align: center;
            line-height: 1.2;
        }

        /* Success and Encouragement Headers */
        .success-subtitle, .encouragement-subtitle {
            font-size: 16px;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .celebration-header, .encouragement-header {
            text-align: center;
            padding: 30px;
            margin-bottom: 25px;
            border-radius: 15px;
        }

        .celebration-header {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
        }

        .encouragement-header {
            background: linear-gradient(135deg, #fdeaea 0%, #fadbd8 100%);
            border: 2px solid #e74c3c;
        }

        /* Celebration Animation Styles */
        .celebration-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 5000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .celebration-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .celebration-content {
            text-align: center;
            color: white;
            position: relative;
        }

        .celebration-fireworks {
            position: absolute;
            top: -100px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 200px;
        }

        .firework {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            animation: fireworkExplode 2s ease-out infinite;
        }

        .firework:nth-child(1) {
            left: 20%;
            animation-delay: 0s;
        }

        .firework:nth-child(2) {
            left: 50%;
            animation-delay: 0.5s;
        }

        .firework:nth-child(3) {
            left: 80%;
            animation-delay: 1s;
        }

        @keyframes fireworkExplode {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        .celebration-emoji {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out infinite;
        }

        .celebration-message h2 {
            font-size: 36px;
            font-weight: 700;
            margin: 0 0 15px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .celebration-message p {
            font-size: 18px;
            margin: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* Achievement Notification Styles */
        .achievement-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
            color: #333;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
            z-index: 4500;
            transform: translateX(400px);
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 350px;
            border: 2px solid #e6ac00;
        }

        .achievement-notification.show {
            transform: translateX(0);
        }

        .achievement-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .achievement-icon {
            font-size: 40px;
            animation: achievementPulse 2s ease-in-out infinite;
        }

        @keyframes achievementPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .achievement-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .achievement-message {
            font-size: 14px;
            opacity: 0.9;
        }

        .result-success h4,
        .result-error h4 {
            margin-bottom: 15px;
            font-size: 18px;
        }

        .result-success p,
        .result-error p {
            margin-bottom: 10px;
            line-height: 1.5;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* True/False Options Styles */
        .true-false-options {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 20px 0;
        }

        .true-option {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            color: #155724;
        }

        .false-option {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            color: #721c24;
        }

        .option-label {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 16px;
            min-width: 120px;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .option-label::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .option-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .option-label:hover::before {
            left: 100%;
        }

        .option-label input[type="radio"] {
            display: none;
        }

        .option-label input[type="radio"]:checked + .option-text {
            font-weight: 700;
        }

        .true-option input[type="radio"]:checked {
            background: #28a745;
        }

        .false-option input[type="radio"]:checked {
            background: #dc3545;
        }

        .option-label input[type="radio"]:checked + .option-text::before {
            content: '●';
            margin-left: 8px;
            font-size: 20px;
        }

        .option-text {
            display: flex;
            align-items: center;
        }

        /* Result Container Styles */
        .result-container {
            margin-top: 30px;
            padding: 20px;
            border-radius: 12px;
            animation: slideInUp 0.5s ease;
        }

        .result-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            color: #155724;
            padding: 20px;
            border-radius: 12px;
        }

        .result-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            color: #721c24;
            padding: 20px;
            border-radius: 12px;
        }

        .result-success h4,
        .result-error h4 {
            margin-bottom: 15px;
            font-size: 18px;
        }

        .result-success p,
        .result-error p {
            margin: 10px 0;
            line-height: 1.6;
        }

        /* Try Again Button */
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        /* Exercise List Styles */
        .exercises-list {
            margin: 30px 0;
        }

        .exercise-question {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .exercise-question::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .exercise-question:hover {
            border-color: #87CEEB;
            box-shadow: 0 5px 15px rgba(135, 206, 235, 0.2);
        }

        .exercise-question:hover::before {
            transform: scaleY(1);
        }

        .exercise-question.question-error {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .question-header h4 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .question-points {
            background: #4682B4;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .question-text {
            color: #495057;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .exercise-info {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .info-icon {
            font-size: 24px;
        }

        .info-text {
            color: #856404;
            font-weight: 600;
            font-size: 16px;
        }

        .exercise-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 40px 0;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-width: 200px;
            justify-content: center;
        }

        .btn-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-large:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-large:hover::before {
            left: 100%;
        }

        .btn-large:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Validation Styles */
        .question-validation {
            margin-top: 15px;
        }

        .validation-error,
        .validation-success {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .validation-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .validation-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .validation-message {
            background: white;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            position: relative;
        }

        .validation-message.validation-error {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .validation-message.validation-success {
            border-color: #28a745;
            background: #f0fff4;
        }

        .validation-close {
            position: absolute;
            top: 10px;
            left: 10px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6c757d;
        }

        /* Results Styles */
        .results-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .results-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .results-header h3 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .score-summary {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            color: #495057;
        }

        .score-icon {
            font-size: 24px;
        }

        .score-percentage {
            background: rgba(0,0,0,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 700;
        }

        .question-result {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .question-result.result-success {
            border-color: #28a745;
            background: #f8fff9;
        }

        .question-result.result-error {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-header h4 {
            margin: 0;
            font-size: 16px;
        }

        .result-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .result-success .result-status {
            background: #28a745;
            color: white;
        }

        .result-error .result-status {
            background: #dc3545;
            color: white;
        }

        .answer-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }

        .user-answer,
        .correct-answer {
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }

        .user-answer {
            background: #f8f9fa;
            border-right: 4px solid #6c757d;
        }

        .correct-answer {
            background: #f0fff4;
            border-right: 4px solid #28a745;
        }

        .explanation {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-size: 14px;
            line-height: 1.6;
        }

        .completion-message {
            text-align: center;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
        }

        .completion-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .completion-message h4 {
            color: #155724;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .completion-message p {
            color: #155724;
            font-size: 16px;
            margin: 0;
        }

        /* Action Buttons Container */
        .action-buttons-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .btn-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
            justify-content: center;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-action.btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .btn-action.btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .btn-action.btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }

        .retry-btn {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
            }
        }

        .attempt-info {
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 10px;
            text-align: center;
        }

        .retry-message {
            color: #856404;
            font-weight: 600;
            font-style: italic;
            margin-top: 10px;
        }

        /* Enhanced Animations and Effects */
        .completion-message-enhanced {
            position: relative;
            text-align: center;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            overflow: hidden;
        }

        .celebration-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ffc107;
            animation: confetti-fall 3s linear infinite;
        }

        .confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #007bff; }
        .confetti:nth-child(2) { left: 30%; animation-delay: 0.5s; background: #28a745; }
        .confetti:nth-child(3) { left: 50%; animation-delay: 1s; background: #dc3545; }
        .confetti:nth-child(4) { left: 70%; animation-delay: 1.5s; background: #ffc107; }
        .confetti:nth-child(5) { left: 90%; animation-delay: 2s; background: #6f42c1; }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(400px) rotate(720deg);
                opacity: 0;
            }
        }

        .completion-content {
            position: relative;
            z-index: 1;
        }

        .completion-icon-large {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-30px);
            }
            60% {
                transform: translateY(-15px);
            }
        }

        .completion-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 25px;
        }

        /* Enhanced Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 15px 20px;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-success {
            border-left: 4px solid #28a745;
        }

        .notification-error {
            border-left: 4px solid #dc3545;
        }

        .notification-info {
            border-left: 4px solid #007bff;
        }

        /* Enhanced Content Display */
        .content-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            margin-bottom: 0;
        }

        .content-header h2 {
            margin: 0 0 15px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .content-meta {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .content-meta span {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        /* Custom Confirmation Modal */
        .custom-confirm-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .custom-confirm-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .custom-confirm-modal {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            transform: scale(0.7);
            transition: transform 0.3s ease;
        }

        .custom-confirm-overlay.show .custom-confirm-modal {
            transform: scale(1);
        }

        .confirm-icon {
            font-size: 60px;
            margin-bottom: 20px;
            color: #ffc107;
        }

        .confirm-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .confirm-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .confirm-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .confirm-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .confirm-btn-yes {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .confirm-btn-yes:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .confirm-btn-no {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .confirm-btn-no:hover {
            background: linear-gradient(135deg, #5a6268 0%, #545b62 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        /* Mobile Responsive for Options */
        @media (max-width: 768px) {
            .true-false-options {
                flex-direction: column;
                gap: 15px;
            }

            .option-label {
                min-width: auto;
                width: 100%;
            }

            .exercise-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }

            .answer-comparison {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .score-summary {
                flex-direction: column;
                gap: 10px;
            }

            .exercise-question {
                padding: 20px;
            }

            .question-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .action-buttons-container {
                flex-direction: column;
                gap: 10px;
            }

            .btn-action {
                width: 100%;
                min-width: auto;
            }

            .completion-actions {
                flex-direction: column;
            }

            .content-meta {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .notification {
                right: 10px;
                left: 10px;
                transform: translateY(-100px);
            }

            .notification.show {
                transform: translateY(0);
            }
        }

        /* Tablet Responsive */
        @media (max-width: 1024px) and (min-width: 769px) {
            .content-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .enhanced-action-buttons {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Print Styles */
        @media print {
            .bottom-content-bar,
            .modal-overlay,
            .content-modal,
            .motivational-modal-overlay,
            .break-timer-overlay,
            .note-modal-overlay,
            .achievement-notification,
            .celebration-overlay {
                display: none !important;
            }

            .modern-course-layout {
                background: white !important;
            }

            .content-item-modern {
                border: 2px solid #000 !important;
                background: white !important;
                color: black !important;
                break-inside: avoid;
            }
        }

        /* Loading States */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(70, 130, 180, 0.3);
            border-radius: 50%;
            border-top-color: #4682B4;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #87CEEB, #4682B4);
        }

        /* Study Tip Notification Styles */
        .study-tip-notification {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 10px 30px rgba(23, 162, 184, 0.4);
            z-index: 4000;
            transform: translateX(-400px);
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 350px;
            overflow: hidden;
        }

        .study-tip-notification.show {
            transform: translateX(0);
        }

        .tip-content {
            padding: 20px;
        }

        .tip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .tip-title {
            font-size: 16px;
            font-weight: 700;
        }

        .tip-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .tip-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .tip-message {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.95;
        }

        /* Content Transition Animations */
        .slide-out-left {
            animation: slideOutLeft 0.3s ease-in-out forwards;
        }

        .slide-out-right {
            animation: slideOutRight 0.3s ease-in-out forwards;
        }

        .slide-in-left {
            animation: slideInLeft 0.3s ease-in-out forwards;
        }

        .slide-in-right {
            animation: slideInRight 0.3s ease-in-out forwards;
        }

        @keyframes slideOutLeft {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Enhanced Button Hover Effects */
        .btn-action {
            position: relative;
            overflow: hidden;
        }

        .btn-action::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-action:hover::before {
            left: 100%;
        }

        /* Floating Action Button for Quick Actions */
        .floating-action-menu {
            position: fixed;
            bottom: 100px;
            right: 30px;
            z-index: 3000;
        }

        .fab-main {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4682B4, #87CEEB);
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .fab-main:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(70, 130, 180, 0.6);
        }

        .fab-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            display: flex;
            flex-direction: column;
            gap: 15px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .fab-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .fab-item {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .fab-item:hover {
            transform: scale(1.1);
        }

        /* Completion Status Banner */
        .completion-status-banner {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }

        .completion-status-banner.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .completion-status-banner.warning {
            background: linear-gradient(135deg, #fdeaea 0%, #fadbd8 100%);
            border-color: #dc3545;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .status-content h3 {
            margin: 0 0 15px 0;
            font-size: 20px;
            font-weight: 700;
        }

        .status-content p {
            margin: 0 0 15px 0;
            font-size: 16px;
            line-height: 1.6;
        }

        .status-content small {
            color: #6c757d;
            font-style: italic;
        }

        .status-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .status-actions .btn {
            min-width: 140px;
            padding: 12px 20px;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .status-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Attempts Details */
        .attempts-details {
            max-height: 300px;
            overflow-y: auto;
            text-align: right;
            direction: rtl;
        }

        .attempt-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 1.6;
        }

        .attempt-item:last-child {
            margin-bottom: 0;
        }

        .attempt-item strong {
            color: #495057;
            display: block;
            margin-bottom: 5px;
        }
    </style>

    <script>
        let currentContentType = null;
        let currentContentId = null;
        let allContentItems = [];
        let currentContentIndex = -1;
        let currentExamId = null;
        let currentTestId = null;
        let idleTimer = null;
        let lastActivity = Date.now();
        let courseId = <?php echo $courseId; ?>;

        // Check content completion status from database
        function checkContentCompletionStatus(type, id) {
            fetch(`../api/get_completion_status.php?type=${type}&id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI based on completion status
                    updateContentCompletionStatus(type, id, data.completed, data.percentage, data.passed);

                    // Show completion message if content was previously completed
                    if (data.completed && data.attempt_count > 0) {
                        showPreviousCompletionMessage(type, data);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking completion status:', error);
            });
        }

        // Show message for previously completed content
        function showPreviousCompletionMessage(type, data) {
            let message = '';
            let title = '';

            switch (type) {
                case 'video':
                    title = '✅ فيديو مكتمل';
                    message = `لقد شاهدت هذا الفيديو من قبل بنسبة ${data.percentage}%`;
                    break;
                case 'exercise':
                    title = data.passed ? '✅ تمرين مكتمل بنجاح' : '❌ تمرين محلول سابقاً';
                    message = data.passed ?
                        `لقد حللت هذا التمرين بنجاح من قبل (${data.attempt_count} محاولة)` :
                        `لقد حاولت حل هذا التمرين ${data.attempt_count} مرة ولم تنجح بعد`;
                    break;
                case 'exam':
                    title = data.passed ? '✅ امتحان مكتمل بنجاح' : '❌ امتحان محلول سابقاً';
                    message = data.passed ?
                        `لقد نجحت في هذا الامتحان بنسبة ${data.percentage}% (${data.attempt_count} محاولة)` :
                        `لقد حاولت هذا الامتحان ${data.attempt_count} مرة. أفضل نتيجة: ${data.percentage}%`;
                    break;
                case 'weekly_test':
                    title = data.passed ? '✅ اختبار أسبوعي مكتمل' : '❌ اختبار محلول سابقاً';
                    message = data.passed ?
                        `لقد نجحت في هذا الاختبار بنسبة ${data.percentage}% (${data.attempt_count} محاولة)` :
                        `لقد حاولت هذا الاختبار ${data.attempt_count} مرة. أفضل نتيجة: ${data.percentage}%`;
                    break;
            }

            // Show completion status in content area
            const contentDisplay = document.getElementById('contentDisplay');
            const statusBanner = document.createElement('div');
            statusBanner.className = `completion-status-banner ${data.passed ? 'success' : 'warning'}`;
            statusBanner.innerHTML = `
                <div class="status-content">
                    <h3>${title}</h3>
                    <p>${message}</p>
                    ${data.last_attempt_date ? `<small>آخر محاولة: ${new Date(data.last_attempt_date).toLocaleDateString('ar-EG')}</small>` : ''}
                    <div class="status-actions">
                        ${type !== 'video' ? `
                            <button class="btn btn-warning" onclick="retryContent('${type}', ${id})">
                                <i class="fas fa-redo"></i> إعادة المحاولة
                            </button>
                        ` : ''}
                        <button class="btn btn-info" onclick="viewDetails('${type}', ${id})">
                            <i class="fas fa-info-circle"></i> عرض التفاصيل
                        </button>
                        <button class="btn btn-success" onclick="continueContent('${type}', ${id})">
                            <i class="fas fa-play"></i> ${type === 'video' ? 'مشاهدة مرة أخرى' : 'مراجعة المحتوى'}
                        </button>
                    </div>
                </div>
            `;

            contentDisplay.insertBefore(statusBanner, contentDisplay.firstChild);

            // إخفاء الرسالة تلقائياً بعد 5 ثوانٍ
            setTimeout(() => {
                if (statusBanner && statusBanner.parentNode) {
                    statusBanner.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
                    statusBanner.style.opacity = '0';
                    statusBanner.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        if (statusBanner.parentNode) {
                            statusBanner.parentNode.removeChild(statusBanner);
                        }
                    }, 500);
                }
            }, 5000);
        }

        // Update content completion status
        function updateContentCompletionStatus(type, id, completed, percentage = 100, passed = true) {
            const contentItem = document.querySelector(`[data-type="${type}"][data-id="${id}"]`);
            if (contentItem) {
                if (completed) {
                    contentItem.classList.add('completed');
                    contentItem.setAttribute('data-completed', 'true');
                    contentItem.setAttribute('data-score', percentage.toString());
                    contentItem.setAttribute('data-perfect', (percentage >= 100).toString());

                    if (percentage >= 100) {
                        contentItem.classList.add('perfect-score');
                    }
                } else {
                    contentItem.classList.remove('completed', 'perfect-score');
                    contentItem.setAttribute('data-completed', 'false');
                    contentItem.setAttribute('data-score', '0');
                    contentItem.setAttribute('data-perfect', 'false');
                }
                updateContentSummary();
            }
        }

        // Retry content function
        function retryContent(type, id) {
            // Remove status banner
            const statusBanner = document.querySelector('.completion-status-banner');
            if (statusBanner) {
                statusBanner.remove();
            }

            // Continue with normal content loading
            continueContent(type, id);
        }

        // Continue with content function
        function continueContent(type, id) {
            // Remove status banner
            const statusBanner = document.querySelector('.completion-status-banner');
            if (statusBanner) {
                statusBanner.remove();
            }

            // Load content normally
            loadContentNormally(type, id);
        }

        // View details function
        function viewDetails(type, id) {
            fetch(`../api/get_completion_status.php?type=${type}&id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.details.length > 0) {
                    let detailsHtml = '<div class="attempts-details">';

                    data.details.forEach((attempt, index) => {
                        detailsHtml += `
                            <div class="attempt-item">
                                <strong>المحاولة ${attempt.attempt_number || (index + 1)}:</strong>
                                ${attempt.percentage ? `النتيجة: ${attempt.percentage}%` : ''}
                                ${attempt.is_correct !== undefined ? (attempt.is_correct ? 'صحيح ✅' : 'خطأ ❌') : ''}
                                ${attempt.passed !== undefined ? (attempt.passed ? 'نجح ✅' : 'لم ينجح ❌') : ''}
                                <br><small>${new Date(attempt.created_at || attempt.completed_at).toLocaleString('ar-EG')}</small>
                            </div>
                        `;
                    });

                    detailsHtml += '</div>';

                    showMotivationalModal(
                        'تفاصيل المحاولات',
                        detailsHtml,
                        [
                            { text: 'إغلاق', action: () => {}, class: 'btn-secondary' }
                        ]
                    );
                }
            })
            .catch(error => {
                console.error('Error fetching details:', error);
            });
        }

        // Initialize content items array
        document.addEventListener('DOMContentLoaded', function() {
            initializeContentItems();
            updateNavigationButtons();
            initializeIdleTimer();
            updateContentSummary();
        });

        function initializeContentItems() {
            allContentItems = [];
            const contentItems = document.querySelectorAll('.content-item-modern');
            contentItems.forEach((item, index) => {
                allContentItems.push({
                    type: item.dataset.type,
                    id: item.dataset.id,
                    element: item,
                    index: index
                });
            });
        }

        function toggleContentModal() {
            const modal = document.getElementById('contentModal');
            const overlay = document.getElementById('modalOverlay');

            modal.classList.toggle('show');
            overlay.classList.toggle('show');

            if (modal.classList.contains('show')) {
                document.body.style.overflow = 'hidden';
                // Update summary when modal opens
                updateContentSummary();
            } else {
                document.body.style.overflow = 'auto';
            }
        }

        function toggleWeekModern(weekNumber) {
            const weekSection = document.querySelector(`#week-modern-${weekNumber}`).parentElement;
            if (weekSection) {
                weekSection.classList.toggle('collapsed');
            }
        }

        function loadContentModern(type, id) {
            // Remove active class from all content items
            document.querySelectorAll('.content-item-modern').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to clicked item
            if (event && event.currentTarget) {
                event.currentTarget.classList.add('active');
            }

            currentContentType = type;
            currentContentId = id;

            // Find current content index
            currentContentIndex = allContentItems.findIndex(item =>
                item.type === type && item.id == id
            );

            // Load content based on type
            const contentDisplay = document.getElementById('contentDisplay');

            // Reset idle timer when loading new content
            resetIdleTimer();

            // Load content normally first, then check completion status
            loadContentNormally(type, id);

            // Check completion status after loading
            setTimeout(() => {
                checkContentCompletionStatus(type, id);
            }, 100);
        }

        // Load content normally (separated for reuse)
        function loadContentNormally(type, id) {
            switch(type) {
                case 'video':
                    loadVideo(id);
                    break;
                case 'exercise':
                    // Check if this is a week-based exercise
                    if (typeof id === 'string' && id.startsWith('week_')) {
                        const weekNumber = id.replace('week_', '');
                        displayMultipleExercises(weekNumber);
                    } else {
                        loadExercise(id);
                    }
                    break;
                case 'exam':
                    loadExam(id);
                    break;
                case 'weekly_test':
                    loadWeeklyTest(id);
                    break;
            }

            // Close modal after selection
            toggleContentModal();
            updateNavigationButtons();
        }

        function navigateContent(direction) {
            if (allContentItems.length === 0) return;

            let newIndex = currentContentIndex;

            if (direction === 'next' && currentContentIndex < allContentItems.length - 1) {
                newIndex = currentContentIndex + 1;
            } else if (direction === 'prev' && currentContentIndex > 0) {
                newIndex = currentContentIndex - 1;
            }

            if (newIndex !== currentContentIndex) {
                const nextContent = allContentItems[newIndex];
                loadContentModern(nextContent.type, nextContent.id);

                // Simulate click on the content item to update active state
                nextContent.element.click();
            }
        }

        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevContentBtn');
            const nextBtn = document.getElementById('nextContentBtn');

            if (prevBtn && nextBtn) {
                prevBtn.disabled = currentContentIndex <= 0;
                nextBtn.disabled = currentContentIndex >= allContentItems.length - 1;
            }
        }

        // Initialize idle timer and activity tracking
        function initializeIdleTimer() {
            // Track user activity
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
                document.addEventListener(event, resetIdleTimer);
            });

            // Start idle timer
            resetIdleTimer();
        }

        function resetIdleTimer() {
            lastActivity = Date.now();

            if (idleTimer) {
                clearTimeout(idleTimer);
            }

            // Set timer for 5 minutes (300000 ms)
            idleTimer = setTimeout(showIdleMotivation, 300000);
        }

        function showIdleMotivation() {
            const motivationalMessages = [
                {
                    title: 'لا تستسلم! استمر في التعلم',
                    message: 'لقد لاحظنا أنك لم تتفاعل مع المحتوى لفترة. تذكر أن التعلم المستمر هو مفتاح النجاح!'
                },
                {
                    title: 'استمر! أنت على الطريق الصحيح',
                    message: 'كل دقيقة تقضيها في التعلم تقربك من هدفك. لا تتوقف الآن!'
                },
                {
                    title: 'نحن هنا لمساعدتك',
                    message: 'إذا واجهت أي صعوبة، لا تتردد في التواصل معنا. فريقنا جاهز لدعمك!'
                }
            ];

            const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];

            showMotivationalModal(
                randomMessage.title,
                randomMessage.message,
                [
                    {
                        text: 'تواصل معنا وسنساعدك',
                        action: () => openContactModal(),
                        class: 'btn-primary'
                    },
                    {
                        text: 'متابعة التعلم',
                        action: () => resetIdleTimer(),
                        class: 'btn-success'
                    },
                    {
                        text: 'أخذ استراحة قصيرة',
                        action: () => startBreakTimer(),
                        class: 'btn-warning'
                    }
                ]
            );
        }

        function openContactModal() {
            showMotivationalModal(
                'تواصل معنا',
                'اختر طريقة التواصل المناسبة لك:',
                [
                    {
                        text: 'واتساب',
                        action: () => window.open('https://wa.me/1234567890?text=أحتاج مساعدة في الكورس', '_blank'),
                        class: 'btn-success'
                    },
                    {
                        text: 'البريد الإلكتروني',
                        action: () => window.open('mailto:<EMAIL>?subject=طلب مساعدة في الكورس', '_blank'),
                        class: 'btn-info'
                    },
                    {
                        text: 'إغلاق',
                        action: () => resetIdleTimer(),
                        class: 'btn-secondary'
                    }
                ]
            );
        }

        function startBreakTimer() {
            const breakDuration = 5; // 5 minutes
            let timeLeft = breakDuration * 60;

            const breakModal = document.createElement('div');
            breakModal.className = 'break-timer-overlay';
            breakModal.innerHTML = `
                <div class="break-timer-modal">
                    <div class="break-header">
                        <div class="break-icon">☕</div>
                        <h3>وقت الاستراحة</h3>
                    </div>
                    <div class="break-content">
                        <div class="timer-display">
                            <div class="timer-circle">
                                <div class="timer-text" id="breakTimerText">${breakDuration}:00</div>
                            </div>
                        </div>
                        <p>خذ استراحة قصيرة واشرب كوباً من الماء</p>
                        <div class="break-tips">
                            <div class="tip">💧 اشرب الماء</div>
                            <div class="tip">👀 أرح عينيك</div>
                            <div class="tip">🧘 تنفس بعمق</div>
                        </div>
                    </div>
                    <div class="break-actions">
                        <button class="btn btn-success" onclick="endBreak()">انتهيت من الاستراحة</button>
                    </div>
                </div>
            `;

            document.body.appendChild(breakModal);
            setTimeout(() => breakModal.classList.add('show'), 100);

            const timer = setInterval(() => {
                timeLeft--;
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const timerText = document.getElementById('breakTimerText');
                if (timerText) {
                    timerText.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }

                if (timeLeft <= 0) {
                    clearInterval(timer);
                    endBreak();
                }
            }, 1000);

            window.endBreak = function() {
                clearInterval(timer);
                breakModal.remove();
                showMotivationalModal(
                    'مرحباً بعودتك!',
                    'أتمنى أن تكون قد استمتعت بالاستراحة. الآن حان وقت العودة للتعلم بنشاط!',
                    [
                        { text: 'لنبدأ!', action: () => resetIdleTimer(), class: 'btn-success' }
                    ]
                );
            };
        }

        function updateContentSummary() {
            const totalItems = document.querySelectorAll('.content-item-modern').length;
            const completedItems = document.querySelectorAll('.content-item-modern.completed').length;
            const perfectScoreItems = document.querySelectorAll('.content-item-modern.perfect-score').length;
            const pendingItems = totalItems - completedItems;
            const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

            // Update button summary
            const summaryEl = document.getElementById('contentSummary');
            if (summaryEl) {
                summaryEl.querySelector('.completed-count').textContent = completedItems;
                summaryEl.querySelector('.total-count').textContent = totalItems;

                // Update summary color based on progress
                if (progress === 100) {
                    summaryEl.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                } else if (progress >= 50) {
                    summaryEl.style.background = 'linear-gradient(135deg, #ffc107, #fd7e14)';
                } else {
                    summaryEl.style.background = 'linear-gradient(135deg, #ff6b6b, #ff5252)';
                }
            }

            // Update modal summary cards
            const completedCountEl = document.getElementById('completedItemsCount');
            const perfectScoreCountEl = document.getElementById('perfectScoreCount');
            const pendingCountEl = document.getElementById('pendingItemsCount');
            const overallProgressEl = document.getElementById('overallProgress');

            if (completedCountEl) completedCountEl.textContent = completedItems;
            if (perfectScoreCountEl) perfectScoreCountEl.textContent = perfectScoreItems;
            if (pendingCountEl) pendingCountEl.textContent = pendingItems;
            if (overallProgressEl) overallProgressEl.textContent = progress + '%';
        }

        function loadVideo(videoId) {
            fetch(`../api/get_course_content.php?type=video&id=${videoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayVideo(data.content);
                    } else {
                        alert('خطأ في تحميل الفيديو');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال');
                });
        }

        function showPerfectScoreMessage(type, id) {
            // Check completion status and show appropriate message
            fetch(`../api/get_completion_status.php?type=${type}&id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.completed) {
                    const typeNames = {
                        'video': 'الفيديو',
                        'exercise': 'التمرين',
                        'exam': 'الامتحان',
                        'weekly_test': 'الاختبار الأسبوعي'
                    };

                    let message = '';
                    if (data.passed) {
                        message = `لقد أكملت ${typeNames[type]} بنجاح بنسبة ${data.percentage}%! هل تريد إعادة المراجعة أم الانتقال للمحتوى التالي؟`;
                    } else {
                        message = `لقد حاولت ${typeNames[type]} من قبل ولم تنجح. أفضل نتيجة: ${data.percentage}%. هل تريد المحاولة مرة أخرى؟`;
                    }

                    showMotivationalModal(
                        data.passed ? '🎉 محتوى مكتمل بنجاح!' : '⚠️ محتوى محلول سابقاً',
                        message,
                        [
                            {
                                text: data.passed ? 'إعادة المراجعة' : 'إعادة المحاولة',
                                action: () => loadContentModern(type, id),
                                class: data.passed ? 'btn-warning' : 'btn-primary'
                            },
                            {
                                text: 'عرض التفاصيل',
                                action: () => viewDetails(type, id),
                                class: 'btn-info'
                            },
                            {
                                text: data.passed ? 'الانتقال للتالي' : 'إغلاق',
                                action: () => data.passed ? navigateContent('next') : {},
                                class: data.passed ? 'btn-success' : 'btn-secondary'
                            }
                        ]
                    );
                } else {
                    // If not completed, load normally
                    loadContentModern(type, id);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Fallback to normal loading
                loadContentModern(type, id);
            });
        }

        function showMotivationalModal(title, message, actions = []) {
            // Remove existing modal if any
            const existingModal = document.querySelector('.motivational-modal-overlay');
            if (existingModal) {
                existingModal.remove();
            }

            const modalOverlay = document.createElement('div');
            modalOverlay.className = 'motivational-modal-overlay';

            const actionsHtml = actions.map(action =>
                `<button class="motivational-btn ${action.class}" onclick="this.closest('.motivational-modal-overlay').remove(); (${action.action})()">${action.text}</button>`
            ).join('');

            modalOverlay.innerHTML = `
                <div class="motivational-modal">
                    <div class="motivational-header">
                        <div class="motivational-icon">💪</div>
                        <h3>${title}</h3>
                    </div>
                    <div class="motivational-content">
                        <p>${message}</p>
                    </div>
                    <div class="motivational-actions">
                        ${actionsHtml}
                    </div>
                </div>
            `;

            document.body.appendChild(modalOverlay);

            // Show modal with animation
            setTimeout(() => modalOverlay.classList.add('show'), 100);
        }

        function displayVideo(video) {
            const contentDisplay = document.getElementById('contentDisplay');
            let videoEmbed = '';

            if (video.video_platform === 'youtube') {
                const videoId = extractYouTubeId(video.video_url);
                videoEmbed = `
                    <div class="video-player-wrapper">
                        <div class="video-player-header">
                            <div class="video-title">${video.title}</div>
                            <div class="video-controls">
                                <button class="video-control-btn" onclick="toggleFullscreen()" title="ملء الشاشة">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="video-control-btn" onclick="toggleVideoInfo()" title="معلومات الفيديو">
                                    <i class="fas fa-info"></i>
                                </button>
                            </div>
                        </div>
                        <div class="custom-video-player">
                            <iframe src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0&controls=1&disablekb=1&fs=0&iv_load_policy=3"
                                    frameborder="0" allowfullscreen></iframe>
                        </div>
                        <div class="video-progress-overlay">
                            <div class="video-progress-bar">
                                <div class="video-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="video-time-info">
                                <span>0:00</span>
                                <span>${video.duration_minutes ? video.duration_minutes + ':00' : 'غير محدد'}</span>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                videoEmbed = `
                    <div class="video-player-wrapper">
                        <div class="custom-video-player">
                            <video controls>
                                <source src="${video.video_url}" type="video/mp4">
                                متصفحك لا يدعم تشغيل الفيديو
                            </video>
                        </div>
                    </div>
                `;
            }

            contentDisplay.innerHTML = `
                <div class="video-content enhanced-video-content">
                    <div class="content-header video-header-enhanced">
                        <h2>${video.title}</h2>
                        <div class="content-meta">
                            <span class="content-type video-type-badge">🎥 فيديو</span>
                            ${video.duration_minutes ? `<span class="duration">${video.duration_minutes} دقيقة</span>` : ''}
                            ${video.timing_info ? `<span class="timing">${video.timing_info}</span>` : ''}
                        </div>
                    </div>
                    ${videoEmbed}
                    ${video.description ? `<div class="content-description video-description"><p>${video.description}</p></div>` : ''}

                    <!-- Video Learning Tools -->
                    <div class="video-learning-tools">
                        <div class="learning-tool">
                            <button class="tool-btn" onclick="takeNotes()">
                                <i class="fas fa-sticky-note"></i>
                                <span>تدوين ملاحظات</span>
                            </button>
                        </div>
                        <div class="learning-tool">
                            <button class="tool-btn" onclick="markAsWatched()">
                                <i class="fas fa-check-circle"></i>
                                <span>تم المشاهدة</span>
                            </button>
                        </div>
                        <div class="learning-tool">
                            <button class="tool-btn" onclick="shareVideo()">
                                <i class="fas fa-share"></i>
                                <span>مشاركة</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadExercise(exerciseId) {
            // Check if this is a week-based exercise (multiple questions)
            const weekMatch = exerciseId.toString().match(/week_(\d+)/);
            if (weekMatch) {
                const weekNumber = weekMatch[1];
                displayMultipleExercises(weekNumber);
                return;
            }

            // Load single exercise (legacy support)
            fetch(`../api/get_course_content.php?type=exercise&id=${exerciseId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayExercise(data.content);
                    } else {
                        alert('خطأ في تحميل التمرين');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال');
                });
        }

        function displayExercise(exercise) {
            const contentDisplay = document.getElementById('contentDisplay');
            let optionsHtml = '';
            
            if (exercise.question_type === 'multiple_choice' && exercise.options) {
                const options = JSON.parse(exercise.options);
                optionsHtml = options.map((option, index) =>
                    `<label class="option-label">
                        <input type="radio" name="answer" value="${option}">
                        <span class="option-text">${option}</span>
                    </label>`
                ).join('');
            } else if (exercise.question_type === 'true_false') {
                optionsHtml = `
                    <div class="true-false-options">
                        <label class="option-label true-option">
                            <input type="radio" name="answer" value="true">
                            <span class="option-text">✓ صح</span>
                        </label>
                        <label class="option-label false-option">
                            <input type="radio" name="answer" value="false">
                            <span class="option-text">✗ خطأ</span>
                        </label>
                    </div>
                `;
            } else {
                // Fallback for old format
                optionsHtml = `
                    <div class="true-false-options">
                        <label class="option-label true-option">
                            <input type="radio" name="answer" value="صح">
                            <span class="option-text">✓ صح</span>
                        </label>
                        <label class="option-label false-option">
                            <input type="radio" name="answer" value="خطأ">
                            <span class="option-text">✗ خطأ</span>
                        </label>
                    </div>
                `;
            }

            contentDisplay.innerHTML = `
                <div class="exercise-content">
                    <div class="content-header">
                        <h2>${exercise.title}</h2>
                        <div class="content-meta">
                            <span class="content-type">📝 تمرين</span>
                            ${exercise.timing_info ? `<span class="timing">${exercise.timing_info}</span>` : ''}
                        </div>
                    </div>
                    ${exercise.description ? `<div class="content-description"><p>${exercise.description}</p></div>` : ''}
                    <div class="question-container">
                        <h3>السؤال:</h3>
                        <p class="question-text">${exercise.question_text}</p>
                        <div class="options-container">
                            ${optionsHtml}
                        </div>
                        <button class="btn btn-primary" onclick="submitExercise(${exercise.id})">إرسال الإجابة</button>
                    </div>
                    <div id="exercise-result" class="result-container" style="display: none;"></div>
                </div>
            `;
        }

        function displayMultipleExercises(weekNumber) {
            // Fetch exercises for this week
            fetch(`../api/get_week_exercises.php?course_id=${courseId}&week_number=${weekNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderMultipleExercises(data.exercises, weekNumber, data.is_completed, data.results);
                    } else {
                        const contentDisplay = document.getElementById('contentDisplay');
                        contentDisplay.innerHTML = `
                            <div class="no-content">
                                <div class="content-header">
                                    <h2>تمارين الأسبوع ${weekNumber}</h2>
                                </div>
                                <div class="empty-state">
                                    <div class="empty-icon">📝</div>
                                    <p>${data.message}</p>
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    const contentDisplay = document.getElementById('contentDisplay');
                    contentDisplay.innerHTML = `
                        <div class="error-message">
                            <div class="content-header">
                                <h2>خطأ في تحميل التمارين</h2>
                            </div>
                            <div class="error-content">
                                <div class="error-icon">⚠️</div>
                                <p>حدث خطأ أثناء تحميل تمارين هذا الأسبوع</p>
                                <button class="btn btn-secondary" onclick="displayMultipleExercises(${weekNumber})">إعادة المحاولة</button>
                            </div>
                        </div>
                    `;
                });
        }

        function renderMultipleExercises(exercises, weekNumber, isCompleted, previousResults = null) {
            let exercisesHtml = '';

            exercises.forEach((exercise, index) => {
                let optionsHtml = '';

                if (exercise.question_type === 'multiple_choice' && exercise.options) {
                    optionsHtml = exercise.options.map((option, optionIndex) =>
                        `<label class="option-label">
                            <input type="radio" name="answer_${index}" value="${option}" ${isCompleted ? 'disabled' : ''}>
                            <span class="option-text">${option}</span>
                        </label>`
                    ).join('');
                } else if (exercise.question_type === 'true_false') {
                    optionsHtml = `
                        <div class="true-false-options">
                            <label class="option-label true-option">
                                <input type="radio" name="answer_${index}" value="true" ${isCompleted ? 'disabled' : ''}>
                                <span class="option-text">✓ صح</span>
                            </label>
                            <label class="option-label false-option">
                                <input type="radio" name="answer_${index}" value="false" ${isCompleted ? 'disabled' : ''}>
                                <span class="option-text">✗ خطأ</span>
                            </label>
                        </div>
                    `;
                }

                exercisesHtml += `
                    <div class="exercise-question" data-question-index="${index}">
                        <div class="question-header">
                            <h4>السؤال ${index + 1}</h4>
                            <span class="question-points">${exercise.points} نقطة</span>
                        </div>
                        <p class="question-text">${exercise.question_text}</p>
                        <div class="exercise-options">
                            ${optionsHtml}
                        </div>
                        <div class="question-validation" id="validation_${index}"></div>
                    </div>
                `;
            });

            let completionHtml = '';
            if (isCompleted && previousResults) {
                const scoreClass = previousResults.passed ? 'score-passed' : 'score-failed';
                const scoreIcon = previousResults.passed ? '🎉' : '📚';

                completionHtml = `
                    <div class="previous-results-summary">
                        <div class="results-header">
                            <h3>آخر نتائجك</h3>
                            <div class="score-display">
                                <div class="score-text">${previousResults.score}%</div>
                            </div>
                        </div>
                        <div class="quick-stats">
                            <div class="stat">
                                <span class="stat-value">${previousResults.correct_answers}</span>
                                <span class="stat-label">إجابات صحيحة</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">${previousResults.total_questions - previousResults.correct_answers}</span>
                                <span class="stat-label">إجابات خاطئة</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">${previousResults.attempt_number}</span>
                                <span class="stat-label">رقم المحاولة</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            const submitButtonHtml = isCompleted ?
                `<div class="completion-message-enhanced">
                    <div class="celebration-animation">
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                    </div>
                    <div class="completion-content">
                        <div class="completion-icon-large">🎉</div>
                        <h4>تم الحل. هل تريد إعادة الحل؟</h4>
                        <p>لقد أكملت تمارين هذا الأسبوع بنجاح!</p>
                        <div class="completion-actions">
                            <button class="btn btn-success btn-large" onclick="showResults(${weekNumber})">
                                <span class="btn-icon">📊</span>
                                عرض النتائج التفصيلية
                            </button>
                            <button class="btn btn-primary btn-large" onclick="retakeExercises(${weekNumber})">
                                <span class="btn-icon">🔄</span>
                                إعادة الحل
                            </button>
                        </div>
                    </div>
                </div>` :
                `<div class="exercise-actions">
                    <button class="btn btn-primary btn-large" onclick="submitAllExercises(${weekNumber})">
                        <span class="btn-icon">📝</span>
                        تسليم التمرين
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="validateAllAnswers()">
                        <span class="btn-icon">🔍</span>
                        التحقق من الإجابات
                    </button>
                </div>`;

            const contentDisplay = document.getElementById('contentDisplay');
            contentDisplay.innerHTML = `
                <div class="exercise-content">
                    <div class="content-header">
                        <h2>تمارين الأسبوع ${weekNumber}</h2>
                        <div class="content-meta">
                            <span class="content-type">📝 تمارين</span>
                            <span class="exercise-count">عدد الأسئلة: ${exercises.length}</span>
                        </div>
                    </div>
                    <div class="exercise-info">
                        <div class="info-card">
                            <span class="info-icon">⚠️</span>
                            <span class="info-text">يجب الإجابة على جميع الأسئلة قبل التسليم</span>
                        </div>
                    </div>
                    ${completionHtml}
                    <div class="exercises-list">
                        ${exercisesHtml}
                    </div>
                    ${submitButtonHtml}
                    <div id="exercise-results"></div>
                </div>
            `;
        }

        // loadExam function moved to bottom with timer support

        function displayMultipleExamQuestions(examId) {
            // Use the enhanced exam loading with timer
            loadExamWithTimer(examId);
        }



        function displayMultipleWeeklyTestQuestions(testId) {
            // Set current test ID for retry functionality
            currentTestId = testId;
            // Fetch weekly test questions
            fetch(`../api/get_weekly_test_questions.php?test_id=${testId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderMultipleWeeklyTestQuestions(data.test, data.questions, data.is_completed);
                    } else {
                        const contentDisplay = document.getElementById('contentDisplay');
                        contentDisplay.innerHTML = `
                            <div class="no-content">
                                <div class="content-header">
                                    <h2>الاختبار الأسبوعي</h2>
                                </div>
                                <div class="empty-state">
                                    <div class="empty-icon">📊</div>
                                    <p>${data.message}</p>
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    const contentDisplay = document.getElementById('contentDisplay');
                    contentDisplay.innerHTML = `
                        <div class="error-message">
                            <div class="content-header">
                                <h2>خطأ في تحميل الاختبار</h2>
                            </div>
                            <div class="error-content">
                                <div class="error-icon">⚠️</div>
                                <p>حدث خطأ أثناء تحميل الاختبار الأسبوعي</p>
                                <button class="btn btn-secondary" onclick="displayMultipleWeeklyTestQuestions(${testId})">إعادة المحاولة</button>
                            </div>
                        </div>
                    `;
                });
        }

        function submitExercise(exerciseId) {
            const selectedAnswer = document.querySelector('input[name="answer"]:checked');
            if (!selectedAnswer) {
                alert('يرجى اختيار إجابة');
                return;
            }

            const formData = new FormData();
            formData.append('exercise_id', exerciseId);
            formData.append('answer', selectedAnswer.value);

            fetch('../api/submit_exercise.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultContainer = document.getElementById('exercise-result');
                resultContainer.style.display = 'block';
                
                if (data.is_correct) {
                    resultContainer.innerHTML = `
                        <div class="result-success enhanced-success">
                            <div class="celebration-animation">
                                <div class="confetti"></div>
                                <div class="confetti"></div>
                                <div class="confetti"></div>
                                <div class="confetti"></div>
                                <div class="confetti"></div>
                            </div>
                            <div class="result-content">
                                <div class="success-icon-large">🎉</div>
                                <h4>تم إكمال التمرين بنجاح - لقد نجحت!</h4>
                                <p class="success-message">أحسنت! إجابتك صحيحة تماماً</p>
                                <div class="answer-details">
                                    <p><strong>إجابتك:</strong> <span class="correct-answer-highlight">${selectedAnswer.value}</span></p>
                                    ${data.explanation ? `<div class="explanation-box"><strong>الشرح:</strong> ${data.explanation}</div>` : ''}
                                    <p class="attempt-info"><small>المحاولة رقم: ${data.attempt_number}</small></p>
                                </div>
                                <div class="action-buttons-container">
                                    <button class="btn-action btn-primary" onclick="retryExercise(${exerciseId})">
                                        <i class="fas fa-redo"></i>
                                        إعادة حل التمرين
                                    </button>
                                    <button class="btn-action btn-success" onclick="proceedToNext()">
                                        <i class="fas fa-arrow-left"></i>
                                        الانتقال للتالي
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultContainer.innerHTML = `
                        <div class="result-error enhanced-error">
                            <div class="result-content">
                                <div class="error-icon-large">📚</div>
                                <h4>يرجى إعادة مشاهدة الفيديو والمحاولة مرة أخرى</h4>
                                <p class="error-message">لا تقلق، التعلم يحتاج للصبر والممارسة</p>
                                <div class="answer-details">
                                    <p><strong>إجابتك:</strong> <span class="wrong-answer-highlight">${selectedAnswer.value}</span></p>
                                    <p><strong>الإجابة الصحيحة:</strong> <span class="correct-answer-highlight">${data.correct_answer}</span></p>
                                    ${data.explanation ? `<div class="explanation-box"><strong>الشرح:</strong> ${data.explanation}</div>` : ''}
                                    <p class="attempt-info"><small>المحاولة رقم: ${data.attempt_number}</small></p>
                                </div>
                                <div class="action-buttons-container">
                                    <button class="btn-action btn-warning" onclick="retryExercise(${exerciseId})">
                                        <i class="fas fa-redo"></i>
                                        حاول مرة أخرى
                                    </button>
                                    <button class="btn-action btn-info" onclick="goBackToVideo()">
                                        <i class="fas fa-video"></i>
                                        مراجعة الفيديو
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // Disable all options after submission
                document.querySelectorAll('input[name="answer"]').forEach(input => {
                    input.disabled = true;
                });

                // Hide submit button
                document.querySelector('button[onclick*="submitExercise"]').style.display = 'none';

                // Update completion status in sidebar if correct
                if (data.is_correct) {
                    updateContentCompletionStatus('exercise', exerciseId, true);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في إرسال الإجابة');
            });
        }

        function extractYouTubeId(url) {
            const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
            const match = url.match(regExp);
            return (match && match[2].length === 11) ? match[2] : null;
        }

        function validateAllAnswers() {
            const questions = document.querySelectorAll('.exercise-question');
            let hasEmptyAnswers = false;
            let emptyQuestions = [];

            questions.forEach((question, index) => {
                const questionIndex = question.getAttribute('data-question-index');
                const selectedAnswer = question.querySelector(`input[name="answer_${questionIndex}"]:checked`);
                const validationDiv = question.querySelector(`#validation_${questionIndex}`);

                if (!selectedAnswer) {
                    hasEmptyAnswers = true;
                    emptyQuestions.push(parseInt(questionIndex) + 1);
                    validationDiv.innerHTML = `
                        <div class="validation-error">
                            <span class="validation-icon">⚠️</span>
                            <span class="validation-text">يرجى اختيار إجابة لهذا السؤال</span>
                        </div>
                    `;
                    question.classList.add('question-error');
                } else {
                    validationDiv.innerHTML = `
                        <div class="validation-success">
                            <span class="validation-icon">✅</span>
                            <span class="validation-text">تم الإجابة على هذا السؤال</span>
                        </div>
                    `;
                    question.classList.remove('question-error');
                }
            });

            if (hasEmptyAnswers) {
                showValidationMessage(`يرجى الإجابة على الأسئلة التالية: ${emptyQuestions.join(', ')}`, 'error');
                // Scroll to first empty question
                const firstEmptyQuestion = document.querySelector('.question-error');
                if (firstEmptyQuestion) {
                    firstEmptyQuestion.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            } else {
                showValidationMessage('تم الإجابة على جميع الأسئلة! يمكنك الآن تسليم التمرين', 'success');
            }
        }

        function submitAllExercises(weekNumber) {
            const questions = document.querySelectorAll('.exercise-question');
            const answers = {};
            let hasEmptyAnswers = false;

            questions.forEach((question, index) => {
                const questionIndex = question.getAttribute('data-question-index');
                const selectedAnswer = question.querySelector(`input[name="answer_${questionIndex}"]:checked`);

                if (!selectedAnswer) {
                    hasEmptyAnswers = true;
                } else {
                    answers[questionIndex] = selectedAnswer.value;
                }
            });

            if (hasEmptyAnswers) {
                validateAllAnswers();
                return;
            }

            // Show loading state
            const submitButton = document.querySelector('button[onclick*="submitAllExercises"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="btn-icon">⏳</span> جاري التسليم...';
            submitButton.disabled = true;

            // Submit answers
            const formData = new FormData();
            formData.append('course_id', courseId);
            formData.append('week_number', weekNumber);

            Object.keys(answers).forEach(questionIndex => {
                formData.append(`answers[${questionIndex}]`, answers[questionIndex]);
            });

            fetch('../api/submit_multi_exercise.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayExerciseResults(data);
                } else {
                    showValidationMessage(data.message || 'حدث خطأ أثناء تسليم التمرين', 'error');
                    // Restore button
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showValidationMessage('خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error');
                // Restore button
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        }

        function showValidationMessage(message, type) {
            const existingMessage = document.querySelector('.validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `validation-message validation-${type}`;
            messageDiv.innerHTML = `
                <span class="validation-icon">${type === 'success' ? '✅' : '⚠️'}</span>
                <span class="validation-text">${message}</span>
                <button class="validation-close" onclick="this.parentElement.remove()">×</button>
            `;

            const exerciseActions = document.querySelector('.exercise-actions');
            if (exerciseActions) {
                exerciseActions.parentNode.insertBefore(messageDiv, exerciseActions);
            }

            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 5000);
            }
        }

        function displayExerciseResults(data) {
            let resultsHtml = '';

            data.results.forEach((result, index) => {
                const resultClass = result.is_correct ? 'result-success' : 'result-error';
                const resultIcon = result.is_correct ? '✅' : '❌';

                resultsHtml += `
                    <div class="question-result ${resultClass}">
                        <div class="result-header">
                            <h4>${resultIcon} السؤال ${result.question_number}</h4>
                            <span class="result-status">${result.is_correct ? 'صحيح' : 'خطأ'}</span>
                        </div>
                        <p class="question-text">${result.question_text}</p>
                        <div class="answer-comparison">
                            <div class="user-answer">
                                <strong>إجابتك:</strong> ${result.user_answer}
                            </div>
                            <div class="correct-answer">
                                <strong>الإجابة الصحيحة:</strong> ${result.correct_answer}
                            </div>
                        </div>
                        ${result.explanation ? `<div class="explanation"><strong>الشرح:</strong> ${result.explanation}</div>` : ''}
                    </div>
                `;
            });

            const scoreClass = data.passed ? 'score-passed' : 'score-failed';
            const scoreIcon = data.passed ? '🎉' : '📚';

            const resultsContainer = document.getElementById('exercise-results');
            resultsContainer.innerHTML = `
                <div class="enhanced-results-container">
                    ${data.passed ? `
                        <div class="celebration-header">
                            <div class="celebration-animation-results">
                                <div class="confetti-results"></div>
                                <div class="confetti-results"></div>
                                <div class="confetti-results"></div>
                                <div class="confetti-results"></div>
                                <div class="confetti-results"></div>
                            </div>
                            <div class="success-icon-large">🎉</div>
                            <h2 class="success-title">تم إكمال التمرين بنجاح - لقد نجحت!</h2>
                            <p class="success-subtitle">أحسنت! لقد أجبت على الأسئلة بشكل ممتاز</p>
                        </div>
                    ` : `
                        <div class="encouragement-header">
                            <div class="encouragement-icon">📚</div>
                            <h2 class="encouragement-title">يرجى إعادة مشاهدة الفيديو والمحاولة مرة أخرى</h2>
                            <p class="encouragement-subtitle">لا تقلق، التعلم يحتاج للصبر والممارسة</p>
                        </div>
                    `}

                    <div class="comprehensive-score-display">
                        <div class="score-card ${scoreClass}">
                            <div class="score-main">
                                <div class="score-circle">
                                    <div class="score-number">${data.score}%</div>
                                </div>
                                <div class="score-details">
                                    <div class="score-fraction">
                                        <span class="correct-count">${data.correct_answers}</span>
                                        <span class="separator">من</span>
                                        <span class="total-count">${data.total_questions}</span>
                                    </div>
                                    <div class="score-display">
                                        <div class="score-text">${data.score}%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="score-stats">
                                <div class="stat-item">
                                    <span class="stat-label">الإجابات الصحيحة</span>
                                    <span class="stat-value">${data.correct_answers}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">الإجابات الخاطئة</span>
                                    <span class="stat-value">${data.total_questions - data.correct_answers}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">المحاولة رقم</span>
                                    <span class="stat-value">${data.attempt_number}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons-container enhanced-action-buttons">
                        ${data.passed ? `
                            <button class="btn-action btn-success btn-large" onclick="proceedToNext()">
                                <i class="fas fa-arrow-left"></i>
                                <span>الانتقال للمحتوى التالي</span>
                            </button>
                            <button class="btn-action btn-primary btn-large" onclick="retakeExercises(${data.week_number})">
                                <i class="fas fa-redo"></i>
                                <span>إعادة حل التمرين</span>
                            </button>
                        ` : `
                            <button class="btn-action btn-warning btn-large" onclick="retakeExercises(${data.week_number})">
                                <i class="fas fa-redo"></i>
                                <span>إعادة المحاولة</span>
                            </button>
                            <button class="btn-action btn-info btn-large" onclick="goBackToVideo()">
                                <i class="fas fa-video"></i>
                                <span>مراجعة الفيديو</span>
                            </button>
                        `}
                        <button class="btn-action btn-secondary btn-large" onclick="showResults(${data.week_number})">
                            <i class="fas fa-chart-bar"></i>
                            <span>مراجعة الإجابات</span>
                        </button>
                    </div>

                    <div class="detailed-results-section" id="detailedResults" style="display: none;">
                        <h3>النتائج التفصيلية</h3>
                        <div class="results-list">
                            ${resultsHtml}
                        </div>
                    </div>
                </div>
            `;

            // Trigger celebration animation if passed
            if (data.passed) {
                setTimeout(() => {
                    showCelebrationAnimation();
                }, 500);
            }

            // Hide exercise form
            const exercisesList = document.querySelector('.exercises-list');
            const exerciseActions = document.querySelector('.exercise-actions');
            if (exercisesList) exercisesList.style.display = 'none';
            if (exerciseActions) exerciseActions.style.display = 'none';

            // Scroll to results
            resultsContainer.scrollIntoView({ behavior: 'smooth' });

            // Update completion status in sidebar if passed
            if (data.passed && data.week_number) {
                updateContentCompletionStatus('exercise', 'week_' + data.week_number, true);
            }
        }

        function renderMultipleExamQuestions(exam, questions, isCompleted) {
            let questionsHtml = '';

            questions.forEach((question, index) => {
                let optionsHtml = '';

                if (question.question_type === 'multiple_choice' && question.options) {
                    optionsHtml = question.options.map((option, optionIndex) =>
                        `<label class="option-label">
                            <input type="radio" name="exam_answer_${index}" value="${option}" ${isCompleted ? 'disabled' : ''}>
                            <span class="option-text">${option}</span>
                        </label>`
                    ).join('');
                } else if (question.question_type === 'true_false') {
                    optionsHtml = `
                        <div class="true-false-options">
                            <label class="option-label true-option">
                                <input type="radio" name="exam_answer_${index}" value="true" ${isCompleted ? 'disabled' : ''}>
                                <span class="option-text">✓ صح</span>
                            </label>
                            <label class="option-label false-option">
                                <input type="radio" name="exam_answer_${index}" value="false" ${isCompleted ? 'disabled' : ''}>
                                <span class="option-text">✗ خطأ</span>
                            </label>
                        </div>
                    `;
                }

                questionsHtml += `
                    <div class="exam-question" data-question-index="${index}">
                        <div class="question-header">
                            <h4>السؤال ${index + 1}</h4>
                            <span class="question-points">${question.points} نقطة</span>
                        </div>
                        <p class="question-text">${question.question_text}</p>
                        <div class="exam-options">
                            ${optionsHtml}
                        </div>
                        <div class="question-validation" id="exam_validation_${index}"></div>
                    </div>
                `;
            });

            const submitButtonHtml = isCompleted ?
                `<div class="completion-message-enhanced">
                    <div class="celebration-animation">
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                    </div>
                    <div class="completion-content">
                        <div class="completion-icon-large">🎉</div>
                        <h4>تم إكمال الامتحان بنجاح!</h4>
                        <p>يمكنك مراجعة إجاباتك أو إعادة المحاولة لتحسين درجتك</p>
                        <div class="completion-actions">
                            <button class="btn btn-success btn-large" onclick="showExamResults()">
                                <span class="btn-icon">📊</span>
                                عرض النتائج
                            </button>
                            <button class="btn btn-primary btn-large" onclick="resetAssessment('exam', ${exam.id})">
                                <span class="btn-icon">🔄</span>
                                إعادة المحاولة
                            </button>
                        </div>
                    </div>
                </div>` :
                `<div class="exam-actions">
                    <button class="btn btn-primary btn-large" onclick="submitExam(${exam.id})">
                        <span class="btn-icon">📝</span>
                        تسليم الامتحان
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="validateExamAnswers()">
                        <span class="btn-icon">🔍</span>
                        التحقق من الإجابات
                    </button>
                </div>`;

            const contentDisplay = document.getElementById('contentDisplay');
            contentDisplay.innerHTML = `
                <div class="exam-content">
                    <div class="content-header">
                        <h2>${exam.title}</h2>
                        <div class="content-meta">
                            <span class="content-type">📋 امتحان</span>
                            <span class="exam-duration">⏱️ ${exam.duration_minutes} دقيقة</span>
                            <span class="exam-marks">📊 ${exam.total_marks} درجة</span>
                        </div>
                    </div>
                    <div class="exam-info">
                        <div class="info-card">
                            <span class="info-icon">⚠️</span>
                            <span class="info-text">يجب الإجابة على جميع الأسئلة قبل التسليم</span>
                        </div>
                        <div class="info-card">
                            <span class="info-icon">🎯</span>
                            <span class="info-text">درجة النجاح: ${exam.passing_marks} من ${exam.total_marks}</span>
                        </div>
                    </div>
                    <div class="questions-list">
                        ${questionsHtml}
                    </div>
                    ${submitButtonHtml}
                    <div id="exam-results"></div>
                </div>
            `;
        }

        function renderMultipleWeeklyTestQuestions(test, questions, isCompleted) {
            let questionsHtml = '';

            questions.forEach((question, index) => {
                let optionsHtml = '';

                if (question.question_type === 'multiple_choice' && question.options) {
                    optionsHtml = question.options.map((option, optionIndex) =>
                        `<label class="option-label">
                            <input type="radio" name="test_answer_${index}" value="${option}" ${isCompleted ? 'disabled' : ''}>
                            <span class="option-text">${option}</span>
                        </label>`
                    ).join('');
                } else if (question.question_type === 'true_false') {
                    optionsHtml = `
                        <div class="true-false-options">
                            <label class="option-label true-option">
                                <input type="radio" name="test_answer_${index}" value="true" ${isCompleted ? 'disabled' : ''}>
                                <span class="option-text">✓ صح</span>
                            </label>
                            <label class="option-label false-option">
                                <input type="radio" name="test_answer_${index}" value="false" ${isCompleted ? 'disabled' : ''}>
                                <span class="option-text">✗ خطأ</span>
                            </label>
                        </div>
                    `;
                }

                questionsHtml += `
                    <div class="test-question" data-question-index="${index}">
                        <div class="question-header">
                            <h4>السؤال ${index + 1}</h4>
                            <span class="question-points">${question.points} نقطة</span>
                        </div>
                        <p class="question-text">${question.question_text}</p>
                        <div class="test-options">
                            ${optionsHtml}
                        </div>
                        <div class="question-validation" id="test_validation_${index}"></div>
                    </div>
                `;
            });

            const submitButtonHtml = isCompleted ?
                `<div class="completion-message-enhanced">
                    <div class="celebration-animation">
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                    </div>
                    <div class="completion-content">
                        <div class="completion-icon-large">🎉</div>
                        <h4>تم إكمال الاختبار الأسبوعي بنجاح!</h4>
                        <p>يمكنك مراجعة إجاباتك أو إعادة المحاولة لتحسين درجتك</p>
                        <div class="completion-actions">
                            <button class="btn btn-success btn-large" onclick="showTestResults()">
                                <span class="btn-icon">📊</span>
                                عرض النتائج
                            </button>
                            <button class="btn btn-primary btn-large" onclick="resetAssessment('weekly_test', ${test.id})">
                                <span class="btn-icon">🔄</span>
                                إعادة المحاولة
                            </button>
                        </div>
                    </div>
                </div>` :
                `<div class="test-actions">
                    <button class="btn btn-primary btn-large" onclick="submitWeeklyTest(${test.id})">
                        <span class="btn-icon">📝</span>
                        تسليم الاختبار
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="validateTestAnswers()">
                        <span class="btn-icon">🔍</span>
                        التحقق من الإجابات
                    </button>
                </div>`;

            const contentDisplay = document.getElementById('contentDisplay');
            contentDisplay.innerHTML = `
                <div class="test-content">
                    <div class="content-header">
                        <h2>${test.title}</h2>
                        <div class="content-meta">
                            <span class="content-type">🧪 اختبار أسبوعي</span>
                            <span class="test-week">📅 الأسبوع ${test.week_number}</span>
                            <span class="test-duration">⏱️ ${test.duration_minutes} دقيقة</span>
                            <span class="test-marks">📊 ${test.total_marks} درجة</span>
                        </div>
                    </div>
                    <div class="test-info">
                        <div class="info-card">
                            <span class="info-icon">⚠️</span>
                            <span class="info-text">يجب الإجابة على جميع الأسئلة قبل التسليم</span>
                        </div>
                        <div class="info-card">
                            <span class="info-icon">🎯</span>
                            <span class="info-text">درجة النجاح: ${test.passing_marks} من ${test.total_marks}</span>
                        </div>
                    </div>
                    <div class="questions-list">
                        ${questionsHtml}
                    </div>
                    ${submitButtonHtml}
                    <div id="test-results"></div>
                </div>
            `;
        }

        function validateExamAnswers() {
            const questions = document.querySelectorAll('.exam-question');
            let hasEmptyAnswers = false;
            let emptyQuestions = [];

            questions.forEach((question, index) => {
                const questionIndex = question.getAttribute('data-question-index');
                const selectedAnswer = question.querySelector(`input[name="exam_answer_${questionIndex}"]:checked`);
                const validationDiv = question.querySelector(`#exam_validation_${questionIndex}`);

                if (!selectedAnswer) {
                    hasEmptyAnswers = true;
                    emptyQuestions.push(parseInt(questionIndex) + 1);
                    validationDiv.innerHTML = `
                        <div class="validation-error">
                            <span class="validation-icon">⚠️</span>
                            <span class="validation-text">يرجى اختيار إجابة لهذا السؤال</span>
                        </div>
                    `;
                    question.classList.add('question-error');
                } else {
                    validationDiv.innerHTML = `
                        <div class="validation-success">
                            <span class="validation-icon">✅</span>
                            <span class="validation-text">تم الإجابة على هذا السؤال</span>
                        </div>
                    `;
                    question.classList.remove('question-error');
                }
            });

            if (hasEmptyAnswers) {
                showValidationMessage(`يرجى الإجابة على الأسئلة التالية: ${emptyQuestions.join(', ')}`, 'error');
                const firstEmptyQuestion = document.querySelector('.question-error');
                if (firstEmptyQuestion) {
                    firstEmptyQuestion.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            } else {
                showValidationMessage('تم الإجابة على جميع الأسئلة! يمكنك الآن تسليم الامتحان', 'success');
            }
        }

        function submitExam(examId) {
            const questions = document.querySelectorAll('.exam-question');
            const answers = {};
            let hasEmptyAnswers = false;

            questions.forEach((question, index) => {
                const questionIndex = question.getAttribute('data-question-index');
                const selectedAnswer = question.querySelector(`input[name="exam_answer_${questionIndex}"]:checked`);

                if (!selectedAnswer) {
                    hasEmptyAnswers = true;
                } else {
                    answers[questionIndex] = selectedAnswer.value;
                }
            });

            if (hasEmptyAnswers) {
                validateExamAnswers();
                return;
            }

            const submitButton = document.querySelector('button[onclick*="submitExam"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="btn-icon">⏳</span> جاري التسليم...';
            submitButton.disabled = true;

            const formData = new FormData();
            formData.append('exam_id', examId);

            Object.keys(answers).forEach(questionIndex => {
                formData.append(`answers[${questionIndex}]`, answers[questionIndex]);
            });

            fetch('../api/submit_exam.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayExamResults(data);
                } else {
                    showValidationMessage(data.message || 'حدث خطأ أثناء تسليم الامتحان', 'error');
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showValidationMessage('خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        }

        function validateTestAnswers() {
            const questions = document.querySelectorAll('.test-question');
            let hasEmptyAnswers = false;
            let emptyQuestions = [];

            questions.forEach((question, index) => {
                const questionIndex = question.getAttribute('data-question-index');
                const selectedAnswer = question.querySelector(`input[name="test_answer_${questionIndex}"]:checked`);
                const validationDiv = question.querySelector(`#test_validation_${questionIndex}`);

                if (!selectedAnswer) {
                    hasEmptyAnswers = true;
                    emptyQuestions.push(parseInt(questionIndex) + 1);
                    validationDiv.innerHTML = `
                        <div class="validation-error">
                            <span class="validation-icon">⚠️</span>
                            <span class="validation-text">يرجى اختيار إجابة لهذا السؤال</span>
                        </div>
                    `;
                    question.classList.add('question-error');
                } else {
                    validationDiv.innerHTML = `
                        <div class="validation-success">
                            <span class="validation-icon">✅</span>
                            <span class="validation-text">تم الإجابة على هذا السؤال</span>
                        </div>
                    `;
                    question.classList.remove('question-error');
                }
            });

            if (hasEmptyAnswers) {
                showValidationMessage(`يرجى الإجابة على الأسئلة التالية: ${emptyQuestions.join(', ')}`, 'error');
                const firstEmptyQuestion = document.querySelector('.question-error');
                if (firstEmptyQuestion) {
                    firstEmptyQuestion.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            } else {
                showValidationMessage('تم الإجابة على جميع الأسئلة! يمكنك الآن تسليم الاختبار', 'success');
            }
        }

        function submitWeeklyTest(testId) {
            const questions = document.querySelectorAll('.test-question');
            const answers = {};
            let hasEmptyAnswers = false;

            questions.forEach((question, index) => {
                const questionIndex = question.getAttribute('data-question-index');
                const selectedAnswer = question.querySelector(`input[name="test_answer_${questionIndex}"]:checked`);

                if (!selectedAnswer) {
                    hasEmptyAnswers = true;
                } else {
                    answers[questionIndex] = selectedAnswer.value;
                }
            });

            if (hasEmptyAnswers) {
                validateTestAnswers();
                return;
            }

            const submitButton = document.querySelector('button[onclick*="submitWeeklyTest"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="btn-icon">⏳</span> جاري التسليم...';
            submitButton.disabled = true;

            const formData = new FormData();
            formData.append('test_id', testId);

            Object.keys(answers).forEach(questionIndex => {
                formData.append(`answers[${questionIndex}]`, answers[questionIndex]);
            });

            fetch('../api/submit_weekly_test.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayWeeklyTestResults(data);
                } else {
                    showValidationMessage(data.message || 'حدث خطأ أثناء تسليم الاختبار', 'error');
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showValidationMessage('خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        }

        function displayExamResults(data) {
            let resultsHtml = '';

            data.results.forEach((result, index) => {
                const resultClass = result.is_correct ? 'result-success' : 'result-error';
                const resultIcon = result.is_correct ? '✅' : '❌';

                resultsHtml += `
                    <div class="question-result ${resultClass}">
                        <div class="result-header">
                            <h4>${resultIcon} السؤال ${result.question_number}</h4>
                            <span class="result-score">${result.score}/${result.max_score}</span>
                        </div>
                        <p class="question-text">${result.question_text}</p>
                        <div class="answer-comparison">
                            <div class="user-answer">
                                <strong>إجابتك:</strong> ${result.user_answer}
                            </div>
                            <div class="correct-answer">
                                <strong>الإجابة الصحيحة:</strong> ${result.correct_answer}
                            </div>
                        </div>
                        ${result.explanation ? `<div class="explanation"><strong>الشرح:</strong> ${result.explanation}</div>` : ''}
                    </div>
                `;
            });

            const scoreClass = data.passed ? 'score-passed' : 'score-failed';
            const scoreIcon = data.passed ? '🎉' : '📚';

            const resultsContainer = document.getElementById('exam-results');
            resultsContainer.innerHTML = `
                <div class="results-container">
                    <div class="results-header">
                        <h3>نتائج الامتحان: ${data.exam_title}</h3>
                        <div class="score-summary">
                            <span class="score-text">${data.total_score} من ${data.max_score} درجة</span>
                            <span class="score-percentage">${data.percentage}%</span>
                        </div>
                    </div>
                    <div class="results-list">
                        ${resultsHtml}
                    </div>
                    <div class="results-footer">
                        <div class="attempt-info">
                            <p>المحاولة رقم: ${data.attempt_number}</p>
                            <p>درجة النجاح المطلوبة: ${data.passing_marks}</p>
                            ${!data.passed ? '<p class="retry-message">يمكنك إعادة المحاولة لتحسين درجتك</p>' : ''}
                        </div>
                        <div class="action-buttons-container">
                            <button class="btn-action btn-primary" onclick="showDetailedResults()">
                                <i class="fas fa-chart-bar"></i>
                                عرض النتائج التفصيلية
                            </button>
                            <button class="btn-action btn-warning retry-btn" onclick="resetAssessment('exam', ${data.exam_id || currentExamId})">
                                <i class="fas fa-redo"></i>
                                إعادة الامتحان
                            </button>
                            ${data.passed ? `
                                <button class="btn-action btn-success" onclick="proceedToNext()">
                                    <i class="fas fa-arrow-left"></i>
                                    الانتقال للتالي
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            document.querySelector('.questions-list').style.display = 'none';
            document.querySelector('.exam-actions').style.display = 'none';
            resultsContainer.scrollIntoView({ behavior: 'smooth' });

            // Update completion status in UI if passed
            if (data.passed) {
                updateContentCompletionStatus('exam', data.exam_id || currentExamId, true, data.percentage, true);

                // Show success celebration
                showCelebrationAnimation();

                // Show motivational message
                setTimeout(() => {
                    showMotivationalModal(
                        '🎉 مبروك! لقد نجحت في الامتحان',
                        `لقد حصلت على ${data.percentage}% في الامتحان. أداء ممتاز!`,
                        [
                            {
                                text: 'الانتقال للتالي',
                                action: () => proceedToNext(),
                                class: 'btn-success'
                            },
                            {
                                text: 'مراجعة النتائج',
                                action: () => {},
                                class: 'btn-info'
                            }
                        ]
                    );
                }, 2000);
            } else {
                // Show encouragement for failed attempt
                setTimeout(() => {
                    showMotivationalModal(
                        '📚 لا تستسلم!',
                        `لقد حصلت على ${data.percentage}%. راجع المواد وحاول مرة أخرى. يمكنك تحقيق نتيجة أفضل!`,
                        [
                            {
                                text: 'إعادة المحاولة',
                                action: () => resetAssessment('exam', data.exam_id || currentExamId),
                                class: 'btn-warning'
                            },
                            {
                                text: 'مراجعة المواد',
                                action: () => navigateContent('prev'),
                                class: 'btn-info'
                            }
                        ]
                    );
                }, 2000);
            }
        }

        function displayWeeklyTestResults(data) {
            let resultsHtml = '';

            data.results.forEach((result, index) => {
                const resultClass = result.is_correct ? 'result-success' : 'result-error';
                const resultIcon = result.is_correct ? '✅' : '❌';

                resultsHtml += `
                    <div class="question-result ${resultClass}">
                        <div class="result-header">
                            <h4>${resultIcon} السؤال ${result.question_number}</h4>
                            <span class="result-score">${result.score}/${result.max_score}</span>
                        </div>
                        <p class="question-text">${result.question_text}</p>
                        <div class="answer-comparison">
                            <div class="user-answer">
                                <strong>إجابتك:</strong> ${result.user_answer}
                            </div>
                            <div class="correct-answer">
                                <strong>الإجابة الصحيحة:</strong> ${result.correct_answer}
                            </div>
                        </div>
                        ${result.explanation ? `<div class="explanation"><strong>الشرح:</strong> ${result.explanation}</div>` : ''}
                    </div>
                `;
            });

            const scoreClass = data.passed ? 'score-passed' : 'score-failed';
            const scoreIcon = data.passed ? '🎉' : '📚';

            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = `
                <div class="results-container">
                    <div class="results-header">
                        <h3>نتائج ${data.test_title} - الأسبوع ${data.week_number}</h3>
                        <div class="score-summary">
                            <span class="score-text">${data.total_score} من ${data.max_score} درجة</span>
                            <span class="score-percentage">${data.percentage}%</span>
                        </div>
                    </div>
                    <div class="results-list">
                        ${resultsHtml}
                    </div>
                    <div class="results-footer">
                        <div class="attempt-info">
                            <p>المحاولة رقم: ${data.attempt_number}</p>
                            <p>درجة النجاح المطلوبة: ${data.passing_marks}</p>
                            ${!data.passed ? '<p class="retry-message">يمكنك إعادة المحاولة لتحسين درجتك</p>' : ''}
                        </div>
                        <div class="action-buttons-container">
                            <button class="btn-action btn-primary" onclick="showDetailedResults()">
                                <i class="fas fa-chart-bar"></i>
                                عرض النتائج التفصيلية
                            </button>
                            <button class="btn-action btn-warning retry-btn" onclick="resetAssessment('weekly_test', ${data.test_id || currentTestId})">
                                <i class="fas fa-redo"></i>
                                إعادة الاختبار
                            </button>
                            ${data.passed ? `
                                <button class="btn-action btn-success" onclick="proceedToNext()">
                                    <i class="fas fa-arrow-left"></i>
                                    الانتقال للتالي
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            document.querySelector('.questions-list').style.display = 'none';
            document.querySelector('.test-actions').style.display = 'none';
            resultsContainer.scrollIntoView({ behavior: 'smooth' });

            // Update completion status in UI if passed
            if (data.passed) {
                updateContentCompletionStatus('weekly_test', data.test_id || currentTestId, true, data.percentage, true);

                // Show success celebration
                showCelebrationAnimation();

                // Show motivational message
                setTimeout(() => {
                    showMotivationalModal(
                        '🎉 مبروك! لقد نجحت في الاختبار الأسبوعي',
                        `لقد حصلت على ${data.percentage}% في الاختبار. أداء رائع!`,
                        [
                            {
                                text: 'الانتقال للتالي',
                                action: () => proceedToNext(),
                                class: 'btn-success'
                            },
                            {
                                text: 'مراجعة النتائج',
                                action: () => {},
                                class: 'btn-info'
                            }
                        ]
                    );
                }, 2000);
            } else {
                // Show encouragement for failed attempt
                setTimeout(() => {
                    showMotivationalModal(
                        '📚 حاول مرة أخرى!',
                        `لقد حصلت على ${data.percentage}%. راجع المواد وحاول مرة أخرى. أنت قريب من النجاح!`,
                        [
                            {
                                text: 'إعادة المحاولة',
                                action: () => resetAssessment('weekly_test', data.test_id || currentTestId),
                                class: 'btn-warning'
                            },
                            {
                                text: 'مراجعة المواد',
                                action: () => navigateContent('prev'),
                                class: 'btn-info'
                            }
                        ]
                    );
                }, 2000);
            }
        }

        // Enhanced Assessment Experience Functions
        function retakeExercises(weekNumber) {
            showCustomConfirm(
                'إعادة حل التمارين',
                'هل أنت متأكد من أنك تريد إعادة حل التمارين؟ سيتم حذف إجاباتك السابقة ولن تتمكن من استرجاعها.',
                function() {
                // Reset exercises for retake
                fetch(`../api/reset_exercises.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        week_number: weekNumber,
                        course_id: courseId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload exercises for retake
                        displayMultipleExercises(weekNumber);
                        showNotification('تم إعادة تعيين التمارين بنجاح!', 'success');
                    } else {
                        showNotification('حدث خطأ أثناء إعادة تعيين التمارين', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال', 'error');
                });
                }
            );
        }

        function retakeExam(examId) {
            showCustomConfirm(
                'إعادة الامتحان',
                'هل أنت متأكد من أنك تريد إعادة الامتحان؟ سيتم حذف إجاباتك السابقة ولن تتمكن من استرجاعها.',
                function() {
                // Reset exam for retake
                fetch(`../api/reset_exam.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        exam_id: examId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload exam for retake
                        displayMultipleExamQuestions(examId);
                        showNotification('تم إعادة تعيين الامتحان بنجاح!', 'success');
                    } else {
                        showNotification('حدث خطأ أثناء إعادة تعيين الامتحان', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال', 'error');
                });
                }
            );
        }

        function showResults(weekNumber) {
            // Fetch and display detailed results
            fetch(`../api/get_exercise_results.php?week_number=${weekNumber}&course_id=${courseId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayDetailedResults(data);
                    } else {
                        showNotification('لا توجد نتائج متاحة', 'info');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في تحميل النتائج', 'error');
                });
        }

        function displayDetailedResults(data) {
            let resultsHtml = '';

            data.results.forEach((result, index) => {
                const resultClass = result.is_correct ? 'result-success' : 'result-error';
                const resultIcon = result.is_correct ? '✅' : '❌';

                resultsHtml += `
                    <div class="question-result ${resultClass}">
                        <div class="result-header">
                            <h4>${resultIcon} السؤال ${result.question_number}</h4>
                            <span class="result-status">${result.is_correct ? 'صحيح' : 'خطأ'}</span>
                        </div>
                        <p class="question-text">${result.question_text}</p>
                        <div class="answer-comparison">
                            <div class="user-answer">
                                <strong>إجابتك:</strong> ${result.user_answer}
                            </div>
                            <div class="correct-answer">
                                <strong>الإجابة الصحيحة:</strong> ${result.correct_answer}
                            </div>
                        </div>
                        ${result.explanation ? `<div class="explanation"><strong>الشرح:</strong> ${result.explanation}</div>` : ''}
                        ${result.attempt_date ? `<div class="attempt-date"><strong>تاريخ الإجابة:</strong> ${new Date(result.attempt_date).toLocaleString('ar-EG')}</div>` : ''}
                    </div>
                `;
            });

            const scoreClass = data.passed ? 'score-passed' : 'score-failed';
            const scoreIcon = data.passed ? '🎉' : '📚';

            const contentDisplay = document.getElementById('contentDisplay');
            contentDisplay.innerHTML = `
                <div class="detailed-results-container">
                    <div class="results-header">
                        <h2>النتائج التفصيلية - تمارين الأسبوع ${data.week_number}</h2>
                        <div class="score-summary ${scoreClass}">
                            <div class="score-main">
                                <div class="score-circle">
                                    <div class="score-number">${data.score}%</div>
                                </div>
                                <div class="score-details">
                                    <div class="score-fraction">
                                        <span class="correct-count">${data.correct_answers}</span>
                                        <span class="separator">من</span>
                                        <span class="total-count">${data.total_questions}</span>
                                    </div>
                                    <div class="score-display">
                                        <div class="score-text">${data.score}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="results-list">
                        ${resultsHtml}
                    </div>
                    <div class="results-footer">
                        <div class="attempt-info">
                            <p><strong>المحاولة رقم:</strong> ${data.attempt_number}</p>
                            <p><strong>درجة النجاح المطلوبة:</strong> ${data.passing_grade}%</p>
                        </div>
                        <div class="action-buttons">
                            <button class="btn-action btn-warning" onclick="retakeExercises(${data.week_number})">
                                <i class="fas fa-redo"></i>
                                إعادة التمرين
                            </button>
                            <button class="btn-action btn-secondary" onclick="loadWeekContent(${data.week_number})">
                                <i class="fas fa-arrow-left"></i>
                                العودة للأسبوع
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }



        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-icon">
                        ${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}
                    </span>
                    <span class="notification-message">${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Hide and remove notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }



        function showTestResults() {
            const resultsContainer = document.getElementById('test-results');
            if (resultsContainer && resultsContainer.innerHTML.trim()) {
                resultsContainer.scrollIntoView({ behavior: 'smooth' });
            } else {
                showNotification('لا توجد نتائج متاحة للعرض', 'info');
            }
        }

        function showExamResults() {
            const resultsContainer = document.getElementById('exam-results');
            if (resultsContainer && resultsContainer.innerHTML.trim()) {
                resultsContainer.scrollIntoView({ behavior: 'smooth' });
            } else {
                showNotification('لا توجد نتائج متاحة للعرض', 'info');
            }
        }

        function showDetailedResults() {
            const detailedResults = document.getElementById('detailedResults');
            if (detailedResults) {
                if (detailedResults.style.display === 'none') {
                    detailedResults.style.display = 'block';
                    detailedResults.scrollIntoView({ behavior: 'smooth' });
                } else {
                    detailedResults.style.display = 'none';
                }
            }
        }

        function proceedToNext() {
            // Navigate to next content item
            navigateContent('next');
        }

        function retryExercise(exerciseId) {
            showCustomConfirm(
                'إعادة حل التدريب',
                'هل تريد إعادة حل هذا التدريب؟ سيتم حذف إجابتك السابقة.',
                function() {
                    // Reset the exercise
                    const resultContainer = document.getElementById('exercise-result');
                    if (resultContainer) {
                        resultContainer.innerHTML = '';
                        resultContainer.style.display = 'none';
                    }

                    // Re-enable all options
                    document.querySelectorAll('input[name="answer"]').forEach(input => {
                        input.disabled = false;
                        input.checked = false;
                    });

                    // Show submit button again
                    const submitBtn = document.querySelector('button[onclick*="submitExercise"]');
                    if (submitBtn) {
                        submitBtn.style.display = 'inline-block';
                    }

                    showNotification('يمكنك الآن إعادة حل التدريب', 'success');
                }
            );
        }

        // Custom Confirmation System
        function showCustomConfirm(title, message, onConfirm, onCancel = null) {
            // Remove existing modal if any
            const existingModal = document.querySelector('.custom-confirm-overlay');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal HTML
            const modalHTML = `
                <div class="custom-confirm-overlay" id="customConfirmModal">
                    <div class="custom-confirm-modal">
                        <div class="confirm-icon">⚠️</div>
                        <h3 class="confirm-title">${title}</h3>
                        <p class="confirm-message">${message}</p>
                        <div class="confirm-buttons">
                            <button class="confirm-btn confirm-btn-yes" id="confirmYes">نعم</button>
                            <button class="confirm-btn confirm-btn-no" id="confirmNo">لا</button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            const modal = document.getElementById('customConfirmModal');
            const yesBtn = document.getElementById('confirmYes');
            const noBtn = document.getElementById('confirmNo');

            // Show modal
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // Handle Yes button
            yesBtn.addEventListener('click', () => {
                hideCustomConfirm();
                if (onConfirm) onConfirm();
            });

            // Handle No button
            noBtn.addEventListener('click', () => {
                hideCustomConfirm();
                if (onCancel) onCancel();
            });

            // Handle overlay click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    hideCustomConfirm();
                    if (onCancel) onCancel();
                }
            });

            // Handle Escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    hideCustomConfirm();
                    if (onCancel) onCancel();
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        }

        function hideCustomConfirm() {
            const modal = document.querySelector('.custom-confirm-overlay');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // Enhanced Assessment Functions
        function resetAssessment(type, id) {
            let confirmTitle = '';
            let confirmMessage = '';
            let endpoint = '';
            let payload = {};

            switch(type) {
                case 'exercise':
                    confirmTitle = 'إعادة حل التمرين';
                    confirmMessage = 'هل أنت متأكد من أنك تريد إعادة حل التمرين؟ سيتم حذف جميع إجاباتك السابقة ولن تتمكن من استرجاعها.';
                    endpoint = '../api/reset_exercises.php';
                    payload = { week_number: id, course_id: courseId };
                    break;
                case 'exam':
                    confirmTitle = 'إعادة الامتحان';
                    confirmMessage = 'هل أنت متأكد من أنك تريد إعادة الامتحان؟ سيتم حذف جميع إجاباتك السابقة ولن تتمكن من استرجاعها.';
                    endpoint = '../api/reset_exam.php';
                    payload = { exam_id: id };
                    break;
                case 'weekly_test':
                    confirmTitle = 'إعادة الاختبار الأسبوعي';
                    confirmMessage = 'هل أنت متأكد من أنك تريد إعادة الاختبار الأسبوعي؟ سيتم حذف جميع إجاباتك السابقة ولن تتمكن من استرجاعها.';
                    endpoint = '../api/reset_weekly_test.php';
                    payload = { test_id: id };
                    break;
                default:
                    showNotification('نوع التقييم غير مدعوم', 'error');
                    return;
            }

            showCustomConfirm(confirmTitle, confirmMessage, function() {
                fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const message = data.deleted_rows > 0 ?
                            `تم إعادة تعيين التقييم بنجاح! (تم حذف ${data.deleted_rows} إجابة)` :
                            'تم إعادة تعيين التقييم بنجاح!';
                        showNotification(message, 'success');

                        // Reload the assessment
                        setTimeout(() => {
                            if (type === 'exercise') {
                                displayMultipleExercises(id);
                            } else if (type === 'exam') {
                                displayMultipleExamQuestions(id);
                            } else if (type === 'weekly_test') {
                                loadWeeklyTest(id);
                            }
                        }, 1000);
                    } else {
                        showNotification(data.message || 'حدث خطأ أثناء إعادة تعيين التقييم', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.', 'error');
                });
            });
        }

        function showDetailedResults() {
            const detailedSection = document.getElementById('detailedResults');
            if (detailedSection) {
                detailedSection.style.display = detailedSection.style.display === 'none' ? 'block' : 'none';

                // Smooth scroll to detailed results
                if (detailedSection.style.display === 'block') {
                    detailedSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        }

        function proceedToNext() {
            // Navigate to next content
            navigateContent('next');
            showNotification('تم الانتقال للمحتوى التالي', 'success');
        }

        // Enhanced celebration animation with fireworks
        function showCelebrationAnimation() {
            const celebration = document.createElement('div');
            celebration.className = 'celebration-overlay-enhanced';
            celebration.innerHTML = `
                <div class="celebration-content-enhanced">
                    <div class="fireworks-container">
                        <div class="firework-burst" style="--delay: 0s; --x: 20%; --y: 30%;"></div>
                        <div class="firework-burst" style="--delay: 0.5s; --x: 80%; --y: 40%;"></div>
                        <div class="firework-burst" style="--delay: 1s; --x: 50%; --y: 20%;"></div>
                        <div class="firework-burst" style="--delay: 1.5s; --x: 30%; --y: 60%;"></div>
                        <div class="firework-burst" style="--delay: 2s; --x: 70%; --y: 70%;"></div>
                    </div>
                    <div class="celebration-message-enhanced">
                        <div class="celebration-emoji">🎉🎊✨</div>
                        <h1 class="celebration-title">أحسنت!</h1>
                        <p class="celebration-subtitle">لقد أكملت التقييم بنجاح</p>
                        <div class="celebration-stars">
                            <span class="star">⭐</span>
                            <span class="star">⭐</span>
                            <span class="star">⭐</span>
                            <span class="star">⭐</span>
                            <span class="star">⭐</span>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(celebration);

            // Trigger animation
            setTimeout(() => {
                celebration.classList.add('show');
            }, 100);

            // Remove after animation
            setTimeout(() => {
                celebration.classList.remove('show');
                setTimeout(() => {
                    celebration.remove();
                }, 500);
            }, 4000);
        }

        // Initialize collapsed weeks
        document.addEventListener('DOMContentLoaded', function() {
            // All weeks start expanded, but you can modify this behavior
        });
    </script>

    <style>
        /* Enhanced Assessment Experience Styles */
        .completion-message-enhanced {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 15px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .celebration-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            animation: confetti-fall 3s linear infinite;
        }

        .confetti:nth-child(1) { left: 10%; animation-delay: 0s; }
        .confetti:nth-child(2) { left: 30%; animation-delay: 0.5s; }
        .confetti:nth-child(3) { left: 50%; animation-delay: 1s; }
        .confetti:nth-child(4) { left: 70%; animation-delay: 1.5s; }
        .confetti:nth-child(5) { left: 90%; animation-delay: 2s; }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(400px) rotate(720deg);
                opacity: 0;
            }
        }

        .completion-content {
            position: relative;
            z-index: 1;
        }

        .completion-icon-large {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-30px);
            }
            60% {
                transform: translateY(-15px);
            }
        }

        .completion-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .celebration-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.5s ease;
        }

        .celebration-content {
            text-align: center;
            color: white;
            position: relative;
        }

        .celebration-message h2 {
            font-size: 48px;
            margin-bottom: 20px;
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            transition: all 0.3s ease;
            opacity: 0;
        }

        .notification.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
        }

        .notification-success {
            border-left: 4px solid #28a745;
        }

        .notification-error {
            border-left: 4px solid #dc3545;
        }

        .notification-info {
            border-left: 4px solid #17a2b8;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Enhanced Assessment Results Styles */
        .enhanced-results-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .celebration-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .celebration-animation-results {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .confetti-results {
            position: absolute;
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            animation: confetti-fall 4s linear infinite;
        }

        .confetti-results:nth-child(1) { left: 10%; animation-delay: 0s; }
        .confetti-results:nth-child(2) { left: 30%; animation-delay: 1s; }
        .confetti-results:nth-child(3) { left: 50%; animation-delay: 0.5s; }
        .confetti-results:nth-child(4) { left: 70%; animation-delay: 1.5s; }
        .confetti-results:nth-child(5) { left: 90%; animation-delay: 2s; }

        .success-icon-large {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .success-title {
            color: #28a745;
            font-size: 32px;
            font-weight: 700;
            margin: 0;
        }

        .encouragement-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .encouragement-icon {
            font-size: 60px;
            margin-bottom: 15px;
        }

        .encouragement-title {
            color: #dc3545;
            font-size: 28px;
            font-weight: 700;
            margin: 0;
        }

        .comprehensive-score-display {
            margin: 30px 0;
        }

        .score-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #dee2e6;
        }

        .score-card.score-passed {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }

        .score-card.score-failed {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #dc3545;
        }

        .score-main {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 25px rgba(70, 130, 180, 0.3);
        }

        .score-number {
            color: white;
            font-size: 28px;
            font-weight: 700;
        }

        .score-details {
            text-align: center;
        }

        .score-fraction {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .correct-count {
            color: #28a745;
        }

        .separator {
            margin: 0 10px;
            color: #6c757d;
        }

        .total-count {
            color: #2c3e50;
        }

        .pass-status {
            font-size: 18px;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
        }

        .score-display {
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            margin-top: 10px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
        }

        .score-display .score-text {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }

        /* Previous Results Summary */
        .previous-results-summary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .previous-results-summary .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .previous-results-summary h3 {
            margin: 0;
            color: #495057;
            font-size: 18px;
        }

        .score-display {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            border-radius: 10px;
        }

        .score-display.score-passed {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 1px solid #28a745;
        }

        .score-display.score-failed {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border: 1px solid #dc3545;
        }

        .score-display .score-icon {
            font-size: 20px;
        }

        .score-display .score-text {
            font-size: 18px;
            font-weight: bold;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .quick-stats .stat {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .quick-stats .stat-value {
            display: block;
            font-size: 20px;
            font-weight: bold;
            color: #495057;
        }

        .quick-stats .stat-label {
            display: block;
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .score-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
        }

        .stat-label {
            display: block;
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .stat-value {
            display: block;
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
        }

        .action-buttons-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
        }

        .btn-action.btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .btn-action.btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .btn-action.btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .detailed-results-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #dee2e6;
        }

        .detailed-results-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Enhanced Celebration Overlay */
        .celebration-overlay-enhanced {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .celebration-overlay-enhanced.show {
            opacity: 1;
        }

        .celebration-content-enhanced {
            text-align: center;
            color: white;
            position: relative;
        }

        .fireworks-container {
            position: absolute;
            top: -200px;
            left: -200px;
            right: -200px;
            bottom: -200px;
        }

        .firework-burst {
            position: absolute;
            left: var(--x);
            top: var(--y);
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #fff 0%, transparent 70%);
            border-radius: 50%;
            animation: firework-explosion 2s ease-out var(--delay) infinite;
        }

        @keyframes firework-explosion {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            50% {
                transform: scale(20);
                opacity: 0.8;
            }
            100% {
                transform: scale(40);
                opacity: 0;
            }
        }

        .celebration-emoji {
            font-size: 60px;
            margin-bottom: 20px;
            animation: bounce 1s infinite;
        }

        .celebration-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }

        .celebration-subtitle {
            font-size: 20px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .celebration-stars {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .star {
            font-size: 30px;
            animation: twinkle 1.5s ease-in-out infinite;
        }

        .star:nth-child(1) { animation-delay: 0s; }
        .star:nth-child(2) { animation-delay: 0.3s; }
        .star:nth-child(3) { animation-delay: 0.6s; }
        .star:nth-child(4) { animation-delay: 0.9s; }
        .star:nth-child(5) { animation-delay: 1.2s; }

        @keyframes twinkle {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .completion-actions {
                flex-direction: column;
                align-items: center;
            }

            .celebration-message h2 {
                font-size: 36px;
            }

            .score-main {
                flex-direction: column;
                gap: 20px;
            }

            .score-stats {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .action-buttons-container {
                flex-direction: column;
                align-items: center;
            }

            .celebration-title {
                font-size: 36px;
            }
        }
    </style>

    <!-- Advanced Timer System -->
    <script>
        // Advanced Timer System
        class AdvancedTimer {
            constructor(durationMinutes, onTimeUp, onWarning) {
                this.totalSeconds = durationMinutes * 60;
                this.remainingSeconds = this.totalSeconds;
                this.onTimeUp = onTimeUp;
                this.onWarning = onWarning;
                this.interval = null;
                this.warningShown = {
                    ten: false,
                    five: false,
                    one: false,
                    thirty: false
                };
                this.isRunning = false;
                this.createTimerUI();
            }

            createTimerUI() {
                // Remove existing timer if any
                const existingTimer = document.querySelector('.exam-timer-container');
                if (existingTimer) {
                    existingTimer.remove();
                }

                // Create timer container
                const timerContainer = document.createElement('div');
                timerContainer.className = 'exam-timer-container';
                timerContainer.innerHTML = `
                    <div class="timer-display">
                        <div class="timer-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="timer-text">الوقت المتبقي:</div>
                        <div class="timer-time" id="timerDisplay">${this.formatTime(this.remainingSeconds)}</div>
                        <div class="timer-progress">
                            <div class="timer-progress-bar" id="timerProgress" style="width: 100%"></div>
                        </div>
                    </div>
                `;

                document.body.appendChild(timerContainer);
                this.timerContainer = timerContainer;
                this.timerDisplay = document.getElementById('timerDisplay');
                this.timerProgress = document.getElementById('timerProgress');
            }

            start() {
                if (this.isRunning) return;

                this.isRunning = true;
                this.interval = setInterval(() => {
                    this.remainingSeconds--;
                    this.updateDisplay();
                    this.checkWarnings();

                    if (this.remainingSeconds <= 0) {
                        this.stop();
                        this.onTimeUp();
                    }
                }, 1000);
            }

            stop() {
                if (this.interval) {
                    clearInterval(this.interval);
                    this.interval = null;
                }
                this.isRunning = false;
            }

            updateDisplay() {
                this.timerDisplay.textContent = this.formatTime(this.remainingSeconds);

                const progressPercentage = (this.remainingSeconds / this.totalSeconds) * 100;
                this.timerProgress.style.width = progressPercentage + '%';

                // Update timer appearance based on remaining time
                const minutes = Math.floor(this.remainingSeconds / 60);

                this.timerContainer.classList.remove('warning', 'critical');

                if (minutes <= 1) {
                    this.timerContainer.classList.add('critical');
                } else if (minutes <= 5) {
                    this.timerContainer.classList.add('warning');
                }
            }

            checkWarnings() {
                const minutes = Math.floor(this.remainingSeconds / 60);
                const seconds = this.remainingSeconds % 60;

                // 10 minutes warning
                if (minutes === 10 && seconds === 0 && !this.warningShown.ten) {
                    this.showWarningModal('تحذير!', 'يتبقى 10 دقائق فقط لانتهاء الامتحان', 'warning');
                    this.warningShown.ten = true;
                }

                // 5 minutes warning
                if (minutes === 5 && seconds === 0 && !this.warningShown.five) {
                    this.showWarningModal('تحذير!', 'يتبقى 5 دقائق فقط لانتهاء الامتحان', 'warning');
                    this.warningShown.five = true;
                }

                // 1 minute warning
                if (minutes === 1 && seconds === 0 && !this.warningShown.one) {
                    this.showWarningModal('تحذير هام!', 'يتبقى دقيقة واحدة فقط لانتهاء الامتحان', 'critical');
                    this.warningShown.one = true;
                }

                // 30 seconds warning
                if (minutes === 0 && seconds === 30 && !this.warningShown.thirty) {
                    this.showWarningModal('تحذير أخير!', 'يتبقى 30 ثانية فقط لانتهاء الامتحان', 'critical');
                    this.warningShown.thirty = true;
                }
            }

            showWarningModal(title, message, type) {
                const modal = document.createElement('div');
                modal.className = 'timer-warning-modal';
                modal.innerHTML = `
                    <div class="timer-warning-content ${type}">
                        <div class="warning-icon ${type}">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 class="warning-title">${title}</h3>
                        <p class="warning-message">${message}</p>
                        <div class="warning-time ${type}">${this.formatTime(this.remainingSeconds)}</div>
                        <button class="btn-action btn-primary" onclick="this.parentElement.parentElement.remove()">
                            <i class="fas fa-check"></i>
                            فهمت
                        </button>
                    </div>
                `;

                document.body.appendChild(modal);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (modal.parentElement) {
                        modal.remove();
                    }
                }, 5000);

                // Play warning sound (optional)
                this.playWarningSound(type);
            }

            playWarningSound(type) {
                // Create audio context for warning sounds
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    if (type === 'critical') {
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                        oscillator.start();
                        oscillator.stop(audioContext.currentTime + 0.2);

                        setTimeout(() => {
                            const oscillator2 = audioContext.createOscillator();
                            const gainNode2 = audioContext.createGain();
                            oscillator2.connect(gainNode2);
                            gainNode2.connect(audioContext.destination);
                            oscillator2.frequency.setValueAtTime(600, audioContext.currentTime);
                            gainNode2.gain.setValueAtTime(0.1, audioContext.currentTime);
                            oscillator2.start();
                            oscillator2.stop(audioContext.currentTime + 0.2);
                        }, 300);
                    } else {
                        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                        oscillator.start();
                        oscillator.stop(audioContext.currentTime + 0.3);
                    }
                } catch (e) {
                    console.log('Audio not supported');
                }
            }

            formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
            }

            destroy() {
                this.stop();
                if (this.timerContainer) {
                    this.timerContainer.remove();
                }
            }
        }

        // Global timer instance
        let examTimer = null;

        // Enhanced exam loading with timer
        function loadExamWithTimer(examId) {
            // Set current exam ID for retry functionality
            currentExamId = examId;

            fetch(`../api/get_exam.php?id=${examId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Start timer if exam has time limit
                        if (data.exam.time_limit_minutes && data.exam.time_limit_minutes > 0) {
                            examTimer = new AdvancedTimer(
                                data.exam.time_limit_minutes,
                                () => {
                                    // Time up - auto submit
                                    showNotification('انتهى الوقت المحدد للامتحان. سيتم تسليم الامتحان تلقائياً.', 'warning');
                                    setTimeout(() => {
                                        submitExam(examId, true); // true for auto-submit
                                    }, 2000);
                                },
                                (timeLeft) => {
                                    console.log(`Warning: ${timeLeft} minutes left`);
                                }
                            );
                            examTimer.start();
                        }

                        // Use the existing exam rendering function
                        renderMultipleExamQuestions(data.exam, data.exam.questions, false);
                    } else {
                        showNotification(data.message || 'حدث خطأ في تحميل الامتحان', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في تحميل الامتحان', 'error');
                });
        }

        // Enhanced weekly test loading with timer
        function loadWeeklyTestWithTimer(testId) {
            fetch(`../api/get_weekly_test.php?id=${testId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Start timer if test has time limit
                        if (data.test.duration_minutes && data.test.duration_minutes > 0) {
                            examTimer = new AdvancedTimer(
                                data.test.duration_minutes,
                                () => {
                                    // Time up - auto submit
                                    showNotification('انتهى الوقت المحدد للاختبار. سيتم تسليم الاختبار تلقائياً.', 'warning');
                                    setTimeout(() => {
                                        submitWeeklyTest(testId, true); // true for auto-submit
                                    }, 2000);
                                },
                                (timeLeft) => {
                                    console.log(`Warning: ${timeLeft} minutes left`);
                                }
                            );
                            examTimer.start();
                        }

                        displayMultipleWeeklyTestQuestions(data.test.id);
                    } else {
                        showNotification(data.message || 'حدث خطأ في تحميل الاختبار', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في تحميل الاختبار', 'error');
                });
        }

        // Update existing loadExam function to use timer
        function loadExam(examId) {
            loadExamWithTimer(examId);
        }

        // Update existing loadWeeklyTest function to use timer
        function loadWeeklyTest(testId) {
            loadWeeklyTestWithTimer(testId);
        }

        // Clean up timer when leaving page
        window.addEventListener('beforeunload', () => {
            if (examTimer) {
                examTimer.destroy();
            }
        });

        // Enhanced Progress Tracking System
        class ProgressTracker {
            constructor() {
                this.courseId = <?php echo $course['id']; ?>;
                this.totalContent = 0;
                this.completedContent = 0;
                this.init();
            }

            init() {
                this.createProgressBar();
                this.updateProgress();
                this.setupProgressTracking();
            }

            createProgressBar() {
                const header = document.querySelector('.course-header-modern');
                if (!header) return;

                const progressContainer = document.createElement('div');
                progressContainer.className = 'course-progress-container';
                progressContainer.innerHTML = `
                    <div class="progress-header">
                        <div class="progress-title">
                            <i class="fas fa-chart-line"></i>
                            تقدمك في الكورس
                        </div>
                        <div class="progress-percentage" id="progressPercentage">0%</div>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar-fill" id="progressBarFill" style="width: 0%"></div>
                    </div>
                    <div class="progress-stats">
                        <div class="progress-stat">
                            <i class="fas fa-check-circle"></i>
                            <span id="completedCount">0</span> مكتمل
                        </div>
                        <div class="progress-stat">
                            <i class="fas fa-list"></i>
                            <span id="totalCount">0</span> إجمالي
                        </div>
                        <div class="progress-stat">
                            <i class="fas fa-clock"></i>
                            <span id="estimatedTime">0</span> دقيقة متبقية
                        </div>
                    </div>
                `;

                header.insertAdjacentElement('afterend', progressContainer);
            }

            updateProgress() {
                const contentItems = document.querySelectorAll('.content-item-modern');
                this.totalContent = contentItems.length;
                this.completedContent = document.querySelectorAll('.content-item-modern.completed').length;

                const percentage = this.totalContent > 0 ? Math.round((this.completedContent / this.totalContent) * 100) : 0;
                const estimatedTime = (this.totalContent - this.completedContent) * 15; // 15 minutes per content

                // Update UI
                const progressPercentage = document.getElementById('progressPercentage');
                const progressBarFill = document.getElementById('progressBarFill');
                const completedCount = document.getElementById('completedCount');
                const totalCount = document.getElementById('totalCount');
                const estimatedTimeEl = document.getElementById('estimatedTime');

                if (progressPercentage) progressPercentage.textContent = percentage + '%';
                if (progressBarFill) progressBarFill.style.width = percentage + '%';
                if (completedCount) completedCount.textContent = this.completedContent;
                if (totalCount) totalCount.textContent = this.totalContent;
                if (estimatedTimeEl) estimatedTimeEl.textContent = estimatedTime;

                // Show achievement notification
                if (percentage === 100 && this.completedContent > 0) {
                    this.showAchievementNotification('تهانينا!', 'لقد أكملت جميع محتويات الكورس بنجاح! 🎉');
                } else if (percentage >= 75 && !this.achievementShown75) {
                    this.showAchievementNotification('إنجاز رائع!', 'لقد أكملت 75% من الكورس! استمر! 💪');
                    this.achievementShown75 = true;
                } else if (percentage >= 50 && !this.achievementShown50) {
                    this.showAchievementNotification('في منتصف الطريق!', 'لقد أكملت نصف الكورس! 🚀');
                    this.achievementShown50 = true;
                }
            }

            setupProgressTracking() {
                // Track content completion
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            this.updateProgress();
                        }
                    });
                });

                document.querySelectorAll('.content-item-modern').forEach(item => {
                    observer.observe(item, { attributes: true });
                });
            }

            showAchievementNotification(title, message) {
                this.showSmartNotification(title, message, 'achievement', [
                    { text: 'رائع!', action: () => {}, primary: true }
                ]);
            }

            showSmartNotification(title, message, type = 'info', actions = []) {
                const notification = document.createElement('div');
                notification.className = 'smart-notification';

                const iconMap = {
                    achievement: 'fas fa-trophy',
                    info: 'fas fa-info-circle',
                    warning: 'fas fa-exclamation-triangle',
                    success: 'fas fa-check-circle'
                };

                notification.innerHTML = `
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="${iconMap[type] || iconMap.info}"></i>
                        </div>
                        <div class="notification-title">${title}</div>
                    </div>
                    <div class="notification-message">${message}</div>
                    ${actions.length > 0 ? `
                        <div class="notification-actions">
                            ${actions.map(action => `
                                <button class="notification-btn ${action.primary ? 'primary' : 'secondary'}"
                                        onclick="this.parentElement.parentElement.parentElement.remove(); (${action.action})()">
                                    ${action.text}
                                </button>
                            `).join('')}
                        </div>
                    ` : ''}
                `;

                document.body.appendChild(notification);

                // Show notification
                setTimeout(() => notification.classList.add('show'), 100);

                // Auto hide after 5 seconds if no actions
                if (actions.length === 0) {
                    setTimeout(() => {
                        notification.classList.remove('show');
                        setTimeout(() => notification.remove(), 400);
                    }, 5000);
                }
            }
        }

        // Smart Study Assistant
        class StudyAssistant {
            constructor() {
                this.studyTime = 0;
                this.breakTime = 0;
                this.lastActivity = Date.now();
                this.init();
            }

            init() {
                this.trackStudyTime();
                this.setupInactivityDetection();
                this.setupStudyReminders();
            }

            trackStudyTime() {
                setInterval(() => {
                    this.studyTime++;

                    // Suggest break every 45 minutes
                    if (this.studyTime % (45 * 60) === 0) {
                        this.suggestBreak();
                    }
                }, 1000);
            }

            setupInactivityDetection() {
                ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
                    document.addEventListener(event, () => {
                        this.lastActivity = Date.now();
                    });
                });

                setInterval(() => {
                    const inactiveTime = Date.now() - this.lastActivity;
                    if (inactiveTime > 10 * 60 * 1000) { // 10 minutes
                        this.showInactivityReminder();
                    }
                }, 60000);
            }

            setupStudyReminders() {
                // Show helpful tips periodically
                setTimeout(() => {
                    this.showStudyTip();
                }, 5 * 60 * 1000); // After 5 minutes
            }

            suggestBreak() {
                progressTracker.showSmartNotification(
                    'وقت الراحة!',
                    'لقد درست لمدة 45 دقيقة. خذ استراحة قصيرة لتحسين التركيز.',
                    'info',
                    [
                        { text: 'استراحة 5 دقائق', action: () => this.startBreakTimer(5), primary: true },
                        { text: 'متابعة الدراسة', action: () => {}, primary: false }
                    ]
                );
            }

            showInactivityReminder() {
                progressTracker.showSmartNotification(
                    'هل ما زلت هنا؟',
                    'لم نلاحظ أي نشاط منذ فترة. هل تريد متابعة الدراسة؟',
                    'warning',
                    [
                        { text: 'نعم، أتابع', action: () => {}, primary: true },
                        { text: 'حفظ التقدم', action: () => this.saveProgress(), primary: false }
                    ]
                );
            }

            showStudyTip() {
                const tips = [
                    'تذكر أن تدوين الملاحظات يساعد على التذكر بشكل أفضل.',
                    'حاول شرح ما تعلمته لشخص آخر لتثبيت المعلومات.',
                    'خذ فترات راحة منتظمة لتحسين التركيز.',
                    'راجع المحتوى السابق بانتظام لتقوية الذاكرة.',
                    'اربط المعلومات الجديدة بما تعرفه مسبقاً.'
                ];

                const randomTip = tips[Math.floor(Math.random() * tips.length)];

                progressTracker.showSmartNotification(
                    'نصيحة دراسية',
                    randomTip,
                    'info'
                );
            }

            startBreakTimer(minutes) {
                let timeLeft = minutes * 60;
                const breakNotification = document.createElement('div');
                breakNotification.className = 'smart-notification show';
                breakNotification.innerHTML = `
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-coffee"></i>
                        </div>
                        <div class="notification-title">استراحة</div>
                    </div>
                    <div class="notification-message">
                        الوقت المتبقي: <span id="breakTimer">${minutes}:00</span>
                    </div>
                `;

                document.body.appendChild(breakNotification);

                const timer = setInterval(() => {
                    timeLeft--;
                    const mins = Math.floor(timeLeft / 60);
                    const secs = timeLeft % 60;
                    const timerEl = document.getElementById('breakTimer');
                    if (timerEl) {
                        timerEl.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
                    }

                    if (timeLeft <= 0) {
                        clearInterval(timer);
                        breakNotification.remove();
                        progressTracker.showSmartNotification(
                            'انتهت الاستراحة!',
                            'حان وقت العودة للدراسة. أتمنى لك التوفيق!',
                            'success'
                        );
                    }
                }, 1000);
            }

            saveProgress() {
                // Save current progress to localStorage or server
                localStorage.setItem('courseProgress', JSON.stringify({
                    courseId: <?php echo $course['id']; ?>,
                    studyTime: this.studyTime,
                    lastAccess: Date.now()
                }));
            }
        }

        // Additional helper functions
        function retryExercise(exerciseId) {
            // Reset the exercise form
            const resultContainer = document.getElementById('exercise-result');
            if (resultContainer) {
                resultContainer.style.display = 'none';
            }

            // Re-enable all options
            document.querySelectorAll('input[name="answer"]').forEach(input => {
                input.disabled = false;
                input.checked = false;
            });

            // Show submit button again
            const submitBtn = document.querySelector('button[onclick*="submitExercise"]');
            if (submitBtn) {
                submitBtn.style.display = 'inline-block';
            }
        }

        function proceedToNext() {
            navigateContent('next');
        }

        function goBackToVideo() {
            // Find the previous video in the content list
            const currentIndex = allContentItems.findIndex(item =>
                item.type === currentContentType && item.id == currentContentId
            );

            // Look for the previous video
            for (let i = currentIndex - 1; i >= 0; i--) {
                if (allContentItems[i].type === 'video') {
                    loadContentModern(allContentItems[i].type, allContentItems[i].id);
                    allContentItems[i].element.click();
                    return;
                }
            }

            // If no previous video found, show message
            showMotivationalModal(
                'لا يوجد فيديو سابق',
                'لم نجد فيديو سابق لمراجعته. يمكنك البحث في قائمة المحتوى عن الفيديو المناسب.',
                [
                    { text: 'فتح قائمة المحتوى', action: () => toggleContentModal(), class: 'btn-primary' },
                    { text: 'إغلاق', action: () => {}, class: 'btn-secondary' }
                ]
            );
        }

        function toggleFullscreen() {
            const videoPlayer = document.querySelector('.custom-video-player iframe');
            if (videoPlayer) {
                if (videoPlayer.requestFullscreen) {
                    videoPlayer.requestFullscreen();
                } else if (videoPlayer.webkitRequestFullscreen) {
                    videoPlayer.webkitRequestFullscreen();
                } else if (videoPlayer.msRequestFullscreen) {
                    videoPlayer.msRequestFullscreen();
                }
            }
        }

        function toggleVideoInfo() {
            // Show video information modal
            const videoTitle = document.querySelector('.video-title').textContent;
            showMotivationalModal(
                'معلومات الفيديو',
                `العنوان: ${videoTitle}<br>يمكنك استخدام مفاتيح الاختصار للتحكم في الفيديو:<br>مسافة: إيقاف/تشغيل<br>← →: التقديم والإرجاع`,
                [
                    { text: 'فهمت', action: () => {}, class: 'btn-primary' }
                ]
            );
        }

        // Video Learning Tools Functions
        function takeNotes() {
            showMotivationalModal(
                'تدوين الملاحظات',
                'هذه ميزة رائعة للتعلم! يمكنك تدوين ملاحظاتك أثناء مشاهدة الفيديو لتحسين الفهم والتذكر.',
                [
                    { text: 'فتح مفكرة', action: () => openNotePad(), class: 'btn-primary' },
                    { text: 'إغلاق', action: () => {}, class: 'btn-secondary' }
                ]
            );
        }

        function markAsWatched() {
            // Mark video as completed
            if (currentContentType === 'video' && currentContentId) {
                // Update video progress in database
                fetch(`../api/update_video_progress.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `video_id=${currentContentId}&progress_percentage=100&completed=true`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateContentCompletionStatus('video', currentContentId, true);
                        showMotivationalModal(
                            'تم تسجيل المشاهدة!',
                            'رائع! لقد تم تسجيل مشاهدتك لهذا الفيديو. استمر في التعلم!',
                            [
                                { text: 'الانتقال للتالي', action: () => navigateContent('next'), class: 'btn-success' },
                                { text: 'إغلاق', action: () => {}, class: 'btn-secondary' }
                            ]
                        );
                    } else {
                        showMotivationalModal(
                            'خطأ',
                            'حدث خطأ أثناء حفظ التقدم. يرجى المحاولة مرة أخرى.',
                            [
                                { text: 'حسناً', action: () => {}, class: 'btn-secondary' }
                            ]
                        );
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMotivationalModal(
                        'خطأ في الاتصال',
                        'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.',
                        [
                            { text: 'حسناً', action: () => {}, class: 'btn-secondary' }
                        ]
                    );
                });
            }
        }

        function shareVideo() {
            const videoTitle = document.querySelector('h2').textContent;
            const shareText = `أشاهد الآن: ${videoTitle}`;

            if (navigator.share) {
                navigator.share({
                    title: videoTitle,
                    text: shareText,
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(`${shareText} - ${window.location.href}`).then(() => {
                    showMotivationalModal(
                        'تم النسخ!',
                        'تم نسخ رابط الفيديو إلى الحافظة. يمكنك مشاركته مع أصدقائك.',
                        [
                            { text: 'رائع!', action: () => {}, class: 'btn-success' }
                        ]
                    );
                });
            }
        }

        function openNotePad() {
            // Create a simple note-taking interface
            const noteModal = document.createElement('div');
            noteModal.className = 'note-modal-overlay';
            noteModal.innerHTML = `
                <div class="note-modal">
                    <div class="note-header">
                        <h3>مفكرة الملاحظات</h3>
                        <button class="note-close" onclick="this.closest('.note-modal-overlay').remove()">×</button>
                    </div>
                    <div class="note-content">
                        <textarea id="noteTextarea" placeholder="اكتب ملاحظاتك هنا..."></textarea>
                    </div>
                    <div class="note-actions">
                        <button class="btn btn-primary" onclick="saveNote()">حفظ الملاحظة</button>
                        <button class="btn btn-secondary" onclick="this.closest('.note-modal-overlay').remove()">إغلاق</button>
                    </div>
                </div>
            `;
            document.body.appendChild(noteModal);
            setTimeout(() => noteModal.classList.add('show'), 100);
        }

        function saveNote() {
            const noteText = document.getElementById('noteTextarea').value;
            if (noteText.trim()) {
                // Save to localStorage for now
                const notes = JSON.parse(localStorage.getItem('courseNotes') || '[]');
                notes.push({
                    content: noteText,
                    videoId: currentContentId,
                    timestamp: new Date().toISOString()
                });
                localStorage.setItem('courseNotes', JSON.stringify(notes));

                showMotivationalModal(
                    'تم حفظ الملاحظة!',
                    'تم حفظ ملاحظتك بنجاح. يمكنك مراجعتها لاحقاً.',
                    [
                        { text: 'رائع!', action: () => {}, class: 'btn-success' }
                    ]
                );

                document.querySelector('.note-modal-overlay').remove();
            }
        }



        // Initialize enhanced features
        let progressTracker, studyAssistant;

        document.addEventListener('DOMContentLoaded', function() {
            progressTracker = new ProgressTracker();
            studyAssistant = new StudyAssistant();

            // Add achievement system
            initializeAchievementSystem();

            // Check if course is completed and show modal
            checkCourseCompletionStatus();
        });

        function initializeAchievementSystem() {
            // Check for achievements on page load
            setTimeout(() => {
                checkForAchievements();
            }, 2000);
        }

        function checkForAchievements() {
            const completedItems = document.querySelectorAll('.content-item-modern.completed').length;
            const totalItems = document.querySelectorAll('.content-item-modern').length;
            const progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

            // Check for first completion
            if (completedItems === 1 && !localStorage.getItem('firstCompletionAchievement')) {
                showAchievementNotification('🎯 أول إنجاز!', 'تهانينا! لقد أكملت أول محتوى في الكورس');
                localStorage.setItem('firstCompletionAchievement', 'true');
            }

            // Check for streak achievements
            const today = new Date().toDateString();
            const lastVisit = localStorage.getItem('lastVisitDate');
            const streak = parseInt(localStorage.getItem('studyStreak') || '0');

            if (lastVisit !== today) {
                const newStreak = lastVisit === new Date(Date.now() - 86400000).toDateString() ? streak + 1 : 1;
                localStorage.setItem('studyStreak', newStreak.toString());
                localStorage.setItem('lastVisitDate', today);

                if (newStreak === 3) {
                    showAchievementNotification('🔥 ثلاثة أيام متتالية!', 'رائع! لقد درست لثلاثة أيام متتالية');
                } else if (newStreak === 7) {
                    showAchievementNotification('⭐ أسبوع كامل!', 'مذهل! لقد حافظت على الدراسة لأسبوع كامل');
                }
            }
        }

        function showAchievementNotification(title, message) {
            const achievement = document.createElement('div');
            achievement.className = 'achievement-notification';
            achievement.innerHTML = `
                <div class="achievement-content">
                    <div class="achievement-icon">🏆</div>
                    <div class="achievement-text">
                        <div class="achievement-title">${title}</div>
                        <div class="achievement-message">${message}</div>
                    </div>
                </div>
            `;

            document.body.appendChild(achievement);
            setTimeout(() => achievement.classList.add('show'), 100);

            setTimeout(() => {
                achievement.classList.remove('show');
                setTimeout(() => achievement.remove(), 500);
            }, 4000);
        }





        // Enhanced Content Navigation
        function enhancedNavigateContent(direction) {
            const currentIndex = allContentItems.findIndex(item =>
                item.type === currentContentType && item.id == currentContentId
            );

            let nextIndex;
            if (direction === 'next') {
                nextIndex = currentIndex + 1;
            } else {
                nextIndex = currentIndex - 1;
            }

            if (nextIndex >= 0 && nextIndex < allContentItems.length) {
                const nextItem = allContentItems[nextIndex];

                // Show transition animation
                showContentTransition(direction, () => {
                    loadContentModern(nextItem.type, nextItem.id);
                    updateNavigationButtons();
                });
            } else {
                // Show completion message if at the end
                if (direction === 'next' && nextIndex >= allContentItems.length) {
                    showCourseCompletionMessage();
                }
            }
        }

        function showContentTransition(direction, callback) {
            const contentDisplay = document.getElementById('contentDisplay');
            const transitionClass = direction === 'next' ? 'slide-out-left' : 'slide-out-right';

            contentDisplay.classList.add(transitionClass);

            setTimeout(() => {
                callback();
                contentDisplay.classList.remove(transitionClass);
                contentDisplay.classList.add(direction === 'next' ? 'slide-in-right' : 'slide-in-left');

                setTimeout(() => {
                    contentDisplay.classList.remove('slide-in-right', 'slide-in-left');
                }, 300);
            }, 300);
        }

        function showCourseCompletionMessage() {
            const stats = progressTracker.getSessionStats();

            // إرسال إشعار إكمال الكورس إلى قاعدة البيانات
            saveCourseCompletionNote();

            const modal = showMotivationalModal(
                '🎉 تهانينا! لقد أكملت الكورس',
                `لقد أنهيت جميع محتويات الكورس بنجاح!<br>
                 وقت الجلسة الحالية: ${stats.sessionHours}س ${stats.sessionMinutes}د<br>
                 استمر في التعلم وطبق ما تعلمته!`,
                [
                    {
                        text: 'مراجعة المحتوى',
                        action: () => toggleContentModal(),
                        class: 'btn-primary'
                    },
                    {
                        text: 'تحميل الشهادة',
                        action: () => downloadCertificate(),
                        class: 'btn-success'
                    },
                    {
                        text: 'مشاركة الإنجاز',
                        action: () => shareAchievement(),
                        class: 'btn-info'
                    }
                ]
            );

            // إخفاء الرسالة تلقائياً بعد 5 ثوانٍ
            setTimeout(() => {
                if (modal && modal.parentNode) {
                    modal.style.transition = 'opacity 0.5s ease-out';
                    modal.style.opacity = '0';
                    setTimeout(() => {
                        if (modal.parentNode) {
                            modal.parentNode.removeChild(modal);
                        }
                    }, 500);
                }
            }, 5000);
        }

        // حفظ ملحوظة إكمال الكورس
        function saveCourseCompletionNote() {
            const courseId = <?php echo $courseId; ?>;
            const userId = <?php echo $userId; ?>;

            fetch('../api/save_course_completion_note.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    course_id: courseId,
                    user_id: userId,
                    completion_date: new Date().toISOString()
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('تم حفظ ملحوظة إكمال الكورس بنجاح');
                }
            })
            .catch(error => {
                console.error('خطأ في حفظ ملحوظة الإكمال:', error);
            });
        }

        function downloadCertificate() {
            // Placeholder for certificate download
            showMotivationalModal(
                'تحميل الشهادة',
                'ميزة تحميل الشهادة ستكون متاحة قريباً. سيتم إشعارك عند توفرها.',
                [
                    { text: 'حسناً', action: () => {}, class: 'btn-primary' }
                ]
            );
        }

        function shareAchievement() {
            const shareText = 'لقد أكملت الكورس بنجاح! 🎉';

            if (navigator.share) {
                navigator.share({
                    title: 'إكمال الكورس',
                    text: shareText,
                    url: window.location.href
                });
            } else {
                navigator.clipboard.writeText(shareText).then(() => {
                    showMotivationalModal(
                        'تم النسخ!',
                        'تم نسخ رسالة الإنجاز. يمكنك مشاركتها على وسائل التواصل الاجتماعي.',
                        [
                            { text: 'رائع!', action: () => {}, class: 'btn-success' }
                        ]
                    );
                });
            }
        }
    </script>

    <!-- Floating Action Menu -->
    <div class="floating-action-menu" id="floatingActionMenu">
        <div class="fab-menu" id="fabMenu">
            <button class="fab-item" onclick="takeNotes()" title="تدوين ملاحظات">
                <i class="fas fa-sticky-note"></i>
            </button>
            <button class="fab-item" onclick="toggleContentModal()" title="قائمة المحتوى">
                <i class="fas fa-list"></i>
            </button>
            <button class="fab-item" onclick="showProgressSummary()" title="ملخص التقدم">
                <i class="fas fa-chart-line"></i>
            </button>
            <button class="fab-item" onclick="openContactModal()" title="تواصل معنا">
                <i class="fas fa-headset"></i>
            </button>
        </div>
        <button class="fab-main" onclick="toggleFabMenu()">
            <i class="fas fa-plus" id="fabIcon"></i>
        </button>
    </div>

    <script>
        // Floating Action Menu Functions
        function toggleFabMenu() {
            const fabMenu = document.getElementById('fabMenu');
            const fabIcon = document.getElementById('fabIcon');

            fabMenu.classList.toggle('show');

            if (fabMenu.classList.contains('show')) {
                fabIcon.className = 'fas fa-times';
                fabIcon.style.transform = 'rotate(45deg)';
            } else {
                fabIcon.className = 'fas fa-plus';
                fabIcon.style.transform = 'rotate(0deg)';
            }
        }

        function showProgressSummary() {
            const stats = progressTracker ? progressTracker.getSessionStats() : { sessionHours: 0, sessionMinutes: 0 };
            const totalItems = document.querySelectorAll('.content-item-modern').length;
            const completedItems = document.querySelectorAll('.content-item-modern.completed').length;
            const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

            showMotivationalModal(
                '📊 ملخص التقدم',
                `
                <div style="text-align: right; line-height: 1.8;">
                    <strong>إحصائيات الجلسة الحالية:</strong><br>
                    ⏱️ وقت الجلسة: ${stats.sessionHours}س ${stats.sessionMinutes}د<br>
                    📚 المحتوى المكتمل: ${completedItems} من ${totalItems}<br>
                    📈 نسبة التقدم: ${progress}%<br>
                    🏆 النقاط المكتسبة: ${completedItems * 10}<br><br>
                    <strong>استمر في التعلم لتحقيق المزيد من الإنجازات!</strong>
                </div>
                `,
                [
                    { text: 'رائع!', action: () => toggleFabMenu(), class: 'btn-success' },
                    { text: 'مشاركة التقدم', action: () => shareProgress(progress), class: 'btn-info' }
                ]
            );
        }

        function shareProgress(progress) {
            const shareText = `لقد أحرزت تقدماً رائعاً في الكورس! نسبة الإكمال: ${progress}% 🎯`;

            if (navigator.share) {
                navigator.share({
                    title: 'تقدمي في الكورس',
                    text: shareText,
                    url: window.location.href
                });
            } else {
                navigator.clipboard.writeText(shareText).then(() => {
                    showMotivationalModal(
                        'تم النسخ!',
                        'تم نسخ تقدمك. يمكنك مشاركته على وسائل التواصل الاجتماعي.',
                        [
                            { text: 'حسناً', action: () => {}, class: 'btn-primary' }
                        ]
                    );
                });
            }
        }

        // Hide FAB menu when clicking outside
        document.addEventListener('click', function(event) {
            const fabMenu = document.getElementById('fabMenu');
            const floatingActionMenu = document.getElementById('floatingActionMenu');

            if (fabMenu && fabMenu.classList.contains('show') &&
                !floatingActionMenu.contains(event.target)) {
                toggleFabMenu();
            }
        });

        // فحص حالة إكمال الكورس عند دخول الصفحة
        function checkCourseCompletionStatus() {
            const courseId = <?php echo $courseId; ?>;
            const userId = <?php echo $userId; ?>;

            fetch(`../api/check_course_completion.php?course_id=${courseId}&user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.is_completed) {
                        showCourseCompletedModal(data.completion_date);
                    }
                })
                .catch(error => {
                    console.error('خطأ في فحص حالة إكمال الكورس:', error);
                });
        }

        // عرض نافذة منبثقة للكورس المكتمل
        function showCourseCompletedModal(completionDate) {
            const modal = document.createElement('div');
            modal.className = 'course-completed-modal';
            modal.innerHTML = `
                <div class="modal-overlay-completed"></div>
                <div class="modal-content-completed">
                    <div class="completion-header">
                        <div class="completion-icon">🎓</div>
                        <h2>كورس مكتمل</h2>
                        <p>لقد أكملت هذا الكورس بنجاح في ${new Date(completionDate).toLocaleDateString('ar-EG')}</p>
                    </div>
                    <div class="completion-message">
                        <p>هذا الكورس مكتمل. هل تريد إعادة المشاهدة والمراجعة؟</p>
                    </div>
                    <div class="completion-actions">
                        <button class="btn btn-primary" onclick="continueReview()">
                            <i class="fas fa-eye"></i>
                            إعادة المشاهدة
                        </button>
                        <button class="btn btn-secondary" onclick="closeCourseCompletedModal()">
                            <i class="fas fa-arrow-right"></i>
                            العودة للكورسات
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إظهار المودال مع تأثير
            setTimeout(() => {
                modal.classList.add('show');
            }, 100);
        }

        // إغلاق نافذة الكورس المكتمل
        function closeCourseCompletedModal() {
            const modal = document.querySelector('.course-completed-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                    // العودة لصفحة الكورسات
                    window.location.href = '../page/courses.php';
                }, 300);
            }
        }

        // متابعة المراجعة
        function continueReview() {
            const modal = document.querySelector('.course-completed-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }
    </script>

    <style>
        /* نافذة الكورس المكتمل */
        .course-completed-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .course-completed-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-overlay-completed {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content-completed {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .completion-header {
            margin-bottom: 30px;
        }

        .completion-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .completion-header h2 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .completion-header p {
            color: #6c757d;
            font-size: 16px;
            margin: 0;
        }

        .completion-message {
            margin-bottom: 30px;
        }

        .completion-message p {
            color: #495057;
            font-size: 18px;
            line-height: 1.6;
            margin: 0;
        }

        .completion-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .completion-actions .btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .completion-actions .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .completion-actions .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .completion-actions .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .completion-actions .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>

    <script src="../js/custom-confirm.js"></script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
