<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/PaymentGateway.php';

class WalletGateway extends PaymentGateway {
    private $config;
    private $authToken;
    
    public function __construct() {
        parent::__construct();
        $this->config = $this->getGatewayConfig('paymob'); // Use same Paymob config
    }
    
    /**
     * Get authentication token
     */
    private function getAuthToken() {
        if ($this->authToken) {
            return $this->authToken;
        }
        
        $authData = [
            'api_key' => $this->config['api_key']
        ];
        
        $response = $this->sendPaymobRequest('/auth/tokens', $authData);
        
        if ($response && isset($response['token'])) {
            $this->authToken = $response['token'];
            return $this->authToken;
        }
        
        throw new Exception('Failed to get Paymob auth token');
    }
    
    /**
     * Create wallet payment request
     */
    public function createWalletPaymentRequest($userId, $courseId, $amount, $customerInfo, $walletType) {
        try {
            // Create transaction record
            $transactionId = $this->createTransaction($userId, $courseId, 'wallet', $amount);
            if (!$transactionId) {
                throw new Exception('Failed to create transaction record');
            }
            
            // Get auth token
            $authToken = $this->getAuthToken();
            
            // Step 1: Create order
            $orderData = [
                'auth_token' => $authToken,
                'delivery_needed' => false,
                'amount_cents' => $amount * 100, // Convert to cents
                'currency' => 'EGP',
                'merchant_order_id' => $transactionId,
                'items' => [
                    [
                        'name' => $customerInfo['course_title'],
                        'amount_cents' => $amount * 100,
                        'description' => 'Course: ' . $customerInfo['course_title'],
                        'quantity' => 1
                    ]
                ]
            ];
            
            $orderResponse = $this->sendPaymobRequest('/ecommerce/orders', $orderData);
            
            if (!$orderResponse || !isset($orderResponse['id'])) {
                throw new Exception('Failed to create Paymob order');
            }
            
            $orderId = $orderResponse['id'];
            
            // Step 2: Create payment key for wallet
            $integrationId = $this->getWalletIntegrationId($walletType);
            
            $paymentKeyData = [
                'auth_token' => $authToken,
                'amount_cents' => $amount * 100,
                'expiration' => 3600, // 1 hour
                'order_id' => $orderId,
                'billing_data' => [
                    'apartment' => 'NA',
                    'email' => $customerInfo['email'],
                    'floor' => 'NA',
                    'first_name' => explode(' ', $customerInfo['name'])[0] ?? $customerInfo['name'],
                    'street' => 'NA',
                    'building' => 'NA',
                    'phone_number' => $customerInfo['mobile'] ?? '01000000000',
                    'shipping_method' => 'NA',
                    'postal_code' => 'NA',
                    'city' => 'Cairo',
                    'country' => 'EG',
                    'last_name' => explode(' ', $customerInfo['name'], 2)[1] ?? 'User',
                    'state' => 'Cairo'
                ],
                'currency' => 'EGP',
                'integration_id' => $integrationId
            ];
            
            $paymentKeyResponse = $this->sendPaymobRequest('/acceptance/payment_keys', $paymentKeyData);
            
            if (!$paymentKeyResponse || !isset($paymentKeyResponse['token'])) {
                throw new Exception('Failed to create Paymob payment key');
            }
            
            $paymentToken = $paymentKeyResponse['token'];
            
            // Update transaction with Paymob order ID
            $this->updateTransactionStatus($transactionId, 'pending', $orderId, [
                'order_id' => $orderId,
                'payment_token' => $paymentToken,
                'wallet_type' => $walletType
            ]);
            
            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'payment_url' => $this->getWalletPaymentUrl($walletType, $paymentToken),
                'payment_token' => $paymentToken,
                'order_id' => $orderId,
                'wallet_type' => $walletType
            ];
            
        } catch (Exception $e) {
            error_log("Wallet payment error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment initialization failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get wallet integration ID based on wallet type
     */
    private function getWalletIntegrationId($walletType) {
        // These are example integration IDs - you need to get the actual ones from Paymob
        $walletIntegrations = [
            'vodafone_cash' => '4835056', // Vodafone Cash integration ID
            'etisalat_cash' => '4835057', // Etisalat Cash integration ID  
            'we_cash' => '4835058',      // WE Cash integration ID
            'orange_cash' => '4835059'   // Orange Cash integration ID
        ];
        
        return $walletIntegrations[$walletType] ?? $this->config['integration_id'];
    }
    
    /**
     * Get wallet payment URL
     */
    private function getWalletPaymentUrl($walletType, $paymentToken) {
        $walletUrls = [
            'vodafone_cash' => 'https://accept.paymob.com/api/acceptance/payments/pay',
            'etisalat_cash' => 'https://accept.paymob.com/api/acceptance/payments/pay',
            'we_cash' => 'https://accept.paymob.com/api/acceptance/payments/pay',
            'orange_cash' => 'https://accept.paymob.com/api/acceptance/payments/pay'
        ];
        
        $baseUrl = $walletUrls[$walletType] ?? 'https://accept.paymob.com/api/acceptance/payments/pay';
        return $baseUrl . '?payment_token=' . $paymentToken;
    }
    
    /**
     * Send request to Paymob API
     */
    private function sendPaymobRequest($endpoint, $data) {
        $url = $this->config['base_url'] . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("Paymob API cURL error: " . $error);
            return false;
        }
        
        if ($httpCode !== 200 && $httpCode !== 201) {
            error_log("Paymob API HTTP error: " . $httpCode . " - " . $response);
            return false;
        }
        
        return json_decode($response, true);
    }
    
    /**
     * Handle wallet callback (same as Paymob)
     */
    public function handleCallback($callbackData) {
        try {
            // Use same verification as Paymob
            require_once __DIR__ . '/PaymobGateway.php';
            $paymobGateway = new PaymobGateway();
            return $paymobGateway->handleCallback($callbackData);
            
        } catch (Exception $e) {
            error_log("Wallet callback error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check payment status
     */
    public function checkPaymentStatus($transactionId) {
        try {
            // Use same status check as Paymob
            require_once __DIR__ . '/PaymobGateway.php';
            $paymobGateway = new PaymobGateway();
            return $paymobGateway->checkPaymentStatus($transactionId);
            
        } catch (Exception $e) {
            error_log("Wallet status check error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Status check failed'];
        }
    }
    
    /**
     * Get available wallet types
     */
    public function getAvailableWallets() {
        return [
            'vodafone_cash' => [
                'name' => 'فودافون كاش',
                'icon' => '📱',
                'color' => '#e60000'
            ],
            'etisalat_cash' => [
                'name' => 'اتصالات كاش',
                'icon' => '💚',
                'color' => '#00b04f'
            ],
            'we_cash' => [
                'name' => 'وي كاش',
                'icon' => '💜',
                'color' => '#662d91'
            ],
            'orange_cash' => [
                'name' => 'أورانج كاش',
                'icon' => '🧡',
                'color' => '#ff6600'
            ]
        ];
    }
}
?>
