<?php
require_once __DIR__ . '/NewsManager.php';
require_once __DIR__ . '/database.php';

// Get user education info for filtering
$userEducation = null;
if (isset($_SESSION['user_id'])) {
    try {
        $userManager = new UserManager();
        $userData = $userManager->getUserById($_SESSION['user_id']);
        if ($userData) {
            $userEducation = [
                'education_level' => $userData['education_level'],
                'education_type' => $userData['education_type'],
                'grade' => $userData['grade']
            ];
        }
    } catch (Exception $e) {
        error_log("Error getting user data for widget: " . $e->getMessage());
    }
}

$newsManager = new NewsManager();
$latestNews = $newsManager->getLatestNews(5, $userEducation);
?>

<div class="news-widget">
    <div class="news-header">
        <h3>أخبار المنهج</h3>
        <div class="widget-icon">📰</div>
    </div>

    <div class="news-stats">
        <div class="news-stat">
            <span class="stat-number"><?php echo count($latestNews); ?></span>
            <span class="stat-label">أخبار جديدة</span>
        </div>
        <div class="news-stat">
            <span class="stat-number">
                <?php 
                $featuredNews = array_filter($latestNews, function($news) {
                    return $news['is_featured'] == 1;
                });
                echo count($featuredNews);
                ?>
            </span>
            <span class="stat-label">أخبار مميزة</span>
        </div>
    </div>

    <div class="news-content">
        <?php if (empty($latestNews)): ?>
            <div class="empty-state">
                <div class="empty-icon">📄</div>
                <p>لا توجد أخبار جديدة</p>
                <small>ستظهر آخر أخبار المنهج هنا</small>
            </div>
        <?php else: ?>
            <div class="news-list">
                <?php foreach ($latestNews as $news): ?>
                    <div class="news-item <?php echo $news['is_featured'] ? 'featured' : ''; ?>">
                        <div class="news-date">
                            <div class="date-day"><?php echo date('d', strtotime($news['publication_date'])); ?></div>
                            <div class="date-month"><?php echo date('M', strtotime($news['publication_date'])); ?></div>
                        </div>
                        <div class="news-details">
                            <div class="news-meta">
                                <span class="news-category"><?php echo htmlspecialchars($news['category']); ?></span>
                                <?php if ($news['is_featured']): ?>
                                    <span class="featured-badge">مميز</span>
                                <?php endif; ?>
                            </div>
                            <h5 class="news-title"><?php echo htmlspecialchars($news['title']); ?></h5>
                            <?php if ($news['summary']): ?>
                                <div class="news-summary">
                                    <?php echo htmlspecialchars($news['summary']); ?>
                                </div>
                            <?php else: ?>
                                <div class="news-summary">
                                    <?php echo htmlspecialchars(substr(strip_tags($news['content']), 0, 120)) . (strlen($news['content']) > 120 ? '...' : ''); ?>
                                </div>
                            <?php endif; ?>
                            <div class="news-footer">
                                <span class="news-time">
                                    <?php 
                                    $publishDate = strtotime($news['publication_date']);
                                    $now = time();
                                    $diff = $now - $publishDate;
                                    
                                    if ($diff < 86400) {
                                        echo 'اليوم';
                                    } elseif ($diff < 172800) {
                                        echo 'أمس';
                                    } elseif ($diff < 604800) {
                                        echo floor($diff / 86400) . ' أيام';
                                    } else {
                                        echo date('Y/m/d', $publishDate);
                                    }
                                    ?>
                                </span>
                                <button class="read-more-btn" onclick="showNewsModal(<?php echo $news['id']; ?>)">
                                    اقرأ المزيد
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- News Modal -->
<div id="newsModal" class="news-modal" style="display: none;">
    <div class="news-modal-content">
        <div class="news-modal-header">
            <h3 id="modalNewsTitle"></h3>
            <span class="news-modal-close" onclick="closeNewsModal()">&times;</span>
        </div>
        <div class="news-modal-body">
            <div id="modalNewsCategory" class="modal-category"></div>
            <div id="modalNewsDate" class="modal-date"></div>
            <div id="modalNewsContent" class="modal-content-text"></div>
        </div>
    </div>
</div>

<script>
function showNewsModal(newsId) {
    // Fetch news details via AJAX
    fetch(`../admin/get_news.php?id=${newsId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const news = data.news;
                document.getElementById('modalNewsTitle').textContent = news.title;
                document.getElementById('modalNewsCategory').textContent = news.category;
                document.getElementById('modalNewsDate').textContent = new Date(news.publication_date).toLocaleDateString('ar-EG');
                document.getElementById('modalNewsContent').innerHTML = news.content.replace(/\n/g, '<br>');
                document.getElementById('newsModal').style.display = 'block';
            } else {
                alert('حدث خطأ في تحميل الخبر');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل الخبر');
        });
}

function closeNewsModal() {
    document.getElementById('newsModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('newsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
</script>

<style>
.news-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15);
    border: 2px solid rgba(70, 130, 180, 0.1);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.news-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 20px 20px 0 0;
}

.news-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.25);
    border-color: rgba(135, 206, 235, 0.3);
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
}

.news-header h3 {
    color: #2c3e50;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.widget-icon {
    font-size: 32px;
    opacity: 0.7;
    animation: pulse 2s infinite;
}

.news-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.news-stat {
    flex: 1;
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(70, 130, 180, 0.05) 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(135, 206, 235, 0.2);
    transition: all 0.3s ease;
}

.news-stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(135, 206, 235, 0.2);
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.15) 0%, rgba(70, 130, 180, 0.1) 100%);
}

.news-stat .stat-number {
    display: block;
    font-size: 28px;
    font-weight: 800;
    color: #4682B4;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(70, 130, 180, 0.1);
}

.news-stat .stat-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.news-content {
    max-height: 400px;
    overflow-y: auto;
}

.news-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.news-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.news-item.featured {
    border-color: rgba(255, 193, 7, 0.3);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.02) 0%, rgba(255, 193, 7, 0.01) 100%);
}

.news-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.news-item.featured::before {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
}

.news-item:hover::before {
    transform: scaleY(1);
}

.news-item:hover {
    transform: translateX(8px);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.15);
    border-color: rgba(135, 206, 235, 0.3);
}

.news-date {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    padding: 15px;
    border-radius: 12px;
    text-align: center;
    min-width: 70px;
    box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
}

.news-item.featured .news-date {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
}

.date-day {
    font-size: 24px;
    font-weight: 800;
    line-height: 1;
}

.date-month {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-top: 5px;
    opacity: 0.9;
}

.news-details {
    flex: 1;
}

.news-meta {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.news-category {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.featured-badge {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #ffc107;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.news-title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 10px 0;
    line-height: 1.3;
}

.news-summary {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 15px;
}

.news-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.news-time {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.read-more-btn {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.empty-state small {
    font-size: 14px;
    opacity: 0.8;
}

/* News Modal Styles */
.news-modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.news-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.news-modal-header {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.news-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
}

.news-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.news-modal-close:hover {
    opacity: 0.7;
}

.news-modal-body {
    padding: 30px;
}

.modal-category {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid rgba(40, 167, 69, 0.2);
    display: inline-block;
    margin-bottom: 15px;
}

.modal-date {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 20px;
    font-weight: 500;
}

.modal-content-text {
    font-size: 16px;
    line-height: 1.6;
    color: #2c3e50;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .news-widget {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 15px;
    }

    .news-header h3 {
        font-size: 18px;
    }

    .widget-icon {
        font-size: 24px;
    }

    .news-stats {
        flex-direction: column;
        gap: 12px;
    }

    .news-stat {
        padding: 15px;
    }

    .news-stat .stat-number {
        font-size: 24px;
    }

    .news-item {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .news-date {
        align-self: flex-start;
        min-width: 60px;
        padding: 12px;
    }

    .date-day {
        font-size: 20px;
    }

    .news-title {
        font-size: 16px;
    }

    .news-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .news-modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .news-modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .news-widget {
        padding: 15px;
        border-radius: 12px;
    }

    .news-header h3 {
        font-size: 16px;
    }

    .news-item {
        padding: 12px;
        gap: 12px;
    }

    .news-date {
        min-width: 50px;
        padding: 10px;
    }

    .date-day {
        font-size: 18px;
    }

    .news-title {
        font-size: 15px;
    }

    .empty-state {
        padding: 40px 15px;
    }

    .empty-icon {
        font-size: 48px;
    }

    .news-modal-content {
        width: 98%;
        margin: 5% auto;
    }

    .news-modal-body {
        padding: 15px;
    }
}
</style>
