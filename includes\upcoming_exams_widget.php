<?php
require_once __DIR__ . '/ExamManager.php';
require_once __DIR__ . '/database.php';

// Get user education info for filtering
$userEducation = null;
if (isset($_SESSION['user_id'])) {
    try {
        $userManager = new UserManager();
        $userData = $userManager->getUserById($_SESSION['user_id']);
        if ($userData) {
            $userEducation = [
                'education_level' => $userData['education_level'],
                'education_type' => $userData['education_type'],
                'grade' => $userData['grade']
            ];
        }
    } catch (Exception $e) {
        error_log("Error getting user data for widget: " . $e->getMessage());
    }
}

$examManager = new ExamManager();
$upcomingExams = $examManager->getUpcomingExams(5, $userEducation);
?>

<div class="exams-widget">
    <div class="exams-header">
        <h3>الامتحانات القادمة</h3>
        <div class="widget-icon">📅</div>
    </div>

    <div class="exams-stats">
        <div class="exam-stat">
            <span class="stat-number"><?php echo count($upcomingExams); ?></span>
            <span class="stat-label">امتحانات قادمة</span>
        </div>
        <div class="exam-stat">
            <span class="stat-number">
                <?php 
                $todayExams = array_filter($upcomingExams, function($exam) {
                    return date('Y-m-d', strtotime($exam['exam_date'])) === date('Y-m-d');
                });
                echo count($todayExams);
                ?>
            </span>
            <span class="stat-label">امتحانات اليوم</span>
        </div>
    </div>

    <div class="exams-content">
        <?php if (empty($upcomingExams)): ?>
            <div class="empty-state">
                <div class="empty-icon">📚</div>
                <p>لا توجد امتحانات قادمة حالياً</p>
                <small>ستظهر الامتحانات المجدولة هنا</small>
            </div>
        <?php else: ?>
            <div class="exams-list">
                <?php foreach ($upcomingExams as $exam): ?>
                    <div class="exam-item">
                        <div class="exam-date">
                            <div class="date-day"><?php echo date('d', strtotime($exam['exam_date'])); ?></div>
                            <div class="date-month"><?php echo date('M', strtotime($exam['exam_date'])); ?></div>
                        </div>
                        <div class="exam-details">
                            <h5 class="exam-title"><?php echo htmlspecialchars($exam['exam_name']); ?></h5>
                            <div class="exam-info">
                                <span class="exam-subject"><?php echo htmlspecialchars($exam['subject']); ?></span>
                                <span class="exam-time"><?php echo date('H:i', strtotime($exam['exam_time'])); ?></span>
                            </div>
                            <div class="exam-meta">
                                <span class="exam-duration"><?php echo $exam['duration_minutes']; ?> دقيقة</span>
                                <?php if ($exam['location']): ?>
                                    <span class="exam-location"><?php echo htmlspecialchars($exam['location']); ?></span>
                                <?php endif; ?>
                            </div>
                            <?php if ($exam['instructions']): ?>
                                <div class="exam-instructions">
                                    <?php echo htmlspecialchars(substr($exam['instructions'], 0, 100)) . (strlen($exam['instructions']) > 100 ? '...' : ''); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="exam-status">
                            <?php 
                            $examDateTime = strtotime($exam['exam_date'] . ' ' . $exam['exam_time']);
                            $now = time();
                            $timeDiff = $examDateTime - $now;
                            
                            if ($timeDiff < 0) {
                                echo '<span class="status-passed">انتهى</span>';
                            } elseif ($timeDiff < 86400) { // Less than 24 hours
                                echo '<span class="status-today">اليوم</span>';
                            } elseif ($timeDiff < 604800) { // Less than 7 days
                                echo '<span class="status-soon">قريباً</span>';
                            } else {
                                echo '<span class="status-upcoming">قادم</span>';
                            }
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.exams-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15);
    border: 2px solid rgba(70, 130, 180, 0.1);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.exams-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 20px 20px 0 0;
}

.exams-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.25);
    border-color: rgba(135, 206, 235, 0.3);
}

.exams-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
}

.exams-header h3 {
    color: #2c3e50;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.widget-icon {
    font-size: 32px;
    opacity: 0.7;
    animation: pulse 2s infinite;
}

.exams-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.exam-stat {
    flex: 1;
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.1) 0%, rgba(70, 130, 180, 0.05) 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(135, 206, 235, 0.2);
    transition: all 0.3s ease;
}

.exam-stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(135, 206, 235, 0.2);
    background: linear-gradient(135deg, rgba(135, 206, 235, 0.15) 0%, rgba(70, 130, 180, 0.1) 100%);
}

.exam-stat .stat-number {
    display: block;
    font-size: 28px;
    font-weight: 800;
    color: #4682B4;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(70, 130, 180, 0.1);
}

.exam-stat .stat-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.exams-content {
    max-height: 400px;
    overflow-y: auto;
}

.exams-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.exam-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.exam-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.exam-item:hover::before {
    transform: scaleY(1);
}

.exam-item:hover {
    transform: translateX(8px);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.15);
    border-color: rgba(135, 206, 235, 0.3);
}

.exam-date {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    padding: 15px;
    border-radius: 12px;
    text-align: center;
    min-width: 70px;
    box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
}

.date-day {
    font-size: 24px;
    font-weight: 800;
    line-height: 1;
}

.date-month {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-top: 5px;
    opacity: 0.9;
}

.exam-details {
    flex: 1;
}

.exam-title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 10px 0;
    line-height: 1.3;
}

.exam-info {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.exam-subject {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.exam-time {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #ffc107;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.exam-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.exam-duration,
.exam-location {
    font-size: 12px;
    color: #6c757d;
    background: rgba(108, 117, 125, 0.1);
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.exam-instructions {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
    margin-top: 8px;
    padding: 10px;
    background: rgba(70, 130, 180, 0.05);
    border-radius: 8px;
    border-left: 3px solid #87CEEB;
}

.exam-status {
    align-self: flex-start;
}

.exam-status span {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-today {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
    animation: pulse 1.5s infinite;
}

.status-soon {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-upcoming {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-passed {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(108, 117, 125, 0.05) 100%);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.empty-state small {
    font-size: 14px;
    opacity: 0.8;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .exams-widget {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 15px;
    }

    .exams-header h3 {
        font-size: 18px;
    }

    .widget-icon {
        font-size: 24px;
    }

    .exams-stats {
        flex-direction: column;
        gap: 12px;
    }

    .exam-stat {
        padding: 15px;
    }

    .exam-stat .stat-number {
        font-size: 24px;
    }

    .exam-item {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .exam-date {
        align-self: flex-start;
        min-width: 60px;
        padding: 12px;
    }

    .date-day {
        font-size: 20px;
    }

    .exam-title {
        font-size: 16px;
    }

    .exam-info,
    .exam-meta {
        flex-direction: column;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .exams-widget {
        padding: 15px;
        border-radius: 12px;
    }

    .exams-header h3 {
        font-size: 16px;
    }

    .exam-item {
        padding: 12px;
        gap: 12px;
    }

    .exam-date {
        min-width: 50px;
        padding: 10px;
    }

    .date-day {
        font-size: 18px;
    }

    .exam-title {
        font-size: 15px;
    }

    .empty-state {
        padding: 40px 15px;
    }

    .empty-icon {
        font-size: 48px;
    }
}
</style>
