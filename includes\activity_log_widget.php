<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    return;
}

require_once 'database.php';
$userManager = new UserManager();

// Check if activity log table exists and create test activity if needed
try {
    $testQuery = $userManager->getActivityLogCount($_SESSION['user_id']);
    if ($testQuery === 0) {
        // Add a welcome activity for first-time users
        $userManager->logActivity($_SESSION['user_id'], 'login', 'مرحباً بك في منصة سلسلة الدكتور!');
    }
} catch (Exception $e) {
    error_log("Activity log table might not exist: " . $e->getMessage());
}

// Get recent activity log
$activities = $userManager->getUserActivityLog($_SESSION['user_id'], 10);
$totalActivities = $userManager->getActivityLogCount($_SESSION['user_id']);

// Debug information
$debugInfo = $userManager->debugActivityLog($_SESSION['user_id']);
error_log("Debug info: " . print_r($debugInfo, true));

error_log("User ID: " . $_SESSION['user_id']);
error_log("Total activities: " . $totalActivities);
error_log("Activities count: " . count($activities));
if (!empty($activities)) {
    error_log("First activity: " . print_r($activities[0], true));
} else {
    error_log("Activities array is empty");
}

// Activity type translations and icons
$activityTypes = [
    'login' => ['icon' => '🔐', 'label' => 'تسجيل دخول', 'color' => '#28a745'],
    'logout' => ['icon' => '🚪', 'label' => 'تسجيل خروج', 'color' => '#6c757d'],
    'todo_create' => ['icon' => '✅', 'label' => 'إضافة مهمة', 'color' => '#007bff'],
    'todo_update' => ['icon' => '📝', 'label' => 'تعديل مهمة', 'color' => '#ffc107'],
    'todo_complete' => ['icon' => '✔️', 'label' => 'إكمال مهمة', 'color' => '#28a745'],
    'todo_delete' => ['icon' => '🗑️', 'label' => 'حذف مهمة', 'color' => '#dc3545'],
    'note_create' => ['icon' => '📝', 'label' => 'إضافة ملاحظة', 'color' => '#17a2b8'],
    'note_update' => ['icon' => '✏️', 'label' => 'تعديل ملاحظة', 'color' => '#ffc107'],
    'note_pin' => ['icon' => '📌', 'label' => 'تثبيت ملاحظة', 'color' => '#fd7e14'],
    'note_delete' => ['icon' => '🗑️', 'label' => 'حذف ملاحظة', 'color' => '#dc3545'],
    'profile_update' => ['icon' => '👤', 'label' => 'تحديث الملف الشخصي', 'color' => '#6f42c1'],
    'password_change' => ['icon' => '🔒', 'label' => 'تغيير كلمة المرور', 'color' => '#e83e8c'],
    'default' => ['icon' => '📋', 'label' => 'نشاط', 'color' => '#6c757d']
];

function getActivityInfo($type, $activityTypes) {
    return $activityTypes[$type] ?? $activityTypes['default'];
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    return floor($time/31536000) . ' سنة';
}
?>

<div class="activity-log-widget">
    <div class="activity-header">
        <h3>📊 سجل الأنشطة</h3>
        <div class="activity-stats">
            <span class="total-activities"><?php echo $totalActivities; ?> نشاط</span>
        </div>
    </div>

    <div class="activity-content">
        <?php if (empty($activities)): ?>
            <div class="empty-state">
                <span class="empty-icon">📋</span>
                <p>لا توجد أنشطة مسجلة</p>
                <p style="font-size: 12px; color: #999; margin-top: 10px;">
                    العدد الإجمالي: <?php echo $totalActivities; ?> |
                    معرف المستخدم: <?php echo $_SESSION['user_id']; ?><br>
                    عدد الأنشطة المسترجعة: <?php echo count($activities); ?>
                    <?php if (isset($debugInfo['error'])): ?>
                        <br>خطأ: <?php echo htmlspecialchars($debugInfo['error']); ?>
                    <?php endif; ?>
                </p>
                <button onclick="location.reload()" style="margin-top: 15px; padding: 8px 16px; background: #4682B4; color: white; border: none; border-radius: 6px; cursor: pointer;">
                    تحديث
                </button>
            </div>
        <?php else: ?>
            <div class="activity-timeline">
                <?php foreach ($activities as $activity): ?>
                    <?php 
                    $activityInfo = getActivityInfo($activity['activity_type'], $activityTypes);
                    $details = $activity['details'] ? json_decode($activity['details'], true) : null;
                    ?>
                    <div class="activity-item">
                        <div class="activity-icon" style="background-color: <?php echo $activityInfo['color']; ?>">
                            <?php echo $activityInfo['icon']; ?>
                        </div>
                        <div class="activity-details">
                            <div class="activity-main">
                                <span class="activity-type"><?php echo $activityInfo['label']; ?></span>
                                <span class="activity-time"><?php echo timeAgo($activity['created_at']); ?></span>
                            </div>
                            <div class="activity-description">
                                <?php echo htmlspecialchars($activity['activity_description']); ?>
                            </div>
                            <?php if ($details): ?>
                                <div class="activity-extra">
                                    <?php if (isset($details['title'])): ?>
                                        <span class="detail-item">📄 <?php echo htmlspecialchars($details['title']); ?></span>
                                    <?php endif; ?>
                                    <?php if (isset($details['priority'])): ?>
                                        <span class="detail-item priority-<?php echo $details['priority']; ?>">
                                            🎯 <?php echo $details['priority']; ?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if (isset($details['category'])): ?>
                                        <span class="detail-item">🏷️ <?php echo htmlspecialchars($details['category']); ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php if ($totalActivities > 10): ?>
                <div class="activity-footer">
                    <button class="btn-view-all" onclick="showAllActivities()">
                        عرض جميع الأنشطة (<?php echo $totalActivities; ?>)
                    </button>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
.activity-log-widget {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(70, 130, 180, 0.1);
    overflow: hidden;
}

.activity-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px 30px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.activity-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
}

.activity-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
}

.total-activities {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.activity-content {
    padding: 25px 30px;
    max-height: 400px;
    overflow-y: auto;
}

.activity-timeline {
    position: relative;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e9ecef, #dee2e6);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 25px;
    position: relative;
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    position: relative;
    z-index: 1;
}

.activity-details {
    flex: 1;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.activity-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.activity-type {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.activity-time {
    font-size: 12px;
    color: #6c757d;
    background: white;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.activity-description {
    color: #495057;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 10px;
}

.activity-extra {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.detail-item {
    background: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    color: #6c757d;
    border: 1px solid #e9ecef;
}

.detail-item.priority-high {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.detail-item.priority-medium {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.detail-item.priority-low {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.activity-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.btn-view-all {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-view-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 15px;
}

/* Custom scrollbar */
.activity-content::-webkit-scrollbar {
    width: 6px;
}

.activity-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.activity-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.activity-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .activity-widget {
        padding: 20px !important;
        margin-bottom: 25px !important;
    }

    .activity-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .activity-header h3 {
        font-size: 18px;
    }

    .activity-stats {
        flex-direction: column;
        gap: 12px;
    }

    .activity-stat {
        padding: 12px;
        min-height: 60px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 11px;
    }

    .activity-content {
        padding: 15px;
        max-height: 300px;
    }

    .activity-timeline::before {
        left: 15px;
    }

    .activity-item {
        gap: 12px;
        padding: 12px 0;
    }

    .activity-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
        min-width: 36px;
    }

    .activity-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }

    .activity-title {
        font-size: 14px;
    }

    .activity-description {
        font-size: 12px;
    }

    .activity-time {
        font-size: 11px;
    }

    .show-all-btn {
        padding: 10px 16px;
        font-size: 13px;
        min-height: 40px;
    }
}

@media (max-width: 480px) {
    .activity-widget {
        padding: 15px !important;
        margin-bottom: 20px !important;
        border-radius: 12px !important;
    }

    .activity-header h3 {
        font-size: 16px;
    }

    .activity-stats {
        gap: 10px;
    }

    .activity-stat {
        padding: 10px;
        min-height: 50px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 10px;
    }

    .activity-content {
        padding: 12px;
        max-height: 250px;
    }

    .activity-timeline::before {
        left: 12px;
    }

    .activity-item {
        gap: 10px;
        padding: 10px 0;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
        min-width: 32px;
    }

    .activity-title {
        font-size: 13px;
    }

    .activity-description {
        font-size: 11px;
        line-height: 1.3;
    }

    .activity-time {
        font-size: 10px;
    }

    .show-all-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 36px;
    }

    .empty-state {
        padding: 30px 15px;
    }

    .empty-state p {
        font-size: 13px;
    }
}

@media (max-width: 320px) {
    .activity-widget {
        padding: 12px !important;
        margin-bottom: 15px !important;
    }

    .activity-header h3 {
        font-size: 15px;
    }

    .activity-stat {
        padding: 8px;
        min-height: 45px;
    }

    .stat-number {
        font-size: 16px;
    }

    .stat-label {
        font-size: 9px;
    }

    .activity-content {
        padding: 10px;
        max-height: 200px;
    }

    .activity-timeline::before {
        left: 10px;
    }

    .activity-item {
        gap: 8px;
        padding: 8px 0;
    }

    .activity-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
        min-width: 28px;
    }

    .activity-title {
        font-size: 12px;
    }

    .activity-description {
        font-size: 10px;
        line-height: 1.2;
    }

    .activity-time {
        font-size: 9px;
    }

    .show-all-btn {
        padding: 6px 10px;
        font-size: 11px;
        min-height: 32px;
    }

    .empty-state {
        padding: 25px 10px;
    }

    .empty-state p {
        font-size: 12px;
    }
}
</style>

<script>
function showAllActivities() {
    // This would open a modal or navigate to a full activity log page
    alert('سيتم إضافة صفحة عرض جميع الأنشطة قريباً');
}
</script>
