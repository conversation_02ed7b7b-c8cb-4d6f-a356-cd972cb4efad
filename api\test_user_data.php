<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<h2>❌ المستخدم غير مسجل الدخول</h2>";
    echo "<p><a href='../auth/login.php'>تسجيل الدخول</a></p>";
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user data
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<h2>❌ المستخدم غير موجود</h2>";
        exit;
    }
    
    echo "<h2>🔍 فحص بيانات المستخدم</h2>";
    
    echo "<h3>البيانات الحالية:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الحقل</th><th>القيمة</th><th>الحالة</th></tr>";
    
    $fields = [
        'id' => 'معرف المستخدم',
        'username' => 'اسم المستخدم',
        'full_name' => 'الاسم الكامل',
        'email' => 'البريد الإلكتروني',
        'phone' => 'رقم الهاتف',
        'grade' => 'الصف',
        'subscription_status' => 'حالة الاشتراك',
        'current_plan_id' => 'معرف الخطة الحالية',
        'subscription_end_date' => 'تاريخ انتهاء الاشتراك'
    ];
    
    foreach ($fields as $field => $label) {
        $value = $user[$field] ?? 'NULL';
        $status = '';
        
        if ($field === 'full_name') {
            if (empty($value) || $value === 'NULL') {
                $status = '❌ مطلوب للدفع';
            } else {
                $status = '✅ متوفر';
            }
        } elseif ($field === 'email') {
            if (empty($value) || $value === 'NULL') {
                $status = '⚠️ سيتم استخدام قيمة افتراضية';
            } else {
                $status = '✅ متوفر';
            }
        } elseif ($field === 'phone') {
            if (empty($value) || $value === 'NULL') {
                $status = '⚠️ سيتم استخدام قيمة افتراضية';
            } else {
                $status = '✅ متوفر';
            }
        } else {
            $status = !empty($value) && $value !== 'NULL' ? '✅ متوفر' : '❌ فارغ';
        }
        
        echo "<tr>";
        echo "<td><strong>$label</strong></td>";
        echo "<td>" . htmlspecialchars($value) . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test name processing
    echo "<h3>معالجة الاسم للدفع:</h3>";
    $full_name = !empty($user['full_name']) ? $user['full_name'] : $user['username'] . ' User';
    $email = $user['email'] ?: '<EMAIL>';
    $phone = $user['phone'] ?: '+201000000000';
    
    $name_parts = explode(' ', trim($full_name));
    $first_name = !empty($name_parts[0]) ? $name_parts[0] : 'User';
    $last_name = isset($name_parts[1]) && !empty($name_parts[1]) ? implode(' ', array_slice($name_parts, 1)) : 'Name';
    
    // Clean names
    $first_name = preg_replace('/[^a-zA-Z\x{0600}-\x{06FF}\s]/u', '', $first_name) ?: 'User';
    $last_name = preg_replace('/[^a-zA-Z\x{0600}-\x{06FF}\s]/u', '', $last_name) ?: 'Name';
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>البيان</th><th>القيمة المعالجة</th></tr>";
    echo "<tr><td><strong>الاسم الأول</strong></td><td>" . htmlspecialchars($first_name) . "</td></tr>";
    echo "<tr><td><strong>الاسم الأخير</strong></td><td>" . htmlspecialchars($last_name) . "</td></tr>";
    echo "<tr><td><strong>البريد الإلكتروني</strong></td><td>" . htmlspecialchars($email) . "</td></tr>";
    echo "<tr><td><strong>رقم الهاتف</strong></td><td>" . htmlspecialchars($phone) . "</td></tr>";
    echo "</table>";
    
    // Check if data is ready for payment
    echo "<h3>جاهزية البيانات للدفع:</h3>";
    if (!empty($first_name) && !empty($last_name) && !empty($email)) {
        echo "<p style='color: green; font-size: 18px;'>✅ البيانات جاهزة للدفع</p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'>❌ البيانات غير مكتملة</p>";
        echo "<p>يرجى تحديث البيانات في الملف الشخصي</p>";
    }
    
    // Show sample payment data
    echo "<h3>بيانات الدفع التي سيتم إرسالها:</h3>";
    $billing_data = [
        'apartment' => 'NA',
        'email' => $email,
        'floor' => 'NA',
        'first_name' => $first_name,
        'street' => 'NA',
        'building' => 'NA',
        'phone_number' => $phone,
        'shipping_method' => 'NA',
        'postal_code' => 'NA',
        'city' => 'Cairo',
        'country' => 'EG',
        'last_name' => $last_name,
        'state' => 'Cairo'
    ];
    
    echo "<pre style='background: #f1f1f1; padding: 10px; border-radius: 5px;'>";
    echo json_encode($billing_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ: " . $e->getMessage() . "</h2>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    direction: rtl;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #4682B4;
    padding-bottom: 10px;
}

table {
    margin: 20px 0;
    background: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

th {
    background: #4682B4;
    color: white;
    padding: 12px;
    text-align: center;
}

td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

pre {
    background: #f1f1f1;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #ddd;
}

a {
    color: #4682B4;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
