<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: courses.php');
    exit;
}

$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$message = '';
$messageType = '';

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: courses.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_video':
                try {
                    $stmt = $db->prepare("
                        INSERT INTO course_videos (course_id, week_number, title, description, video_url, video_platform, duration_minutes, video_order, timing_info, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    if ($stmt->execute([
                        $courseId,
                        $_POST['week_number'],
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['video_url'],
                        $_POST['video_platform'],
                        $_POST['duration_minutes'] ?? 0,
                        $_POST['video_order'] ?? 0,
                        $_POST['timing_info'],
                        $_SESSION['admin_id']
                    ])) {
                        $message = 'مبروك تم أضافت الفديو بنجاح تحياتي';
                        $messageType = 'success';
                    } else {
                        $message = ' للأسف لم يتم أضافت الفديو أعد تشغيل الصفحه أو تواصل معي تحياتي عبدالله';
                        $messageType = 'error';
                    }
                } catch (Exception $e) {
                    $message = 'يوجد عطل كبير يحتاج لي الدعم تواصل معي تحياتي عبدالله';
                    $messageType = 'error';
                }
                break;

            case 'add_exercise':
                try {
                    $db->beginTransaction();

                    // Handle both old format (direct fields) and new format (questions array)
                    if (isset($_POST['questions']) && !empty($_POST['questions'])) {
                        // New format with questions array
                        $firstQuestion = $_POST['questions'][0] ?? null;
                        if (!$firstQuestion) {
                            throw new Exception('لازم تضيف سؤال واحد علي الاقل عشان نعرف نكمل لك أضافت التمرين');
                        }

                        $options = null;
                        if ($firstQuestion['type'] === 'multiple_choice' && !empty($firstQuestion['options'])) {
                            $options = json_encode(array_filter(array_map('trim', $firstQuestion['options'])));
                        }

                        $questionType = $firstQuestion['type'];
                        $questionText = $firstQuestion['text'];
                        $correctAnswer = $firstQuestion['correct_answer'];
                        $explanation = $firstQuestion['explanation'] ?? '';
                    } else {
                        // Old format with direct fields
                        if (empty($_POST['question_text'])) {
                            throw new Exception('لازم يكون في نص عشان نعرف نكمل');
                        }

                        if (empty($_POST['question_type'])) {
                            throw new Exception(' لازم نختار السؤال عشان نعرف ما هو نوع السؤال عشان نفتح ليك الاشياء الخاصه بي السؤال يلا عدل عشان الطالب🤣👌');
                        }

                        // Debug: Log what we received
                        error_log("POST data: " . print_r($_POST, true));

                        // Check for correct answer in multiple possible fields
                        $correctAnswer = '';
                        if (!empty($_POST['final_correct_answer'])) {
                            $correctAnswer = $_POST['final_correct_answer'];
                        } elseif (!empty($_POST['correct_answer'])) {
                            $correctAnswer = $_POST['correct_answer'];
                        }

                        error_log("Final correct answer: " . $correctAnswer);

                        if (empty($correctAnswer)) {
                            throw new Exception('لازم نحدد أجابة السؤل عشان الطالب يفهم عشان لو معرفش يحل هنعمل ايه؟😂 - البيانات المستلمة: ' . print_r($_POST, true));
                        }

                        $options = null;
                        if ($_POST['question_type'] === 'multiple_choice' && !empty($_POST['options'])) {
                            $options = json_encode(array_filter(array_map('trim', $_POST['options'])));
                        }

                        $questionType = $_POST['question_type'];
                        $questionText = $_POST['question_text'];
                        $explanation = $_POST['explanation'] ?? '';
                    }

                    $stmt = $db->prepare("
                        INSERT INTO course_exercises (course_id, week_number, title, description, question_type, question_text, options, correct_answer, explanation, exercise_order, timing_info, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $courseId,
                        $_POST['week_number'],
                        $_POST['title'],
                        $_POST['description'],
                        $questionType,
                        $questionText,
                        $options,
                        $correctAnswer,
                        $explanation,
                        $_POST['exercise_order'] ?? 0,
                        $_POST['timing_info'],
                        $_SESSION['admin_id']
                    ]);

                    $exerciseId = $db->lastInsertId();

                    // Add additional questions as separate exercises with the same title and week (only for new format)
                    if (isset($_POST['questions']) && count($_POST['questions']) > 1) {
                        for ($i = 1; $i < count($_POST['questions']); $i++) {
                            $question = $_POST['questions'][$i];
                            $questionOptions = null;
                            if ($question['type'] === 'multiple_choice' && !empty($question['options'])) {
                                $questionOptions = json_encode(array_filter(array_map('trim', $question['options'])));
                            }

                            $stmt->execute([
                                $courseId,
                                $_POST['week_number'],
                                $_POST['title'] . ' - سؤال ' . ($i + 1),
                                $_POST['description'],
                                $question['type'],
                                $question['text'],
                                $questionOptions,
                                $question['correct_answer'],
                                $question['explanation'] ?? '',
                                ($_POST['exercise_order'] ?? 0) + $i,
                                $_POST['timing_info'],
                                $_SESSION['admin_id']
                            ]);
                        }
                    }

                    $db->commit();
                    $questionCount = isset($_POST['questions']) ? count($_POST['questions']) : 1;
                    $message = 'مبروك تم أضافة التمرين 😊 ' . $questionCount . ' سؤال';
                    $messageType = 'success';

                } catch (Exception $e) {
                    if ($db->inTransaction()) {
                        $db->rollBack();
                    }
                    error_log("Error adding exercise: " . $e->getMessage());
                    $message = ' مكنتش أتمني ان تواجه هذا الخطاء من عندي تواصل معي وسوف حاله في أسرع وقت متاسف علي الخطاء غير المتوقع😢: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;

            case 'add_exam':
                try {
                    $db->beginTransaction();
                    
                    // Create exam
                    $stmt = $db->prepare("
                        INSERT INTO course_exams (course_id, week_number, title, description, duration_minutes, passing_marks, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $courseId,
                        $_POST['week_number'],
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['duration_minutes'] ?? 60,
                        $_POST['passing_marks'] ?? 60,
                        $_SESSION['admin_id']
                    ]);
                    
                    $examId = $db->lastInsertId();
                    
                    // Add questions
                    if (!empty($_POST['questions'])) {
                        $questionStmt = $db->prepare("
                            INSERT INTO course_exam_questions (exam_id, question_type, question_text, options, correct_answer, points)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ");

                        foreach ($_POST['questions'] as $index => $question) {
                            $options = null;
                            if ($question['type'] === 'multiple_choice' && !empty($question['options'])) {
                                $options = json_encode(array_filter(array_map('trim', $question['options'])));
                            }

                            $questionStmt->execute([
                                $examId,
                                $question['type'],
                                $question['text'],
                                $options,
                                $question['correct_answer'],
                                $question['points'] ?? 1
                            ]);
                        }
                    }
                    
                    $db->commit();
                    $message = '👍🎉مبروك تم أضافة الأمتحان بنجاح 👍🎉';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    if ($db->inTransaction()) {
                        $db->rollBack();
                    }
                    error_log("Error adding exam: " . $e->getMessage());
                    $message = ' مكنتش أتمني ان تواجه هذا الخطاء من عندي تواصل معي وسوف حاله في أسرع وقت متاسف علي الخطاء غير المتوقع😢: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;

            case 'add_weekly_test':
                try {
                    $db->beginTransaction();
                    
                    // Create weekly test
                    $stmt = $db->prepare("
                        INSERT INTO course_weekly_tests (course_id, week_number, title, description, duration_minutes, created_by)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $courseId,
                        $_POST['week_number'],
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['duration_minutes'] ?? 30,
                        $_SESSION['admin_id']
                    ]);
                    
                    $testId = $db->lastInsertId();
                    
                    // Add questions
                    if (!empty($_POST['questions'])) {
                        $questionStmt = $db->prepare("
                            INSERT INTO course_weekly_test_questions (test_id, question_type, question_text, options, correct_answer, points)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ");

                        foreach ($_POST['questions'] as $index => $question) {
                            $options = null;
                            if ($question['type'] === 'multiple_choice' && !empty($question['options'])) {
                                $options = json_encode(array_filter(array_map('trim', $question['options'])));
                            }

                            $questionStmt->execute([
                                $testId,
                                $question['type'],
                                $question['text'],
                                $options,
                                $question['correct_answer'],
                                $question['points'] ?? 1
                            ]);
                        }
                    }
                    
                    $db->commit();
                    $message = '👍🎉😉 مبروك تم أضافة الأختبار الأسبوعي بنجاح';
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    if ($db->inTransaction()) {
                        $db->rollBack();
                    }
                    error_log("Error adding weekly test: " . $e->getMessage());
                    $message = ' مكنتش أتمني ان تواجه هذا الخطاء من عندي تواصل معي وسوف حاله في أسرع وقت متاسف علي الخطاء غير المتوقع😢: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;

            case 'update_passing_grade':
                try {
                    $passingGrade = floatval($_POST['passing_grade']);
                    if ($passingGrade < 0 || $passingGrade > 100) {
                        throw new Exception('درجة النجاح يجب أن تكون بين 0 و 100');
                    }

                    $stmt = $db->prepare("UPDATE courses SET passing_grade = ? WHERE id = ?");
                    $stmt->execute([$passingGrade, $courseId]);

                    $message = 'تم تحديث درجة النجاح بنجاح';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'خطأ في تحديث درجة النجاح: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get course content
$stmt = $db->prepare("SELECT * FROM course_videos WHERE course_id = ? ORDER BY week_number, video_order");
$stmt->execute([$courseId]);
$videos = $stmt->fetchAll();

$stmt = $db->prepare("SELECT * FROM course_exercises WHERE course_id = ? ORDER BY week_number, exercise_order");
$stmt->execute([$courseId]);
$exercises = $stmt->fetchAll();

$stmt = $db->prepare("SELECT * FROM course_exams WHERE course_id = ? ORDER BY week_number, id");
$stmt->execute([$courseId]);
$exams = $stmt->fetchAll();

$stmt = $db->prepare("SELECT * FROM course_weekly_tests WHERE course_id = ? ORDER BY week_number, id");
$stmt->execute([$courseId]);
$weeklyTests = $stmt->fetchAll();
?>  

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محتوى الكورس - <?php echo htmlspecialchars($course['title']); ?> - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
        <link rel="stylesheet" href="../css/notifications.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>محتوى الكورس: <?php echo htmlspecialchars($course['title']); ?></h1>
                <p>إدارة محتوى الكورس - فيديوهات، تمارين، امتحانات، واختبارات أسبوعية</p>
                <div class="header-actions">
                    <a href="courses.php" class="btn btn-secondary">العودة للكورسات</a>
                </div>
            </div>

            <!-- Course Settings -->
            <div class="course-settings-section">
                <h3>⚙️ إعدادات الكورس</h3>
                <div class="settings-grid">
                    <div class="setting-item">
                        <form method="POST" class="passing-grade-form">
                            <input type="hidden" name="action" value="update_passing_grade">
                            <div class="form-group">
                                <label for="passing_grade">درجة النجاح المطلوبة (%)</label>
                                <div class="input-group">
                                    <input type="number"
                                           id="passing_grade"
                                           name="passing_grade"
                                           value="<?php echo $course['passing_grade'] ?? 60; ?>"
                                           min="0"
                                           max="100"
                                           step="0.1"
                                           required>
                                    <span class="input-suffix">%</span>
                                    <button type="submit" class="btn btn-primary btn-sm">تحديث</button>
                                </div>
                                <small class="form-hint">الدرجة المطلوبة للنجاح في التدريبات والامتحانات</small>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Content Management Tabs -->
            <div class="content-tabs">
                <button class="tab-btn active" onclick="showTab('videos')">الفيديوهات</button>
                <hr class="as"></hr>
                <button class="tab-btn" onclick="showTab('exercises')">التمارين</button>
                <hr class="as"></hr>
                <button class="tab-btn" onclick="showTab('exams')">الامتحانات</button>
                <hr class="as"></hr>
                <button class="tab-btn" onclick="showTab('weekly-tests')">الاختبارات الأسبوعية</button>
            </div>

            <!-- Videos Tab -->
            <div id="videos-tab" class="tab-content active">
                <div class="content-card">
                    <div class="card-header">
                        <h2>إضافة فيديو جديد</h2>
                        <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="toggleForm('video-form')">إضافة فيديو</button>
                    </div>

                    <div id="video-form" class="content-form" style="display: none;">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_video">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="video_week">رقم الأسبوع *</label>
                                    <input type="number" id="video_week" name="week_number" min="1" required>
                                </div>
                                <div class="form-group">
                                    <label for="video_order">ترتيب الفيديو</label>
                                    <input type="number" id="video_order" name="video_order" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="video_title">عنوان الفيديو *</label>
                                    <input type="text" id="video_title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="video_platform">منصة الفيديو</label>
                                    <select id="video_platform" name="video_platform">
                                        <option value="youtube">YouTube</option>
                                        <option value="vimeo">Vimeo</option>
                                        <option value="custom">مخصص</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="video_url">رابط الفيديو *</label>
                                    <input type="url" id="video_url" name="video_url" required>
                                </div>
                                <div class="form-group">
                                    <label for="video_duration">مدة الفيديو (بالدقائق)</label>
                                    <input type="number" id="video_duration" name="duration_minutes" min="0">
                                </div>
                                <div class="form-group">
                                    <label for="video_timing">معلومات التوقيت</label>
                                    <input type="text" id="video_timing" name="timing_info" placeholder="مثال: الأسبوع 1، اليوم 3">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="video_description">وصف الفيديو</label>
                                <textarea id="video_description" name="description" rows="3"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">إضافة الفيديو</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleForm('video-form')">إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Videos List -->
                    <div class="content-list">
                        <h3>الفيديوهات الحالية</h3>
                        <?php if (!empty($videos)): ?>
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>الأسبوع</th>
                                            <th>العنوان</th>
                                            <th>المنصة</th>
                                            <th>المدة</th>
                                            <th>التوقيت</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($videos as $video): ?>
                                            <tr>
                                                <td><?php echo $video['week_number']; ?></td>
                                                <td><?php echo htmlspecialchars($video['title']); ?></td>
                                                <td><?php echo ucfirst($video['video_platform']); ?></td>
                                                <td><?php echo $video['duration_minutes']; ?> دقيقة</td>
                                                <td><?php echo htmlspecialchars($video['timing_info']); ?></td>
                                                <td>
                                                    <a href="<?php echo htmlspecialchars($video['video_url']); ?>" target="_blank" class="btn btn-sm btn-info">عرض</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="empty-message">لا توجد فيديوهات حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Exercises Tab -->
            <div id="exercises-tab" class="tab-content">
                <div class="content-card">
                    <div class="card-header">
                        <h2>إضافة تمرين جديد</h2>
                        <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="toggleForm('exercise-form')">إضافة تمرين</button>
                    </div>

                    <div id="exercise-form" class="content-form" style="display: none;">
                        <form method="POST" onsubmit="return validateExerciseForm()">
                            <input type="hidden" name="action" value="add_exercise">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="exercise_week">رقم الأسبوع *</label>
                                    <input type="number" id="exercise_week" name="week_number" min="1" required>
                                </div>
                                <div class="form-group">
                                    <label for="exercise_order">ترتيب التمرين</label>
                                    <input type="number" id="exercise_order" name="exercise_order" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="exercise_title">عنوان التمرين *</label>
                                    <input type="text" id="exercise_title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="exercise_type">نوع السؤال *</label>
                                    <select id="exercise_type" name="question_type" onchange="toggleExerciseOptions()" required>
                                        <option value="">اختر نوع السؤال</option>
                                        <option value="true_false">صح أم خطأ</option>
                                        <option value="multiple_choice">اختيار متعدد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="exercise_timing">معلومات التوقيت</label>
                                    <input type="text" id="exercise_timing" name="timing_info" placeholder="مثال: الأسبوع 1، اليوم 5">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exercise_description">وصف التمرين</label>
                                <textarea id="exercise_description" name="description" rows="2"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="exercise_question">نص السؤال *</label>
                                <textarea id="exercise_question" name="question_text" rows="3" required></textarea>
                            </div>
                            <!-- True/False Answer Section -->
                            <div id="true_false_answer" class="form-group" style="display: none;">
                                <label>الإجابة الصحيحة *</label>
                                <div class="radio-group">
                                    <label class="radio-option">
                                        <input type="radio" name="correct_answer" value="true">
                                        <span class="radio-custom"></span>
                                        صح
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="correct_answer" value="false">
                                        <span class="radio-custom"></span>
                                        خطأ
                                    </label>
                                </div>
                            </div>

                            <!-- Multiple Choice Options Section -->
                            <div id="exercise_options" class="form-group" style="display: none;">
                                <label>خيارات الإجابة</label>
                                <div id="options_container">
                                    <div class="option-item">
                                        <input type="radio" name="correct_option" value="0" id="correct_0">
                                        <input type="text" name="options[]" placeholder="الخيار الأول" onchange="updateCorrectAnswer()">
                                        <label for="correct_0" class="correct-label">صحيح</label>
                                    </div>
                                    <div class="option-item">
                                        <input type="radio" name="correct_option" value="1" id="correct_1">
                                        <input type="text" name="options[]" placeholder="الخيار الثاني" onchange="updateCorrectAnswer()">
                                        <label for="correct_1" class="correct-label">صحيح</label>
                                    </div>
                                    <div class="option-item">
                                        <input type="radio" name="correct_option" value="2" id="correct_2">
                                        <input type="text" name="options[]" placeholder="الخيار الثالث" onchange="updateCorrectAnswer()">
                                        <label for="correct_2" class="correct-label">صحيح</label>
                                    </div>
                                    <div class="option-item">
                                        <input type="radio" name="correct_option" value="3" id="correct_3">
                                        <input type="text" name="options[]" placeholder="الخيار الرابع" onchange="updateCorrectAnswer()">
                                        <label for="correct_3" class="correct-label">صحيح</label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-secondary" onclick="addOption()">إضافة خيار</button>
                            </div>

                            <!-- Hidden field for correct answer -->
                            <input type="hidden" id="exercise_answer" name="final_correct_answer" required>
                            <div class="form-group">
                                <label for="exercise_explanation">شرح الإجابة</label>
                                <textarea id="exercise_explanation" name="explanation" rows="2"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">إضافة التمرين</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleForm('exercise-form')">إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Exercises List -->
                    <div class="content-list">
                        <h3>التمارين الحالية</h3>
                        <?php if (!empty($exercises)): ?>
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>الأسبوع</th>
                                            <th>العنوان</th>
                                            <th>نوع السؤال</th>
                                            <th>التوقيت</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($exercises as $exercise): ?>
                                            <tr>
                                                <td><?php echo $exercise['week_number']; ?></td>
                                                <td><?php echo htmlspecialchars($exercise['title']); ?></td>
                                                <td><?php echo $exercise['question_type'] === 'true_false' ? 'صح أم خطأ' : 'اختيار متعدد'; ?></td>
                                                <td><?php echo htmlspecialchars($exercise['timing_info']); ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-info" onclick="viewExercise(<?php echo $exercise['id']; ?>)">عرض</button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="empty-message">لا توجد تمارين حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Exams Tab -->
            <div id="exams-tab" class="tab-content">
                <div class="content-card">
                    <div class="card-header">
                        <h2>إضافة امتحان جديد</h2>
                        <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="toggleForm('exam-form')">إضافة امتحان</button>
                    </div>

                    <div id="exam-form" class="content-form" style="display: none;">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_exam">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="exam_week">رقم الأسبوع *</label>
                                    <input type="number" id="exam_week" name="week_number" min="1" required>
                                </div>
                                <div class="form-group">
                                    <label for="exam_order">ترتيب الامتحان</label>
                                    <input type="number" id="exam_order" name="exam_order" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="exam_title">عنوان الامتحان *</label>
                                    <input type="text" id="exam_title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="exam_duration">مدة الامتحان (بالدقائق)</label>
                                    <input type="number" id="exam_duration" name="duration_minutes" min="1" value="60">
                                </div>
                                <div class="form-group">
                                    <label for="exam_passing_score">درجة النجاح</label>
                                    <input type="number" id="exam_passing_score" name="passing_marks" min="0" max="100" value="60">
                                </div>
                                <div class="form-group">
                                    <label for="exam_timing">معلومات التوقيت</label>
                                    <input type="text" id="exam_timing" name="timing_info" placeholder="مثال: نهاية الأسبوع 2">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exam_description">وصف الامتحان</label>
                                <textarea id="exam_description" name="description" rows="2"></textarea>
                            </div>

                            <!-- Questions Section -->
                            <div class="questions-section">
                                <h4>أسئلة الامتحان</h4>
                                <div class="questions-info">
                                    <div class="info-card">
                                        <span class="info-icon">💡</span>
                                        <span class="info-text">يمكنك إضافة أسئلة متعددة للامتحان. كل سؤال له نقاط منفصلة.</span>
                                    </div>
                                </div>
                                <div id="exam_questions_container">
                                    <!-- Questions will be added dynamically -->
                                </div>
                                <div class="questions-actions">
                                    <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="addExamQuestion()">
                                        <span class="btn-icon">➕</span>
                                        إضافة سؤال جديد
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearAllExamQuestions()">
                                        <span class="btn-icon">🗑️</span>
                                        مسح جميع الأسئلة
                                    </button>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">إضافة الامتحان</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleForm('exam-form')">إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Exams List -->
                    <div class="content-list">
                        <h3>الامتحانات الحالية</h3>
                        <?php if (!empty($exams)): ?>
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>الأسبوع</th>
                                            <th>العنوان</th>
                                            <th>المدة</th>
                                            <th>عدد الأسئلة</th>
                                            <th>درجة النجاح</th>
                                            <th>التوقيت</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($exams as $exam): ?>
                                            <tr>
                                                <td><?php echo $exam['week_number']; ?></td>
                                                <td><?php echo htmlspecialchars($exam['title']); ?></td>
                                                <td><?php echo $exam['duration_minutes']; ?> دقيقة</td>
                                                <td>
                                                    <?php
                                                    // Count questions for this exam
                                                    $questionStmt = $db->prepare("SELECT COUNT(*) FROM course_exam_questions WHERE exam_id = ?");
                                                    $questionStmt->execute([$exam['id']]);
                                                    echo $questionStmt->fetchColumn();
                                                    ?>
                                                </td>
                                                <td><?php echo $exam['passing_marks']; ?></td>
                                                <td><?php echo isset($exam['timing_info']) ? htmlspecialchars($exam['timing_info']) : $exam['duration_minutes'] . ' دقيقة'; ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-info" onclick="viewExam(<?php echo $exam['id']; ?>)">عرض</button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="empty-message">لا توجد امتحانات حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- Weekly Tests Tab -->
            <div id="weekly-tests-tab" class="tab-content">
                <div class="content-card">
                    <div class="card-header">
                        <h2>إضافة اختبار أسبوعي جديد</h2>
                        <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="toggleForm('weekly-test-form')">إضافة اختبار</button>
                    </div>

                    <div id="weekly-test-form" class="content-form" style="display: none;">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_weekly_test">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="test_week">رقم الأسبوع *</label>
                                    <input type="number" id="test_week" name="week_number" min="1" required>
                                </div>
                                <div class="form-group">
                                    <label for="test_order">ترتيب الاختبار</label>
                                    <input type="number" id="test_order" name="test_order" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="test_title">عنوان الاختبار *</label>
                                    <input type="text" id="test_title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="test_duration">مدة الاختبار (بالدقائق)</label>
                                    <input type="number" id="test_duration" name="duration_minutes" min="1" value="30">
                                </div>
                                <div class="form-group">
                                    <label for="test_timing">معلومات التوقيت</label>
                                    <input type="text" id="test_timing" name="timing_info" placeholder="مثال: نهاية الأسبوع 1">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="test_description">وصف الاختبار</label>
                                <textarea id="test_description" name="description" rows="2"></textarea>
                            </div>

                            <!-- Questions Section -->
                            <div class="questions-section">
                                <h4>أسئلة الاختبار الأسبوعي</h4>
                                <div class="questions-info">
                                    <div class="info-card">
                                        <span class="info-icon">💡</span>
                                        <span class="info-text">يمكنك إضافة أسئلة متعددة للاختبار الأسبوعي. كل سؤال له نقاط منفصلة.</span>
                                    </div>
                                </div>
                                <div id="test_questions_container">
                                    <!-- Questions will be added dynamically -->
                                </div>
                                <div class="questions-actions">
                                    <button type="button" class="btn btn-primary" onclick="openModal('addModal')" onclick="addTestQuestion()">
                                        <span class="btn-icon">➕</span>
                                        إضافة سؤال جديد
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearAllTestQuestions()">
                                        <span class="btn-icon">🗑️</span>
                                        مسح جميع الأسئلة
                                    </button>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary" onclick="openModal('addModal')">إضافة الاختبار</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleForm('weekly-test-form')">إلغاء</button>
                            </div>
                        </form>
                    </div>

                    <!-- Weekly Tests List -->
                    <div class="content-list">
                        <h3>الاختبارات الأسبوعية الحالية</h3>
                        <?php if (!empty($weeklyTests)): ?>
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>الأسبوع</th>
                                            <th>العنوان</th>
                                            <th>المدة</th>
                                            <th>عدد الأسئلة</th>
                                            <th>التوقيت</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($weeklyTests as $test): ?>
                                            <tr>
                                                <td><?php echo $test['week_number']; ?></td>
                                                <td><?php echo htmlspecialchars($test['title']); ?></td>
                                                <td><?php echo $test['duration_minutes']; ?> دقيقة</td>
                                                <td>
                                                    <?php
                                                    // Count questions for this test
                                                    $questionStmt = $db->prepare("SELECT COUNT(*) FROM course_weekly_test_questions WHERE test_id = ?");
                                                    $questionStmt->execute([$test['id']]);
                                                    echo $questionStmt->fetchColumn();
                                                    ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($test['timing_info']); ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-info" onclick="viewWeeklyTest(<?php echo $test['id']; ?>)">عرض</button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="empty-message">لا توجد اختبارات أسبوعية حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Enhanced Question Styles */
        .enhanced-question {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .enhanced-question.question-complete {
            border-color: #28a745;
            background: #f8fff8;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .question-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-title h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
        }

        .question-status {
            background: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .answer-label {
            display: block;
            margin-bottom: 10px;
        }

        .label-text {
            font-weight: 600;
            color: #495057;
            display: block;
        }

        .label-hint {
            font-size: 12px;
            color: #6c757d;
            font-style: italic;
        }

        .enhanced-radio {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .radio-option:hover {
            border-color: #4682B4;
            background: #f0f8ff;
        }

        .radio-option input[type="radio"]:checked + .radio-custom + .radio-text {
            color: #28a745;
            font-weight: 600;
        }

        .radio-option:has(input[type="radio"]:checked) {
            border-color: #28a745;
            background: #f8fff8;
        }

        .enhanced-option {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
            transition: all 0.3s ease;
        }

        .enhanced-option:hover {
            border-color: #4682B4;
        }

        .enhanced-option:has(input[type="radio"]:checked) {
            border-color: #28a745;
            background: #f8fff8;
        }

        .option-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .correct-indicator {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .enhanced-option:has(input[type="radio"]:checked) .correct-indicator {
            background: #28a745;
            color: white;
        }

        .enhanced-option input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
        }

        .enhanced-option input[type="text"]:focus {
            border-color: #4682B4;
            box-shadow: 0 0 0 0.2rem rgba(70, 130, 180, 0.25);
        }

        .options-actions {
            margin-top: 15px;
            text-align: center;
        }

        .question-validation {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .validation-icon {
            font-size: 16px;
        }

        .btn-icon {
            margin-left: 5px;
        }

        /* Form improvements */
        .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            display: block;
        }

        .form-group input, .form-group textarea, .form-group select {
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            border-color: #4682B4;
            box-shadow: 0 0 0 0.2rem rgba(70, 130, 180, 0.25);
        }

        /* Questions section styling */
        .questions-section {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .questions-info {
            margin-bottom: 20px;
        }

        .info-card {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-icon {
            font-size: 20px;
        }

        .info-text {
            color: #1565c0;
            font-weight: 500;
        }

        .questions-actions {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        /* Button improvements */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            border: none;
        }

        .btn-secondary {
            background: #6c757d;
            border: none;
        }

        .btn-danger {
            background: #dc3545;
            border: none;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        /* Course Settings */
        .course-settings-section {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .course-settings-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .setting-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .passing-grade-form .form-group {
            margin: 0;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 5px;
        }

        .input-group input[type="number"] {
            flex: 1;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }

        .input-suffix {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: 500;
            color: #495057;
        }

        .form-hint {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }

        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    </style>

    <script>
        let examQuestionCount = 0;
        let testQuestionCount = 0;

        function showTab(tabName) {
            // Hide all tabs
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        function toggleForm(formId) {
            const form = document.getElementById(formId);
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }

        function toggleExerciseOptions() {
            const type = document.getElementById('exercise_type').value;
            const optionsDiv = document.getElementById('exercise_options');
            const trueFalseDiv = document.getElementById('true_false_answer');

            if (type === 'multiple_choice') {
                optionsDiv.style.display = 'block';
                trueFalseDiv.style.display = 'none';
                // Clear true/false selection
                document.querySelectorAll('input[name="correct_answer"][type="radio"]').forEach(radio => {
                    radio.checked = false;
                });
                // Setup listeners for multiple choice
                setTimeout(setupCorrectAnswerListeners, 100);
            } else if (type === 'true_false') {
                optionsDiv.style.display = 'none';
                trueFalseDiv.style.display = 'block';
                // Clear multiple choice selection
                document.querySelectorAll('input[name="correct_option"]').forEach(radio => {
                    radio.checked = false;
                });
                document.getElementById('exercise_answer').value = '';
                // Setup listeners for true/false
                document.querySelectorAll('input[name="correct_answer"][type="radio"]').forEach(input => {
                    input.addEventListener('change', function() {
                        const hiddenField = document.getElementById('exercise_answer');
                        if (hiddenField) {
                            hiddenField.value = this.value;
                            console.log('True/False answer set to:', this.value);
                        }
                    });
                });
            } else {
                optionsDiv.style.display = 'none';
                trueFalseDiv.style.display = 'none';
                document.getElementById('exercise_answer').value = '';
            }
        }

        function updateCorrectAnswer() {
            const selectedOption = document.querySelector('input[name="correct_option"]:checked');
            if (selectedOption) {
                const optionIndex = selectedOption.value;
                const optionInputs = document.querySelectorAll('input[name="options[]"]');
                if (optionInputs[optionIndex]) {
                    const optionText = optionInputs[optionIndex].value;
                    const hiddenInput = document.getElementById('exercise_answer');
                    if (hiddenInput) {
                        hiddenInput.value = optionText;
                        console.log('Multiple choice answer set to:', optionText);
                    }
                }
            }
        }

        // Add event listeners for radio buttons to update correct answer
        function setupCorrectAnswerListeners() {
            document.querySelectorAll('input[name="correct_option"]').forEach(radio => {
                radio.addEventListener('change', updateCorrectAnswer);
            });
        }

        function addOption() {
            const container = document.getElementById('options_container');
            const optionCount = container.children.length;

            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item';
            optionDiv.innerHTML = `
                <input type="radio" name="correct_option" value="${optionCount}" id="correct_${optionCount}">
                <input type="text" name="options[]" placeholder="خيار جديد" onchange="updateCorrectAnswer()">
                <label for="correct_${optionCount}" class="correct-label">صحيح</label>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeOption(this)">حذف</button>
            `;

            container.appendChild(optionDiv);

            // Setup event listener for the new radio button
            const newRadio = optionDiv.querySelector('input[type="radio"]');
            newRadio.addEventListener('change', updateCorrectAnswer);
        }

        function removeOption(button) {
            const optionDiv = button.parentElement;
            optionDiv.remove();

            // Update option indices
            const options = document.querySelectorAll('#options_container .option-item');
            options.forEach((option, index) => {
                const radio = option.querySelector('input[type="radio"]');
                const label = option.querySelector('.correct-label');
                radio.value = index;
                radio.id = `correct_${index}`;
                label.setAttribute('for', `correct_${index}`);
            });
        }

        function addExamQuestion() {
            examQuestionCount++;
            const container = document.getElementById('exam_questions_container');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-item enhanced-question';
            questionDiv.setAttribute('data-question-index', examQuestionCount - 1);
            questionDiv.innerHTML = `
                <div class="question-header">
                    <div class="question-title">
                        <h5>السؤال ${examQuestionCount}</h5>
                        <span class="question-status">⚠️ يحتاج إجابة صحيحة</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">
                        <span class="btn-icon">🗑️</span>
                        حذف
                    </button>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>نوع السؤال *</label>
                        <select name="questions[${examQuestionCount-1}][type]" onchange="toggleQuestionOptions(this)" required>
                            <option value="">اختر نوع السؤال</option>
                            <option value="true_false">صح أم خطأ</option>
                            <option value="multiple_choice">اختيار متعدد</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>النقاط</label>
                        <input type="number" name="questions[${examQuestionCount-1}][points]" min="0.5" step="0.5" value="1">
                    </div>
                </div>
                <div class="form-group">
                    <label>نص السؤال *</label>
                    <textarea name="questions[${examQuestionCount-1}][text]" rows="3" placeholder="اكتب نص السؤال هنا..." required></textarea>
                </div>

                <!-- True/False Answer Section -->
                <div class="true-false-answer" style="display: none;">
                    <label class="answer-label">
                        <span class="label-text">الإجابة الصحيحة *</span>
                        <span class="label-hint">اختر الإجابة الصحيحة</span>
                    </label>
                    <div class="radio-group enhanced-radio">
                        <label class="radio-option">
                            <input type="radio" name="questions[${examQuestionCount-1}][correct_answer]" value="true" onchange="updateExamTrueFalseAnswer(${examQuestionCount-1}, 'true')">
                            <span class="radio-custom"></span>
                            <span class="radio-text">✓ صح</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="questions[${examQuestionCount-1}][correct_answer]" value="false" onchange="updateExamTrueFalseAnswer(${examQuestionCount-1}, 'false')">
                            <span class="radio-custom"></span>
                            <span class="radio-text">✗ خطأ</span>
                        </label>
                    </div>
                </div>

                <!-- Multiple Choice Options Section -->
                <div class="question-options" style="display: none;">
                    <label class="answer-label">
                        <span class="label-text">خيارات الإجابة *</span>
                        <span class="label-hint">اكتب الخيارات واختر الصحيح منها</span>
                    </label>
                    <div class="options-container">
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${examQuestionCount-1}][correct_option]" value="0" onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${examQuestionCount-1}][options][]" placeholder="اكتب الخيار الأول..." onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                        </div>
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${examQuestionCount-1}][correct_option]" value="1" onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${examQuestionCount-1}][options][]" placeholder="اكتب الخيار الثاني..." onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                        </div>
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${examQuestionCount-1}][correct_option]" value="2" onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${examQuestionCount-1}][options][]" placeholder="اكتب الخيار الثالث..." onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                        </div>
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${examQuestionCount-1}][correct_option]" value="3" onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${examQuestionCount-1}][options][]" placeholder="اكتب الخيار الرابع..." onchange="updateExamCorrectAnswer(${examQuestionCount-1})">
                        </div>
                    </div>
                    <div class="options-actions">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="addExamOption(${examQuestionCount-1})">
                            <span class="btn-icon">➕</span>
                            إضافة خيار
                        </button>
                    </div>
                </div>

                <!-- Hidden field for correct answer -->
                <input type="hidden" name="questions[${examQuestionCount-1}][correct_answer]" class="correct-answer-field">

                <!-- Question validation message -->
                <div class="question-validation" style="display: none;">
                    <span class="validation-icon">⚠️</span>
                    <span class="validation-text">يرجى تحديد الإجابة الصحيحة</span>
                </div>
            `;
            container.appendChild(questionDiv);
            updateQuestionsCounter('exam');
        }

        function clearAllExamQuestions() {
            customConfirm.show(
                'حذف جميع الأسئلة',
                'هل أنت متأكد من حذف جميع أسئلة الامتحان؟ لن تتمكن من استرجاعها.'
            ).then(confirmed => {
                if (confirmed) {
                    document.getElementById('exam_questions_container').innerHTML = '';
                    examQuestionCount = 0;
                    updateQuestionsCounter('exam');
                }
            });
        }

        function clearAllTestQuestions() {
            customConfirm.show(
                'حذف جميع الأسئلة',
                'هل أنت متأكد من حذف جميع أسئلة الاختبار؟ لن تتمكن من استرجاعها.'
            ).then(confirmed => {
                if (confirmed) {
                    document.getElementById('test_questions_container').innerHTML = '';
                    testQuestionCount = 0;
                    updateQuestionsCounter('test');
                }
            });
        }

        function updateQuestionsCounter(type) {
            const container = type === 'exam' ? 'exam_questions_container' : 'test_questions_container';
            const count = document.getElementById(container).children.length;

            // Update counter display if exists
            const counterElement = document.querySelector(`.${type}-questions-counter`);
            if (counterElement) {
                counterElement.textContent = `عدد الأسئلة: ${count}`;
            }

            // Update total points
            updateTotalPoints();
        }

        function updateTotalPoints() {
            let totalPoints = 0;

            // Calculate exam points
            document.querySelectorAll('#exam_questions_container input[name*="[points]"]').forEach(input => {
                totalPoints += parseFloat(input.value) || 0;
            });

            // Calculate test points
            document.querySelectorAll('#test_questions_container input[name*="[points]"]').forEach(input => {
                totalPoints += parseFloat(input.value) || 0;
            });

            // Update total marks fields
            const examTotalField = document.querySelector('input[name="exam_total_marks"]');
            const testTotalField = document.querySelector('input[name="test_total_marks"]');

            if (examTotalField) {
                examTotalField.value = totalPoints;
            }
            if (testTotalField) {
                testTotalField.value = totalPoints;
            }
        }

        function duplicateQuestion(button, type) {
            const questionItem = button.closest('.question-item');
            const clone = questionItem.cloneNode(true);

            // Update question number and names
            if (type === 'exam') {
                examQuestionCount++;
                updateQuestionNames(clone, examQuestionCount, 'exam');
            } else {
                testQuestionCount++;
                updateQuestionNames(clone, testQuestionCount, 'test');
            }

            // Insert after current question
            questionItem.parentNode.insertBefore(clone, questionItem.nextSibling);
            updateQuestionsCounter(type);
        }

        function updateQuestionNames(element, newIndex, type) {
            // Update question number display
            const questionNumber = element.querySelector('.question-number');
            if (questionNumber) {
                questionNumber.textContent = `السؤال ${newIndex}`;
            }

            // Update all input names
            element.querySelectorAll('input, textarea, select').forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/\[\d+\]/, `[${newIndex-1}]`);
                }
                if (input.id) {
                    input.id = input.id.replace(/_\d+/, `_${newIndex}`);
                }
            });
        }

        function addTestQuestion() {
            testQuestionCount++;
            const container = document.getElementById('test_questions_container');
            const questionDiv = document.createElement('div');
            questionDiv.className = 'question-item';
            questionDiv.innerHTML = `
                <div class="question-header">
                    <h5>السؤال ${testQuestionCount}</h5>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeQuestion(this)">حذف</button>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>نوع السؤال</label>
                        <select name="questions[${testQuestionCount-1}][type]" onchange="toggleQuestionOptions(this)" required>
                            <option value="">اختر نوع السؤال</option>
                            <option value="true_false">صح أم خطأ</option>
                            <option value="multiple_choice">اختيار متعدد</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>النقاط</label>
                        <input type="number" name="questions[${testQuestionCount-1}][points]" min="0.5" step="0.5" value="1">
                    </div>
                </div>
                <div class="form-group">
                    <label>نص السؤال</label>
                    <textarea name="questions[${testQuestionCount-1}][text]" rows="2" required></textarea>
                </div>

                <!-- True/False Answer Section -->
                <div class="true-false-answer" style="display: none;">
                    <label class="answer-label">
                        <span class="label-text">الإجابة الصحيحة *</span>
                        <span class="label-hint">اختر الإجابة الصحيحة</span>
                    </label>
                    <div class="radio-group enhanced-radio">
                        <label class="radio-option">
                            <input type="radio" name="questions[${testQuestionCount-1}][correct_answer]" value="true" onchange="updateTestTrueFalseAnswer(${testQuestionCount-1}, 'true')">
                            <span class="radio-custom"></span>
                            <span class="radio-text">✓ صح</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="questions[${testQuestionCount-1}][correct_answer]" value="false" onchange="updateTestTrueFalseAnswer(${testQuestionCount-1}, 'false')">
                            <span class="radio-custom"></span>
                            <span class="radio-text">✗ خطأ</span>
                        </label>
                    </div>
                </div>

                <!-- Multiple Choice Options Section -->
                <div class="question-options" style="display: none;">
                    <label class="answer-label">
                        <span class="label-text">خيارات الإجابة *</span>
                        <span class="label-hint">اكتب الخيارات واختر الصحيح منها</span>
                    </label>
                    <div class="options-container">
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${testQuestionCount-1}][correct_option]" value="0" onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${testQuestionCount-1}][options][]" placeholder="اكتب الخيار الأول..." onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                        </div>
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${testQuestionCount-1}][correct_option]" value="1" onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${testQuestionCount-1}][options][]" placeholder="اكتب الخيار الثاني..." onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                        </div>
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${testQuestionCount-1}][correct_option]" value="2" onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${testQuestionCount-1}][options][]" placeholder="اكتب الخيار الثالث..." onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                        </div>
                        <div class="option-item enhanced-option">
                            <div class="option-header">
                                <input type="radio" name="questions[${testQuestionCount-1}][correct_option]" value="3" onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                                <label class="correct-indicator">الإجابة الصحيحة</label>
                            </div>
                            <input type="text" name="questions[${testQuestionCount-1}][options][]" placeholder="اكتب الخيار الرابع..." onchange="updateTestCorrectAnswer(${testQuestionCount-1})">
                        </div>
                    </div>
                    <div class="options-actions">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="addTestOption(${testQuestionCount-1})">
                            <span class="btn-icon">➕</span>
                            إضافة خيار
                        </button>
                    </div>
                </div>

                <!-- Hidden field for correct answer -->
                <input type="hidden" name="questions[${testQuestionCount-1}][correct_answer]" class="correct-answer-field">

                <!-- Question validation message -->
                <div class="question-validation" style="display: none;">
                    <span class="validation-icon">⚠️</span>
                    <span class="validation-text">يرجى تحديد الإجابة الصحيحة</span>
                </div>
            `;
            container.appendChild(questionDiv);
            updateQuestionsCounter('test');
        }

        function removeQuestion(button) {
            button.closest('.question-item').remove();
        }

        function toggleQuestionOptions(select) {
            const questionItem = select.closest('.question-item');
            const optionsDiv = questionItem.querySelector('.question-options');
            const trueFalseDiv = questionItem.querySelector('.true-false-answer');

            // Remove required attribute from all option inputs
            const allOptionInputs = questionItem.querySelectorAll('input[name*="[options][]"]');
            allOptionInputs.forEach(input => {
                input.removeAttribute('required');
            });

            if (select.value === 'multiple_choice') {
                optionsDiv.style.display = 'block';
                if (trueFalseDiv) trueFalseDiv.style.display = 'none';
                // Clear true/false selection
                const trueFalseRadios = questionItem.querySelectorAll('input[type="radio"][value="true"], input[type="radio"][value="false"]');
                trueFalseRadios.forEach(radio => radio.checked = false);
            } else if (select.value === 'true_false') {
                optionsDiv.style.display = 'none';
                if (trueFalseDiv) trueFalseDiv.style.display = 'block';
                // Clear multiple choice selection
                const mcRadios = questionItem.querySelectorAll('input[name*="[correct_option]"]');
                mcRadios.forEach(radio => radio.checked = false);
                const hiddenInput = questionItem.querySelector('input[type="hidden"][name*="[correct_answer]"]');
                if (hiddenInput) hiddenInput.value = '';
            } else {
                optionsDiv.style.display = 'none';
                if (trueFalseDiv) trueFalseDiv.style.display = 'none';
            }
        }

        function updateExamCorrectAnswer(questionIndex) {
            const selectedOption = document.querySelector(`input[name="questions[${questionIndex}][correct_option]"]:checked`);
            if (selectedOption) {
                const optionIndex = selectedOption.value;
                const optionInputs = document.querySelectorAll(`input[name="questions[${questionIndex}][options][]"]`);
                const hiddenInput = document.querySelector(`input[type="hidden"][name="questions[${questionIndex}][correct_answer]"]`);

                if (optionInputs[optionIndex] && hiddenInput) {
                    const optionText = optionInputs[optionIndex].value.trim();
                    hiddenInput.value = optionText;
                    console.log(`Exam Question ${questionIndex}: Correct answer set to "${optionText}"`);

                    // Visual feedback
                    optionInputs.forEach((input, index) => {
                        const container = input.closest('.option-item');
                        if (container) {
                            if (index == optionIndex) {
                                container.style.backgroundColor = '#e8f5e8';
                                container.style.border = '2px solid #28a745';
                            } else {
                                container.style.backgroundColor = '';
                                container.style.border = '';
                            }
                        }
                    });
                }
            }
        }

        function updateTestCorrectAnswer(questionIndex) {
            const selectedOption = document.querySelector(`input[name="questions[${questionIndex}][correct_option]"]:checked`);
            if (selectedOption) {
                const optionIndex = selectedOption.value;
                const optionInputs = document.querySelectorAll(`input[name="questions[${questionIndex}][options][]"]`);
                const hiddenInput = document.querySelector(`input[type="hidden"][name="questions[${questionIndex}][correct_answer]"]`);

                if (optionInputs[optionIndex] && hiddenInput) {
                    const optionText = optionInputs[optionIndex].value.trim();
                    hiddenInput.value = optionText;
                    console.log(`Test Question ${questionIndex}: Correct answer set to "${optionText}"`);

                    // Visual feedback
                    optionInputs.forEach((input, index) => {
                        const container = input.closest('.option-item');
                        if (container) {
                            if (index == optionIndex) {
                                container.style.backgroundColor = '#e8f5e8';
                                container.style.border = '2px solid #28a745';
                            } else {
                                container.style.backgroundColor = '';
                                container.style.border = '';
                            }
                        }
                    });

                    // Mark question as complete
                    markQuestionComplete(questionIndex);
                }
            }
        }

        // Mark question as complete when correct answer is set
        function markQuestionComplete(questionIndex) {
            const questionDiv = document.querySelector(`[data-question-index="${questionIndex}"]`);
            if (questionDiv) {
                const statusSpan = questionDiv.querySelector('.question-status');
                const validationDiv = questionDiv.querySelector('.question-validation');

                if (statusSpan) {
                    statusSpan.textContent = '✅ مكتمل';
                    statusSpan.style.color = '#28a745';
                }

                if (validationDiv) {
                    validationDiv.style.display = 'none';
                }

                questionDiv.classList.add('question-complete');
            }
        }

        // Update true/false answer for exam questions
        function updateExamTrueFalseAnswer(questionIndex, value) {
            const hiddenInput = document.querySelector(`input[type="hidden"][name="questions[${questionIndex}][correct_answer]"]`);
            if (hiddenInput) {
                hiddenInput.value = value;
                console.log(`Exam Question ${questionIndex}: True/False answer set to "${value}"`);
                markQuestionComplete(questionIndex);
            }
        }

        // Update true/false answer for test questions
        function updateTestTrueFalseAnswer(questionIndex, value) {
            const hiddenInput = document.querySelector(`input[type="hidden"][name="questions[${questionIndex}][correct_answer]"]`);
            if (hiddenInput) {
                hiddenInput.value = value;
                console.log(`Test Question ${questionIndex}: True/False answer set to "${value}"`);
                markQuestionComplete(questionIndex);
            }
        }

        // Add option for exam questions
        function addExamOption(questionIndex) {
            const container = document.querySelector(`[data-question-index="${questionIndex}"] .options-container`);
            if (container) {
                const optionCount = container.children.length;
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option-item enhanced-option';
                optionDiv.innerHTML = `
                    <div class="option-header">
                        <input type="radio" name="questions[${questionIndex}][correct_option]" value="${optionCount}" onchange="updateExamCorrectAnswer(${questionIndex})">
                        <label class="correct-indicator">الإجابة الصحيحة</label>
                    </div>
                    <input type="text" name="questions[${questionIndex}][options][]" placeholder="اكتب خيار جديد..." onchange="updateExamCorrectAnswer(${questionIndex})">
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeOption(this)">
                        <span class="btn-icon">🗑️</span>
                        حذف
                    </button>
                `;
                container.appendChild(optionDiv);
            }
        }

        // Add option for test questions
        function addTestOption(questionIndex) {
            const container = document.querySelector(`[data-question-index="${questionIndex}"] .options-container`);
            if (container) {
                const optionCount = container.children.length;
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option-item enhanced-option';
                optionDiv.innerHTML = `
                    <div class="option-header">
                        <input type="radio" name="questions[${questionIndex}][correct_option]" value="${optionCount}" onchange="updateTestCorrectAnswer(${questionIndex})">
                        <label class="correct-indicator">الإجابة الصحيحة</label>
                    </div>
                    <input type="text" name="questions[${questionIndex}][options][]" placeholder="اكتب خيار جديد..." onchange="updateTestCorrectAnswer(${questionIndex})">
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeOption(this)">
                        <span class="btn-icon">🗑️</span>
                        حذف
                    </button>
                `;
                container.appendChild(optionDiv);
            }
        }

        // Remove option
        function removeOption(button) {
            const optionDiv = button.closest('.option-item');
            const container = optionDiv.closest('.options-container');
            optionDiv.remove();

            // Update option indices
            const options = container.querySelectorAll('.option-item');
            options.forEach((option, index) => {
                const radio = option.querySelector('input[type="radio"]');
                if (radio) {
                    radio.value = index;
                }
            });
        }

        function viewExercise(id) {
            // Implementation for viewing exercise details
            alert('عرض تفاصيل التمرين رقم: ' + id);
        }

        function viewExam(id) {
            // Implementation for viewing exam details
            alert('عرض تفاصيل الامتحان رقم: ' + id);
        }

        function viewWeeklyTest(id) {
            // Implementation for viewing weekly test details
            alert('عرض تفاصيل الاختبار الأسبوعي رقم: ' + id);
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }, 5000);
            });

            // Setup initial event listeners
            setupCorrectAnswerListeners();

            // Add additional event listeners for form changes
            document.addEventListener('change', function(e) {
                if (e.target.name === 'correct_answer' && e.target.type === 'radio') {
                    const hiddenField = document.getElementById('exercise_answer');
                    if (hiddenField) {
                        hiddenField.value = e.target.value;
                        console.log('Auto-updated hidden field from true/false to:', e.target.value);
                    }
                } else if (e.target.name === 'correct_option') {
                    updateCorrectAnswer();
                }
            });

        // Validate exercise form before submission
        function validateExerciseForm() {
            const questionType = document.getElementById('exercise_type').value;
            const hiddenAnswer = document.getElementById('exercise_answer').value;

            console.log('Validating form - Question type:', questionType);
            console.log('Current hidden answer value:', hiddenAnswer);

            if (!questionType) {
                showNotification('يرجى اختيار نوع السؤال', 'error');
                return false;
            }

            if (questionType === 'true_false') {
                const selectedTrueFalse = document.querySelector('input[name="correct_answer"]:checked');
                console.log('Selected true/false:', selectedTrueFalse);
                if (!selectedTrueFalse) {
                    showNotification('يرجى تحديد الإجابة الصحيحة (صح أم خطأ)', 'error');
                    return false;
                }
                // Update hidden field
                document.getElementById('exercise_answer').value = selectedTrueFalse.value;
                console.log('Updated hidden field to:', selectedTrueFalse.value);
            } else if (questionType === 'multiple_choice') {
                const selectedOption = document.querySelector('input[name="correct_option"]:checked');
                console.log('Selected option:', selectedOption);
                if (!selectedOption) {
                    showNotification('يرجى تحديد الخيار الصحيح', 'error');
                    return false;
                }

                const optionIndex = selectedOption.value;
                const optionInputs = document.querySelectorAll('input[name="options[]"]');
                const optionText = optionInputs[optionIndex]?.value;

                console.log('Option text:', optionText);

                if (!optionText || optionText.trim() === '') {
                    showNotification('يرجى كتابة نص الخيار الصحيح', 'error');
                    return false;
                }

                // Update hidden field
                document.getElementById('exercise_answer').value = optionText;
                console.log('Updated hidden field to:', optionText);
            }

            const finalValue = document.getElementById('exercise_answer').value;
            console.log('Final hidden field value:', finalValue);

            if (!finalValue) {
                showNotification('يجب تحديد الإجابة الصحيحة', 'error');
                return false;
            }

            return true;
        }

        // Simple notification function for admin panel
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                padding: 15px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add form validation for exams and tests
        document.addEventListener('DOMContentLoaded', function() {
            // Validate exam form submission
            const examForm = document.querySelector('#exam-form form');
            if (examForm) {
                examForm.addEventListener('submit', function(e) {
                    const questions = document.querySelectorAll('#exam_questions_container .question-item');
                    if (questions.length === 0) {
                        e.preventDefault();
                        showNotification('يرجى إضافة سؤال واحد على الأقل للامتحان', 'error');
                        return false;
                    }

                    for (let i = 0; i < questions.length; i++) {
                        const questionText = questions[i].querySelector('textarea[name*="[text]"]');
                        const questionType = questions[i].querySelector('select[name*="[type]"]');
                        const hiddenAnswer = questions[i].querySelector('input[type="hidden"][name*="[correct_answer]"]');

                        if (!questionText || !questionText.value.trim()) {
                            e.preventDefault();
                            showNotification(`يرجى كتابة نص السؤال رقم ${i + 1}`, 'error');
                            questionText.focus();
                            return false;
                        }

                        if (!questionType || !questionType.value) {
                            e.preventDefault();
                            showNotification(`يرجى اختيار نوع السؤال رقم ${i + 1}`, 'error');
                            questionType.focus();
                            return false;
                        }

                        // Check for correct answer based on question type
                        let hasCorrectAnswer = false;

                        if (questionType.value === 'true_false') {
                            const trueFalseAnswer = questions[i].querySelector('input[name*="[correct_answer]"]:checked');
                            if (trueFalseAnswer) {
                                hasCorrectAnswer = true;
                                // Update hidden field if not already set
                                if (hiddenAnswer) {
                                    hiddenAnswer.value = trueFalseAnswer.value;
                                }
                            }
                        } else if (questionType.value === 'multiple_choice') {
                            const selectedOption = questions[i].querySelector('input[name*="[correct_option]"]:checked');
                            const optionInputs = questions[i].querySelectorAll('input[name*="[options][]"]');

                            // Check if at least 2 options are filled
                            let filledOptions = 0;
                            optionInputs.forEach(input => {
                                if (input.value.trim() !== '') {
                                    filledOptions++;
                                }
                            });

                            if (filledOptions < 2) {
                                e.preventDefault();
                                showNotification(`يرجى كتابة خيارين على الأقل للسؤال رقم ${i + 1}`, 'error');
                                questions[i].scrollIntoView({ behavior: 'smooth', block: 'center' });
                                return false;
                            }

                            if (selectedOption && hiddenAnswer && hiddenAnswer.value.trim()) {
                                hasCorrectAnswer = true;
                            }
                        }

                        if (!hasCorrectAnswer) {
                            e.preventDefault();
                            showNotification(`يرجى تحديد الإجابة الصحيحة للسؤال رقم ${i + 1}`, 'error');
                            questions[i].scrollIntoView({ behavior: 'smooth', block: 'center' });
                            return false;
                        }

                        console.log(`Question ${i + 1} validation passed. Answer: "${hiddenAnswer ? hiddenAnswer.value : 'N/A'}"`);
                    }

                    console.log('All exam questions validated successfully');
                });
            }

            // Validate weekly test form submission
            const testForm = document.querySelector('#weekly-test-form form');
            if (testForm) {
                testForm.addEventListener('submit', function(e) {
                    const questions = document.querySelectorAll('#test_questions_container .question-item');
                    if (questions.length === 0) {
                        e.preventDefault();
                        showNotification('يرجى إضافة سؤال واحد على الأقل للاختبار', 'error');
                        return false;
                    }

                    for (let i = 0; i < questions.length; i++) {
                        const questionText = questions[i].querySelector('textarea[name*="[text]"]');
                        const questionType = questions[i].querySelector('select[name*="[type]"]');
                        const hiddenAnswer = questions[i].querySelector('input[type="hidden"][name*="[correct_answer]"]');

                        if (!questionText || !questionText.value.trim()) {
                            e.preventDefault();
                            showNotification(`يرجى كتابة نص السؤال رقم ${i + 1}`, 'error');
                            questionText.focus();
                            return false;
                        }

                        if (!questionType || !questionType.value) {
                            e.preventDefault();
                            showNotification(`يرجى اختيار نوع السؤال رقم ${i + 1}`, 'error');
                            questionType.focus();
                            return false;
                        }

                        // Check for correct answer based on question type
                        let hasCorrectAnswer = false;

                        if (questionType.value === 'true_false') {
                            const trueFalseAnswer = questions[i].querySelector('input[name*="[correct_answer]"]:checked');
                            if (trueFalseAnswer) {
                                hasCorrectAnswer = true;
                                // Update hidden field if not already set
                                if (hiddenAnswer) {
                                    hiddenAnswer.value = trueFalseAnswer.value;
                                }
                            }
                        } else if (questionType.value === 'multiple_choice') {
                            const selectedOption = questions[i].querySelector('input[name*="[correct_option]"]:checked');
                            const optionInputs = questions[i].querySelectorAll('input[name*="[options][]"]');

                            // Check if at least 2 options are filled
                            let filledOptions = 0;
                            optionInputs.forEach(input => {
                                if (input.value.trim() !== '') {
                                    filledOptions++;
                                }
                            });

                            if (filledOptions < 2) {
                                e.preventDefault();
                                showNotification(`يرجى كتابة خيارين على الأقل للسؤال رقم ${i + 1}`, 'error');
                                questions[i].scrollIntoView({ behavior: 'smooth', block: 'center' });
                                return false;
                            }

                            if (selectedOption && hiddenAnswer && hiddenAnswer.value.trim()) {
                                hasCorrectAnswer = true;
                            }
                        }

                        if (!hasCorrectAnswer) {
                            e.preventDefault();
                            showNotification(`يرجى تحديد الإجابة الصحيحة للسؤال رقم ${i + 1}`, 'error');
                            questions[i].scrollIntoView({ behavior: 'smooth', block: 'center' });
                            return false;
                        }

                        console.log(`Test Question ${i + 1} validation passed. Answer: "${hiddenAnswer ? hiddenAnswer.value : 'N/A'}"`);
                    }

                    console.log('All test questions validated successfully');
                });
            }
        });
        });
    </script>
    <script src="../js/custom-confirm.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
