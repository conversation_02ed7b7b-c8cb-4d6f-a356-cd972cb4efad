<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    return;
}

require_once 'database.php';
$userManager = new UserManager();

// Get user notes
$notes = $userManager->getUserNotes($_SESSION['user_id']);
$pinnedNotes = array_filter($notes, function($note) { return $note['is_pinned']; });
$regularNotes = array_filter($notes, function($note) { return !$note['is_pinned']; });
?>

<div class="notes-widget">
    <div class="notes-header">
        <h3>الملاحظات</h3>
        <button class="btn-add-note" onclick="showAddNoteModal()">
            <span>📝</span>
            إضافة ملاحظة
        </button>
    </div>

    <div class="notes-stats">
        <div class="note-stat">
            <span class="stat-number"><?php echo count($notes); ?></span>
            <span class="stat-label">إجمالي الملاحظات</span>
        </div>
        <div class="note-stat">
            <span class="stat-number"><?php echo count($pinnedNotes); ?></span>
            <span class="stat-label">ملاحظات مثبتة</span>
        </div>
    </div>

    <div class="notes-content">
        <!-- Pinned Notes -->
        <?php if (!empty($pinnedNotes)): ?>
            <div class="notes-section">
                <h4 class="section-title">📌 ملاحظات مثبتة</h4>
                <div class="notes-grid">
                    <?php foreach ($pinnedNotes as $note): ?>
                        <div class="note-card pinned" data-id="<?php echo $note['id']; ?>">
                            <div class="note-header">
                                <h5 class="note-title"><?php echo htmlspecialchars($note['title']); ?></h5>
                                <div class="note-actions">
                                    <button onclick="toggleNotePin(<?php echo $note['id']; ?>)" class="note-action-btn pin active" title="إلغاء التثبيت">
                                        📌
                                    </button>
                                    <button onclick="editNote(<?php echo $note['id']; ?>)" class="note-action-btn edit" title="تعديل">
                                        ✏️
                                    </button>
                                    <button onclick="deleteNote(<?php echo $note['id']; ?>)" class="note-action-btn delete" title="حذف">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                            <div class="note-content">
                                <?php echo nl2br(htmlspecialchars(substr($note['content'], 0, 150))); ?>
                                <?php if (strlen($note['content']) > 150): ?>
                                    <span class="note-more">...</span>
                                <?php endif; ?>
                            </div>
                            <div class="note-footer">
                                <span class="note-category"><?php echo htmlspecialchars($note['category']); ?></span>
                                <span class="note-date"><?php echo date('Y-m-d H:i', strtotime($note['updated_at'])); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Regular Notes -->
        <?php if (!empty($regularNotes)): ?>
            <div class="notes-section">
                <h4 class="section-title">📄 الملاحظات</h4>
                <div class="notes-grid">
                    <?php foreach ($regularNotes as $note): ?>
                        <div class="note-card" data-id="<?php echo $note['id']; ?>">
                            <div class="note-header">
                                <h5 class="note-title"><?php echo htmlspecialchars($note['title']); ?></h5>
                                <div class="note-actions">
                                    <button onclick="toggleNotePin(<?php echo $note['id']; ?>)" class="note-action-btn pin" title="تثبيت">
                                        📌
                                    </button>
                                    <button onclick="editNote(<?php echo $note['id']; ?>)" class="note-action-btn edit" title="تعديل">
                                        ✏️
                                    </button>
                                    <button onclick="deleteNote(<?php echo $note['id']; ?>)" class="note-action-btn delete" title="حذف">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                            <div class="note-content">
                                <?php echo nl2br(htmlspecialchars(substr($note['content'], 0, 150))); ?>
                                <?php if (strlen($note['content']) > 150): ?>
                                    <span class="note-more">...</span>
                                <?php endif; ?>
                            </div>
                            <div class="note-footer">
                                <span class="note-category"><?php echo htmlspecialchars($note['category']); ?></span>
                                <span class="note-date"><?php echo date('Y-m-d H:i', strtotime($note['updated_at'])); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (empty($notes)): ?>
            <div class="empty-state">
                <span class="empty-icon">📝</span>
                <p>لا توجد ملاحظات</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Note Modal -->
<div id="noteModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="noteModalTitle">إضافة ملاحظة جديدة</h3>
            <button onclick="closeNoteModal()" class="modal-close">×</button>
        </div>
        <form id="noteForm">
            <input type="hidden" id="noteId" name="note_id">
            <div class="form-group">
                <label for="noteTitle" class="form-label">عنوان الملاحظة *</label>
                <input type="text" id="noteTitle" name="title" class="form-input" required>
            </div>
            <div class="form-group">
                <label for="noteCategory" class="form-label">التصنيف</label>
                <select id="noteCategory" name="category" class="form-select">
                    <option value="general">عام</option>
                    <option value="study">دراسة</option>
                    <option value="personal">شخصي</option>
                    <option value="work">عمل</option>
                    <option value="ideas">أفكار</option>
                </select>
            </div>
            <div class="form-group">
                <label for="noteContent" class="form-label">المحتوى *</label>
                <textarea id="noteContent" name="content" class="form-textarea" rows="8" required></textarea>
            </div>
            <div class="modal-actions">
                <button type="submit" class="btn btn-primary">حفظ</button>
                <button type="button" onclick="closeNoteModal()" class="btn btn-secondary">إلغاء</button>
            </div>
        </form>
    </div>
</div>

<style>
.notes-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15);
    border: 2px solid rgba(70, 130, 180, 0.1);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 20px 20px 0 0;
}

.notes-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.25);
    border-color: rgba(135, 206, 235, 0.3);
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
    position: relative;
}

.notes-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 2px;
}

.notes-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 22px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.notes-header h3::before {
    content: '📝';
    font-size: 28px;
    transition: all 0.3s ease;
}

.notes-header h3:hover::before {
    transform: rotate(10deg) scale(1.1);
}

.btn-add-note {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 20px rgba(70, 130, 180, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-add-note::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-add-note:hover::before {
    left: 100%;
}

.btn-add-note:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.4);
}

.btn-add-note:active {
    transform: translateY(-1px) scale(0.98);
}

.btn-add-note span {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.btn-add-note:hover span {
    transform: rotate(90deg) scale(1.1);
}

.notes-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.note-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    flex: 1;
    text-align: center;
    border: 2px solid rgba(70, 130, 180, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.note-stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.note-stat:hover::before {
    transform: scaleX(1);
}

.note-stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.2);
    border-color: rgba(135, 206, 235, 0.3);
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.note-stat:hover .stat-number {
    transform: scale(1.1);
}

.stat-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.notes-section {
    margin-bottom: 35px;
}

.section-title {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title::before {
    content: '📌';
    font-size: 20px;
}

.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
}

.note-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.note-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.note-card:hover::before {
    transform: scaleX(1);
}

.note-card:hover {
    box-shadow: 0 15px 40px rgba(70, 130, 180, 0.2);
    transform: translateY(-5px);
    border-color: rgba(135, 206, 235, 0.3);
}

.note-card.pinned {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-color: rgba(255, 193, 7, 0.3);
}

.note-card.pinned::before {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
}

.note-card.pinned:hover {
    box-shadow: 0 15px 40px rgba(255, 193, 7, 0.3);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.note-title {
    color: #333;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    flex: 1;
    margin-left: 10px;
}

.note-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.note-card:hover .note-actions {
    opacity: 1;
}

.note-action-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.note-action-btn:hover {
    background: rgba(0,0,0,0.1);
}

.note-action-btn.pin.active {
    color: #ffc107;
}

.note-content {
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
    font-size: 14px;
}

.note-more {
    color: #4682B4;
    font-weight: 500;
}

.note-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}

.note-category {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 64px;
    display: block;
    margin-bottom: 20px;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal[style*="flex"] {
    display: flex !important;
}

.modal-content {
    background: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #4682B4;
}

.form-textarea {
    resize: vertical;
    font-family: inherit;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .notes-widget {
        padding: 20px !important;
        margin-bottom: 25px !important;
    }

    .notes-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .notes-header h3 {
        text-align: center;
        font-size: 18px;
    }

    .btn-add-note {
        justify-content: center;
        padding: 12px 20px;
        font-size: 15px;
        min-height: 44px;
    }

    .notes-stats {
        flex-direction: column;
        gap: 12px;
    }

    .note-stat {
        padding: 12px;
        min-height: 60px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 11px;
    }

    .notes-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .note-card {
        padding: 15px;
    }

    .note-title {
        font-size: 15px;
    }

    .note-content {
        font-size: 13px;
        line-height: 1.4;
    }

    .note-footer {
        font-size: 11px;
    }

    .note-actions {
        opacity: 1;
    }

    .note-action-btn {
        padding: 5px;
        min-width: 32px;
        min-height: 32px;
        font-size: 13px;
    }

    .modal-content {
        width: 95%;
        margin: 15px;
        max-height: 95vh;
    }
}

@media (max-width: 480px) {
    .notes-widget {
        padding: 15px !important;
        margin-bottom: 20px !important;
        border-radius: 12px !important;
    }

    .notes-header h3 {
        font-size: 16px;
    }

    .btn-add-note {
        padding: 10px 16px;
        font-size: 14px;
        min-height: 40px;
    }

    .notes-stats {
        gap: 10px;
    }

    .note-stat {
        padding: 10px;
        min-height: 50px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 10px;
    }

    .notes-grid {
        gap: 12px;
    }

    .note-card {
        padding: 12px;
        border-radius: 8px;
    }

    .note-title {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .note-content {
        font-size: 12px;
        margin-bottom: 12px;
    }

    .note-footer {
        font-size: 10px;
    }

    .note-action-btn {
        padding: 4px;
        min-width: 28px;
        min-height: 28px;
        font-size: 12px;
    }

    .empty-state {
        padding: 30px 15px;
    }

    .empty-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .empty-state p {
        font-size: 13px;
    }

    .empty-state .btn {
        padding: 10px 16px;
        font-size: 13px;
        min-height: 40px;
    }
}

@media (max-width: 320px) {
    .notes-widget {
        padding: 12px !important;
        margin-bottom: 15px !important;
    }

    .notes-header h3 {
        font-size: 15px;
    }

    .btn-add-note {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 36px;
    }

    .note-stat {
        padding: 8px;
        min-height: 45px;
    }

    .stat-number {
        font-size: 16px;
    }

    .stat-label {
        font-size: 9px;
    }

    .notes-grid {
        gap: 10px;
    }

    .note-card {
        padding: 10px;
    }

    .note-title {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .note-content {
        font-size: 11px;
        margin-bottom: 10px;
    }

    .note-footer {
        font-size: 9px;
    }

    .note-action-btn {
        padding: 3px;
        min-width: 24px;
        min-height: 24px;
        font-size: 11px;
    }

    .empty-state {
        padding: 25px 10px;
    }

    .empty-icon {
        font-size: 36px;
        margin-bottom: 10px;
    }

    .empty-state p {
        font-size: 12px;
    }

    .empty-state .btn {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 36px;
    }
}
</style>


