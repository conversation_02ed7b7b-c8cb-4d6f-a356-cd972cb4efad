<?php
session_start();
require_once '../includes/database.php';

header('Content-Type: application/json');

// Simulate user session for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Test user ID
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Test data
    $testData = [
        'exam' => ['type' => 'exam', 'id' => 1],
        'weekly_test' => ['type' => 'weekly_test', 'id' => 1]
    ];
    
    $results = [];
    
    foreach ($testData as $name => $data) {
        $type = $data['type'];
        $id = $data['id'];
        $userId = $_SESSION['user_id'];
        
        // Simulate the completion status check
        $result = [
            'success' => true,
            'completed' => false,
            'score' => 0,
            'percentage' => 0,
            'passed' => false,
            'attempt_count' => 0,
            'last_attempt_date' => null,
            'details' => []
        ];
        
        if ($type === 'exam') {
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempt_count,
                       MAX(percentage) as best_percentage,
                       MAX(passed) as passed,
                       MAX(completed_at) as last_attempt_date,
                       MAX(total_score) as best_score,
                       MAX(max_score) as max_score
                FROM user_exam_attempts 
                WHERE user_id = ? AND exam_id = ? AND completed_at IS NOT NULL
            ");
            $stmt->execute([$userId, $id]);
            $exam = $stmt->fetch();
            
            if ($exam && $exam['attempt_count'] > 0) {
                $result['completed'] = true;
                $result['attempt_count'] = intval($exam['attempt_count']);
                $result['percentage'] = floatval($exam['best_percentage']);
                $result['passed'] = (bool)$exam['passed'];
                $result['score'] = floatval($exam['best_score']);
                $result['last_attempt_date'] = $exam['last_attempt_date'];
            }
        } elseif ($type === 'weekly_test') {
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempt_count,
                       MAX(percentage) as best_percentage,
                       MAX(completed_at) as last_attempt_date,
                       MAX(total_score) as best_score,
                       MAX(max_score) as max_score,
                       MAX(passed) as passed
                FROM user_weekly_test_attempts 
                WHERE user_id = ? AND test_id = ? AND completed_at IS NOT NULL
            ");
            $stmt->execute([$userId, $id]);
            $test = $stmt->fetch();
            
            if ($test && $test['attempt_count'] > 0) {
                $result['completed'] = true;
                $result['attempt_count'] = intval($test['attempt_count']);
                $result['percentage'] = floatval($test['best_percentage']);
                $result['passed'] = (bool)$test['passed'] || $result['percentage'] >= 60;
                $result['score'] = floatval($test['best_score']);
                $result['last_attempt_date'] = $test['last_attempt_date'];
            }
        }
        
        $results[$name] = $result;
    }
    
    echo json_encode([
        'success' => true,
        'results' => $results
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
