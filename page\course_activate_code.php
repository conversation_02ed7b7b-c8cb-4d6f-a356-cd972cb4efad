<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Check if user already has access
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
if ($userCourseStatus && $userCourseStatus['activation_status'] === 'active') {
    header('Location: ' . SITE_URL . '/page/course_content.php?id=' . $courseId);
    exit;
}

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['activation_code'])) {
        $activationCode = trim($_POST['activation_code']);
        
        if (empty($activationCode)) {
            $message = 'يرجى إدخال كود التفعيل';
            $messageType = 'error';
        } else {
            // Check if activation code is valid
            $stmt = $db->prepare("
                SELECT * FROM activation_codes 
                WHERE code = ? AND course_id = ? AND is_active = 1 
                AND (expires_at IS NULL OR expires_at > NOW())
                AND used_count < user_limit
            ");
            $stmt->execute([$activationCode, $courseId]);
            $codeData = $stmt->fetch();
            
            if (!$codeData) {
                $message = 'كود التفعيل غير صحيح أو منتهي الصلاحية';
                $messageType = 'error';
            } else {
                try {
                    // Create or update subscription
                    $courseManager->createSubscription($userId, $courseId, 'code');

                    // Activate subscription (this handles its own transaction)
                    $activationResult = $courseManager->activateSubscription($userId, $courseId, 'code', [
                        'activation_code' => $activationCode
                    ]);

                    if ($activationResult) {
                        // Update code usage count
                        $stmt = $db->prepare("UPDATE activation_codes SET used_count = used_count + 1 WHERE id = ?");
                        $stmt->execute([$codeData['id']]);

                        // Redirect to success page
                        header('Location: ' . SITE_URL . '/page/course_activation_success.php?id=' . $courseId . '&method=code');
                        exit;
                    } else {
                        $message = 'فشل في تفعيل الكورس. يرجى المحاولة مرة أخرى';
                        $messageType = 'error';
                    }

                } catch (Exception $e) {
                    error_log("Error activating course: " . $e->getMessage());
                    $message = 'حدث خطأ أثناء تفعيل الكورس. يرجى المحاولة مرة أخرى';
                    $messageType = 'error';
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفعيل بالكود - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="../page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="../page/courses.php">الكورسات</a>
                    <span>/</span>
                    <a href="../page/course_details.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a>
                    <span>/</span>
                    <span>تفعيل بالكود</span>
                </div>

                <div class="activation-container">
                    <!-- Course Info Card -->
                    <div class="course-info-card">
                        <div class="course-image">
                            <?php if ($course['main_image']): ?>
                                <img src="../uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-details">
                            <h3><?php echo htmlspecialchars($course['title']); ?></h3>
                            <p><?php echo htmlspecialchars($course['subject']); ?></p>
                        </div>
                    </div>

                    <!-- Activation Form -->
                    <div class="activation-form-card">
                        <div class="form-header">
                            <div class="activation-icon">🔑</div>
                            <h1>تفعيل الكورس بالكود</h1>
                            <p>أدخل كود التفعيل الذي حصلت عليه</p>
                        </div>

                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" class="activation-form">
                            <div class="form-group">
                                <label for="activation_code">كود التفعيل *</label>
                                <input type="text" 
                                       id="activation_code" 
                                       name="activation_code" 
                                       placeholder="أدخل كود التفعيل هنا"
                                       value="<?php echo isset($_POST['activation_code']) ? htmlspecialchars($_POST['activation_code']) : ''; ?>"
                                       required
                                       autocomplete="off">
                                <small class="form-help">الكود يتكون من أحرف وأرقام</small>
                            </div>

                            <div class="form-group">
                                <label for="username">اسم المستخدم</label>
                                <input type="text" 
                                       id="username" 
                                       name="username" 
                                       value="<?php echo htmlspecialchars($_SESSION['username']); ?>" 
                                       readonly>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large">
                                    <span class="btn-icon">🚀</span>
                                    تفعيل الكورس
                                </button>
                                <a href="../page/course_register.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">↩️</span>
                                    العودة لخيارات التفعيل
                                </a>
                            </div>
                        </form>

                        <!-- Help Section -->
                        <div class="help-section">
                            <h3>🤔 تحتاج مساعدة؟</h3>
                            <div class="help-options">
                                <div class="help-option">
                                    <div class="help-icon">📱</div>
                                    <div class="help-content">
                                        <h4>تواصل عبر الواتساب</h4>
                                        <p>للحصول على كود التفعيل</p>
                                        <a href="https://wa.me/201128031228" target="_blank" class="help-link">
                                            01128031228
                                        </a>
                                    </div>
                                </div>
                                
                                <div class="help-option">
                                    <div class="help-icon">💳</div>
                                    <div class="help-content">
                                        <h4>الدفع والتفعيل</h4>
                                        <p>حول المبلغ واطلب التفعيل</p>
                                        <a href="../page/course_activate_payment.php?id=<?php echo $courseId; ?>" class="help-link">
                                            طلب تفعيل بالدفع
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .activation-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 30px;
        }

        .course-info-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .course-image {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 40px;
            color: white;
        }

        .course-details h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .course-details p {
            color: #6c757d;
            font-size: 16px;
            margin: 0;
        }

        .activation-form-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .activation-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #6c757d;
            font-size: 18px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }

        .form-group input[readonly] {
            background: #f8f9fa;
            color: #6c757d;
        }

        .form-help {
            display: block;
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 40px;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .help-section {
            border-top: 2px solid #f8f9fa;
            padding-top: 30px;
        }

        .help-section h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .help-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .help-option {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .help-option:hover {
            border-color: #87CEEB;
            transform: translateY(-3px);
        }

        .help-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .help-content h4 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .help-content p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .help-link {
            color: #4682B4;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
        }

        .help-link:hover {
            text-decoration: underline;
        }

        .breadcrumb {
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb a {
            color: #4682B4;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #6c757d;
            margin: 0 10px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .activation-container {
                padding: 15px;
            }

            .course-info-card {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .activation-form-card {
                padding: 25px;
            }

            .form-header h1 {
                font-size: 24px;
            }

            .activation-icon {
                font-size: 60px;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }

            .help-options {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // Auto-focus on activation code input
        document.addEventListener('DOMContentLoaded', function() {
            const codeInput = document.getElementById('activation_code');
            if (codeInput) {
                codeInput.focus();
            }
        });

        // Format activation code input (uppercase)
        document.getElementById('activation_code').addEventListener('input', function(e) {
            e.target.value = e.target.value.toUpperCase();
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
