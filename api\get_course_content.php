<?php
header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

if (!isset($_GET['type']) || !isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    $courseManager = new CourseManager();
    $userId = $_SESSION['user_id'];
    $contentType = $_GET['type'];
    $contentId = $_GET['id'];
    
    $content = null;
    $courseId = null;
    
    switch ($contentType) {
        case 'video':
            $stmt = $db->prepare("SELECT * FROM course_videos WHERE id = ? AND is_active = 1");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch();
            $courseId = $content['course_id'] ?? null;
            break;
            
        case 'exercise':
            $stmt = $db->prepare("SELECT * FROM course_exercises WHERE id = ? AND is_active = 1");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch();
            $courseId = $content['course_id'] ?? null;
            break;
            
        case 'exam':
            $stmt = $db->prepare("SELECT * FROM course_exams WHERE id = ? AND is_active = 1");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch();
            $courseId = $content['course_id'] ?? null;
            break;
            
        case 'weekly_test':
            $stmt = $db->prepare("SELECT * FROM course_weekly_tests WHERE id = ? AND is_active = 1");
            $stmt->execute([$contentId]);
            $content = $stmt->fetch();
            $courseId = $content['course_id'] ?? null;
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'نوع المحتوى غير صحيح']);
            exit;
    }
    
    if (!$content) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'المحتوى غير موجود']);
        exit;
    }
    
    // Check if user has access to this course
    if (!$courseManager->userHasAccess($userId, $courseId)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول لهذا المحتوى']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'content' => $content
    ]);
    
} catch (Exception $e) {
    error_log("Error getting course content: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
