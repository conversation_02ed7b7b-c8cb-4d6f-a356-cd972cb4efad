-- إنشاء جدول ملحوظات إكمال الكورسات
CREATE TABLE IF NOT EXISTS course_completion_notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    completion_date DATETIME NOT NULL,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_completion (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_completion_date (completion_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بعض البيانات التجريبية (اختياري)
-- INSERT INTO course_completion_notes (user_id, course_id, completion_date, note) 
-- VALUES (1, 1, NOW(), 'تم إكمال الكورس بنجاح');
