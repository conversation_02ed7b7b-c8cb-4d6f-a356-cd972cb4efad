<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['has_subscription' => false, 'message' => 'غير مسجل دخول']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get user subscription info
    $stmt = $db->prepare("SELECT u.subscription_status, u.subscription_end_date, sp.name as plan_name
                         FROM users u 
                         LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                         WHERE u.id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['has_subscription' => false, 'message' => 'مستخدم غير موجود']);
        exit;
    }
    
    // Check if user has active subscription
    $has_subscription = $user['subscription_status'] === 'active' && 
                       $user['subscription_end_date'] && 
                       strtotime($user['subscription_end_date']) > time();
    
    $response = [
        'has_subscription' => $has_subscription,
        'subscription_status' => $user['subscription_status'],
        'plan_name' => $user['plan_name'],
        'end_date' => $user['subscription_end_date']
    ];
    
    if ($has_subscription) {
        $days_left = ceil((strtotime($user['subscription_end_date']) - time()) / (60 * 60 * 24));
        $response['days_left'] = $days_left;
        $response['message'] = "اشتراك نشط - متبقي {$days_left} يوم";
    } else {
        $response['message'] = 'لا يوجد اشتراك نشط';
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode(['has_subscription' => false, 'message' => 'خطأ في النظام']);
}
?>
