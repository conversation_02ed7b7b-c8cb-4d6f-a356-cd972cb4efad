/**
 * Modern Admin Panel JavaScript
 * Handles all interactive functionality for the admin panel
 */

class ModernAdminPanel {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.loadNotifications();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => this.toggleMobileMenu());
        }

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Notification dropdown
        const notificationBtn = document.getElementById('notificationBtn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleNotificationDropdown();
            });
        }

        // Admin profile dropdown
        const adminProfileBtn = document.getElementById('adminProfileBtn');
        if (adminProfileBtn) {
            adminProfileBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleAdminProfileDropdown();
            });
        }

        // Search functionality
        const headerSearch = document.getElementById('headerSearch');
        if (headerSearch) {
            headerSearch.addEventListener('input', (e) => this.handleSearch(e.target.value));
            headerSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(e.target.value);
                }
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', () => this.closeAllDropdowns());

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
                this.closeAllModals();
            }
        });

        // Window resize handler
        window.addEventListener('resize', () => this.handleResize());
    }

    initializeComponents() {
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize animations
        this.initializeAnimations();
        
        // Initialize charts if available
        this.initializeCharts();
        
        // Set initial states
        this.setInitialStates();
    }

    // Mobile Menu Functions
    toggleMobileMenu() {
        const sidebar = document.getElementById('modernSidebar');
        const overlay = document.querySelector('.mobile-overlay') || this.createMobileOverlay();
        
        if (sidebar) {
            sidebar.classList.toggle('open');
            overlay.classList.toggle('show');
            document.body.classList.toggle('mobile-menu-open');
        }
    }

    createMobileOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'mobile-overlay';
        overlay.addEventListener('click', () => this.closeMobileMenu());
        document.body.appendChild(overlay);
        return overlay;
    }

    closeMobileMenu() {
        const sidebar = document.getElementById('modernSidebar');
        const overlay = document.querySelector('.mobile-overlay');
        
        if (sidebar) {
            sidebar.classList.remove('open');
        }
        if (overlay) {
            overlay.classList.remove('show');
        }
        document.body.classList.remove('mobile-menu-open');
    }

    // Sidebar Functions
    toggleSidebar() {
        const sidebar = document.getElementById('modernSidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
            
            // Save state to localStorage
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        }
    }

    // Notification Functions
    toggleNotificationDropdown() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
            
            if (dropdown.classList.contains('show')) {
                this.loadNotifications();
            }
        }
    }

    async loadNotifications() {
        const notificationList = document.getElementById('notificationList');
        if (!notificationList) return;

        try {
            notificationList.innerHTML = '<div class="loading">جاري التحميل...</div>';
            
            const response = await fetch('api/get_notifications.php');
            const data = await response.json();
            
            if (data.success) {
                this.renderNotifications(data.notifications);
                this.updateNotificationBadge(data.unread_count);
            } else {
                notificationList.innerHTML = '<div class="empty-state">لا توجد إشعارات</div>';
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
            notificationList.innerHTML = '<div class="error">خطأ في تحميل الإشعارات</div>';
        }
    }

    renderNotifications(notifications) {
        const notificationList = document.getElementById('notificationList');
        if (!notificationList) return;

        if (notifications.length === 0) {
            notificationList.innerHTML = '<div class="empty-state">لا توجد إشعارات</div>';
            return;
        }

        const notificationsHTML = notifications.map(notification => `
            <div class="notification-item ${notification.is_read ? '' : 'unread'}" data-id="${notification.id}">
                <div class="notification-content">
                    <h4>${notification.title}</h4>
                    <p>${notification.message}</p>
                    <span class="notification-time">${this.formatTime(notification.created_at)}</span>
                </div>
                ${!notification.is_read ? '<div class="notification-indicator"></div>' : ''}
            </div>
        `).join('');

        notificationList.innerHTML = notificationsHTML;

        // Add click handlers for notifications
        notificationList.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', () => this.markNotificationAsRead(item.dataset.id));
        });
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    async markNotificationAsRead(notificationId) {
        try {
            const response = await fetch('api/mark_notification_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ notification_id: notificationId })
            });

            const data = await response.json();
            if (data.success) {
                this.loadNotifications();
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    async markAllNotificationsRead() {
        try {
            const response = await fetch('api/mark_all_notifications_read.php', {
                method: 'POST'
            });

            const data = await response.json();
            if (data.success) {
                this.loadNotifications();
                this.showToast('تم تحديد جميع الإشعارات كمقروءة', 'success');
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            this.showToast('خطأ في تحديث الإشعارات', 'error');
        }
    }

    // Admin Profile Functions
    toggleAdminProfileDropdown() {
        const dropdown = document.getElementById('adminProfileDropdown');
        const profileBtn = document.getElementById('adminProfileBtn');
        
        if (dropdown && profileBtn) {
            dropdown.classList.toggle('show');
            profileBtn.parentElement.classList.toggle('open');
        }
    }

    // Search Functions
    handleSearch(query) {
        if (query.length < 2) {
            this.hideSearchResults();
            return;
        }

        // Debounce search
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    async performSearch(query) {
        try {
            const response = await fetch(`api/search.php?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.success) {
                this.showSearchResults(data.results);
            }
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    showSearchResults(results) {
        // Implementation for showing search results
        console.log('Search results:', results);
    }

    hideSearchResults() {
        // Implementation for hiding search results
    }

    // Modal Functions
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        const overlay = document.querySelector('.modal-overlay') || this.createModalOverlay();
        
        if (modal) {
            modal.classList.add('show');
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        const overlay = document.querySelector('.modal-overlay');
        
        if (modal) {
            modal.classList.remove('show');
        }
        if (overlay) {
            overlay.classList.remove('show');
        }
        document.body.style.overflow = '';
    }

    closeAllModals() {
        const modals = document.querySelectorAll('.modal.show');
        const overlay = document.querySelector('.modal-overlay');
        
        modals.forEach(modal => modal.classList.remove('show'));
        if (overlay) {
            overlay.classList.remove('show');
        }
        document.body.style.overflow = '';
    }

    createModalOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.addEventListener('click', () => this.closeAllModals());
        document.body.appendChild(overlay);
        return overlay;
    }

    // Utility Functions
    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('.notification-dropdown-menu.show, .admin-profile-dropdown-menu.show');
        dropdowns.forEach(dropdown => dropdown.classList.remove('show'));
        
        const profileDropdown = document.querySelector('.admin-profile-dropdown.open');
        if (profileDropdown) {
            profileDropdown.classList.remove('open');
        }
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return 'الآن';
        if (minutes < 60) return `منذ ${minutes} دقيقة`;
        if (hours < 24) return `منذ ${hours} ساعة`;
        if (days < 7) return `منذ ${days} يوم`;
        
        return date.toLocaleDateString('ar-EG');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span>${message}</span>
                <button class="toast-close">&times;</button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Auto hide after 5 seconds
        setTimeout(() => this.hideToast(toast), 5000);
        
        // Close button handler
        toast.querySelector('.toast-close').addEventListener('click', () => this.hideToast(toast));
    }

    hideToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => this.showTooltip(e));
            element.addEventListener('mouseleave', () => this.hideTooltip());
        });
    }

    showTooltip(event) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = event.target.getAttribute('title');
        
        document.body.appendChild(tooltip);
        
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        // Remove title to prevent default tooltip
        event.target.setAttribute('data-title', event.target.getAttribute('title'));
        event.target.removeAttribute('title');
        
        setTimeout(() => tooltip.classList.add('show'), 100);
    }

    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.classList.remove('show');
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 200);
        }
        
        // Restore title
        const elementWithDataTitle = document.querySelector('[data-title]');
        if (elementWithDataTitle) {
            elementWithDataTitle.setAttribute('title', elementWithDataTitle.getAttribute('data-title'));
            elementWithDataTitle.removeAttribute('data-title');
        }
    }

    initializeAnimations() {
        // Intersection Observer for fade-in animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, { threshold: 0.1 });

        // Observe all cards and stats
        const animatedElements = document.querySelectorAll('.content-card, .stat-card');
        animatedElements.forEach(el => observer.observe(el));
    }

    initializeCharts() {
        // Initialize any charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.createDashboardCharts();
        }
    }

    createDashboardCharts() {
        // Implementation for dashboard charts
        // This would be expanded based on specific chart requirements
    }

    setInitialStates() {
        // Restore sidebar state
        const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (sidebarCollapsed) {
            const sidebar = document.getElementById('modernSidebar');
            const mainContent = document.querySelector('.main-content');
            
            if (sidebar && mainContent) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('sidebar-collapsed');
            }
        }
    }

    handleResize() {
        // Handle responsive behavior
        const width = window.innerWidth;
        
        if (width <= 992) {
            this.closeMobileMenu();
        }
        
        this.closeAllDropdowns();
    }

    startAutoRefresh() {
        // Auto-refresh notifications every 30 seconds
        setInterval(() => {
            this.loadNotifications();
        }, 30000);
    }
}

// Global functions for backward compatibility
function openModal(modalId) {
    if (window.adminPanel) {
        window.adminPanel.openModal(modalId);
    }
}

function closeModal(modalId) {
    if (window.adminPanel) {
        window.adminPanel.closeModal(modalId);
    }
}

function markAllNotificationsRead() {
    if (window.adminPanel) {
        window.adminPanel.markAllNotificationsRead();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new ModernAdminPanel();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernAdminPanel;
}
