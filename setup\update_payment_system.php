<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<title>تحديث نظام الدفع الإلكتروني</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }\n";
echo ".success { color: green; }\n";
echo ".error { color: red; }\n";
echo ".info { color: blue; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>تحديث نظام الدفع الإلكتروني</h1>\n";

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<div class='info'>بدء تحديث قاعدة البيانات...</div><br>\n";
    
    // Read and execute SQL file
    $sqlFile = __DIR__ . '/../sql/payment_transactions.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;
        
        try {
            $db->exec($statement);
            echo "<div class='success'>✓ تم تنفيذ الاستعلام بنجاح</div>\n";
        } catch (PDOException $e) {
            // Check if it's a "column already exists" or "table already exists" error
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate column') !== false) {
                echo "<div class='info'>⚠ العنصر موجود بالفعل - تم التخطي</div>\n";
            } else {
                echo "<div class='error'>✗ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</div>\n";
            }
        }
    }
    
    // Verify tables exist
    echo "<br><div class='info'>التحقق من الجداول...</div><br>\n";
    
    $tables = ['payment_transactions', 'course_subscriptions'];
    foreach ($tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✓ جدول $table موجود</div>\n";
        } else {
            echo "<div class='error'>✗ جدول $table غير موجود</div>\n";
        }
    }
    
    // Check payment_transactions table structure
    echo "<br><div class='info'>التحقق من بنية جدول payment_transactions...</div><br>\n";
    $stmt = $db->prepare("DESCRIBE payment_transactions");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['id', 'transaction_id', 'user_id', 'course_id', 'gateway', 'amount', 'currency', 'status'];
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columns)) {
            echo "<div class='success'>✓ عمود $column موجود</div>\n";
        } else {
            echo "<div class='error'>✗ عمود $column غير موجود</div>\n";
        }
    }
    
    // Check course_subscriptions table updates
    echo "<br><div class='info'>التحقق من تحديثات جدول course_subscriptions...</div><br>\n";
    $stmt = $db->prepare("DESCRIBE course_subscriptions");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $newColumns = ['payment_gateway', 'transaction_id'];
    foreach ($newColumns as $column) {
        if (in_array($column, $columns)) {
            echo "<div class='success'>✓ عمود $column موجود في course_subscriptions</div>\n";
        } else {
            echo "<div class='error'>✗ عمود $column غير موجود في course_subscriptions</div>\n";
        }
    }
    
    echo "<br><div class='success'><h2>✓ تم تحديث نظام الدفع الإلكتروني بنجاح!</h2></div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'><h2>✗ خطأ في التحديث: " . $e->getMessage() . "</h2></div>\n";
}

echo "<br><a href='../page/dashboard.php'>العودة للوحة التحكم</a>\n";
echo "</body>\n";
echo "</html>\n";
?>
