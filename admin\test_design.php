<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>اختبار التصميم الجديد</h1>
                <p>صفحة تجريبية لاختبار جميع عناصر التصميم الحديث</p>
            </div>

            <!-- Test Cards -->
            <div class="stats-grid">
                <div class="stat-card enhanced-card">
                    <div class="stat-number">150</div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                    <div class="stat-change positive">+12% من الشهر الماضي</div>
                </div>

                <div class="stat-card enhanced-card">
                    <div class="stat-number">45</div>
                    <div class="stat-label">الكورسات النشطة</div>
                    <div class="stat-change positive">+8% من الأسبوع الماضي</div>
                </div>

                <div class="stat-card enhanced-card">
                    <div class="stat-number">23</div>
                    <div class="stat-label">طلبات جديدة</div>
                    <div class="stat-change neutral">نفس معدل أمس</div>
                </div>

                <div class="stat-card enhanced-card">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">معدل الرضا</div>
                    <div class="stat-change positive">+2% من الشهر الماضي</div>
                </div>
            </div>

            <!-- Test Form -->
            <div class="content-card enhanced-card">
                <div class="card-header">
                    <h2>نموذج تجريبي</h2>
                    <p>اختبار عناصر النموذج الحديثة</p>
                </div>
                <div class="card-body">
                    <form>
                        <div class="form-row">
                            <div class="form-floating">
                                <input type="text" id="testName" placeholder=" ">
                                <label for="testName">الاسم</label>
                            </div>
                            <div class="form-floating">
                                <input type="email" id="testEmail" placeholder=" ">
                                <label for="testEmail">البريد الإلكتروني</label>
                            </div>
                        </div>
                        
                        <div class="form-floating">
                            <textarea id="testMessage" rows="4" placeholder=" "></textarea>
                            <label for="testMessage">الرسالة</label>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-gradient">إرسال</button>
                            <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Test Alerts -->
            <div class="alert alert-success">
                <strong>نجح!</strong> تم حفظ البيانات بنجاح.
            </div>
            
            <div class="alert alert-warning">
                <strong>تحذير!</strong> يرجى مراجعة البيانات المدخلة.
            </div>
            
            <div class="alert alert-error">
                <strong>خطأ!</strong> حدث خطأ أثناء معالجة الطلب.
            </div>

            <!-- Test Table -->
            <div class="content-card enhanced-card">
                <div class="card-header">
                    <h2>جدول تجريبي</h2>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد</td>
                                <td><EMAIL></td>
                                <td><span class="badge badge-success">نشط</span></td>
                                <td>2024-01-15</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">تعديل</button>
                                    <button class="btn btn-sm btn-danger">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>فاطمة علي</td>
                                <td><EMAIL></td>
                                <td><span class="badge badge-warning">معلق</span></td>
                                <td>2024-01-14</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">تعديل</button>
                                    <button class="btn btn-sm btn-danger">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
