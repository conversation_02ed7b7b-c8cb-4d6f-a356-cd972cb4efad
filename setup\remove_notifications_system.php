<?php
/**
 * Remove Notifications System Script
 * This script removes the notifications system and related components
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

class NotificationsRemover {
    private $db;
    private $errors = [];
    private $success = [];
    
    public function __construct() {
        try {
            $this->db = Database::getInstance()->getConnection();
        } catch (Exception $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function remove() {
        echo "<h2>إزالة نظام الإشعارات...</h2>\n";
        
        // Drop notifications table
        $this->dropNotificationsTable();
        
        // Update other tables to remove notification-related code
        $this->cleanupTables();
        
        // Display results
        $this->displayResults();
        
        return empty($this->errors);
    }
    
    private function dropNotificationsTable() {
        echo "<h3>حذف جدول الإشعارات...</h3>\n";
        
        try {
            // Disable foreign key checks temporarily
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Drop the notifications table
            $this->db->exec("DROP TABLE IF EXISTS message_notifications");
            $this->success[] = "تم حذف جدول message_notifications";
            
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 1");
            
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في حذف جدول الإشعارات: " . $e->getMessage();
        }
    }
    
    private function cleanupTables() {
        echo "<h3>تنظيف الجداول الأخرى...</h3>\n";
        
        // No additional cleanup needed for now
        $this->success[] = "تم تنظيف الجداول الأخرى";
    }
    
    private function displayResults() {
        echo "<h3>نتائج العملية:</h3>\n";
        
        if (!empty($this->success)) {
            echo "<div style='color: green; background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>✅ نجح:</h4>\n";
            foreach ($this->success as $message) {
                echo "<p>• {$message}</p>\n";
            }
            echo "</div>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<div style='color: red; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>❌ أخطاء:</h4>\n";
            foreach ($this->errors as $error) {
                echo "<p>• {$error}</p>\n";
            }
            echo "</div>\n";
        }
        
        if (empty($this->errors)) {
            echo "<div style='color: blue; background: #cce7ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4>🎉 تم إزالة نظام الإشعارات بنجاح!</h4>\n";
            echo "<p>تم حذف جدول الإشعارات وتنظيف النظام.</p>\n";
            echo "<p><strong>الخطوات التالية:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>تم التركيز على التواصل عبر مواقع التواصل الاجتماعي</li>\n";
            echo "<li>تم تحسين صفحة 'اسأل معلم' للتواصل المباشر</li>\n";
            echo "<li>سيتم إضافة التواصل عبر المنصة قريباً</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }
    }
}

// Run the removal if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'remove_notifications_system.php') {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إزالة نظام الإشعارات - <?php echo SITE_NAME; ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .btn { background: #4682B4; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
            .btn:hover { background: #357abd; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>إزالة نظام الإشعارات</h1>
            
            <?php
            $remover = new NotificationsRemover();
            $success = $remover->remove();
            
            if ($success) {
                echo "<p style='text-align: center; margin-top: 20px;'>";
                echo "<a href='../page/ask_teacher.php' class='btn'>الذهاب إلى صفحة اسأل معلم</a>";
                echo "</p>";
            }
            ?>
        </div>
    </body>
    </html>
    <?php
}
?>
