<?php
/**
 * Message Operations API
 * Handles AJAX requests for message operations
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/MessageManager.php';

header('Content-Type: application/json');

// Check if user is logged in (admin or student)
if (!isset($_SESSION['user_id']) && !isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$messageManager = new MessageManager();
$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'send_message':
            if (!isset($_SESSION['user_id'])) {
                throw new Exception('يجب تسجيل الدخول كطالب');
            }
            
            $subject = trim($_POST['subject'] ?? '');
            $message = trim($_POST['message'] ?? '');
            $messageType = $_POST['message_type'] ?? 'question';
            $priority = $_POST['priority'] ?? 'medium';
            $categoryId = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
            
            if (empty($subject) || empty($message)) {
                throw new Exception('الموضوع والرسالة مطلوبان');
            }
            
            if (strlen($subject) > 255) {
                throw new Exception('الموضوع طويل جداً');
            }
            
            if (strlen($message) > 5000) {
                throw new Exception('الرسالة طويلة جداً');
            }
            
            $messageId = $messageManager->createMessage(
                $_SESSION['user_id'],
                $subject,
                $message,
                $messageType,
                $priority,
                $categoryId
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إرسال الرسالة بنجاح',
                'message_id' => $messageId
            ]);
            break;
            
        case 'send_reply':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $messageId = (int)($_POST['message_id'] ?? 0);
            $replyText = trim($_POST['reply_text'] ?? '');
            $isPublic = isset($_POST['is_public']) && $_POST['is_public'] === '1';
            
            if (!$messageId || empty($replyText)) {
                throw new Exception('معرف الرسالة ونص الرد مطلوبان');
            }
            
            $result = $messageManager->createReply(
                $messageId,
                $_SESSION['admin_id'],
                $replyText,
                $isPublic
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إرسال الرد بنجاح'
            ]);
            break;
            
        case 'update_status':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $messageId = (int)($_POST['message_id'] ?? 0);
            $newStatus = $_POST['new_status'] ?? '';
            
            if (!$messageId || empty($newStatus)) {
                throw new Exception('معرف الرسالة والحالة الجديدة مطلوبان');
            }
            
            $validStatuses = ['pending', 'read', 'replied', 'closed'];
            if (!in_array($newStatus, $validStatuses)) {
                throw new Exception('حالة غير صحيحة');
            }
            
            $result = $messageManager->updateMessageStatus($messageId, $newStatus);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تحديث حالة الرسالة بنجاح'
                ]);
            } else {
                throw new Exception('فشل في تحديث حالة الرسالة');
            }
            break;
            
        case 'mark_as_read':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $messageId = (int)($_POST['message_id'] ?? 0);
            
            if ($messageId) {
                $messageManager->markNotificationsAsRead($_SESSION['admin_id'], $messageId);
                $messageManager->updateMessageStatus($messageId, 'read');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديد الرسالة كمقروءة'
            ]);
            break;
            
        case 'get_unread_count':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $count = $messageManager->getUnreadMessageCount($_SESSION['admin_id']);
            
            echo json_encode([
                'success' => true,
                'count' => $count
            ]);
            break;
            
        case 'get_statistics':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $stats = $messageManager->getStatistics();
            
            echo json_encode([
                'success' => true,
                'statistics' => $stats
            ]);
            break;
            
        case 'get_categories':
            $categories = $messageManager->getCategories();
            
            echo json_encode([
                'success' => true,
                'categories' => $categories
            ]);
            break;
            
        case 'get_templates':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $categoryId = !empty($_GET['category_id']) ? (int)$_GET['category_id'] : null;
            $templates = $messageManager->getTemplates($categoryId);
            
            echo json_encode([
                'success' => true,
                'templates' => $templates
            ]);
            break;
            
        case 'get_message':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $messageId = (int)($_GET['message_id'] ?? 0);
            
            if (!$messageId) {
                throw new Exception('معرف الرسالة مطلوب');
            }
            
            $message = $messageManager->getMessageWithReplies($messageId);
            
            if (!$message) {
                throw new Exception('الرسالة غير موجودة');
            }
            
            echo json_encode([
                'success' => true,
                'message' => $message
            ]);
            break;
            
        case 'search_messages':
            if (!isset($_SESSION['admin_id'])) {
                throw new Exception('يجب تسجيل الدخول كمدير');
            }
            
            $filters = [
                'status' => $_GET['status'] ?? '',
                'category' => $_GET['category'] ?? '',
                'priority' => $_GET['priority'] ?? '',
                'search' => $_GET['search'] ?? ''
            ];
            
            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = max(1, min(100, (int)($_GET['per_page'] ?? 20)));
            
            $result = $messageManager->getMessages($filters, $page, $perPage);
            
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        default:
            throw new Exception('عملية غير صحيحة');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
