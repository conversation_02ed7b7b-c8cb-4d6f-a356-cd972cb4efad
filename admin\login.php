<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

$error = '';
$success = '';

// Check if admin is already logged in
if (isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $usernameOrEmail = trim($_POST['username_email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($usernameOrEmail) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم/البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $adminManager = new AdminManager();
            $admin = $adminManager->authenticateAdmin($usernameOrEmail, $password);

            if ($admin) {
                // Set session variables
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_email'] = $admin['email'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_login_time'] = time();

                $success = 'تم تسجيل الدخول بنجاح';
                header('Location: index.php');
                exit;
            } else {
                $error = 'اسم المستخدم/البريد الإلكتروني أو كلمة المرور غير صحيحة';
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء تسجيل الدخول: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول المدير - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .admin-login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }
        
        .admin-login-box {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .admin-login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .admin-login-header img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
        }
        
        .admin-login-header h1 {
            color: #333;
            font-size: 24px;
            margin: 0 0 10px;
        }
        
        .admin-login-header p {
            color: #666;
            margin: 0;
        }
        
        .admin-form-group {
            margin-bottom: 20px;
        }
        
        .admin-form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .admin-form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .admin-form-input:focus {
            outline: none;
            border-color: #4682B4;
        }
        
        .admin-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .admin-btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .back-to-site {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-to-site a {
            color: #4682B4;
            text-decoration: none;
            font-size: 14px;
        }
        
        .back-to-site a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="admin-login-container">
        <div class="admin-login-box">
            <div class="admin-login-header">
                <img src="../img/logo-b.png" alt="شعار سلسلة الدكتور">
                <h1>لوحة تحكم المدير</h1>
                <p>تسجيل الدخول إلى نظام الإدارة</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>

            <form method="POST">
                <div class="admin-form-group">
                    <label for="username_email" class="admin-form-label">اسم المستخدم أو البريد الإلكتروني</label>
                    <input
                        type="text"
                        id="username_email"
                        name="username_email"
                        class="admin-form-input"
                        required
                        placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                        value="<?php echo htmlspecialchars($_POST['username_email'] ?? ''); ?>"
                    >
                </div>

                <div class="admin-form-group">
                    <label for="password" class="admin-form-label">كلمة المرور</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="admin-form-input"
                        required
                        placeholder="أدخل كلمة المرور"
                    >
                </div>

                <button type="submit" class="admin-btn">
                    تسجيل الدخول
                </button>
            </form>

            <div class="back-to-site">
                <a href="../index.php">العودة إلى الموقع الرئيسي</a>
            </div>
        </div>
    </div>
</body>
</html>
