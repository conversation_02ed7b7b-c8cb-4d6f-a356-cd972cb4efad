// Dashboard JavaScript Functions - FIXED VERSION
// Site URL definition - use relative path
const SITE_BASE = '..';

let currentEditingTodo = null;
let currentEditingNote = null;

// Todo functions
function showAddTodoModal() {
    console.log('showAddTodoModal called');
    const modal = document.getElementById('todoModal');
    console.log('Todo modal found:', modal);
    if (modal) {
        const title = document.getElementById('todoModalTitle');
        const form = document.getElementById('todoForm');
        const todoId = document.getElementById('todoId');

        console.log('Elements found:', { title, form, todoId });

        if (title) title.textContent = 'إضافة مهمة جديدة';
        if (form) form.reset();
        if (todoId) todoId.value = '';

        currentEditingTodo = null;
        
        // Show modal with proper styling
        modal.style.display = 'flex';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.classList.add('modal-open');
        
        console.log('Todo modal displayed, style:', modal.style.display);
        console.log('Modal classes:', modal.className);
    }
}

function showEditTodoModal(todoId, title, description, priority, dueDate) {
    console.log('showEditTodoModal called with:', { todoId, title, description, priority, dueDate });
    const modal = document.getElementById('todoModal');
    if (modal) {
        const modalTitle = document.getElementById('todoModalTitle');
        const form = document.getElementById('todoForm');
        const todoIdInput = document.getElementById('todoId');
        const titleInput = document.getElementById('todoTitle');
        const descInput = document.getElementById('todoDescription');
        const prioritySelect = document.getElementById('todoPriority');
        const dueDateInput = document.getElementById('todoDueDate');

        if (modalTitle) modalTitle.textContent = 'تعديل المهمة';
        if (todoIdInput) todoIdInput.value = todoId;
        if (titleInput) titleInput.value = title;
        if (descInput) descInput.value = description;
        if (prioritySelect) prioritySelect.value = priority;
        if (dueDateInput) dueDateInput.value = dueDate;

        currentEditingTodo = todoId;
        
        // Show modal
        modal.style.display = 'flex';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.classList.add('modal-open');
    }
}

function closeTodoModal() {
    const modal = document.getElementById('todoModal');
    if (modal) {
        modal.style.display = 'none';
        modal.style.visibility = 'hidden';
        modal.style.opacity = '0';
        modal.classList.remove('modal-open');
    }
}

function toggleTodo(todoId) {
    console.log('Toggling todo:', todoId);
    fetch(SITE_BASE + '/page/todo_actions.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'toggle', todo_id: todoId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            refreshTodoWidget();
        } else {
            console.error('Toggle todo error:', data.message);
        }
    })
    .catch(error => {
        console.error('Toggle todo error:', error);
    });
}

function deleteTodo(todoId) {
    console.log('Deleting todo:', todoId);
    
    // Custom confirmation
    const confirmTitle = 'حذف المهمة';
    const confirmMessage = 'هل أنت متأكد من أنك تريد حذف هذه المهمة؟ لن تتمكن من استرجاعها.';
    
    confirmDelete().then(confirmed => {
        if (confirmed) {
            fetch(SITE_BASE + '/page/todo_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'delete', todo_id: todoId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    refreshTodoWidget();
                } else {
                    console.error('Delete todo error:', data.message);
                }
            })
            .catch(error => {
                console.error('Delete todo error:', error);
            });
        }
    });
}

function editTodo(todoId) {
    // Get todo data from the DOM or make an API call
    const todoElement = document.querySelector(`[data-todo-id="${todoId}"]`);
    if (todoElement) {
        const title = todoElement.querySelector('.todo-title')?.textContent || '';
        const description = todoElement.querySelector('.todo-description')?.textContent || '';
        const priority = todoElement.dataset.priority || 'medium';
        const dueDate = todoElement.dataset.dueDate || '';
        
        showEditTodoModal(todoId, title, description, priority, dueDate);
    }
}

// Note functions
function showAddNoteModal() {
    console.log('showAddNoteModal called');
    const modal = document.getElementById('noteModal');
    console.log('Note modal found:', modal);
    if (modal) {
        const title = document.getElementById('noteModalTitle');
        const form = document.getElementById('noteForm');
        const noteId = document.getElementById('noteId');

        console.log('Elements found:', { title, form, noteId });

        if (title) title.textContent = 'إضافة ملاحظة جديدة';
        if (form) form.reset();
        if (noteId) noteId.value = '';

        currentEditingNote = null;
        
        // Show modal
        modal.style.display = 'flex';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.classList.add('modal-open');
        
        console.log('Note modal displayed, style:', modal.style.display);
        console.log('Modal classes:', modal.className);
    }
}

function showEditNoteModal(noteId, title, content, category) {
    console.log('showEditNoteModal called with:', { noteId, title, content, category });
    const modal = document.getElementById('noteModal');
    if (modal) {
        const modalTitle = document.getElementById('noteModalTitle');
        const form = document.getElementById('noteForm');
        const noteIdInput = document.getElementById('noteId');
        const titleInput = document.getElementById('noteTitle');
        const contentInput = document.getElementById('noteContent');
        const categorySelect = document.getElementById('noteCategory');

        if (modalTitle) modalTitle.textContent = 'تعديل الملاحظة';
        if (noteIdInput) noteIdInput.value = noteId;
        if (titleInput) titleInput.value = title;
        if (contentInput) contentInput.value = content;
        if (categorySelect) categorySelect.value = category;

        currentEditingNote = noteId;
        
        // Show modal
        modal.style.display = 'flex';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.classList.add('modal-open');
    }
}

function closeNoteModal() {
    const modal = document.getElementById('noteModal');
    if (modal) {
        modal.style.display = 'none';
        modal.style.visibility = 'hidden';
        modal.style.opacity = '0';
        modal.classList.remove('modal-open');
    }
}

function toggleNotePin(noteId) {
    console.log('Toggling note pin:', noteId);
    fetch(SITE_BASE + '/page/note_actions.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'toggle_pin', note_id: noteId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            refreshNotesWidget();
        } else {
            console.error('Toggle note pin error:', data.message);
        }
    })
    .catch(error => {
        console.error('Toggle note pin error:', error);
    });
}

function deleteNote(noteId) {
    console.log('Deleting note:', noteId);
    
    const confirmTitle = 'حذف الملاحظة';
    const confirmMessage = 'هل أنت متأكد من أنك تريد حذف هذه الملاحظة؟ لن تتمكن من استرجاعها.';
    
    confirmDelete().then(confirmed => {
        if (confirmed) {
            fetch(SITE_BASE + '/page/note_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'delete', note_id: noteId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    refreshNotesWidget();
                } else {
                    console.error('Delete note error:', data.message);
                }
            })
            .catch(error => {
                console.error('Delete note error:', error);
            });
        }
    });
}

function editNote(noteId) {
    // Get note data from the DOM
    const noteElement = document.querySelector(`[data-note-id="${noteId}"]`);
    if (noteElement) {
        const title = noteElement.querySelector('.note-title')?.textContent || '';
        const content = noteElement.querySelector('.note-content')?.textContent || '';
        const category = noteElement.dataset.category || 'general';

        showEditNoteModal(noteId, title, content, category);
    }
}

// Form submission handlers
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up dashboard');

    // Todo form handler
    const todoForm = document.getElementById('todoForm');
    if (todoForm) {
        console.log('Todo form found, adding event listener');
        todoForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Todo form submitted');

            const formData = new FormData(this);
            const todoId = formData.get('todo_id');

            const data = {
                action: todoId ? 'update' : 'create',
                todo_id: todoId,
                title: formData.get('title'),
                description: formData.get('description'),
                priority: formData.get('priority'),
                due_date: formData.get('due_date')
            };

            console.log('Sending todo data:', data);

            fetch(SITE_BASE + '/page/todo_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => {
                console.log('Todo API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Todo response:', data);
                if (data.success) {
                    closeTodoModal();
                    refreshTodoWidget();
                    showSuccessMessage(todoId ? 'تم تحديث المهمة بنجاح' : 'تم إضافة المهمة بنجاح');
                } else {
                    showErrorMessage(data.message || 'حدث خطأ أثناء حفظ المهمة');
                }
            })
            .catch(error => {
                console.error('Todo error:', error);
                showErrorMessage('حدث خطأ في الاتصال: ' + error.message);
            });
        });
    }

    // Note form handler
    const noteForm = document.getElementById('noteForm');
    if (noteForm) {
        console.log('Note form found, adding event listener');
        noteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Note form submitted');

            const formData = new FormData(this);
            const noteId = formData.get('note_id');

            const data = {
                action: noteId ? 'update' : 'create',
                note_id: noteId,
                title: formData.get('title'),
                content: formData.get('content'),
                category: formData.get('category')
            };

            console.log('Sending note data:', data);

            fetch(SITE_BASE + '/page/note_actions.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => {
                console.log('Note API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Note response:', data);
                if (data.success) {
                    closeNoteModal();
                    refreshNotesWidget();
                    showSuccessMessage(noteId ? 'تم تحديث الملاحظة بنجاح' : 'تم إضافة الملاحظة بنجاح');
                } else {
                    showErrorMessage(data.message || 'حدث خطأ أثناء حفظ الملاحظة');
                }
            })
            .catch(error => {
                console.error('Note error:', error);
                showErrorMessage('حدث خطأ في الاتصال: ' + error.message);
            });
        });
    }

    console.log('Dashboard initialization complete');
});

// Utility functions
function refreshTodoWidget() {
    console.log('Refreshing todo widget');
    fetch(SITE_BASE + '/includes/todo_widget.php')
        .then(response => response.text())
        .then(html => {
            const widget = document.querySelector('.todo-widget-content');
            if (widget) {
                widget.innerHTML = html;
            }
        })
        .catch(error => console.error('Error refreshing todo widget:', error));
}

function refreshNotesWidget() {
    console.log('Refreshing notes widget');
    fetch(SITE_BASE + '/includes/notes_widget.php')
        .then(response => response.text())
        .then(html => {
            const widget = document.querySelector('.notes-widget-content');
            if (widget) {
                widget.innerHTML = html;
            }
        })
        .catch(error => console.error('Error refreshing notes widget:', error));
}

function showSuccessMessage(message) {
    // Create or update success message
    let messageDiv = document.getElementById('success-message');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'success-message';
        messageDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 15px; border-radius: 5px; z-index: 9999;';
        document.body.appendChild(messageDiv);
    }
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';

    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}

function showErrorMessage(message) {
    // Create or update error message
    let messageDiv = document.getElementById('error-message');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'error-message';
        messageDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #dc3545; color: white; padding: 15px; border-radius: 5px; z-index: 9999;';
        document.body.appendChild(messageDiv);
    }
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';

    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

function confirmDelete() {
    return new Promise((resolve) => {
        if (confirm('هل أنت متأكد من أنك تريد الحذف؟')) {
            resolve(true);
        } else {
            resolve(false);
        }
    });
}
