<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/PaymentGateway.php';

class FawryGateway extends PaymentGateway {
    private $config;
    
    public function __construct() {
        parent::__construct();
        $this->config = $this->getGatewayConfig('fawry');
    }
    
    /**
     * Create Fawry payment request
     */
    public function createPaymentRequest($userId, $courseId, $amount, $customerInfo) {
        try {
            // Create transaction record
            $transactionId = $this->createTransaction($userId, $courseId, 'fawry', $amount);
            if (!$transactionId) {
                throw new Exception('Failed to create transaction record');
            }
            
            // Prepare Fawry payment data
            $paymentData = [
                'merchantCode' => $this->config['merchant_code'],
                'merchantRefNumber' => $transactionId,
                'customerMobile' => $customerInfo['mobile'],
                'customerEmail' => $customerInfo['email'],
                'customerName' => $customerInfo['name'],
                'paymentAmount' => $amount,
                'currencyCode' => 'EGP',
                'description' => 'Course Payment - ' . $customerInfo['course_title'],
                'paymentExpiry' => date('Y-m-d H:i:s', strtotime('+24 hours')),
                'returnUrl' => $this->config['return_url'] . '?transaction_id=' . $transactionId,
                'chargeItems' => [
                    [
                        'itemId' => $courseId,
                        'description' => $customerInfo['course_title'],
                        'price' => $amount,
                        'quantity' => 1
                    ]
                ]
            ];
            
            // Generate signature
            $signature = $this->generateSignature($paymentData);
            $paymentData['signature'] = $signature;
            
            // Send request to Fawry
            $response = $this->sendFawryRequest('/fawrypay-api/api/payments/init', $paymentData);
            
            if ($response && isset($response['statusCode']) && $response['statusCode'] == 200) {
                // Update transaction with Fawry reference
                $this->updateTransactionStatus($transactionId, 'pending', $response['referenceNumber'], $response);
                
                return [
                    'success' => true,
                    'transaction_id' => $transactionId,
                    'payment_url' => $this->config['base_url'] . '/fawrypay-api/api/payments/charge',
                    'reference_number' => $response['referenceNumber'],
                    'payment_data' => $paymentData
                ];
            } else {
                $this->updateTransactionStatus($transactionId, 'failed', null, $response);
                return [
                    'success' => false,
                    'error' => 'Failed to initialize Fawry payment: ' . ($response['statusDescription'] ?? 'Unknown error')
                ];
            }
            
        } catch (Exception $e) {
            error_log("Fawry payment error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Payment initialization failed'
            ];
        }
    }
    
    /**
     * Generate Fawry signature
     */
    private function generateSignature($data) {
        $signatureString = $data['merchantCode'] . $data['merchantRefNumber'] . 
                          $data['customerMobile'] . $data['paymentAmount'] . 
                          $this->config['security_key'];
        return hash('sha256', $signatureString);
    }
    
    /**
     * Send request to Fawry API
     */
    private function sendFawryRequest($endpoint, $data) {
        $url = $this->config['base_url'] . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("Fawry API cURL error: " . $error);
            return false;
        }
        
        if ($httpCode !== 200) {
            error_log("Fawry API HTTP error: " . $httpCode);
            return false;
        }
        
        return json_decode($response, true);
    }
    
    /**
     * Handle Fawry callback
     */
    public function handleCallback($callbackData) {
        try {
            // Verify signature
            $signature = $callbackData['messageSignature'] ?? '';
            unset($callbackData['messageSignature']);
            
            if (!$this->verifyCallbackSignature($callbackData, $signature)) {
                error_log("Fawry callback signature verification failed");
                return false;
            }
            
            $transactionId = $callbackData['merchantRefNumber'];
            $fawryRefNumber = $callbackData['fawryRefNumber'];
            $paymentStatus = $callbackData['paymentStatus'];
            
            // Get transaction
            $transaction = $this->getTransaction($transactionId);
            if (!$transaction) {
                error_log("Transaction not found: " . $transactionId);
                return false;
            }
            
            // Process based on payment status
            switch ($paymentStatus) {
                case 'PAID':
                    // Payment successful
                    $success = $this->processSuccessfulPayment($transactionId, $fawryRefNumber, $callbackData);
                    if ($success) {
                        error_log("Fawry payment successful: " . $transactionId);
                        return true;
                    } else {
                        error_log("Failed to process successful Fawry payment: " . $transactionId);
                        return false;
                    }
                    break;
                    
                case 'CANCELLED':
                case 'EXPIRED':
                    $this->updateTransactionStatus($transactionId, 'cancelled', $fawryRefNumber, $callbackData);
                    error_log("Fawry payment cancelled/expired: " . $transactionId);
                    return true;
                    break;
                    
                case 'FAILED':
                default:
                    $this->updateTransactionStatus($transactionId, 'failed', $fawryRefNumber, $callbackData);
                    error_log("Fawry payment failed: " . $transactionId);
                    return true;
                    break;
            }
            
        } catch (Exception $e) {
            error_log("Fawry callback error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Verify Fawry callback signature
     */
    private function verifyCallbackSignature($data, $signature) {
        $signatureString = $data['fawryRefNumber'] . $data['merchantRefNumber'] . 
                          $data['paymentAmount'] . $data['paymentStatus'] . 
                          $this->config['security_key'];
        $expectedSignature = hash('sha256', $signatureString);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Check payment status
     */
    public function checkPaymentStatus($transactionId) {
        try {
            $transaction = $this->getTransaction($transactionId);
            if (!$transaction) {
                return ['success' => false, 'error' => 'Transaction not found'];
            }
            
            // Query Fawry for payment status
            $queryData = [
                'merchantCode' => $this->config['merchant_code'],
                'merchantRefNumber' => $transactionId
            ];
            
            $signature = hash('sha256', $queryData['merchantCode'] . $queryData['merchantRefNumber'] . $this->config['security_key']);
            $queryData['signature'] = $signature;
            
            $response = $this->sendFawryRequest('/fawrypay-api/api/payments/status', $queryData);
            
            if ($response && isset($response['paymentStatus'])) {
                return [
                    'success' => true,
                    'status' => $response['paymentStatus'],
                    'fawry_ref' => $response['fawryRefNumber'] ?? null,
                    'amount' => $response['paymentAmount'] ?? null
                ];
            }
            
            return ['success' => false, 'error' => 'Failed to check payment status'];
            
        } catch (Exception $e) {
            error_log("Fawry status check error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Status check failed'];
        }
    }
}
?>
