<?php
require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';

header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');


// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// For testing, allow access without login
// if (!isset($_SESSION['user_id'])) {
//     http_response_code(401);
//     echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
//     exit;
// }

// Use a test user ID if not logged in
$userId = $_SESSION['user_id'] ?? 1;

try {
    $db = Database::getInstance()->getConnection();

    $examId = $_GET['exam_id'] ?? null;
    
    if (!$examId) {
        echo json_encode(['success' => false, 'message' => 'معرف الامتحان مفقود']);
        exit;
    }

    // Check and create tables if they don't exist
    try {
        // Check if course_exams table exists
        $db->query("SELECT 1 FROM course_exams LIMIT 1");
    } catch (Exception $e) {
        // Create the table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS course_exams (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                duration_minutes INT DEFAULT 60,
                total_marks DECIMAL(5,2) DEFAULT 0,
                passing_marks DECIMAL(5,2) DEFAULT 0,
                week_number INT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_course_id (course_id),
                INDEX idx_week_number (week_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    try {
        // Check if course_exam_questions table exists
        $db->query("SELECT 1 FROM course_exam_questions LIMIT 1");
    } catch (Exception $e) {
        // Create the table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS course_exam_questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                exam_id INT NOT NULL,
                question_text TEXT NOT NULL,
                question_type ENUM('true_false', 'multiple_choice') NOT NULL,
                options JSON,
                correct_answer TEXT NOT NULL,
                explanation TEXT,
                points DECIMAL(5,2) DEFAULT 1.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_exam_id (exam_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    // Check if user_exam_answers_simple table exists
    try {
        $db->query("SELECT 1 FROM user_exam_answers_simple LIMIT 1");
    } catch (Exception $e) {
        // Create the table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS user_exam_answers_simple (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                question_id INT NOT NULL,
                user_answer TEXT NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                score DECIMAL(5,2) DEFAULT 0,
                attempt_number INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_question (user_id, question_id),
                INDEX idx_attempt (user_id, question_id, attempt_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }
    
    // Get exam details
    $stmt = $db->prepare("
        SELECT id, title, description, duration_minutes, total_marks, passing_marks
        FROM course_exams 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$examId]);
    $exam = $stmt->fetch();
    
    if (!$exam) {
        echo json_encode(['success' => false, 'message' => 'الامتحان غير موجود']);
        exit;
    }
    
    // Get exam questions
    $stmt = $db->prepare("
        SELECT id, question_text, question_type, options, points
        FROM course_exam_questions 
        WHERE exam_id = ? 
        ORDER BY id ASC
    ");
    $stmt->execute([$examId]);
    $questions = $stmt->fetchAll();
    
    if (empty($questions)) {
        echo json_encode(['success' => false, 'message' => 'لا توجد أسئلة لهذا الامتحان']);
        exit;
    }
    
    // Check if user has already completed this exam
    $stmt = $db->prepare("
        SELECT COUNT(DISTINCT question_id) as completed_count,
               MAX(attempt_number) as last_attempt,
               MAX(score) as best_score
        FROM user_exam_answers_simple
        WHERE user_id = ? AND question_id IN (" . implode(',', array_column($questions, 'id')) . ")
    ");
    $stmt->execute([$userId]);
    $completionData = $stmt->fetch();
    
    $isCompleted = $completionData['completed_count'] == count($questions);
    
    // Get user's last attempt results if any
    $lastResults = [];
    if ($completionData['last_attempt']) {
        $stmt = $db->prepare("
            SELECT question_id, user_answer, is_correct, score
            FROM user_exam_answers_simple
            WHERE user_id = ? AND question_id IN (" . implode(',', array_column($questions, 'id')) . ")
            AND attempt_number = ?
        ");
        $stmt->execute([$userId, $completionData['last_attempt']]);
        $attempts = $stmt->fetchAll();

        foreach ($attempts as $attempt) {
            $lastResults[$attempt['question_id']] = [
                'user_answer' => $attempt['user_answer'],
                'is_correct' => $attempt['is_correct'],
                'score' => $attempt['score']
            ];
        }
    }
    
    // Format questions for frontend
    $formattedQuestions = [];
    foreach ($questions as $question) {
        $formattedQuestion = [
            'id' => $question['id'],
            'question_text' => $question['question_text'],
            'question_type' => $question['question_type'],
            'points' => $question['points'],
            'options' => null
        ];
        
        // Add options for multiple choice questions
        if ($question['question_type'] === 'multiple_choice' && $question['options']) {
            $formattedQuestion['options'] = json_decode($question['options'], true);
        }
        
        // Add last attempt data if available
        if (isset($lastResults[$question['id']])) {
            $formattedQuestion['last_attempt'] = $lastResults[$question['id']];
        }
        
        $formattedQuestions[] = $formattedQuestion;
    }
    
    echo json_encode([
        'success' => true,
        'exam' => $exam,
        'questions' => $formattedQuestions,
        'is_completed' => $isCompleted,
        'last_attempt_number' => $completionData['last_attempt'] ?? 0,
        'best_score' => $completionData['best_score'] ?? 0,
        'total_questions' => count($questions)
    ]);
    
} catch (Exception $e) {
    error_log("Error getting exam questions: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
