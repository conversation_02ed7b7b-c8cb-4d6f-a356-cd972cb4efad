<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/PaymobGateway.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['transaction_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Transaction ID required']);
    exit;
}

try {
    $paymobGateway = new PaymobGateway();
    $result = $paymobGateway->checkPaymentStatus($input['transaction_id']);
    
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log("Paymob status check API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>
