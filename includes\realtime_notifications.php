<?php
/**
 * Real-time Notifications System
 * Provides live notification updates without page refresh
 */

require_once __DIR__ . '/../config/config.php';
require_once 'database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Set headers for Server-Sent Events
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Function to send SSE data
function sendSSE($data) {
    echo "data: " . json_encode($data) . "\n\n";
    ob_flush();
    flush();
}

try {
    $userManager = new UserManager();
    $userId = $_SESSION['user_id'];
    
    // Get last notification check time from session or use current time
    $lastCheck = $_SESSION['last_notification_check'] ?? time();
    
    // Check for new notifications since last check
    $stmt = $userManager->db->prepare("
        SELECT id, title, message, type, is_read, created_at
        FROM notifications
        WHERE (user_id = ? OR is_global = 1) 
        AND created_at > FROM_UNIXTIME(?)
        ORDER BY created_at DESC
        LIMIT 10
    ");
    
    $stmt->execute([$userId, $lastCheck]);
    $newNotifications = $stmt->fetchAll();
    
    if (!empty($newNotifications)) {
        // Format notifications
        $formattedNotifications = array_map(function($notification) {
            return [
                'id' => $notification['id'],
                'title' => $notification['title'],
                'message' => $notification['message'],
                'type' => $notification['type'],
                'is_read' => (bool)$notification['is_read'],
                'created_at' => date('Y-m-d H:i', strtotime($notification['created_at']))
            ];
        }, $newNotifications);
        
        // Send new notifications
        sendSSE([
            'type' => 'new_notifications',
            'notifications' => $formattedNotifications,
            'count' => count($formattedNotifications)
        ]);
        
        // Update last check time
        $_SESSION['last_notification_check'] = time();
    }
    
    // Send heartbeat to keep connection alive
    sendSSE([
        'type' => 'heartbeat',
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    sendSSE([
        'type' => 'error',
        'message' => 'حدث خطأ في النظام'
    ]);
    error_log("Real-time notifications error: " . $e->getMessage());
}
?>
