<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';

// Get dashboard statistics
$userManager = new UserManager();
$db = Database::getInstance()->getConnection();

// Get comprehensive statistics
$totalUsers = $userManager->getTotalUsersCount();
$activeUsers = $userManager->getActiveUsersCount();
$newUsersToday = $userManager->getNewUsersToday();

// Get notification statistics
$notificationStmt = $db->prepare("
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today
    FROM notifications
");
$notificationStmt->execute();
$notificationStats = $notificationStmt->fetch();

// Get recent activities
$recentUsersStmt = $db->prepare("
    SELECT first_name, second_name, username, created_at
    FROM users
    ORDER BY created_at DESC
    LIMIT 5
");
$recentUsersStmt->execute();
$recentUsers = $recentUsersStmt->fetchAll();

// Get system info
$systemInfo = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'mysql_version' => $db->query('SELECT VERSION()')->fetchColumn(),
    'disk_space' => function_exists('disk_free_space') ? disk_free_space('.') : 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>مرحباً بك في لوحة التحكم</h1>
                <p>أهلاً وسهلاً <?php echo htmlspecialchars($adminName); ?> - إدارة شاملة لجميع جوانب الموقع</p>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card hover-lift">
                    <div class="stat-number" data-target="<?php echo $totalUsers; ?>">0</div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                    <div class="stat-change positive">+12% من الشهر الماضي</div>
                </div>

                <div class="stat-card hover-lift">
                    <div class="stat-number" data-target="<?php echo $activeUsers; ?>">0</div>
                    <div class="stat-label">المستخدمين النشطين</div>
                    <div class="stat-change positive">+8% من الأسبوع الماضي</div>
                </div>

                <div class="stat-card hover-lift">
                    <div class="stat-number" data-target="<?php echo $newUsersToday; ?>">0</div>
                    <div class="stat-label">مستخدمين جدد اليوم</div>
                    <div class="stat-change neutral">نفس معدل أمس</div>
                </div>

                <div class="stat-card hover-lift">
                    <div class="stat-number" data-target="<?php echo $notificationStats['total']; ?>">0</div>
                    <div class="stat-label">إجمالي الإشعارات</div>
                    <div class="stat-change positive">+25% من الأسبوع الماضي</div>
                </div>

                <div class="stat-card hover-lift">
                    <div class="stat-number" data-target="<?php echo $notificationStats['unread']; ?>">0</div>
                    <div class="stat-label">إشعارات غير مقروءة</div>
                    <div class="stat-change negative">-5% من أمس</div>
                </div>

                <div class="stat-card hover-lift">
                    <div class="stat-number" data-target="<?php echo $notificationStats['today']; ?>">0</div>
                    <div class="stat-label">إشعارات اليوم</div>
                    <div class="stat-change positive">+15% من أمس</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="content-card hover-glow">
                <h2>الإجراءات السريعة</h2>
                <div class="grid-4">
                    <a href="send_notifications.php" class="btn btn-primary hover-lift">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>إرسال إشعار</span>
                    </a>

                    <a href="manage_notifications.php" class="btn btn-info hover-lift">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>إدارة الإشعارات</span>
                    </a>

                    <a href="users.php" class="btn btn-success hover-lift">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>إدارة المستخدمين</span>
                    </a>

                    <a href="messages.php" class="btn btn-warning hover-lift">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>الرسائل</span>
                    </a>
                </div>
            </div>

            <!-- Dashboard Content Grid -->
            <div class="grid-2">
                <div class="content-card hover-glow">
                    <h2>المستخدمين الجدد</h2>
                    <?php if (!empty($recentUsers)): ?>
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>اسم المستخدم</th>
                                        <th>تاريخ التسجيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentUsers as $user): ?>
                                        <tr class="hover-lift">
                                            <td><?php echo htmlspecialchars(trim($user['first_name'] . ' ' . $user['second_name']) ?: 'غير محدد'); ?></td>
                                            <td><span class="badge badge-primary"><?php echo htmlspecialchars($user['username']); ?></span></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <p>لا توجد مستخدمين جدد</p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="content-card hover-glow">
                    <h2>معلومات النظام</h2>
                    <div class="system-info">
                        <div class="info-item">
                            <div class="info-label">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                إصدار PHP:
                            </div>
                            <div class="info-value"><?php echo $systemInfo['php_version']; ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                    <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
                                    <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                خادم الويب:
                            </div>
                            <div class="info-value"><?php echo htmlspecialchars($systemInfo['server_software']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <ellipse cx="12" cy="5" rx="9" ry="3" stroke="currentColor" stroke-width="2"/>
                                    <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3" stroke="currentColor" stroke-width="2"/>
                                    <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                إصدار MySQL:
                            </div>
                            <div class="info-value"><?php echo $systemInfo['mysql_version']; ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                المساحة المتاحة:
                            </div>
                            <div class="info-value"><?php echo $systemInfo['disk_space'] ? number_format($systemInfo['disk_space'] / 1024 / 1024 / 1024, 2) . ' GB' : 'غير متاح'; ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Management Actions -->
            <div class="content-card">
                <h2>إدارة الكورسات</h2>
                <div class="grid-3">
                    <a href="courses.php" class="btn btn-primary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إدارة الكورسات
                    </a>

                    <a href="activation_codes.php" class="btn btn-warning">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        أكواد التفعيل
                    </a>

                    <a href="payment_requests.php" class="btn btn-success">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="1" y="4" width="22" height="16" rx="2" ry="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="1" y1="10" x2="23" y2="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        طلبات الدفع
                    </a>
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="content-card">
                <h2>إجراءات إضافية</h2>
                <div class="grid-4">
                    <a href="exams.php" class="btn btn-warning">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إدارة الامتحانات
                    </a>

                    <a href="news.php" class="btn btn-success">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M6 12h4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إدارة الأخبار
                    </a>

                    <a href="honor_board.php" class="btn btn-warning">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        لوحة الشرف
                    </a>

                    <a href="add_admin.php" class="btn btn-danger">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="20" y1="8" x2="20" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="23" y1="11" x2="17" y2="11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إضافة إدارة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div class="modal" id="addUserModal">
        <div class="modal-header">
            <h3 class="modal-title">إضافة مستخدم جديد</h3>
            <button class="modal-close" onclick="closeModal('addUserModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="addUserForm">
                <div class="form-group">
                    <label class="form-label">الاسم الأول</label>
                    <input type="text" class="form-input" name="first_name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">الاسم الثاني</label>
                    <input type="text" class="form-input" name="second_name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-input" name="username" required>
                </div>
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-input" name="email" required>
                </div>
                <div class="form-group">
                    <label class="form-label">كلمة المرور</label>
                    <input type="password" class="form-input" name="password" required>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitAddUser()">إضافة المستخدم</button>
            <button class="btn btn-secondary" onclick="closeModal('addUserModal')">إلغاء</button>
        </div>
    </div>

    <div class="modal" id="sendNotificationModal">
        <div class="modal-header">
            <h3 class="modal-title">إرسال إشعار جديد</h3>
            <button class="modal-close" onclick="closeModal('sendNotificationModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="sendNotificationForm">
                <div class="form-group">
                    <label class="form-label">عنوان الإشعار</label>
                    <input type="text" class="form-input" name="title" required>
                </div>
                <div class="form-group">
                    <label class="form-label">محتوى الإشعار</label>
                    <textarea class="form-textarea" name="message" required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">نوع الإشعار</label>
                    <select class="form-select" name="type">
                        <option value="info">معلومات</option>
                        <option value="success">نجاح</option>
                        <option value="warning">تحذير</option>
                        <option value="error">خطأ</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="submitSendNotification()">إرسال الإشعار</button>
            <button class="btn btn-secondary" onclick="closeModal('sendNotificationModal')">إلغاء</button>
        </div>
    </div>

    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
    <script>
        // Additional functionality for the dashboard
        function submitAddUser() {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);

            fetch('api/add_user.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.adminPanel.showToast('تم إضافة المستخدم بنجاح', 'success');
                    closeModal('addUserModal');
                    form.reset();
                    // Refresh user count
                    location.reload();
                } else {
                    window.adminPanel.showToast(data.message || 'خطأ في إضافة المستخدم', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في الاتصال بالخادم', 'error');
            });
        }

        function submitSendNotification() {
            const form = document.getElementById('sendNotificationForm');
            const formData = new FormData(form);

            fetch('api/send_notification.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.adminPanel.showToast('تم إرسال الإشعار بنجاح', 'success');
                    closeModal('sendNotificationModal');
                    form.reset();
                } else {
                    window.adminPanel.showToast(data.message || 'خطأ في إرسال الإشعار', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.adminPanel.showToast('خطأ في الاتصال بالخادم', 'error');
            });
        }

        // Animate statistics numbers
        document.addEventListener('DOMContentLoaded', function() {
            const statNumbers = document.querySelectorAll('.stat-number[data-target]');

            statNumbers.forEach(stat => {
                const target = parseInt(stat.getAttribute('data-target'));
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(current);
                }, 16);
            });
        });
    </script>
</body>
</html>
