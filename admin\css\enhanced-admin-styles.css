/* ===== MODERN ADMIN PANEL STYLES - ENHANCED DESIGN ===== */

/* CSS Variables for Consistent Theming */
:root {
    /* Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c3aed;
    --secondary-color: #764ba2;

    /* Accent Colors */
    --accent-blue: #4299e1;
    --accent-purple: #9f7aea;
    --accent-pink: #ed64a6;
    --accent-green: #48bb78;
    --accent-orange: #ed8936;
    --accent-red: #f56565;

    /* Neutral Colors */
    --bg-primary: #f7fafc;
    --bg-secondary: #edf2f7;
    --bg-card: #ffffff;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    min-height: 100vh;
    direction: rtl;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-light));
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
    position: relative;
    background: var(--bg-primary);
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-right: 300px;
    padding: var(--spacing-2xl);
    background: var(--bg-primary);
    min-height: 100vh;
    transition: all var(--transition-normal);
    position: relative;
}

.main-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Page Headers */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--primary-light) 100%);
    color: white;
    padding: var(--spacing-2xl) var(--spacing-xl);
    border-radius: var(--radius-2xl);
    margin-bottom: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%),
        conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: float 8s ease-in-out infinite;
}

.page-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
    }
    50% {
        transform: translateY(-20px) rotate(180deg) scale(1.05);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.page-header h1 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: 2.5rem;
    font-weight: 800;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    margin: 0;
    opacity: 0.95;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
    font-weight: 500;
}

/* Content Cards */
.content-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-purple) 100%);
    opacity: 0.05;
    border-radius: 0 var(--radius-xl) 0 100px;
    transition: all var(--transition-normal);
}

.content-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-pink) 0%, var(--accent-orange) 100%);
    opacity: 0.03;
    border-radius: 100px var(--radius-xl) 0 0;
    transition: all var(--transition-normal);
}

.content-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.content-card:hover::before {
    opacity: 0.1;
    transform: scale(1.1) rotate(10deg);
}

.content-card:hover::after {
    opacity: 0.08;
    transform: scale(1.2) rotate(-10deg);
}

.content-card h2 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-size: 1.75rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.content-card h3 {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: 1.25rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.form-control {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background: var(--bg-card);
    transition: all var(--transition-normal);
    color: var(--text-primary);
    font-family: inherit;
    position: relative;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-card);
    transform: translateY(-2px);
}

.form-control::placeholder {
    color: var(--text-muted);
    transition: all var(--transition-normal);
}

.form-control:focus::placeholder {
    opacity: 0.7;
    transform: translateY(-2px);
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.form-control:invalid {
    border-color: var(--accent-red);
}

.form-control:valid {
    border-color: var(--accent-green);
}

/* Input with Icon */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group .form-control {
    padding-right: 3rem;
}

.input-group .input-icon {
    position: absolute;
    right: var(--spacing-md);
    color: var(--text-muted);
    pointer-events: none;
    transition: all var(--transition-normal);
}

.input-group .form-control:focus + .input-icon {
    color: var(--primary-color);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    margin-left: var(--spacing-md);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    text-align: center;
    min-width: 140px;
    font-family: inherit;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-normal);
    z-index: 0;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:active {
    transform: translateY(1px) scale(0.98);
}

.btn svg,
.btn span {
    position: relative;
    z-index: 1;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-light) 100%);
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--text-secondary) 0%, var(--text-primary) 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--text-primary) 0%, #1a202c 100%);
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-green) 0%, #38a169 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-success:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.btn-danger {
    background: linear-gradient(135deg, var(--accent-red) 0%, #e53e3e 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.btn-warning {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #dd6b20 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.btn-info {
    background: linear-gradient(135deg, var(--accent-blue) 0%, #3182ce 100%);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-info:hover {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

/* Table Styles */
.table-container {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.table-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: var(--spacing-lg) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.table-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

.table-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.table th,
.table td {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tr {
    transition: all var(--transition-normal);
}

.table tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(159, 122, 234, 0.05) 100%);
    transform: scale(1.01);
}

.table tr:hover td {
    color: var(--primary-dark);
    font-weight: 500;
}

/* Grid Layouts */
.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
}

.grid-auto {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-purple) 100%);
    opacity: 0.05;
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.stat-card::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--accent-pink) 0%, var(--accent-orange) 100%);
    opacity: 0.03;
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.stat-card:hover::before {
    opacity: 0.1;
    transform: scale(1.2) rotate(45deg);
}

.stat-card:hover::after {
    opacity: 0.08;
    transform: scale(1.3) rotate(-45deg);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--accent-purple) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 2;
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
    margin-bottom: var(--spacing-sm);
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.stat-change.positive {
    color: var(--accent-green);
}

.stat-change.negative {
    color: var(--accent-red);
}

.stat-change.neutral {
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 1400px) {
    .grid-4 { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 1200px) {
    .grid-4 { grid-template-columns: repeat(2, 1fr); }
    .grid-3 { grid-template-columns: repeat(2, 1fr); }
    .main-content { margin-right: 280px; }
}

@media (max-width: 992px) {
    .main-content {
        margin-right: 0;
        padding: var(--spacing-lg);
    }

    .grid-2,
    .grid-3,
    .grid-4,
    .grid-auto {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }

    .page-header {
        padding: var(--spacing-xl) var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .content-card {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: 0.9rem;
        min-width: 120px;
    }

    .table-header {
        padding: var(--spacing-md) var(--spacing-lg);
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .table th,
    .table td {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-sm);
    }

    .page-header {
        padding: var(--spacing-lg);
    }

    .page-header h1 {
        font-size: 1.75rem;
    }

    .content-card {
        padding: var(--spacing-md);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
        min-width: 100px;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Advanced Loading Spinner */
.spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.spinner-small {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

.spinner-large {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Hover Effects */
.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.hover-glow {
    transition: all var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
