<?php
session_start();
require_once '../includes/database.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance()->getConnection();
    
    // Test basic connection
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    $response = [
        'success' => true,
        'message' => 'Database connection successful',
        'test_query' => $result['test']
    ];
    
    // Check if required tables exist
    $tables = [
        'user_video_progress',
        'user_exercise_attempts', 
        'user_exam_attempts',
        'user_weekly_test_attempts'
    ];
    
    $tableStatus = [];
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->fetch();
            $tableStatus[$table] = $exists ? 'exists' : 'missing';
        } catch (Exception $e) {
            $tableStatus[$table] = 'error: ' . $e->getMessage();
        }
    }
    
    $response['tables'] = $tableStatus;
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'error' => $e->getMessage()
    ]);
}
?>
