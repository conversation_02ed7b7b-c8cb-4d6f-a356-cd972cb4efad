<?php
session_start();
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: courses.php');
    exit;
}

$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: courses.php');
    exit;
}

// Get course subscribers
$stmt = $db->prepare("
    SELECT cs.*, u.username, u.first_name, u.second_name, u.email, u.phone,
           u.education_level, u.education_type, u.grade, u.specialization,
           ac.code as activation_code_used,
           pr.mobile_number as payment_mobile, pr.transfer_method, pr.status as payment_status
    FROM course_subscriptions cs
    JOIN users u ON cs.user_id = u.id
    LEFT JOIN activation_codes ac ON cs.activation_code = ac.code
    LEFT JOIN payment_requests pr ON cs.payment_request_id = pr.id
    WHERE cs.course_id = ?
    ORDER BY cs.created_at DESC
");
$stmt->execute([$courseId]);
$subscribers = $stmt->fetchAll();

// Get statistics
$stats = [
    'total' => count($subscribers),
    'active' => 0,
    'pending' => 0,
    'expired' => 0,
    'rejected' => 0
];

foreach ($subscribers as $subscriber) {
    $stats[$subscriber['activation_status']]++;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشتركي الكورس - <?php echo htmlspecialchars($course['title']); ?> - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>مشتركي الكورس: <?php echo htmlspecialchars($course['title']); ?></h1>
                <p>عرض وإدارة المشتركين في الكورس</p>
                <a href="courses.php" class="btn btn-secondary">العودة للكورسات</a>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">إجمالي المشتركين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active']; ?></div>
                    <div class="stat-label">مشتركين نشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['pending']; ?></div>
                    <div class="stat-label">في انتظار التفعيل</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['rejected']; ?></div>
                    <div class="stat-label">مرفوضين</div>
                </div>
            </div>

            <!-- Course Information -->
            <div class="content-card">
                <h2>معلومات الكورس</h2>
                <div class="course-info-grid">
                    <div class="info-item">
                        <strong>العنوان:</strong>
                        <span><?php echo htmlspecialchars($course['title']); ?></span>
                    </div>
                    <div class="info-item">
                        <strong>المادة:</strong>
                        <span><?php echo htmlspecialchars($course['subject']); ?></span>
                    </div>
                    <div class="info-item">
                        <strong>السعر:</strong>
                        <span>
                            <?php if ($course['discount_percentage'] > 0): ?>
                                <span class="original-price"><?php echo number_format($course['price'], 2); ?> جنيه</span>
                                <span class="discounted-price"><?php echo number_format($course['discounted_price'], 2); ?> جنيه</span>
                            <?php else: ?>
                                <?php echo number_format($course['price'], 2); ?> جنيه
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>تاريخ الإنشاء:</strong>
                        <span><?php echo date('Y-m-d H:i', strtotime($course['created_at'])); ?></span>
                    </div>
                </div>
            </div>

            <!-- Subscribers List -->
            <div class="content-card">
                <h2>قائمة المشتركين</h2>
                <?php if (!empty($subscribers)): ?>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البيانات التعليمية</th>
                                    <th>طريقة التفعيل</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>تاريخ التفعيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subscribers as $subscriber): ?>
                                    <tr>
                                        <td>
                                            <div class="user-info">
                                                <strong><?php echo htmlspecialchars($subscriber['first_name'] . ' ' . $subscriber['second_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($subscriber['username']); ?></small>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($subscriber['email']); ?></small>
                                                <?php if ($subscriber['phone']): ?>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($subscriber['phone']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $levels = ['primary' => 'ابتدائي', 'preparatory' => 'إعدادي', 'secondary' => 'ثانوي'];
                                            $types = ['azhari' => 'أزهري', 'general' => 'عام'];
                                            $gradeNumbers = ['1' => 'الأول', '2' => 'الثاني', '3' => 'الثالث', '4' => 'الرابع', '5' => 'الخامس', '6' => 'السادس'];

                                            $levelText = $levels[$subscriber['education_level']] ?? $subscriber['education_level'];
                                            $typeText = $types[$subscriber['education_type']] ?? $subscriber['education_type'];
                                            $gradeText = $gradeNumbers[$subscriber['grade']] ?? $subscriber['grade'];
                                            ?>
                                            <div class="education-info">
                                                <span class="education-badge">
                                                    الصف <?php echo $gradeText; ?> <?php echo $levelText; ?> <?php echo $typeText; ?>
                                                </span>
                                                <?php if ($subscriber['specialization'] && $subscriber['specialization'] !== 'all'): ?>
                                                    <br>
                                                    <small class="specialization"><?php echo $subscriber['specialization'] === 'scientific' ? 'علمي' : 'أدبي'; ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($subscriber['activation_method'] === 'code'): ?>
                                                <div class="activation-method">
                                                    <span class="method-badge method-code">كود التفعيل</span>
                                                    <?php if ($subscriber['activation_code_used']): ?>
                                                        <br>
                                                        <small class="code-used"><?php echo htmlspecialchars($subscriber['activation_code_used']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <div class="activation-method">
                                                    <span class="method-badge method-payment">طلب دفع</span>
                                                    <?php if ($subscriber['payment_mobile']): ?>
                                                        <br>
                                                        <small class="payment-info">
                                                            <?php echo htmlspecialchars($subscriber['payment_mobile']); ?>
                                                            <br>
                                                            <?php
                                                            $methods = [
                                                                'vodafone_cash' => 'فودافون كاش',
                                                                'etisalat_cash' => 'اتصالات كاش',
                                                                'we_cash' => 'وي كاش',
                                                                'orange_cash' => 'أورانج كاش'
                                                            ];
                                                            echo $methods[$subscriber['transfer_method']] ?? $subscriber['transfer_method'];
                                                            ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClasses = [
                                                'pending' => 'status-pending',
                                                'active' => 'status-active',
                                                'expired' => 'status-expired',
                                                'rejected' => 'status-rejected'
                                            ];
                                            $statusTexts = [
                                                'pending' => 'معلق',
                                                'active' => 'نشط',
                                                'expired' => 'منتهي',
                                                'rejected' => 'مرفوض'
                                            ];
                                            ?>
                                            <span class="status-badge <?php echo $statusClasses[$subscriber['activation_status']]; ?>">
                                                <?php echo $statusTexts[$subscriber['activation_status']]; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($subscriber['created_at'])); ?></td>
                                        <td>
                                            <?php if ($subscriber['activated_at']): ?>
                                                <?php echo date('Y-m-d H:i', strtotime($subscriber['activated_at'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">غير مفعل</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <h3>لا يوجد مشتركين</h3>
                        <p>لم يسجل أي مستخدم في هذا الكورس بعد</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
        .course-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-item strong {
            color: #4682B4;
            font-weight: 600;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 14px;
            margin-left: 10px;
        }

        .discounted-price {
            color: #dc3545;
            font-weight: 700;
        }

        .user-info {
            min-width: 200px;
        }

        .text-muted {
            color: #6c757d;
            font-size: 0.9em;
        }

        .education-info {
            min-width: 150px;
        }

        .education-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .specialization {
            color: #6c757d;
            font-style: italic;
        }

        .activation-method {
            min-width: 120px;
        }

        .method-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .method-code {
            background: #fff3cd;
            color: #856404;
        }

        .method-payment {
            background: #d1ecf1;
            color: #0c5460;
        }

        .code-used, .payment-info {
            color: #6c757d;
            font-size: 11px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #6c757d;
        }
    </style>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
