<?php
session_start();
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    
    $stmt = $db->prepare("UPDATE notifications SET is_read = 1 WHERE is_read = 0");
    
    if ($stmt->execute()) {
        $affectedRows = $stmt->rowCount();
        echo json_encode([
            'success' => true, 
            'message' => "تم تحديد {$affectedRows} إشعار كمقروء",
            'affected_rows' => $affectedRows
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في تحديث الإشعارات']);
    }
    
} catch (Exception $e) {
    error_log("Error in mark_all_notifications_read.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في تحديث الإشعارات'
    ]);
}
?>
