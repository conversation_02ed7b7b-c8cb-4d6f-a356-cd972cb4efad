<?php
// ملف إصلاح سريع للمشاكل الشائعة
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>إصلاح سريع للمشاكل</h1>";

// 1. إنشاء المجلدات المطلوبة
echo "<h2>1. إنشاء المجلدات المطلوبة</h2>";
$directories = [
    'uploads',
    'uploads/courses',
    'uploads/videos', 
    'uploads/lessons',
    'uploads/profile_pictures',
    'uploads/receipts',
    'uploads/summaries',
    'uploads/temp',
    'uploads/exams',
    'uploads/exercises'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ تم إنشاء مجلد: $dir<br>";
        } else {
            echo "❌ فشل إنشاء مجلد: $dir<br>";
        }
    } else {
        echo "✅ مجلد موجود: $dir<br>";
    }
}

// 2. إنشاء ملف .htaccess للحماية في مجلد uploads
echo "<h2>2. حماية مجلد uploads</h2>";
$htaccess_content = "# منع تنفيذ ملفات PHP في مجلد uploads\n";
$htaccess_content .= "<Files *.php>\n";
$htaccess_content .= "    Order allow,deny\n";
$htaccess_content .= "    Deny from all\n";
$htaccess_content .= "</Files>\n";

if (file_put_contents('uploads/.htaccess', $htaccess_content)) {
    echo "✅ تم إنشاء ملف حماية uploads/.htaccess<br>";
} else {
    echo "❌ فشل إنشاء ملف حماية uploads/.htaccess<br>";
}

// 3. فحص وإصلاح إعدادات قاعدة البيانات
echo "<h2>3. فحص قاعدة البيانات</h2>";
try {
    require_once 'config/config.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // فحص الجداول الأساسية
    $required_tables = [
        'users' => 'CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            second_name VARCHAR(50),
            third_name VARCHAR(50),
            fourth_name VARCHAR(50),
            gender ENUM("male", "female") NOT NULL,
            birth_date DATE NOT NULL,
            personal_phone VARCHAR(15),
            father_phone VARCHAR(15),
            mother_phone VARCHAR(15),
            education_level ENUM("primary", "preparatory", "secondary") NOT NULL,
            education_type ENUM("azhari", "general") NOT NULL,
            grade VARCHAR(10) NOT NULL,
            specialization ENUM("scientific", "literary") NULL,
            is_active BOOLEAN DEFAULT TRUE,
            email_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        )',
        
        'curriculum_subjects' => 'CREATE TABLE IF NOT EXISTS curriculum_subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(50) DEFAULT "📚",
            education_level ENUM("primary", "preparatory", "secondary", "all") NOT NULL,
            education_type ENUM("azhari", "general", "all") NOT NULL,
            grade VARCHAR(10) NOT NULL,
            specialization ENUM("scientific", "literary", "all") DEFAULT "all",
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )',
        
        'notifications' => 'CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            admin_id INT DEFAULT 1,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM("info", "success", "warning", "error") DEFAULT "info",
            is_global BOOLEAN DEFAULT FALSE,
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        )'
    ];
    
    foreach ($required_tables as $table_name => $create_sql) {
        try {
            $pdo->exec($create_sql);
            echo "✅ جدول $table_name جاهز<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في جدول $table_name: " . $e->getMessage() . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// 4. إنشاء مستخدم تجريبي
echo "<h2>4. إنشاء مستخدم تجريبي</h2>";
try {
    // فحص وجود مستخدم تجريبي
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'test_user' LIMIT 1");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("
            INSERT INTO users (
                username, email, password_hash, first_name, second_name,
                gender, birth_date, education_level, education_type, grade
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'test_user',
            '<EMAIL>',
            password_hash('123456', PASSWORD_DEFAULT),
            'مستخدم',
            'تجريبي',
            'male',
            '2000-01-01',
            'secondary',
            'general',
            '3'
        ]);
        
        if ($result) {
            echo "✅ تم إنشاء مستخدم تجريبي (اسم المستخدم: test_user، كلمة المرور: 123456)<br>";
        } else {
            echo "❌ فشل إنشاء المستخدم التجريبي<br>";
        }
    } else {
        echo "✅ المستخدم التجريبي موجود بالفعل<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء المستخدم التجريبي: " . $e->getMessage() . "<br>";
}

// 5. إضافة مواد تجريبية
echo "<h2>5. إضافة مواد تجريبية</h2>";
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM curriculum_subjects");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $subjects = [
            ['اللغة العربية', 'مادة اللغة العربية للمرحلة الثانوية', '📖', 'secondary', 'general', '3', 'all'],
            ['النحو والصرف', 'قواعد النحو والصرف', '📝', 'secondary', 'general', '3', 'all'],
            ['البلاغة', 'علم البلاغة والبيان', '🎭', 'secondary', 'general', '3', 'all'],
            ['الأدب', 'الأدب العربي والشعر', '📚', 'secondary', 'general', '3', 'all']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO curriculum_subjects (name, description, icon, education_level, education_type, grade, specialization)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($subjects as $subject) {
            $stmt->execute($subject);
        }
        
        echo "✅ تم إضافة " . count($subjects) . " مادة تجريبية<br>";
    } else {
        echo "✅ يوجد $count مادة في قاعدة البيانات<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في إضافة المواد: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>تم الانتهاء من الإصلاح السريع!</h2>";
echo "<p><strong>الخطوات التالية:</strong></p>";
echo "<ol>";
echo "<li><a href='test.php'>اختبار الموقع الأساسي</a></li>";
echo "<li><a href='debug.php'>فحص شامل للموقع</a></li>";
echo "<li><a href='login.php'>تجربة تسجيل الدخول</a></li>";
echo "<li><a href='page/curriculum_simple.php'>اختبار صفحة المنهج المبسطة</a></li>";
echo "</ol>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>معلومات المستخدم التجريبي:</h3>";
echo "<p><strong>اسم المستخدم:</strong> test_user</p>";
echo "<p><strong>كلمة المرور:</strong> 123456</p>";
echo "<p><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
echo "</div>";
?>
