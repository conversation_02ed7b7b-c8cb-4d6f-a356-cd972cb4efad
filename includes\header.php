<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

// Get user information
require_once 'database.php';
$userManager = new UserManager();

// Get full user data including education info
$userData = $userManager->getUserById($_SESSION['user_id']);
$firstName = $userData['first_name'] ?? $_SESSION['first_name'] ?? 'المستخدم';

// Get unread notifications count
$notificationCount = $userManager->getUnreadNotificationCount($_SESSION['user_id']);
?>

<!-- Include enhanced UI styles -->
<link rel="stylesheet" href="css/enhanced-ui.css">
<link rel="stylesheet" href="css/loading-system.css">
<!-- Include notification system styles and scripts -->
<link rel="stylesheet" href="css/notifications.css">
<script src="js/notifications.js"></script>
<script src="js/loading-system.js"></script>

<!-- User Info for Loading System -->
<script>
    sessionStorage.setItem('first_name', '<?php echo htmlspecialchars($firstName); ?>');
    sessionStorage.setItem('username', '<?php echo htmlspecialchars($_SESSION['username'] ?? 'المستخدم'); ?>');
</script>

<header class="dashboard-header">
    <div class="header-container">
        <!-- Logo and Title -->
        <div class="header-logo">
            <div class="logo-img">
                <img src="img/logo-b.png" alt="شعار سلسلة الدكتور">
            </div>
            <div class="logo-text">
                <h2><?php echo SITE_NAME; ?></h2>
            </div>
        </div>

        <!-- Header Actions -->
        <div class="header-actions">
            <!-- Current Time and Date -->
            <div class="header-datetime">
                <div class="current-time" id="currentTime"></div>
                <div class="current-date" id="currentDate"></div>
            </div>

            <!-- Notifications -->
            <div class="header-notifications">
                <button class="notification-btn" onclick="toggleNotifications()" aria-label="الإشعارات">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php if ($notificationCount > 0): ?>
                        <span class="notification-badge" id="notificationBadge"><?php echo $notificationCount; ?></span>
                    <?php endif; ?>
                </button>

                <!-- Enhanced Notifications Dropdown -->
                <div class="notifications-dropdown" id="notificationsDropdown">
                    <div class="notifications-header">
                        <div class="notifications-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <h3>الإشعارات</h3>
                        </div>
                        <button class="mark-all-read" onclick="markAllNotificationsRead()" title="تحديد الكل كمقروء">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <div class="notifications-list" id="notificationsList">
                        <div class="notifications-loading">
                            <div class="loading-spinner-small"></div>
                            <span>جاري تحميل الإشعارات...</span>
                        </div>
                    </div>
                    <div class="notifications-footer">
                        <button class="view-all-notifications" onclick="viewAllNotifications()">عرض جميع الإشعارات</button>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="header-user">
                <span class="user-greeting">مرحباً، <?php echo htmlspecialchars($firstName); ?></span>
                <div class="user-avatar" onclick="toggleUserMenu()">
                    <?php
                    $initials = mb_substr($firstName, 0, 1, 'UTF-8');
                    if (isset($userData['second_name'])) {
                        $initials .= mb_substr($userData['second_name'], 0, 1, 'UTF-8');
                    }
                    echo htmlspecialchars($initials);
                    ?>
                </div>

                <!-- User Dropdown Menu -->
                <div class="user-dropdown" id="userDropdown">
                    <div class="user-dropdown-header">
                        <div class="user-info">
                            <strong><?php echo htmlspecialchars($firstName); ?></strong>
                            <span><?php echo htmlspecialchars($userData['email'] ?? ''); ?></span>
                        </div>
                    </div>
                    <div class="user-dropdown-menu">
                        <a href="../page/update_profile.php" class="dropdown-item">
                            <i class="fas fa-user-edit"></i>
                            تحديث الملف الشخصي
                        </a>
                        <a href="../page/subscriptions.php" class="dropdown-item">
                            <i class="fas fa-crown"></i>
                            الاشتراكات
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="../logout.php" class="dropdown-item logout">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>

                <a href="logout.php" class="logout-btn mobile-only">تسجيل الخروج</a>
            </div>
        </div>
    </div>
</header>

<style>
.dashboard-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 12px 0;
    box-shadow: 0 4px 20px rgba(70, 130, 180, 0.15);
    position: sticky;
    top: 0;
    z-index: 999;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 32px;
    height: 32px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(70, 130, 180, 0.1);
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
    justify-content: center;
}

.header-logo .logo-img img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.2);
}

.header-logo .logo-text h2 {
    color: #2c3e50;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-datetime {
    text-align: center;
    color: #666;
    font-size: 14px;
}

.current-time {
    font-weight: bold;
    color: #4682B4;
    font-size: 16px;
}

.current-date {
    margin-top: 2px;
}

.header-notifications {
    position: relative;
}

.notification-btn {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    color: #4682B4;
    cursor: pointer;
    padding: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-btn:hover {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 50%;
    min-width: 20px;
    min-height: 20px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.notifications-dropdown {
    position: absolute;
    top: calc(100% + 15px);
    right: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-radius: 20px;
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.25);
    width: 380px;
    max-height: 500px;
    display: none;
    z-index: 1001;
    animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.notifications-dropdown.show {
    display: block;
}

.notifications-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    z-index: -1;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.notifications-header {
    padding: 20px 25px 15px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.notifications-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2c3e50;
}

.notifications-title svg {
    color: #4682B4;
}

.notifications-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.mark-all-read {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid rgba(70, 130, 180, 0.2);
    color: #4682B4;
    cursor: pointer;
    padding: 8px 10px;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    min-height: 36px;
}

.mark-all-read:hover {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(70, 130, 180, 0.3);
}

.notifications-list {
    max-height: 350px;
    overflow-y: auto;
    padding: 10px 0;
}

.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: rgba(70, 130, 180, 0.1);
    border-radius: 10px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 10px;
}

.notifications-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 30px 20px;
    color: #666;
    font-size: 14px;
}

.loading-spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(70, 130, 180, 0.2);
    border-top: 2px solid #4682B4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.notifications-footer {
    padding: 15px 25px;
    border-top: 2px solid rgba(70, 130, 180, 0.1);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.view-all-notifications {
    width: 100%;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.view-all-notifications:hover {
    background: linear-gradient(135deg, #4682B4 0%, #2c5282 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
}

/* Individual Notification Styles */
.notification-item {
    padding: 15px 25px;
    border-bottom: 1px solid rgba(70, 130, 180, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.notification-item:hover {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.1);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: linear-gradient(135deg, #fff5f5 0%, #fef5e7 100%);
    border-left: 4px solid #4682B4;
}

.notification-item.unread:hover {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

.notification-content {
    position: relative;
    padding-right: 15px;
}

.notification-content h4 {
    margin: 0 0 8px 0;
    font-size: 15px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
}

.notification-content p {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.notification-content small {
    font-size: 11px;
    color: #999;
    display: flex;
    align-items: center;
    gap: 5px;
}

.notification-content small::before {
    content: '🕒';
    font-size: 10px;
}

.notification-dot {
    position: absolute;
    top: 20px;
    right: 10px;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

.no-notifications {
    padding: 40px 25px;
    text-align: center;
    color: #999;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.no-notifications::before {
    content: '🔔';
    font-size: 48px;
    opacity: 0.5;
}

/* Notification Type Indicators */
.notification-item[data-type="success"] {
    border-left-color: #28a745;
}

.notification-item[data-type="warning"] {
    border-left-color: #ffc107;
}

.notification-item[data-type="error"] {
    border-left-color: #dc3545;
}

.notification-item[data-type="info"] {
    border-left-color: #4682B4;
}

/* Toast Notifications */
.notification-toast {
    position: fixed;
    top: 100px;
    right: 30px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.2);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(70, 130, 180, 0.2);
    z-index: 10000;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.notification-toast-success {
    border-left: 4px solid #28a745;
}

.notification-toast-error {
    border-left: 4px solid #dc3545;
}

.notification-toast-warning {
    border-left: 4px solid #ffc107;
}

.notification-toast-info {
    border-left: 4px solid #4682B4;
}

.toast-content {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.toast-content span {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
}

.toast-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toast-close:hover {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced animations */
.notification-item {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Improved notification button animation */
.notification-btn {
    position: relative;
    overflow: hidden;
}

.notification-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.notification-btn:hover::before {
    width: 100px;
    height: 100px;
}

/* Notification badge pulse animation */
.notification-badge {
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.header-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-greeting {
    color: #333;
    font-weight: 500;
}

.user-avatar {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
    border: 2px solid white;
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
    cursor: pointer;
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    top: calc(100% + 15px);
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    min-width: 250px;
    z-index: 1000;
    display: none;
    overflow: hidden;
    border: 1px solid rgba(70, 130, 180, 0.1);
}

.user-dropdown.show {
    display: block;
    animation: slideDown 0.3s ease;
}

.user-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background: white;
    border: 1px solid rgba(70, 130, 180, 0.1);
    border-bottom: none;
    border-right: none;
    transform: rotate(-45deg);
}

.user-dropdown-header {
    padding: 20px;
    background: linear-gradient(135deg, #4682B4, #20B2AA);
    color: white;
}

.user-info strong {
    display: block;
    font-size: 16px;
    margin-bottom: 5px;
}

.user-info span {
    font-size: 14px;
    opacity: 0.9;
}

.user-dropdown-menu {
    padding: 10px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #4682B4;
}

.dropdown-item.logout:hover {
    background: #f8d7da;
    color: #dc3545;
}

.dropdown-divider {
    height: 1px;
    background: #eee;
    margin: 10px 0;
}

.mobile-only {
    display: none;
}

.logout-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 10px 16px;
    border-radius: 10px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    min-height: 44px;
    display: none;

}

.logout-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 10px 0;
    }

    .header-container {
        padding: 0 15px;
        gap: 15px;
    }

    .mobile-menu-toggle {
        display: flex;
        order: -1;
    }

    .header-logo {
        flex: 1;
        justify-content: flex-start;
        margin-left: 15px;
    }

    .header-logo .logo-text h2 {
        font-size: 18px;
    }

    .header-logo .logo-img img {
        width: 42px;
        height: 42px;
    }

    .header-actions {
        gap: 12px;
    }

    .header-datetime {
        display: none;
    }

    .user-greeting {
        display: none;
    }

    .user-avatar {
        display: none;
    }

    .mobile-only {
        display: block;
    }

    .user-dropdown {
        display: none !important;
    }

    .logout-btn {
        display: block;
        padding: 8px 12px;
        font-size: 13px;
        min-height: 40px;
    }

    .notification-btn {
        padding: 10px;
        min-width: 40px;
        min-height: 40px;
    }

    .notifications-dropdown {
        width: 320px;
        right: -20px;
        left: auto;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 8px 0;
    }

    .header-container {
        padding: 0 12px;
        gap: 10px;
    }

    .mobile-menu-toggle {
        width: 28px;
        height: 28px;
    }

    .hamburger-line {
        height: 2px;
    }

    .header-logo {
        margin-left: 10px;
    }

    .header-logo .logo-text h2 {
        font-size: 16px;
    }

    .header-logo .logo-img img {
        width: 36px;
        height: 36px;
    }

    .header-actions {
        gap: 8px;
    }

    .logout-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-height: 36px;
        border-radius: 8px;
    }

    .notification-btn {
        padding: 8px;
        min-width: 36px;
        min-height: 36px;
    }

    .notification-btn svg {
        width: 20px;
        height: 20px;
    }

    .notification-badge {
        min-width: 18px;
        min-height: 18px;
        font-size: 10px;
        top: -1px;
        right: -1px;
    }

    .notifications-dropdown {
        width: calc(100vw - 24px);
        right: -12px;
        left: 12px;
        max-width: 300px;
    }

    .notifications-header {
        padding: 12px 15px;
    }

    .notifications-header h3 {
        font-size: 14px;
    }

    .mark-all-read {
        font-size: 11px;
    }
        .logout-btn {
        display: block;
        padding: 8px 12px;
        font-size: 13px;
        min-height: 40px;
    }
}

@media (max-width: 320px) {
    .header-container {
        padding: 0 8px;
        gap: 8px;
    }

    .header-logo .logo-text h2 {
        font-size: 14px;
    }

    .header-logo .logo-img img {
        width: 32px;
        height: 32px;
    }

    .logout-btn {
        padding: 5px 8px;
        font-size: 11px;
        min-height: 32px;
    }

    .notification-btn {
        padding: 6px;
        min-width: 32px;
        min-height: 32px;
    }

    .mobile-menu-toggle {
        width: 24px;
        height: 24px;
    }

    .notifications-dropdown {
        width: calc(100vw - 16px);
        right: -8px;
        left: 8px;
    }
        .logout-btn {
        display: block;
        padding: 8px 12px;
        font-size: 13px;
        min-height: 40px;
    }
}
</style>

<script>
// Update time and date
function updateDateTime() {
    const now = new Date();
    
    // Format time
    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };
    document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-EG', timeOptions);
    
    // Format date
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-EG', dateOptions);
}

// Update every second
setInterval(updateDateTime, 1000);
updateDateTime(); // Initial call

// Toggle notifications dropdown
function toggleNotifications() {
    const dropdown = document.getElementById('notificationsDropdown');
    dropdown.classList.toggle('show');

    if (dropdown.classList.contains('show')) {
        loadNotifications();
    }
}

// Toggle user dropdown
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const notificationButton = document.querySelector('.notification-btn');
    const userDropdown = document.getElementById('userDropdown');
    const userAvatar = document.querySelector('.user-avatar');

    // Close notifications dropdown
    if (!notificationsDropdown.contains(event.target) && !notificationButton.contains(event.target)) {
        notificationsDropdown.classList.remove('show');
    }

    // Close user dropdown
    if (!userDropdown.contains(event.target) && !userAvatar.contains(event.target)) {
        userDropdown.classList.remove('show');
    }
});

// Load notifications via AJAX
function loadNotifications() {
    const notificationsList = document.getElementById('notificationsList');

    if (!notificationsList) {
        console.error('Notifications list element not found');
        return;
    }

    // Show loading state
    notificationsList.innerHTML = `
        <div class="notifications-loading">
            <div class="loading-spinner-small"></div>
            <span>جاري تحميل الإشعارات...</span>
        </div>
    `;

    // Determine the correct path based on current location
    let notificationPath = '/manash/includes/get_notifications.php';

    console.log('Loading notifications from:', notificationPath);

    fetch(notificationPath, {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.text().then(text => {
                console.log('Raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                }
            });
        })
        .then(data => {
            console.log('Parsed data:', data);

            if (data.success) {
                if (data.notifications && data.notifications.length > 0) {
                    notificationsList.innerHTML = data.notifications.map(notification => `
                        <div class="notification-item ${notification.is_read ? '' : 'unread'}"
                             data-id="${notification.id}"
                             data-type="${notification.type}"
                             onclick="handleNotificationClick(${notification.id}, ${notification.is_read})">
                            <div class="notification-content">
                                <h4>${escapeHtml(notification.title)}</h4>
                                <p>${escapeHtml(notification.message)}</p>
                                <small>${formatNotificationTime(notification.created_at)}</small>
                            </div>
                            ${!notification.is_read ? '<div class="notification-dot"></div>' : ''}
                        </div>
                    `).join('');

                    // Add click handlers for individual notifications
                    addNotificationClickHandlers();
                } else {
                    notificationsList.innerHTML = '<div class="no-notifications">لا توجد إشعارات جديدة</div>';
                }
            } else {
                throw new Error(data.message || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            notificationsList.innerHTML = `
                <div class="no-notifications">
                    <span style="color: #dc3545;">حدث خطأ في تحميل الإشعارات</span>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">${error.message}</div>
                    <button onclick="loadNotifications()" style="margin-top: 10px; padding: 8px 16px; background: #4682B4; color: white; border: none; border-radius: 8px; cursor: pointer;">إعادة المحاولة</button>
                </div>
            `;
        });
}

// Handle notification click
function handleNotificationClick(notificationId, isRead) {
    if (!isRead) {
        markNotificationAsRead(notificationId);
    }
}

// Mark individual notification as read
function markNotificationAsRead(notificationId) {
    fetch('includes/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notification_id: notificationId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the notification item visually
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.remove('unread');
                const dot = notificationItem.querySelector('.notification-dot');
                if (dot) {
                    dot.remove();
                }
            }

            // Update badge count
            updateNotificationBadge();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

// Add click handlers for notifications
function addNotificationClickHandlers() {
    document.querySelectorAll('.notification-item').forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            const isRead = !this.classList.contains('unread');

            if (!isRead) {
                markNotificationAsRead(notificationId);
            }
        });
    });
}

// Mark all notifications as read
function markAllNotificationsRead() {
    const button = document.querySelector('.mark-all-read');
    const originalContent = button.innerHTML;

    // Show loading state
    button.innerHTML = '<div class="loading-spinner-small"></div>';
    button.disabled = true;

    fetch('includes/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mark_all: true })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove all unread indicators
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                const dot = item.querySelector('.notification-dot');
                if (dot) {
                    dot.remove();
                }
            });

            // Update notification badge
            updateNotificationBadge();

            // Show success message
            showNotificationToast('تم تحديد جميع الإشعارات كمقروءة', 'success');
        } else {
            showNotificationToast('حدث خطأ في تحديث الإشعارات', 'error');
        }
    })
    .catch(error => {
        console.error('Error marking notifications as read:', error);
        showNotificationToast('حدث خطأ في الاتصال', 'error');
    })
    .finally(() => {
        button.innerHTML = originalContent;
        button.disabled = false;
    });
}

// Update notification badge
function updateNotificationBadge() {
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    const badge = document.getElementById('notificationBadge');

    if (unreadCount > 0) {
        if (badge) {
            badge.textContent = unreadCount;
        } else {
            const notificationBtn = document.querySelector('.notification-btn');
            const newBadge = document.createElement('span');
            newBadge.className = 'notification-badge';
            newBadge.id = 'notificationBadge';
            newBadge.textContent = unreadCount;
            notificationBtn.appendChild(newBadge);
        }
    } else {
        if (badge) {
            badge.remove();
        }
    }
}

// Helper functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatNotificationTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) {
        return 'الآن';
    } else if (diffInMinutes < 60) {
        return `منذ ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) {
        const hours = Math.floor(diffInMinutes / 60);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInMinutes / 1440);
        return `منذ ${days} يوم`;
    }
}

// View all notifications
function viewAllNotifications() {
    window.location.href = 'notifications.php';
}

// Show notification toast (using new notification system)
function showNotificationToast(message, type = 'info') {
    if (window.notifications) {
        window.notifications.showToast(message, type);
    } else {
        // Fallback for older browsers
        alert(message);
    }
}

// Initialize notification system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh notifications every 30 seconds
    setInterval(() => {
        if (window.notifications) {
            window.notifications.refreshNotificationBadge();
        }
    }, 30000);

    // Load notifications on page load
    if (document.getElementById('notificationsDropdown')) {
        // Initial load will happen when dropdown is opened
    }
});
</script>
