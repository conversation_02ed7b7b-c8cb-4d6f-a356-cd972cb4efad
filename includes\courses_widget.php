<?php
// Get user's active courses
$stmt = $db->prepare("
    SELECT c.id, c.title, c.subject, c.main_image, cs.activated_at,
           COUNT(DISTINCT cv.id) as total_videos,
           COUNT(DISTINCT CASE WHEN uvp.completed = 1 THEN uvp.video_id END) as completed_videos
    FROM course_subscriptions cs
    JOIN courses c ON cs.course_id = c.id
    LEFT JOIN course_videos cv ON c.id = cv.course_id AND cv.is_active = 1
    LEFT JOIN user_video_progress uvp ON cv.id = uvp.video_id AND uvp.user_id = cs.user_id
    WHERE cs.user_id = ? AND cs.activation_status = 'active' AND c.is_active = 1
    GROUP BY c.id, c.title, c.subject, c.main_image, cs.activated_at
    ORDER BY cs.activated_at DESC
    LIMIT 3
");
$stmt->execute([$_SESSION['user_id']]);
$userCourses = $stmt->fetchAll();
?>

<div class="widget courses-widget">
    <div class="widget-header">
        <h3>
            <span class="widget-icon">🎓</span>
            كورساتي
        </h3>
        <a href="courses.php" class="widget-action">عرض الكل</a>
    </div>
    
    <div class="widget-content">
        <?php if (!empty($userCourses)): ?>
            <div class="courses-list">
                <?php foreach ($userCourses as $course): ?>
                    <div class="course-item">
                        <div class="course-image">
                            <?php if ($course['main_image']): ?>
                                <img src="../uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-info">
                            <h4 class="course-title"><?php echo htmlspecialchars($course['title']); ?></h4>
                            <p class="course-subject"><?php echo htmlspecialchars($course['subject']); ?></p>
                            
                            <?php if ($course['total_videos'] > 0): ?>
                                <div class="course-progress">
                                    <div class="progress-info">
                                        <span><?php echo $course['completed_videos']; ?> من <?php echo $course['total_videos']; ?> فيديو</span>
                                        <span><?php echo $course['total_videos'] > 0 ? round(($course['completed_videos'] / $course['total_videos']) * 100) : 0; ?>%</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo $course['total_videos'] > 0 ? ($course['completed_videos'] / $course['total_videos']) * 100 : 0; ?>%"></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-actions">
                            <a href="course_content.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-primary">
                                <span class="btn-icon">▶️</span>
                                متابعة
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php if (count($userCourses) === 0): ?>
                <div class="empty-state">
                    <div class="empty-icon">🎓</div>
                    <p>لم تسجل في أي كورس بعد</p>
                    <a href="courses.php" class="btn btn-primary">تصفح الكورسات</a>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-icon">🎓</div>
                <p>لم تسجل في أي كورس بعد</p>
                <a href="courses.php" class="btn btn-primary">تصفح الكورسات</a>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.courses-widget .courses-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.courses-widget .course-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.courses-widget .course-item:hover {
    border-color: #87CEEB;
    background: rgba(135, 206, 235, 0.05);
}

.courses-widget .course-image {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.courses-widget .course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.courses-widget .course-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.courses-widget .course-icon {
    font-size: 24px;
    color: white;
}

.courses-widget .course-info {
    flex: 1;
    min-width: 0;
}

.courses-widget .course-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 5px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.courses-widget .course-subject {
    font-size: 14px;
    color: #6c757d;
    margin: 0 0 10px 0;
}

.courses-widget .course-progress {
    margin-top: 8px;
}

.courses-widget .progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.courses-widget .progress-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.courses-widget .progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    transition: width 0.3s ease;
}

.courses-widget .course-actions {
    flex-shrink: 0;
}

.courses-widget .btn-sm {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.courses-widget .btn-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border: none;
}

.courses-widget .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
}

.courses-widget .btn-icon {
    font-size: 12px;
}

.courses-widget .empty-state {
    text-align: center;
    padding: 40px 20px;
}

.courses-widget .empty-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.courses-widget .empty-state p {
    color: #6c757d;
    margin-bottom: 20px;
}

.courses-widget .empty-state .btn {
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.courses-widget .empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .courses-widget .course-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .courses-widget .course-image {
        width: 80px;
        height: 80px;
    }
    
    .courses-widget .course-info {
        text-align: center;
    }
    
    .courses-widget .course-title {
        white-space: normal;
    }
}
</style>
