<?php
require_once '../config/config.php';
require_once '../includes/database.php';
require_once '../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$transactionId = $_GET['transaction_id'] ?? null;
$courseId = null;
$course = null;
$transaction = null;

if ($transactionId) {
    // Get transaction details
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("
        SELECT pt.*, c.title as course_title, c.id as course_id
        FROM payment_transactions pt
        JOIN courses c ON pt.course_id = c.id
        WHERE pt.transaction_id = ? AND pt.user_id = ?
    ");
    $stmt->execute([$transactionId, $userId]);
    $transaction = $stmt->fetch();
    
    if ($transaction) {
        $courseId = $transaction['course_id'];
        $courseManager = new CourseManager();
        $course = $courseManager->getCourseById($courseId);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم الدفع بنجاح - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once '../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include '../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <div class="success-container">
                    <div class="success-card">
                        <div class="success-icon">✅</div>
                        <h1>تم الدفع بنجاح!</h1>
                        <p class="success-message">تهانينا! تم تفعيل الكورس بنجاح ويمكنك الآن الوصول إلى جميع محتوياته.</p>
                        
                        <?php if ($transaction && $course): ?>
                            <div class="transaction-details">
                                <h3>تفاصيل المعاملة</h3>
                                <div class="detail-row">
                                    <span>الكورس:</span>
                                    <span><?php echo htmlspecialchars($course['title']); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span>المبلغ المدفوع:</span>
                                    <span><?php echo number_format($transaction['amount'], 0); ?> جنيه</span>
                                </div>
                                <div class="detail-row">
                                    <span>طريقة الدفع:</span>
                                    <span>
                                        <?php 
                                        echo $transaction['gateway'] === 'fawry' ? 'فوري' : 'فيزا/ماستركارد';
                                        ?>
                                    </span>
                                </div>
                                <div class="detail-row">
                                    <span>رقم المعاملة:</span>
                                    <span class="transaction-id"><?php echo htmlspecialchars($transaction['transaction_id']); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span>تاريخ الدفع:</span>
                                    <span><?php echo date('Y-m-d H:i', strtotime($transaction['updated_at'])); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="success-actions">
                            <?php if ($courseId): ?>
                                <a href="../page/course_content.php?id=<?php echo $courseId; ?>" class="btn btn-primary btn-large">
                                    <span class="btn-icon">📚</span>
                                    بدء الكورس الآن
                                </a>
                            <?php endif; ?>
                            <a href="../page/courses.php" class="btn btn-secondary btn-large">
                                <span class="btn-icon">📋</span>
                                عرض جميع الكورسات
                            </a>
                            <a href="../page/dashboard.php" class="btn btn-outline btn-large">
                                <span class="btn-icon">🏠</span>
                                العودة للرئيسية
                            </a>
                        </div>
                        
                        <div class="next-steps">
                            <h3>الخطوات التالية</h3>
                            <div class="steps-grid">
                                <div class="step">
                                    <div class="step-icon">📖</div>
                                    <h4>ابدأ التعلم</h4>
                                    <p>اضغط على "بدء الكورس" للوصول إلى المحتوى</p>
                                </div>
                                <div class="step">
                                    <div class="step-icon">📝</div>
                                    <h4>تابع التقدم</h4>
                                    <p>راقب تقدمك في الكورس من لوحة التحكم</p>
                                </div>
                                <div class="step">
                                    <div class="step-icon">🎯</div>
                                    <h4>اختبر نفسك</h4>
                                    <p>حل الاختبارات والتمارين لتقييم فهمك</p>
                                </div>
                                <div class="step">
                                    <div class="step-icon">🏆</div>
                                    <h4>احصل على الشهادة</h4>
                                    <p>أكمل الكورس واحصل على شهادة إتمام</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="support-info">
                            <h3>تحتاج مساعدة؟</h3>
                            <p>إذا واجهت أي مشكلة أو لديك استفسار، تواصل معنا:</p>
                            <a href="https://wa.me/201128031228" target="_blank" class="whatsapp-link">
                                📱 واتساب: 01128031228
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .success-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .success-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .success-icon {
            font-size: 120px;
            margin-bottom: 30px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .success-card h1 {
            color: #28a745;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .success-message {
            color: #6c757d;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 40px;
        }

        .transaction-details {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            text-align: right;
        }

        .transaction-details h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .transaction-id {
            font-family: monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
        }

        .success-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .btn-outline {
            background: white;
            color: #6c757d;
            border: 2px solid #6c757d;
        }

        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }

        .next-steps {
            margin-bottom: 40px;
        }

        .next-steps h3 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 30px;
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
        }

        .step {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .step:hover {
            transform: translateY(-5px);
        }

        .step-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .step h4 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step p {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }

        .support-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
        }

        .support-info h3 {
            color: #1976d2;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .support-info p {
            color: #1976d2;
            margin-bottom: 20px;
        }

        .whatsapp-link {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: #25d366;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .whatsapp-link:hover {
            background: #128c7e;
            transform: translateY(-2px);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .success-container {
                padding: 15px;
            }

            .success-card {
                padding: 25px;
            }

            .success-card h1 {
                font-size: 28px;
            }

            .success-icon {
                font-size: 80px;
            }

            .success-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }

            .steps-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // Auto-redirect to course if no interaction after 10 seconds
        <?php if ($courseId): ?>
        setTimeout(function() {
            if (confirm('هل تريد الانتقال إلى الكورس الآن؟')) {
                window.location.href = '../page/course_content.php?id=<?php echo $courseId; ?>';
            }
        }, 10000);
        <?php endif; ?>
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
