<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Handle admin creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirmPassword = trim($_POST['confirm_password'] ?? '');
    $fullName = trim($_POST['full_name'] ?? '');
    $role = $_POST['role'] ?? 'admin';
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($fullName)) {
        $message = 'جميع الحقول مطلوبة';
        $messageType = 'error';
    } elseif ($password !== $confirmPassword) {
        $message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
        $messageType = 'error';
    } elseif (strlen($password) < 6) {
        $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'البريد الإلكتروني غير صحيح';
        $messageType = 'error';
    } else {
        try {
            $adminManager = new AdminManager();
            
            // Check if username or email already exists
            if ($adminManager->adminExists($username, $email)) {
                $message = 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل';
                $messageType = 'error';
            } else {
                // Create admin
                $result = $adminManager->createAdmin($username, $email, $password, $fullName, $role);
                
                if ($result) {
                    $message = 'تم إنشاء حساب المدير بنجاح';
                    $messageType = 'success';
                    
                    // Clear form data on success
                    $_POST = [];
                } else {
                    $message = 'فشل في إنشاء حساب المدير';
                    $messageType = 'error';
                }
            }
        } catch (Exception $e) {
            $message = 'حدث خطأ: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get current admin information
$adminManager = new AdminManager();
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);
$adminName = $adminData['full_name'] ?? 'المدير';
$currentAdminRole = $adminData['role'] ?? 'admin';

// Check if current admin can create other admins
$canCreateSuperAdmin = ($currentAdminRole === 'super_admin');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مدير - <?php echo defined('SITE_NAME') ? SITE_NAME : 'لوحة التحكم'; ?></title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="css/admin-fixes.css">
    <link rel="stylesheet" href="../css/notifications.css">
    <style>
        .admin-form {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            color: #4682B4;
            border-bottom: 2px solid #87CEEB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #4682B4;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .role-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .role-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .role-option:hover {
            border-color: #4682B4;
            background: #f8f9fa;
        }
        
        .role-option.selected {
            border-color: #4682B4;
            background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
        }
        
        .role-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .role-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .role-description {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .role-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
        
        .btn-primary:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .message {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 14px;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-box .icon {
            color: #1976d2;
            margin-left: 8px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .role-selection {
                grid-template-columns: 1fr;
            }
            
            .admin-form {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="page-header">
                <h1>👨‍💼 إضافة مدير جديد</h1>
                <p>إدارة وتحكم في النظام</p>
            </div>
                
                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <div class="info-box">
                    <span class="icon">ℹ️</span>
                    <strong>ملاحظة:</strong> يمكن للمدير العادي إدارة المستخدمين وإرسال الإشعارات، بينما المدير الأعلى يمكنه أيضاً إدارة المديرين الآخرين.
                </div>

                <form method="POST" class="admin-form" id="adminForm">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3>📝 المعلومات الأساسية</h3>
                        
                        <div class="form-group">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" id="full_name" name="full_name" class="form-input" required 
                                   placeholder="أدخل الاسم الكامل" value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" id="username" name="username" class="form-input" required 
                                       placeholder="أدخل اسم المستخدم" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" id="email" name="email" class="form-input" required 
                                       placeholder="أدخل البريد الإلكتروني" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Password Section -->
                    <div class="form-section">
                        <h3>🔒 كلمة المرور</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input type="password" id="password" name="password" class="form-input" required 
                                       placeholder="أدخل كلمة مرور قوية" minlength="6">
                                <div id="passwordStrength" class="password-strength"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                <input type="password" id="confirm_password" name="confirm_password" class="form-input" required 
                                       placeholder="أعد إدخال كلمة المرور" minlength="6">
                                <div id="passwordMatch" class="password-strength"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Role Selection -->
                    <div class="form-section">
                        <h3>🎭 صلاحيات المدير</h3>
                        
                        <div class="role-selection">
                            <label class="role-option" for="role_admin">
                                <input type="radio" id="role_admin" name="role" value="admin" 
                                       <?php echo ($_POST['role'] ?? 'admin') === 'admin' ? 'checked' : ''; ?>>
                                <span class="role-icon">👨‍💼</span>
                                <div class="role-title">مدير عادي</div>
                                <div class="role-description">يمكنه إدارة المستخدمين وإرسال الإشعارات</div>
                            </label>
                            
                            <?php if ($canCreateSuperAdmin): ?>
                            <label class="role-option" for="role_super_admin">
                                <input type="radio" id="role_super_admin" name="role" value="super_admin"
                                       <?php echo ($_POST['role'] ?? '') === 'super_admin' ? 'checked' : ''; ?>>
                                <span class="role-icon">👑</span>
                                <div class="role-title">مدير أعلى</div>
                                <div class="role-description">صلاحيات كاملة بما في ذلك إدارة المديرين</div>
                            </label>
                            <?php else: ?>
                            <div class="role-option" style="opacity: 0.5; cursor: not-allowed;">
                                <span class="role-icon">👑</span>
                                <div class="role-title">مدير أعلى</div>
                                <div class="role-description">يتطلب صلاحيات مدير أعلى لإنشاء هذا النوع</div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn-primary" id="submitBtn">
                            ➕ إنشاء حساب المدير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Role selection handling
        document.querySelectorAll('.role-option').forEach(option => {
            option.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                if (radio && !radio.disabled) {
                    radio.checked = true;
                    updateRoleSelection();
                }
            });
        });

        function updateRoleSelection() {
            document.querySelectorAll('.role-option').forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                if (radio && radio.checked) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            if (strength < 2) {
                strengthDiv.textContent = 'ضعيفة';
                strengthDiv.className = 'password-strength strength-weak';
            } else if (strength < 4) {
                strengthDiv.textContent = 'متوسطة';
                strengthDiv.className = 'password-strength strength-medium';
            } else {
                strengthDiv.textContent = 'قوية';
                strengthDiv.className = 'password-strength strength-strong';
            }
            
            checkPasswordMatch();
        });

        // Password match checker
        document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);

        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchDiv = document.getElementById('passwordMatch');
            
            if (confirmPassword.length === 0) {
                matchDiv.textContent = '';
                return;
            }
            
            if (password === confirmPassword) {
                matchDiv.textContent = 'كلمات المرور متطابقة ✓';
                matchDiv.className = 'password-strength strength-strong';
            } else {
                matchDiv.textContent = 'كلمات المرور غير متطابقة ✗';
                matchDiv.className = 'password-strength strength-weak';
            }
        }

        // Form validation
        document.getElementById('adminForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateRoleSelection();
        });
    </script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
