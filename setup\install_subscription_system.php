<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>تثبيت نظام الاشتراكات</h2>";
    
    // Read and execute SQL file
    $sql = file_get_contents(__DIR__ . '/../sql/subscription_system.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            try {
                $db->exec($statement);
                $successCount++;
                echo "<p style='color: green;'>✅ تم تنفيذ الاستعلام بنجاح</p>";
            } catch (Exception $e) {
                $errorCount++;
                echo "<p style='color: red;'>❌ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
                echo "<details><summary>تفاصيل الاستعلام</summary><pre>" . htmlspecialchars($statement) . "</pre></details>";
            }
        }
    }
    
    // Check if tables were created
    $tables = ['subscription_plans', 'user_subscriptions', 'activation_codes', 'payments', 'subscription_stats'];
    $createdTables = [];
    
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $createdTables[] = $table;
        }
    }
    
    echo "<h3>النتائج:</h3>";
    echo "<p>✅ الاستعلامات الناجحة: $successCount</p>";
    echo "<p>❌ الاستعلامات الفاشلة: $errorCount</p>";
    echo "<p>📊 الجداول المنشأة: " . count($createdTables) . " من " . count($tables) . "</p>";
    
    if (count($createdTables) === count($tables)) {
        echo "<h3 style='color: green;'>🎉 تم تثبيت نظام الاشتراكات بنجاح!</h3>";
        
        // Show sample data
        echo "<h3>الخطط المتاحة:</h3>";
        $stmt = $db->query("SELECT * FROM subscription_plans ORDER BY sort_order");
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($plans) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #4682B4; color: white;'>";
            echo "<th>الاسم</th><th>السعر</th><th>السعر بعد الخصم</th><th>المدة (أيام)</th><th>شائع</th><th>نشط</th>";
            echo "</tr>";
            
            foreach ($plans as $plan) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($plan['name']) . "</td>";
                echo "<td>" . number_format($plan['price'], 2) . " جنيه</td>";
                echo "<td>" . number_format($plan['discounted_price'], 2) . " جنيه</td>";
                echo "<td>" . $plan['duration_days'] . " يوم</td>";
                echo "<td>" . ($plan['is_popular'] ? '⭐ نعم' : 'لا') . "</td>";
                echo "<td>" . ($plan['is_active'] ? '✅ نشط' : '❌ غير نشط') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Show activation codes
        echo "<h3>أكواد التفعيل التجريبية:</h3>";
        $stmt = $db->query("SELECT ac.code, sp.name as plan_name, ac.expires_at, ac.is_used 
                           FROM activation_codes ac 
                           JOIN subscription_plans sp ON ac.plan_id = sp.id 
                           ORDER BY ac.created_at DESC");
        $codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($codes) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #28a745; color: white;'>";
            echo "<th>الكود</th><th>الخطة</th><th>تاريخ الانتهاء</th><th>الحالة</th>";
            echo "</tr>";
            
            foreach ($codes as $code) {
                echo "<tr>";
                echo "<td><strong>" . htmlspecialchars($code['code']) . "</strong></td>";
                echo "<td>" . htmlspecialchars($code['plan_name']) . "</td>";
                echo "<td>" . date('Y-m-d H:i', strtotime($code['expires_at'])) . "</td>";
                echo "<td>" . ($code['is_used'] ? '❌ مستخدم' : '✅ متاح') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<h3 style='color: red;'>❌ فشل في تثبيت بعض الجداول</h3>";
        echo "<p>الجداول المفقودة: " . implode(', ', array_diff($tables, $createdTables)) . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='../admin/subscription_plans.php'>إدارة الخطط</a></li>";
    echo "<li><a href='../admin/activation_codes.php'>إدارة أكواد التفعيل</a></li>";
    echo "<li><a href='../admin/subscribers.php'>إدارة المشتركين</a></li>";
    echo "<li><a href='../page/subscriptions.php'>صفحة الاشتراكات للمستخدمين</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background: #f5f5f5;
        direction: rtl;
    }
    
    h2, h3 {
        color: #333;
    }
    
    table {
        background: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 5px;
        overflow: hidden;
    }
    
    th {
        padding: 12px;
        font-weight: bold;
    }
    
    td {
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }
    
    tr:hover {
        background: #f9f9f9;
    }
    
    a {
        color: #4682B4;
        text-decoration: none;
        font-weight: bold;
    }
    
    a:hover {
        text-decoration: underline;
    }
    
    ul {
        background: white;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    li {
        margin: 10px 0;
    }
    
    details {
        margin: 10px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    
    summary {
        cursor: pointer;
        font-weight: bold;
        color: #dc3545;
    }
    
    pre {
        background: white;
        padding: 10px;
        border-radius: 3px;
        overflow-x: auto;
        font-size: 12px;
    }
</style>
