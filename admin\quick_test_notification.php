<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/NotificationManager.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>اختبار سريع للإشعارات</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
.success { color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
.error { color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
.info { color: blue; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
.btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; color: white; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; }
</style>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $notificationManager = new NotificationManager();
    
    // Get first user for testing
    $stmt = $db->query("SELECT id, username FROM users ORDER BY id LIMIT 1");
    $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testUser) {
        echo "<div class='error'>لا يوجد مستخدمين للاختبار</div>";
        exit;
    }
    
    $user_id = $testUser['id'];
    $username = $testUser['username'];
    
    echo "<div class='info'>🧪 اختبار مع المستخدم: $username (ID: $user_id)</div>";
    
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'setup':
                // 1. Create tables
                try {
                    $createNotificationsSQL = "CREATE TABLE IF NOT EXISTS user_notifications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        notification_type ENUM('subscription_expired', 'subscription_expiring', 'subscription_renewed', 'subscription_cancelled') NOT NULL,
                        title VARCHAR(255) NOT NULL,
                        message TEXT NOT NULL,
                        is_read BOOLEAN DEFAULT FALSE,
                        is_dismissed BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NULL,
                        metadata JSON NULL,
                        INDEX idx_user_id (user_id),
                        INDEX idx_type (notification_type)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                    
                    $db->exec($createNotificationsSQL);
                    
                    $createSettingsSQL = "CREATE TABLE IF NOT EXISTS notification_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        notification_type VARCHAR(50) NOT NULL,
                        is_enabled BOOLEAN DEFAULT TRUE,
                        last_shown TIMESTAMP NULL,
                        show_count INT DEFAULT 0,
                        max_show_count INT DEFAULT 1,
                        UNIQUE KEY unique_user_type (user_id, notification_type)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                    
                    $db->exec($createSettingsSQL);
                    
                    echo "<div class='success'>✅ تم إنشاء الجداول</div>";
                } catch (Exception $e) {
                    echo "<div class='info'>الجداول موجودة بالفعل</div>";
                }
                
                // 2. Expire user subscription
                $stmt = $db->prepare("UPDATE users SET subscription_status = 'expired', current_plan_id = NULL WHERE id = ?");
                $stmt->execute([$user_id]);
                echo "<div class='success'>✅ تم انتهاء اشتراك المستخدم</div>";
                
                // 3. Reset notification settings
                $notificationManager->resetUserNotificationSettings($user_id, 'subscription_expired');
                echo "<div class='success'>✅ تم إعادة تعيين إعدادات الإشعارات</div>";
                
                echo "<div class='success'>🎉 تم الإعداد بنجاح! الآن افتح الداشبورد للمستخدم لرؤية الإشعار</div>";
                break;
                
            case 'reset':
                // Reset notification settings
                $notificationManager->resetUserNotificationSettings($user_id, 'subscription_expired');
                echo "<div class='success'>✅ تم إعادة تعيين الإشعارات - يمكن إظهار الإشعار مرة أخرى</div>";
                break;
                
            case 'check':
                // Check if notification should show
                $shouldShow = $notificationManager->shouldShowSubscriptionExpiredNotification($user_id);
                echo "<div class='info'>🔍 يجب إظهار الإشعار: " . ($shouldShow ? 'نعم ✅' : 'لا ❌') . "</div>";
                break;
        }
    }
    
    // Show current status
    $stmt = $db->prepare("SELECT subscription_status FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_status = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='info'>📊 حالة اشتراك المستخدم: " . ($user_status['subscription_status'] ?: 'غير محدد') . "</div>";
    
    // Check notification settings
    try {
        $stmt = $db->prepare("SELECT * FROM notification_settings WHERE user_id = ? AND notification_type = 'subscription_expired'");
        $stmt->execute([$user_id]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($settings) {
            echo "<div class='info'>⚙️ إعدادات الإشعار: عُرض {$settings['show_count']} مرة، آخر عرض: " . ($settings['last_shown'] ?: 'لم يُعرض') . "</div>";
        } else {
            echo "<div class='info'>⚙️ لا توجد إعدادات إشعار (سيظهر الإشعار)</div>";
        }
    } catch (Exception $e) {
        echo "<div class='info'>⚙️ جدول الإعدادات غير موجود</div>";
    }
    
    // Check if should show
    $shouldShow = $notificationManager->shouldShowSubscriptionExpiredNotification($user_id);
    echo "<div class='info'>🔔 يجب إظهار الإشعار: " . ($shouldShow ? 'نعم ✅' : 'لا ❌') . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<h3>خطوات الاختبار:</h3>

<form method="POST">
    <button type="submit" name="action" value="setup" class="btn btn-primary">
        1️⃣ إعداد الاختبار (إنشاء جداول + انتهاء اشتراك)
    </button>
</form>

<form method="POST">
    <button type="submit" name="action" value="check" class="btn btn-warning">
        2️⃣ فحص حالة الإشعار
    </button>
</form>

<div class="info">
    <strong>3️⃣ افتح الداشبورد:</strong>
    <a href="../page/dashboard.php" class="btn btn-success" target="_blank">فتح الداشبورد</a>
    <p>يجب أن يظهر إشعار انتهاء الاشتراك</p>
</div>

<div class="info">
    <strong>4️⃣ أغلق الإشعار وأعد تحميل الصفحة - لن يظهر مرة أخرى</strong>
</div>

<form method="POST">
    <button type="submit" name="action" value="reset" class="btn btn-danger">
        5️⃣ إعادة تعيين الإشعارات (لإظهار الإشعار مرة أخرى)
    </button>
</form>

<div class="info">
    <h4>📋 ملاحظات:</h4>
    <ul>
        <li>الإشعار يظهر مرة واحدة فقط لكل مستخدم</li>
        <li>بعد إغلاق الإشعار، لن يظهر مرة أخرى حتى إعادة تعيين الإعدادات</li>
        <li>الإشعار يظهر في الداشبورد وصفحة المنهج وصفحة الاشتراكات</li>
        <li>يمكن إغلاق الإشعار بالنقر على X أو خارج النافذة أو مفتاح Escape</li>
    </ul>
</div>

<p>
    <a href="test_notifications.php" class="btn btn-primary">اختبار متقدم للإشعارات</a>
    <a href="test_subscription_expiry.php" class="btn btn-warning">اختبار انتهاء الاشتراك</a>
</p>
