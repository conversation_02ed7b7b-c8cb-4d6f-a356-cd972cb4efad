<?php
require_once 'database.php';

class UserSettingsManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * Get user settings
     */
    public function getUserSettings($userId) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM user_settings WHERE user_id = ?");
            $stmt->execute([$userId]);
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Return default settings if no settings found
            if (!$settings) {
                return $this->getDefaultSettings();
            }
            
            return $settings;
        } catch (Exception $e) {
            error_log("Error getting user settings: " . $e->getMessage());
            return $this->getDefaultSettings();
        }
    }
    
    /**
     * Update user settings
     */
    public function updateUserSettings($userId, $settings) {
        try {
            // Check if settings exist
            $existingSettings = $this->getUserSettings($userId);
            
            if ($existingSettings && isset($existingSettings['user_id'])) {
                // Update existing settings
                $stmt = $this->db->prepare("
                    UPDATE user_settings 
                    SET theme_preference = ?, 
                        notification_preferences = ?, 
                        language_preference = ?,
                        updated_at = NOW()
                    WHERE user_id = ?
                ");
                
                return $stmt->execute([
                    $settings['theme_preference'] ?? 'light',
                    json_encode($settings['notification_preferences'] ?? []),
                    $settings['language_preference'] ?? 'ar',
                    $userId
                ]);
            } else {
                // Insert new settings
                $stmt = $this->db->prepare("
                    INSERT INTO user_settings 
                    (user_id, theme_preference, notification_preferences, language_preference, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                ");
                
                return $stmt->execute([
                    $userId,
                    $settings['theme_preference'] ?? 'light',
                    json_encode($settings['notification_preferences'] ?? []),
                    $settings['language_preference'] ?? 'ar'
                ]);
            }
        } catch (Exception $e) {
            error_log("Error updating user settings: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get default settings
     */
    private function getDefaultSettings() {
        return [
            'theme_preference' => 'light',
            'notification_preferences' => [],
            'language_preference' => 'ar'
        ];
    }
    
    /**
     * Create user settings table if not exists
     */
    public function createUserSettingsTable() {
        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS user_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    theme_preference VARCHAR(20) DEFAULT 'light',
                    notification_preferences TEXT,
                    language_preference VARCHAR(10) DEFAULT 'ar',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user_settings (user_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            return $this->db->exec($sql);
        } catch (Exception $e) {
            error_log("Error creating user_settings table: " . $e->getMessage());
            return false;
        }
    }
}
?>
