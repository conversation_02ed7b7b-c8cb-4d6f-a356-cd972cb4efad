<?php
require_once __DIR__ . '/../config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إضافة عمود duration_days</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #333; text-align: center; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 إضافة عمود duration_days لجدول user_subscriptions</h1>";

try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='info'>🔍 فحص وجود عمود duration_days...</div>";
    
    // Check if duration_days column exists
    $stmt = $db->query("SHOW COLUMNS FROM user_subscriptions LIKE 'duration_days'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='info'>➕ إضافة عمود duration_days...</div>";
        $db->exec("ALTER TABLE user_subscriptions ADD COLUMN duration_days INT NULL COMMENT 'مدة الاشتراك بالأيام'");
        echo "<div class='success'>✅ تم إضافة عمود duration_days بنجاح</div>";
        
        // Update existing records with duration from their plans
        echo "<div class='info'>🔄 تحديث السجلات الموجودة...</div>";
        $db->exec("UPDATE user_subscriptions us 
                   JOIN subscription_plans sp ON us.plan_id = sp.id 
                   SET us.duration_days = sp.duration_days 
                   WHERE us.duration_days IS NULL");
        echo "<div class='success'>✅ تم تحديث السجلات الموجودة</div>";
    } else {
        echo "<div class='info'>ℹ️ عمود duration_days موجود بالفعل</div>";
    }
    
    echo "<div class='success'>🎉 تم الانتهاء من العملية بنجاح!</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "    </div>
</body>
</html>";
?>
