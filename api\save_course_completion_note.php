<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['course_id'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit;
    }
    
    $courseId = (int)$input['course_id'];
    $userId = $_SESSION['user_id'];
    $completionDate = $input['completion_date'] ?? date('Y-m-d H:i:s');
    
    // Create table if it doesn't exist
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_completion_notes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            completion_date DATETIME NOT NULL,
            note TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_completion (user_id, course_id),
            INDEX idx_user_id (user_id),
            INDEX idx_course_id (course_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Get course title
    $stmt = $db->prepare("SELECT title FROM courses WHERE id = ?");
    $stmt->execute([$courseId]);
    $course = $stmt->fetch();
    
    if (!$course) {
        echo json_encode(['success' => false, 'message' => 'الكورس غير موجود']);
        exit;
    }
    
    // Insert or update completion note
    $stmt = $db->prepare("
        INSERT INTO course_completion_notes (user_id, course_id, completion_date, note)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            completion_date = VALUES(completion_date),
            note = VALUES(note)
    ");
    
    $note = "تم إكمال كورس '{$course['title']}' بنجاح في " . date('Y-m-d H:i:s', strtotime($completionDate));
    
    $result = $stmt->execute([$userId, $courseId, $completionDate, $note]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ ملحوظة إكمال الكورس بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ في حفظ الملحوظة'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error saving course completion note: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم'
    ]);
}
?>
