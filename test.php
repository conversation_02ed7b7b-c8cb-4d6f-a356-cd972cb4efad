<?php
// ملف اختبار بسيط
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار الموقع</title>";
echo "</head>";
echo "<body>";
echo "<h1>مرحباً! الموقع يعمل بشكل صحيح</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار قاعدة البيانات
try {
    require_once 'config/config.php';
    echo "<p>✅ تم تحميل ملف الإعدادات بنجاح</p>";
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>روابط الاختبار:</h2>";
echo "<ul>";
echo "<li><a href='debug.php'>صفحة التشخيص الكاملة</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='login.php'>صفحة تسجيل الدخول</a></li>";
echo "<li><a href='page/curriculum.php'>صفحة المنهج</a></li>";
echo "</ul>";

echo "</body>";
echo "</html>";
?>
