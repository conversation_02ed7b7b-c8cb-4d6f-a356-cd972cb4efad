<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// This file is for resetting admin password - remove after use!

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $fullName = trim($_POST['full_name'] ?? '');
    
    if (empty($username) || empty($email) || empty($password)) {
        $message = 'جميع الحقول مطلوبة';
    } else {
        try {
            $db = Database::getInstance()->getConnection();
            
            // Check if admin exists
            $checkStmt = $db->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
            $checkStmt->execute([$username, $email]);
            $existingAdmin = $checkStmt->fetch();
            
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            if ($existingAdmin) {
                // Update existing admin
                $stmt = $db->prepare("
                    UPDATE admins 
                    SET password_hash = ?, full_name = ?, is_active = 1, updated_at = NOW()
                    WHERE username = ? OR email = ?
                ");
                $result = $stmt->execute([$hashedPassword, $fullName, $username, $email]);
                
                if ($result) {
                    $message = "تم تحديث بيانات الأدمن بنجاح!";
                } else {
                    $message = "فشل في تحديث بيانات الأدمن";
                }
            } else {
                // Create new admin
                $stmt = $db->prepare("
                    INSERT INTO admins (username, email, password_hash, full_name, role, is_active)
                    VALUES (?, ?, ?, ?, 'super_admin', 1)
                ");
                $result = $stmt->execute([$username, $email, $hashedPassword, $fullName]);
                
                if ($result) {
                    $message = "تم إنشاء حساب أدمن جديد بنجاح!";
                } else {
                    $message = "فشل في إنشاء حساب الأدمن";
                }
            }
            
        } catch (Exception $e) {
            $message = "خطأ: " . $e->getMessage();
        }
    }
}

// Check and update table structure
try {
    $db = Database::getInstance()->getConnection();

    // Check if full_name column exists
    $stmt = $db->query("SHOW COLUMNS FROM admins LIKE 'full_name'");
    $columnExists = $stmt->fetch();

    if (!$columnExists) {
        // Add missing columns
        $db->exec("ALTER TABLE admins ADD COLUMN full_name VARCHAR(100) NOT NULL DEFAULT 'مدير النظام' AFTER password_hash");
        $message .= " | تم إضافة عمود full_name";
    }

    // Check if is_active column exists
    $stmt = $db->query("SHOW COLUMNS FROM admins LIKE 'is_active'");
    $columnExists = $stmt->fetch();

    if (!$columnExists) {
        $db->exec("ALTER TABLE admins ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER role");
        $message .= " | تم إضافة عمود is_active";
    }

    // Check if created_at column exists
    $stmt = $db->query("SHOW COLUMNS FROM admins LIKE 'created_at'");
    $columnExists = $stmt->fetch();

    if (!$columnExists) {
        $db->exec("ALTER TABLE admins ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER is_active");
        $message .= " | تم إضافة عمود created_at";
    }

    // Check if updated_at column exists
    $stmt = $db->query("SHOW COLUMNS FROM admins LIKE 'updated_at'");
    $columnExists = $stmt->fetch();

    if (!$columnExists) {
        $db->exec("ALTER TABLE admins ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        $message .= " | تم إضافة عمود updated_at";
    }

    // Check if last_login column exists
    $stmt = $db->query("SHOW COLUMNS FROM admins LIKE 'last_login'");
    $columnExists = $stmt->fetch();

    if (!$columnExists) {
        $db->exec("ALTER TABLE admins ADD COLUMN last_login TIMESTAMP NULL AFTER updated_at");
        $message .= " | تم إضافة عمود last_login";
    }

} catch (Exception $e) {
    $message .= " | خطأ في تحديث بنية الجدول: " . $e->getMessage();
}

// Display current admins
try {
    $db = Database::getInstance()->getConnection();
    $stmt = $db->query("SELECT id, username, email, full_name, role, is_active, created_at, last_login FROM admins");
    $admins = $stmt->fetchAll();
} catch (Exception $e) {
    $admins = [];
    $message .= " | خطأ في جلب بيانات الأدمن: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة مرور الأدمن</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            opacity: 0.9;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <div class="container">
        <h1>🔧 إعادة تعيين كلمة مرور الأدمن</h1>
        
        <div class="warning">
            ⚠️ <strong>تحذير:</strong> احذف هذا الملف بعد الانتهاء من إعادة تعيين كلمة المرور لأسباب أمنية!
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo strpos($message, 'فشل') !== false || strpos($message, 'خطأ') !== false ? 'error' : ''; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>

            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور الجديدة:</label>
                <input type="password" id="password" name="password" placeholder="أدخل كلمة مرور قوية" required>
            </div>

            <div class="form-group">
                <label for="full_name">الاسم الكامل:</label>
                <input type="text" id="full_name" name="full_name" value="مدير النظام" required>
            </div>

            <button type="submit">تحديث/إنشاء حساب الأدمن</button>
        </form>

        <h2>الأدمن الحاليون</h2>
        <?php if (!empty($admins)): ?>
            <table>
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>اسم المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الاسم الكامل</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>آخر دخول</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($admins as $admin): ?>
                        <tr>
                            <td><?php echo $admin['id']; ?></td>
                            <td><?php echo htmlspecialchars($admin['username']); ?></td>
                            <td><?php echo htmlspecialchars($admin['email']); ?></td>
                            <td><?php echo htmlspecialchars($admin['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($admin['role']); ?></td>
                            <td class="<?php echo $admin['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                <?php echo $admin['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </td>
                            <td><?php echo $admin['last_login'] ? date('Y-m-d H:i', strtotime($admin['last_login'])) : 'لم يسجل دخول'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>لا توجد حسابات أدمن في قاعدة البيانات.</p>
        <?php endif; ?>

        <div style="margin-top: 30px; text-align: center;">
            <a href="login.php" style="color: #667eea; text-decoration: none; font-weight: bold;">
                ← العودة لصفحة تسجيل الدخول
            </a>
        </div>
    </div>
    <script src="../js/notifications.js"></script>
    <script src="js/admin-modern.js"></script>
</body>
</html>
