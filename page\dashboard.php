<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';
require_once __DIR__ . '/../includes/MessageManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$firstName = $_SESSION['first_name'] ?? 'المستخدم';
$username = $_SESSION['username'] ?? '';
$email = $_SESSION['email'] ?? '';
$loginTime = $_SESSION['login_time'] ?? time();

// Get user data for grade display
$userManager = new UserManager();
$userData = $userManager->getUserById($_SESSION['user_id']);

// Get user subscription info
try {
    $db = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $db->prepare("SELECT u.*, sp.name as plan_name, sp.icon as plan_icon, sp.color as plan_color
                         FROM users u
                         LEFT JOIN subscription_plans sp ON u.current_plan_id = sp.id
                         WHERE u.id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if user has active subscription
    $has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                       $userSubscription['subscription_end_date'] &&
                       strtotime($userSubscription['subscription_end_date']) > time();
} catch (Exception $e) {
    $userSubscription = null;
    $has_subscription = false;
}

// Get course statistics
$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();

// Get user's course subscriptions
$stmt = $db->prepare("
    SELECT COUNT(*) as total_courses,
           COUNT(CASE WHEN activation_status = 'active' THEN 1 END) as active_courses,
           COUNT(CASE WHEN activation_status = 'pending' THEN 1 END) as pending_courses
    FROM course_subscriptions
    WHERE user_id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$courseStats = $stmt->fetch();

// Get completed videos count
$stmt = $db->prepare("
    SELECT COUNT(*) as completed_videos
    FROM user_video_progress uvp
    JOIN course_videos cv ON uvp.video_id = cv.id
    JOIN course_subscriptions cs ON cv.course_id = cs.course_id
    WHERE uvp.user_id = ? AND uvp.completed = 1 AND cs.user_id = ? AND cs.activation_status = 'active'
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$videoStats = $stmt->fetch();

// Get exercise attempts count
$stmt = $db->prepare("
    SELECT COUNT(DISTINCT exercise_id) as attempted_exercises
    FROM user_exercise_attempts uea
    JOIN course_exercises ce ON uea.exercise_id = ce.id
    JOIN course_subscriptions cs ON ce.course_id = cs.course_id
    WHERE uea.user_id = ? AND cs.user_id = ? AND cs.activation_status = 'active'
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$exerciseStats = $stmt->fetch();

// Get message statistics
$messageManager = new MessageManager();

// Get total messages count
$stmt = $db->prepare("SELECT COUNT(*) as total_messages FROM student_messages WHERE user_id = ?");
$stmt->execute([$_SESSION['user_id']]);
$totalMessages = $stmt->fetchColumn();

// Get messages by status
$stmt = $db->prepare("
    SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'read' THEN 1 END) as read_only,
        COUNT(CASE WHEN status = 'replied' THEN 1 END) as replied,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed
    FROM student_messages
    WHERE user_id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$messageStats = $stmt->fetch();

// Get messages with replies count
$stmt = $db->prepare("
    SELECT COUNT(DISTINCT sm.id) as messages_with_replies
    FROM student_messages sm
    INNER JOIN admin_replies ar ON sm.id = ar.message_id
    WHERE sm.user_id = ? AND ar.is_public = 1
");
$stmt->execute([$_SESSION['user_id']]);
$messagesWithReplies = $stmt->fetchColumn();

// Calculate messages without replies
$messagesWithoutReplies = $totalMessages - $messagesWithReplies;

// Get recent messages (last 5)
$recentMessages = $messageManager->getUserMessages($_SESSION['user_id'], 1, 5);

// Get curriculum statistics
$stmt = $db->prepare("
    SELECT
        COUNT(DISTINCT cs.id) as total_subjects,
        COUNT(DISTINCT CASE WHEN cs.education_level = ? THEN cs.id END) as level_subjects,
        COUNT(DISTINCT CASE WHEN cs.education_level = 'all' THEN cs.id END) as general_subjects
    FROM curriculum_subjects cs
    WHERE cs.is_active = 1
    AND (cs.education_level = ? OR cs.education_level = 'all')
    AND (cs.education_type = ? OR cs.education_type = 'all')
    AND (cs.grade = ? OR cs.grade = 'all')
");
$stmt->execute([$userData['education_level'], $userData['education_level'], $userData['education_type'], $userData['grade']]);
$curriculumStats = $stmt->fetch();

// Get user's current subscription for curriculum
$stmt = $db->prepare("
    SELECT us.*, sp.name as plan_name, sp.color, sp.icon
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = ? AND us.payment_status = 'completed' AND us.end_date > NOW()
    ORDER BY us.end_date DESC
    LIMIT 1
");
$stmt->execute([$_SESSION['user_id']]);
$currentCurriculumSubscription = $stmt->fetch();

// Get recent activities for activity log
$stmt = $db->prepare("
    SELECT
        'video_watched' as activity_type,
        'شاهد فيديو' as activity_title,
        COALESCE(lv.title, 'فيديو تعليمي') as activity_description,
        COALESCE(cs.name, 'منهج دراسي') as subject_name,
        uvp.completed_at as activity_date,
        '🎥' as activity_icon,
        '#17a2b8' as activity_color
    FROM user_video_progress uvp
    LEFT JOIN lesson_videos lv ON uvp.video_id = lv.id
    LEFT JOIN lessons l ON lv.lesson_id = l.id
    LEFT JOIN curriculum_subjects cs ON l.subject_id = cs.id
    WHERE uvp.user_id = ? AND uvp.completed = 1 AND uvp.completed_at IS NOT NULL

    UNION ALL

    SELECT
        'course_subscribed' as activity_type,
        'اشترك في كورس' as activity_title,
        COALESCE(c.title, 'كورس تعليمي') as activity_description,
        'كورسات' as subject_name,
        cs.created_at as activity_date,
        '🎓' as activity_icon,
        '#007bff' as activity_color
    FROM course_subscriptions cs
    LEFT JOIN courses c ON cs.course_id = c.id
    WHERE cs.user_id = ? AND cs.activation_status = 'active'

    UNION ALL

    SELECT
        'subscription_created' as activity_type,
        'اشترك في المنصة' as activity_title,
        sp.name as activity_description,
        'اشتراك' as subject_name,
        us.created_at as activity_date,
        '👑' as activity_icon,
        '#6f42c1' as activity_color
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = ? AND us.payment_status = 'completed'

    UNION ALL

    SELECT
        'message_sent' as activity_type,
        'أرسل رسالة' as activity_title,
        sm.subject as activity_description,
        'رسائل' as subject_name,
        sm.created_at as activity_date,
        '📧' as activity_icon,
        '#dc3545' as activity_color
    FROM student_messages sm
    WHERE sm.user_id = ?

    UNION ALL

    SELECT
        'login' as activity_type,
        'دخول للمنصة' as activity_title,
        'تسجيل دخول جديد' as activity_description,
        'نظام' as subject_name,
        created_at as activity_date,
        '🔐' as activity_icon,
        '#28a745' as activity_color
    FROM user_sessions
    WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)

    ORDER BY activity_date DESC
    LIMIT 15
");
$stmt->execute([
    $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'],
    $_SESSION['user_id'], $_SESSION['user_id']
]);
$recentActivities = $stmt->fetchAll();

// Get curriculum subjects with progress for quick access
$stmt = $db->prepare("
    SELECT cs.*,
           COUNT(DISTINCT l.id) as total_lessons,
           COUNT(DISTINCT CASE WHEN ulp.is_completed = 1 THEN l.id END) as completed_lessons,
           COUNT(DISTINCT lv.id) as total_videos,
           COUNT(DISTINCT uvp.video_id) as watched_videos
    FROM curriculum_subjects cs
    LEFT JOIN lessons l ON cs.id = l.subject_id AND l.is_active = 1
    LEFT JOIN user_lesson_progress ulp ON l.id = ulp.lesson_id AND ulp.user_id = ?
    LEFT JOIN lesson_videos lv ON l.id = lv.lesson_id AND lv.is_active = 1
    LEFT JOIN user_video_progress uvp ON lv.id = uvp.video_id AND uvp.user_id = ? AND uvp.completed = 1
    WHERE cs.is_active = 1
    AND (cs.education_level = ? OR cs.education_level = 'all')
    AND (cs.education_type = ? OR cs.education_type = 'all')
    AND (cs.grade = ? OR cs.grade = 'all')
    GROUP BY cs.id
    ORDER BY cs.sort_order ASC, cs.name ASC
    LIMIT 6
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $userData['education_level'], $userData['education_type'], $userData['grade']]);
$quickAccessSubjects = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/enhanced-ui.css">
    <link rel="stylesheet" href="../css/loading-system.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Loading Screen will be created by JavaScript -->
    <!-- User Info for Loading System -->
    <script>
        sessionStorage.setItem('first_name', '<?php echo htmlspecialchars($firstName); ?>');
        sessionStorage.setItem('username', '<?php echo htmlspecialchars($username); ?>');
    </script>

    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">

            <!-- Modern Welcome Section -->
            <div class="modern-welcome-section">
                <div class="welcome-background"></div>
                <div class="welcome-container">
                    <div class="welcome-main">
                        <div class="welcome-greeting">
                            <div class="greeting-text">
                                <h1 class="greeting-title">
                                    <?php
                                    $hour = date('H');
                                    $greeting = '';
                                    $emoji = '';
                                    if ($hour < 12) {
                                        $greeting = 'صباح الخير';
                                        $emoji = '🌅';
                                    } elseif ($hour < 17) {
                                        $greeting = 'مساء الخير';
                                        $emoji = '☀️';
                                    } else {
                                        $greeting = 'مساء الخير';
                                        $emoji = '🌙';
                                    }
                                    echo $greeting . '، ' . htmlspecialchars($firstName);
                                    ?>
                                    <span class="greeting-emoji"><?php echo $emoji; ?></span>
                                </h1>
                                <p class="greeting-subtitle">مرحباً بك في منصة التعلم الذكية</p>
                            </div>

                            <?php if ($userData): ?>
                                <div class="student-badge">
                                    <div class="badge-icon">🎓</div>
                                    <div class="badge-info">
                                        <?php
                                        $levels = ['primary' => 'ابتدائي', 'preparatory' => 'إعدادي', 'secondary' => 'ثانوي'];
                                        $types = ['azhari' => 'أزهري', 'general' => 'عام'];
                                        $gradeNumbers = ['1' => 'الأول', '2' => 'الثاني', '3' => 'الثالث', '4' => 'الرابع', '5' => 'الخامس', '6' => 'السادس'];

                                        $levelText = $levels[$userData['education_level']] ?? $userData['education_level'];
                                        $typeText = $types[$userData['education_type']] ?? $userData['education_type'];
                                        $gradeText = $gradeNumbers[$userData['grade']] ?? $userData['grade'];

                                        echo "الصف {$gradeText} {$levelText} {$typeText}";
                                        ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="welcome-date-time">
                            <div class="date-info">
                                <span class="date-icon">📅</span>
                                <span class="date-text"><?php echo date('l, F j, Y', time()); ?></span>
                            </div>
                            <div class="time-info">
                                <span class="time-icon">⏰</span>
                                <span class="time-text" id="currentTime"><?php echo date('H:i', time()); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats Grid -->
                    <div class="quick-stats-grid">
                        <div class="quick-stat-card courses">
                            <div class="stat-icon">🎓</div>
                            <div class="stat-content">
                                <div class="stat-number"><?php echo $courseStats['active_courses'] ?? 0; ?></div>
                                <div class="stat-label">كورسات نشطة</div>
                            </div>
                            <div class="stat-trend">+<?php echo $courseStats['total_courses'] ?? 0; ?></div>
                        </div>

                        <div class="quick-stat-card videos">
                            <div class="stat-icon">📚</div>
                            <div class="stat-content">
                                <div class="stat-number"><?php echo $videoStats['completed_videos'] ?? 0; ?></div>
                                <div class="stat-label">فيديوهات مكتملة</div>
                            </div>
                            <div class="stat-trend">📈</div>
                        </div>

                        <div class="quick-stat-card exercises">
                            <div class="stat-icon">📝</div>
                            <div class="stat-content">
                                <div class="stat-number"><?php echo $exerciseStats['attempted_exercises'] ?? 0; ?></div>
                                <div class="stat-label">تمارين محلولة</div>
                            </div>
                            <div class="stat-trend">💪</div>
                        </div>

                        <div class="quick-stat-card todos">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-content">
                                <div class="stat-number"><?php echo count($userManager->getUserTodos($_SESSION['user_id'], 0)); ?></div>
                                <div class="stat-label">مهام معلقة</div>
                            </div>
                            <div class="stat-trend">⚡</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Subscription Section -->
            <div class="modern-subscription-section">
                <?php if ($has_subscription): ?>
                    <div class="subscription-active-card">
                        <div class="subscription-background"></div>
                        <div class="subscription-content">
                            <div class="subscription-header">
                                <div class="plan-icon-modern" style="background: <?php echo $userSubscription['plan_color']; ?>;">
                                    <?php echo $userSubscription['plan_icon']; ?>
                                </div>
                                <div class="subscription-info">
                                    <h3 class="plan-name"><?php echo htmlspecialchars($userSubscription['plan_name']); ?></h3>
                                    <div class="subscription-status">
                                        <span class="status-badge active">
                                            <i class="fas fa-check-circle"></i>
                                            نشط
                                        </span>
                                    </div>
                                </div>
                                <div class="subscription-actions">
                                    <a href="subscriptions.php" class="action-btn">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                </div>
                            </div>

                            <div class="subscription-details">
                                <?php
                                $days_left = ceil((strtotime($userSubscription['subscription_end_date']) - time()) / (60 * 60 * 24));
                                $total_days = 30; // افتراض أن الاشتراك 30 يوم
                                $progress = max(0, min(100, (($total_days - $days_left) / $total_days) * 100));
                                ?>
                                <div class="subscription-progress">
                                    <div class="progress-info">
                                        <span class="progress-label">الأيام المتبقية</span>
                                        <span class="progress-value"><?php echo $days_left; ?> يوم</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo $progress; ?>%;"></div>
                                    </div>
                                </div>

                                <div class="subscription-meta">
                                    <div class="meta-item">
                                        <span class="meta-label">تاريخ الانتهاء</span>
                                        <span class="meta-value"><?php echo date('Y/m/d', strtotime($userSubscription['subscription_end_date'])); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="subscription-inactive-card">
                        <div class="inactive-background"></div>
                        <div class="inactive-content">
                            <div class="inactive-icon">
                                <i class="fas fa-crown"></i>
                            </div>
                            <h3 class="inactive-title">اشترك الآن واحصل على الوصول الكامل</h3>
                            <p class="inactive-description">استمتع بجميع الدروس والتمارين والامتحانات</p>

                            <div class="subscription-options">
                                <a href="subscriptions.php" class="btn-primary-modern">
                                    <i class="fas fa-star"></i>
                                    <span>اشترك الآن</span>
                                </a>
                                <button class="btn-secondary-modern" onclick="showActivationModal()">
                                    <i class="fas fa-key"></i>
                                    <span>كود التفعيل</span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Modern Analytics Section -->
            <div class="modern-analytics-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-line"></i> تحليلات الأداء</h2>
                    <p>تتبع تقدمك وإنجازاتك في التعلم</p>
                </div>

                <div class="analytics-grid-modern">
                    <!-- Learning Progress Card -->
                    <div class="analytics-card-modern progress-card">
                        <div class="card-header-modern">
                            <div class="header-icon">📊</div>
                            <div class="header-content">
                                <h3>تقدم التعلم</h3>
                                <p>إحصائيات شاملة لأدائك</p>
                            </div>
                        </div>

                        <div class="progress-stats">
                            <div class="progress-item">
                                <div class="progress-circle" data-percentage="<?php echo round(($exerciseStats['correct_answers'] ?? 0) / max(($exerciseStats['total_answers'] ?? 1), 1) * 100); ?>">
                                    <svg viewBox="0 0 36 36">
                                        <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                        <path class="circle" stroke-dasharray="<?php echo round(($exerciseStats['correct_answers'] ?? 0) / max(($exerciseStats['total_answers'] ?? 1), 1) * 100); ?>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                    </svg>
                                    <div class="percentage"><?php echo round(($exerciseStats['correct_answers'] ?? 0) / max(($exerciseStats['total_answers'] ?? 1), 1) * 100); ?>%</div>
                                </div>
                                <div class="progress-label">معدل النجاح</div>
                            </div>

                            <div class="progress-details">
                                <div class="detail-item">
                                    <span class="detail-dot completed"></span>
                                    <span class="detail-text"><?php echo $videoStats['completed_videos'] ?? 0; ?> فيديو مكتمل</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-dot in-progress"></span>
                                    <span class="detail-text"><?php echo $exerciseStats['attempted_exercises'] ?? 0; ?> تمرين محلول</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-dot pending"></span>
                                    <span class="detail-text"><?php echo $courseStats['pending_courses'] ?? 0; ?> كورس معلق</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Card -->
                    <div class="analytics-card-modern activity-card">
                        <div class="card-header-modern">
                            <div class="header-icon">📅</div>
                            <div class="header-content">
                                <h3>النشاط الأسبوعي</h3>
                                <p>ساعات التعلم اليومية</p>
                            </div>
                        </div>

                        <div class="activity-chart">
                            <div class="chart-bars">
                                <div class="bar-item">
                                    <div class="bar" style="height: 60%;" data-value="3"></div>
                                    <span class="bar-label">س</span>
                                </div>
                                <div class="bar-item">
                                    <div class="bar" style="height: 80%;" data-value="4"></div>
                                    <span class="bar-label">ح</span>
                                </div>
                                <div class="bar-item">
                                    <div class="bar" style="height: 40%;" data-value="2"></div>
                                    <span class="bar-label">ن</span>
                                </div>
                                <div class="bar-item">
                                    <div class="bar" style="height: 90%;" data-value="4.5"></div>
                                    <span class="bar-label">ث</span>
                                </div>
                                <div class="bar-item">
                                    <div class="bar" style="height: 70%;" data-value="3.5"></div>
                                    <span class="bar-label">ر</span>
                                </div>
                                <div class="bar-item">
                                    <div class="bar" style="height: 50%;" data-value="2.5"></div>
                                    <span class="bar-label">خ</span>
                                </div>
                                <div class="bar-item">
                                    <div class="bar" style="height: 30%;" data-value="1.5"></div>
                                    <span class="bar-label">ج</span>
                                </div>
                            </div>
                            <div class="chart-summary">
                                <span class="summary-text">المتوسط الأسبوعي: <?php echo round(($videoStats['total_watch_time'] ?? 0) / 7, 1); ?> ساعة</span>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics Card -->
                    <div class="analytics-card-modern metrics-card">
                        <div class="card-header-modern">
                            <div class="header-icon">🏆</div>
                            <div class="header-content">
                                <h3>مؤشرات الأداء</h3>
                                <p>إحصائيات مفصلة</p>
                            </div>
                        </div>

                        <div class="metrics-grid-modern">
                            <div class="metric-item-modern">
                                <div class="metric-icon-modern">🎯</div>
                                <div class="metric-content-modern">
                                    <div class="metric-value-modern"><?php echo round(($exerciseStats['correct_answers'] ?? 0) / max(($exerciseStats['total_answers'] ?? 1), 1) * 100); ?>%</div>
                                    <div class="metric-label-modern">معدل الإجابات الصحيحة</div>
                                </div>
                            </div>
                            <div class="metric-item-modern">
                                <div class="metric-icon-modern">⚡</div>
                                <div class="metric-content-modern">
                                    <div class="metric-value-modern"><?php echo $videoStats['total_watch_time'] ?? 0; ?></div>
                                    <div class="metric-label-modern">ساعات المشاهدة</div>
                                </div>
                            </div>
                            <div class="metric-item-modern">
                                <div class="metric-icon-modern">🔥</div>
                                <div class="metric-content-modern">
                                    <div class="metric-value-modern"><?php echo $courseStats['streak_days'] ?? 0; ?></div>
                                    <div class="metric-label-modern">أيام متتالية</div>
                                </div>
                            </div>
                            <div class="metric-item-modern">
                                <div class="metric-icon-modern">📈</div>
                                <div class="metric-content-modern">
                                    <div class="metric-value-modern">+<?php echo $courseStats['improvement_rate'] ?? 0; ?>%</div>
                                    <div class="metric-label-modern">معدل التحسن</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Achievements -->
                    <div class="analytics-card">
                        <div class="card-header">
                            <h3><i class="fas fa-medal"></i> الإنجازات الأخيرة</h3>
                        </div>
                        <div class="achievements-list">
                            <div class="achievement-item">
                                <div class="achievement-icon">🏆</div>
                                <div class="achievement-info">
                                    <h4>أول كورس مكتمل</h4>
                                    <p>أكملت أول كورس بنجاح</p>
                                    <span class="achievement-date">منذ 3 أيام</span>
                                </div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon">🎯</div>
                                <div class="achievement-info">
                                    <h4>10 تمارين متتالية</h4>
                                    <p>حللت 10 تمارين بدون أخطاء</p>
                                    <span class="achievement-date">منذ أسبوع</span>
                                </div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon">⚡</div>
                                <div class="achievement-info">
                                    <h4>متعلم سريع</h4>
                                    <p>أكملت 5 ساعات مشاهدة في يوم واحد</p>
                                    <span class="achievement-date">منذ أسبوعين</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Widgets -->
            <div class="widgets-grid">
                <!-- Enhanced My Courses Widget -->
                <div class="widget courses-widget-modern">
                    <div class="widget-header">
                        <div class="header-icon">🎓</div>
                        <div class="header-content">
                            <h3>كورساتي</h3>
                            <p>تتبع تقدمك في الكورسات</p>
                        </div>
                        <a href="../page/courses.php" class="widget-action">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>

                    <div class="widget-content">
                        <div class="courses-stats-grid">
                            <div class="course-stat-item active">
                                <div class="stat-icon">✅</div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo $courseStats['active_courses'] ?? 0; ?></div>
                                    <div class="stat-label">كورسات نشطة</div>
                                </div>
                            </div>

                            <div class="course-stat-item total">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo $courseStats['total_courses'] ?? 0; ?></div>
                                    <div class="stat-label">إجمالي الكورسات</div>
                                </div>
                            </div>

                            <div class="course-stat-item pending">
                                <div class="stat-icon">⏳</div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo $courseStats['pending_courses'] ?? 0; ?></div>
                                    <div class="stat-label">في الانتظار</div>
                                </div>
                            </div>

                            <div class="course-stat-item progress">
                                <div class="stat-icon">📈</div>
                                <div class="stat-content">
                                    <div class="stat-number"><?php echo round(($courseStats['active_courses'] ?? 0) / max(($courseStats['total_courses'] ?? 1), 1) * 100); ?>%</div>
                                    <div class="stat-label">معدل التقدم</div>
                                </div>
                            </div>
                        </div>

                        <?php if (($courseStats['total_courses'] ?? 0) > 0): ?>
                            <div class="courses-progress">
                                <h4>تقدم الكورسات</h4>
                                <div class="progress-chart">
                                    <div class="progress-item">
                                        <span class="progress-label">نشطة</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill active" style="width: <?php echo (($courseStats['active_courses'] ?? 0) / ($courseStats['total_courses'] ?? 1)) * 100; ?>%"></div>
                                        </div>
                                        <span class="progress-value"><?php echo $courseStats['active_courses'] ?? 0; ?></span>
                                    </div>

                                    <div class="progress-item">
                                        <span class="progress-label">معلقة</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill pending" style="width: <?php echo (($courseStats['pending_courses'] ?? 0) / ($courseStats['total_courses'] ?? 1)) * 100; ?>%"></div>
                                        </div>
                                        <span class="progress-value"><?php echo $courseStats['pending_courses'] ?? 0; ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="no-courses">
                                <div class="no-courses-icon">🎓</div>
                                <h4>لم تشترك في أي كورس بعد</h4>
                                <p>ابدأ رحلتك التعليمية واشترك في الكورسات المتاحة</p>
                                <a href="../page/courses.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> تصفح الكورسات
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Todo Widget -->
                <div>
                    <?php include __DIR__ . '/../includes/todo_widget.php'; ?>
                </div>

                <!-- Notes Widget -->
                <div>
                    <?php include __DIR__ . '/../includes/notes_widget.php'; ?>
                </div>

                <!-- Messages Widget -->
                <div>
                    <div class="widget messages-widget">
                        <div class="widget-header">
                            <h3><i class="fas fa-envelope"></i> إحصائيات الرسائل</h3>
                            <a href="../page/my_messages.php" class="widget-action">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                        <div class="widget-content">
                            <!-- Messages Statistics -->
                            <div class="messages-stats-grid">
                                <div class="message-stat-card total">
                                    <div class="stat-icon">📧</div>
                                    <div class="stat-info">
                                        <div class="stat-number"><?php echo $totalMessages; ?></div>
                                        <div class="stat-label">إجمالي الرسائل</div>
                                    </div>
                                </div>

                                <div class="message-stat-card replied">
                                    <div class="stat-icon">✅</div>
                                    <div class="stat-info">
                                        <div class="stat-number"><?php echo $messagesWithReplies; ?></div>
                                        <div class="stat-label">تم الرد عليها</div>
                                    </div>
                                </div>

                                <div class="message-stat-card pending">
                                    <div class="stat-icon">⏳</div>
                                    <div class="stat-info">
                                        <div class="stat-number"><?php echo $messagesWithoutReplies; ?></div>
                                        <div class="stat-label">بانتظار الرد</div>
                                    </div>
                                </div>

                                <div class="message-stat-card status">
                                    <div class="stat-icon">📊</div>
                                    <div class="stat-info">
                                        <div class="stat-number"><?php echo $messageStats['pending'] ?? 0; ?></div>
                                        <div class="stat-label">معلقة</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Messages Status Breakdown -->
                            <div class="messages-breakdown">
                                <h4>تفصيل حالات الرسائل</h4>
                                <div class="status-bars">
                                    <?php if ($totalMessages > 0): ?>
                                        <div class="status-bar">
                                            <div class="status-info">
                                                <span class="status-label">تم الرد</span>
                                                <span class="status-count"><?php echo $messageStats['replied'] ?? 0; ?></span>
                                            </div>
                                            <div class="status-progress">
                                                <div class="progress-fill replied"
                                                     style="width: <?php echo ($messageStats['replied'] / $totalMessages) * 100; ?>%"></div>
                                            </div>
                                        </div>

                                        <div class="status-bar">
                                            <div class="status-info">
                                                <span class="status-label">في الانتظار</span>
                                                <span class="status-count"><?php echo $messageStats['pending'] ?? 0; ?></span>
                                            </div>
                                            <div class="status-progress">
                                                <div class="progress-fill pending"
                                                     style="width: <?php echo ($messageStats['pending'] / $totalMessages) * 100; ?>%"></div>
                                            </div>
                                        </div>

                                        <div class="status-bar">
                                            <div class="status-info">
                                                <span class="status-label">تم القراءة</span>
                                                <span class="status-count"><?php echo $messageStats['read_only'] ?? 0; ?></span>
                                            </div>
                                            <div class="status-progress">
                                                <div class="progress-fill read"
                                                     style="width: <?php echo ($messageStats['read_only'] / $totalMessages) * 100; ?>%"></div>
                                            </div>
                                        </div>

                                        <div class="status-bar">
                                            <div class="status-info">
                                                <span class="status-label">مغلقة</span>
                                                <span class="status-count"><?php echo $messageStats['closed'] ?? 0; ?></span>
                                            </div>
                                            <div class="status-progress">
                                                <div class="progress-fill closed"
                                                     style="width: <?php echo ($messageStats['closed'] / $totalMessages) * 100; ?>%"></div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-messages">
                                            <i class="fas fa-inbox"></i>
                                            <p>لم ترسل أي رسائل بعد</p>
                                            <a href="../page/ask_teacher.php" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus"></i> إرسال رسالة
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Recent Messages -->
                            <?php if (!empty($recentMessages['messages'])): ?>
                                <div class="recent-messages">
                                    <h4>الرسائل الأخيرة</h4>
                                    <div class="messages-list">
                                        <?php foreach (array_slice($recentMessages['messages'], 0, 3) as $message): ?>
                                            <div class="message-item">
                                                <div class="message-icon">
                                                    <?php
                                                    $statusIcons = [
                                                        'pending' => '⏳',
                                                        'read' => '👁️',
                                                        'replied' => '✅',
                                                        'closed' => '🔒'
                                                    ];
                                                    echo $statusIcons[$message['status']] ?? '📧';
                                                    ?>
                                                </div>
                                                <div class="message-content">
                                                    <div class="message-subject">
                                                        <?php echo htmlspecialchars(mb_substr($message['subject'], 0, 30)) . (mb_strlen($message['subject']) > 30 ? '...' : ''); ?>
                                                    </div>
                                                    <div class="message-meta">
                                                        <span class="message-date"><?php echo date('Y/m/d', strtotime($message['created_at'])); ?></span>
                                                        <span class="message-status status-<?php echo $message['status']; ?>">
                                                            <?php
                                                            $statusLabels = [
                                                                'pending' => 'في الانتظار',
                                                                'read' => 'تم القراءة',
                                                                'replied' => 'تم الرد',
                                                                'closed' => 'مغلقة'
                                                            ];
                                                            echo $statusLabels[$message['status']] ?? $message['status'];
                                                            ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="message-actions">
                                                    <a href="../page/my_messages.php?id=<?php echo $message['id']; ?>"
                                                       class="btn btn-sm btn-outline">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <div class="widget-footer">
                                        <a href="../page/my_messages.php" class="btn btn-outline">
                                            <i class="fas fa-list"></i> عرض جميع الرسائل
                                        </a>
                                        <a href="../page/ask_teacher.php" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> رسالة جديدة
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Activity Log Widget -->
                <div class="widget activity-log-widget">
                    <div class="widget-header">
                        <div class="header-icon">📋</div>
                        <div class="header-content">
                            <h3>سجل الأنشطة</h3>
                            <p>آخر أنشطتك في المنصة</p>
                        </div>
                        <div class="widget-action">
                            <i class="fas fa-history"></i>
                        </div>
                    </div>

                    <div class="widget-content">
                        <?php if (!empty($recentActivities)): ?>
                            <div class="activities-list">
                                <?php foreach (array_slice($recentActivities, 0, 8) as $activity): ?>
                                    <div class="activity-item">
                                        <div class="activity-icon" style="background: <?php echo $activity['activity_color']; ?>20; color: <?php echo $activity['activity_color']; ?>;">
                                            <?php echo $activity['activity_icon']; ?>
                                        </div>

                                        <div class="activity-content">
                                            <div class="activity-header">
                                                <span class="activity-title"><?php echo $activity['activity_title']; ?></span>
                                                <span class="activity-time">
                                                    <?php
                                                    $time_diff = time() - strtotime($activity['activity_date']);
                                                    if ($time_diff < 3600) {
                                                        echo floor($time_diff / 60) . ' دقيقة';
                                                    } elseif ($time_diff < 86400) {
                                                        echo floor($time_diff / 3600) . ' ساعة';
                                                    } else {
                                                        echo floor($time_diff / 86400) . ' يوم';
                                                    }
                                                    ?>
                                                </span>
                                            </div>

                                            <div class="activity-description">
                                                <strong><?php echo htmlspecialchars($activity['activity_description']); ?></strong>
                                                <?php if ($activity['subject_name'] && $activity['subject_name'] !== $activity['activity_description']): ?>
                                                    <span class="activity-subject">في <?php echo htmlspecialchars($activity['subject_name']); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="activity-indicator" style="background: <?php echo $activity['activity_color']; ?>;"></div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="activity-summary">
                                <div class="summary-stats">
                                    <div class="summary-item">
                                        <span class="summary-number"><?php echo count(array_filter($recentActivities, function($a) { return $a['activity_type'] === 'lesson_completed'; })); ?></span>
                                        <span class="summary-label">دروس مكتملة</span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-number"><?php echo count(array_filter($recentActivities, function($a) { return $a['activity_type'] === 'video_watched'; })); ?></span>
                                        <span class="summary-label">فيديوهات</span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-number"><?php echo count(array_filter($recentActivities, function($a) { return $a['activity_type'] === 'exercise_completed'; })); ?></span>
                                        <span class="summary-label">تمارين</span>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="no-activities">
                                <div class="no-activities-icon">📋</div>
                                <h4>لا توجد أنشطة حتى الآن</h4>
                                <p>ابدأ التعلم لترى أنشطتك هنا</p>
                                <a href="../page/curriculum.php" class="btn btn-primary">
                                    <i class="fas fa-play"></i> ابدأ التعلم
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- New Feature Widgets -->
            <div class="widgets-grid">
                <!-- Upcoming Exams Widget -->
                <div>
                    <?php include __DIR__ . '/../includes/upcoming_exams_widget.php'; ?>
                </div>

                <!-- Curriculum News Widget -->
                <div>
                    <?php include __DIR__ . '/../includes/curriculum_news_widget.php'; ?>
                </div>
            </div>

            <!-- Honor Board Widget -->
            <div class="widgets-grid">
                <div>
                    <?php include __DIR__ . '/../includes/honor_board_widget.php'; ?>
                </div>
            </div>

            <!-- Activity Log Widget -->
            <div class="widgets-grid">
                <div>
                    <?php include __DIR__ . '/../includes/activity_log_widget.php'; ?>
                </div>

                <!-- User Information Card -->
                <div class="user-info-card">
                    <div class="card-header">
                        <h3>📋 معلومات الحساب</h3>
                    </div>
                    <div class="card-content">
                        <div class="info-row">
                            <span class="info-label">اسم المستخدم:</span>
                            <span class="info-value"><?php echo htmlspecialchars($username); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value"><?php echo htmlspecialchars($email); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">تاريخ التسجيل:</span>
                            <span class="info-value"><?php echo date('Y-m-d', strtotime($userData['created_at'])); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">آخر تسجيل دخول:</span>
                            <span class="info-value"><?php echo date('Y-m-d H:i', $loginTime); ?></span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <a href="../page/profile.php" class="btn btn-primary">تعديل الملف الشخصي</a>
                    </div>
                </div>
            </div>

            <!-- Curriculum Statistics Section -->
            <div class="curriculum-section">
                <div class="section-header">
                    <h2><i class="fas fa-graduation-cap"></i> إحصائيات المنهج</h2>
                    <p>معلومات شاملة عن المواد الدراسية المتاحة لك</p>
                </div>

                <div class="curriculum-stats-grid">
                    <div class="curriculum-stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $curriculumStats['total_subjects'] ?? 0; ?></div>
                            <div class="stat-label">إجمالي المواد المتاحة</div>
                            <div class="stat-description">جميع المواد المناسبة لمستواك التعليمي</div>
                        </div>
                    </div>

                    <div class="curriculum-stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #20c997 0%, #28a745 100%);">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $curriculumStats['general_subjects'] ?? 0; ?></div>
                            <div class="stat-label">مواد عامة</div>
                            <div class="stat-description">مواد مشتركة لجميع المراحل</div>
                        </div>
                    </div>

                    <div class="curriculum-stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $curriculumStats['level_subjects'] ?? 0; ?></div>
                            <div class="stat-label">مواد متخصصة</div>
                            <div class="stat-description">مواد خاصة بمرحلتك التعليمية</div>
                        </div>
                    </div>

                    <div class="curriculum-stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, <?php echo $currentCurriculumSubscription ? '#28a745' : '#dc3545'; ?> 0%, <?php echo $currentCurriculumSubscription ? '#20c997' : '#c82333'; ?> 100%);">
                            <i class="fas <?php echo $currentCurriculumSubscription ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $currentCurriculumSubscription ? 'نشط' : 'غير نشط'; ?></div>
                            <div class="stat-label">حالة الاشتراك</div>
                            <div class="stat-description">
                                <?php if ($currentCurriculumSubscription): ?>
                                    <?php
                                    $remaining_days = ceil((strtotime($currentCurriculumSubscription['end_date']) - time()) / (60 * 60 * 24));
                                    echo "متبقي {$remaining_days} يوم";
                                    ?>
                                <?php else: ?>
                                    يرجى الاشتراك للوصول الكامل
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="curriculum-actions">
                    <a href="../page/curriculum.php" class="btn btn-primary">
                        <i class="fas fa-book-open"></i>
                        استكشاف المنهج
                    </a>
                    <?php if (!$currentCurriculumSubscription): ?>
                        <a href="../page/subscription_plans.php" class="btn btn-outline">
                            <i class="fas fa-star"></i>
                            عرض خطط الاشتراك
                        </a>
                    <?php else: ?>
                        <a href="../page/settings.php" class="btn btn-outline">
                            <i class="fas fa-cog"></i>
                            إدارة الاشتراك
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Access Subjects Section -->
            <div class="quick-subjects-section">
                <div class="section-header">
                    <h2><i class="fas fa-rocket"></i> الوصول السريع للأقسام</h2>
                    <p>أقسامك المفضلة مع إحصائيات التقدم</p>
                </div>

                <?php if (!empty($quickAccessSubjects)): ?>
                    <div class="quick-subjects-grid">
                        <?php foreach ($quickAccessSubjects as $subject): ?>
                            <?php
                            $progress_percentage = 0;
                            if ($subject['total_lessons'] > 0) {
                                $progress_percentage = round(($subject['completed_lessons'] / $subject['total_lessons']) * 100);
                            }
                            ?>
                            <div class="quick-subject-card" onclick="window.location.href='subject_lessons.php?subject_id=<?php echo $subject['id']; ?>'"
                                <div class="subject-icon-wrapper" style="background: linear-gradient(135deg, <?php echo $subject['color']; ?>20, <?php echo $subject['color']; ?>40);">
                                    <span class="subject-icon" style="color: <?php echo $subject['color']; ?>;">
                                        <?php echo $subject['icon']; ?>
                                    </span>
                                </div>

                                <div class="subject-content">
                                    <h3 class="subject-title"><?php echo htmlspecialchars($subject['name']); ?></h3>

                                    <div class="subject-stats">
                                        <div class="stat-item">
                                            <span class="stat-icon">📚</span>
                                            <span class="stat-text"><?php echo $subject['total_lessons']; ?> درس</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-icon">🎥</span>
                                            <span class="stat-text"><?php echo $subject['total_videos']; ?> فيديو</span>
                                        </div>
                                    </div>

                                    <div class="progress-section">
                                        <div class="progress-info">
                                            <span class="progress-label">التقدم</span>
                                            <span class="progress-percentage"><?php echo $progress_percentage; ?>%</span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: <?php echo $progress_percentage; ?>%; background: <?php echo $subject['color']; ?>;"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="subject-action">
                                    <i class="fas fa-arrow-left"></i>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="quick-subjects-footer">
                        <a href="../page/curriculum.php" class="btn btn-outline">
                            <i class="fas fa-th-large"></i>
                            عرض جميع الأقسام
                        </a>
                    </div>
                <?php else: ?>
                    <div class="no-subjects-message">
                        <div class="no-subjects-icon">📚</div>
                        <h3>لا توجد أقسام متاحة</h3>
                        <p>لم يتم إضافة أقسام دراسية لمستواك التعليمي بعد</p>
                        <a href="../page/curriculum.php" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            استكشاف المنهج
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions Section -->
            <div class="quick-actions-section">
                <h2>الإجراءات السريعة</h2>
                <div class="quick-actions-grid">
                    <a href="../page/curriculum.php" class="action-card">
                        <span class="action-icon">📚</span>
                        <h4>المنهج الدراسي</h4>
                        <p>تصفح المنهج والمواد التعليمية</p>
                    </a>

                    <a href="../page/courses.php" class="action-card">
                        <span class="action-icon">�</span>
                        <h4>الكورسات</h4>
                        <p>الكورسات والدورات المتاحة</p>
                    </a>

                    <a href="../page/ask_teacher.php" class="action-card">
                        <span class="action-icon">❓</span>
                        <h4>اسأل معلم</h4>
                        <p>اطرح أسئلتك واحصل على إجابات</p>
                    </a>

                    <a href="../page/subscriptions.php" class="action-card">
                        <span class="action-icon">💳</span>
                        <h4>الاشتراكات</h4>
                        <p>إدارة اشتراكاتك وخطط الدفع</p>
                    </a>

                    <a href="../page/settings.php" class="action-card">
                        <span class="action-icon">⚙️</span>
                        <h4>الإعدادات</h4>
                        <p>تعديل بيانات الحساب والتفضيلات</p>
                    </a>

                    <a href="../page/profile.php" class="action-card">
                        <span class="action-icon">👤</span>
                        <h4>الملف الشخصي</h4>
                        <p>تحديث معلوماتك الشخصية</p>
                    </a>
                </div>
            </div>
        </main>
    </div>

    <!-- Enhanced Modal Styles -->
    <style>
        /* Modern Modal Design */
        #todoModal, #noteModal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(70, 130, 180, 0.3) 100%) !important;
            backdrop-filter: blur(8px) !important;
            z-index: 999999 !important;
            display: none !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 20px !important;
            box-sizing: border-box !important;
            /* animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important; */
            overflow-y: auto !important;
        }

        /* Force modal to show when modal-open class is added */
        #todoModal.modal-open, #noteModal.modal-open {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(8px);
            }
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-60px) scale(0.9);
                opacity: 0;
            }
            50% {
                transform: translateY(-20px) scale(0.95);
                opacity: 0.7;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        @keyframes modalPulse {
            0% {
                box-shadow: 0 25px 80px rgba(70, 130, 180, 0.2);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 35px 100px rgba(70, 130, 180, 0.3);
                transform: scale(1.01);
            }
            100% {
                box-shadow: 0 25px 80px rgba(70, 130, 180, 0.2);
                transform: scale(1);
            }
        }

        @keyframes modalShimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .modal-open {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        #todoModal .modal-content, #noteModal .modal-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            border-radius: 25px !important;
            width: 100% !important;
            max-width: 700px !important;
            max-height: 90vh !important;
            overflow: hidden !important;
            box-shadow: 0 30px 100px rgba(70, 130, 180, 0.3) !important;
            position: relative !important;
            margin: auto !important;
            animation: modalSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
            border: 3px solid rgba(135, 206, 235, 0.2) !important;
            display: flex !important;
            flex-direction: column !important;
        }

        #todoModal .modal-content::before, #noteModal .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: -200%;
            width: 200%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.1), transparent);
            animation: modalShimmer 3s infinite;
            pointer-events: none;
        }

        #todoModal .modal-content:hover, #noteModal .modal-content:hover {
            animation: modalPulse 2s infinite !important;
            border-color: rgba(135, 206, 235, 0.4) !important;
        }

        /* Enhanced Header */
        #todoModal .modal-header, #noteModal .modal-header {
            padding: 35px !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-bottom: 2px solid rgba(70, 130, 180, 0.1) !important;
            border-radius: 25px 25px 0 0 !important;
            position: relative !important;
            box-shadow: 0 4px 20px rgba(70, 130, 180, 0.15) !important;
        }

        #todoModal .modal-header::after, #noteModal .modal-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 35px;
            width: 80px;
            height: 2px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 2px;
        }

        #todoModal .modal-header::before, #noteModal .modal-header::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 4px !important;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%) !important;
            border-radius: 20px 20px 0 0 !important;
        }

        #todoModal .modal-header h3, #noteModal .modal-header h3 {
            margin: 0 !important;
            color: #2c3e50 !important;
            font-size: 22px !important;
            font-weight: 600 !important;
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
        }

        #todoModal .modal-header h3::before {
            content: '✓' !important;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%) !important;
            color: white !important;
            width: 32px !important;
            height: 32px !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 16px !important;
            font-weight: bold !important;
        }

        #noteModal .modal-header h3::before {
            content: '📝' !important;
            font-size: 24px !important;
        }

        #todoModal .modal-close, #noteModal .modal-close {
            position: absolute !important;
            top: 25px !important;
            left: 30px !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            border: 2px solid rgba(220, 53, 69, 0.2) !important;
            font-size: 18px !important;
            cursor: pointer !important;
            color: #dc3545 !important;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 50% !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            font-weight: bold !important;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2) !important;
            z-index: 10 !important;
        }

        #todoModal .modal-close:hover, #noteModal .modal-close:hover {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
            transform: scale(1.1) rotate(90deg) !important;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4) !important;
            border-color: rgba(220, 53, 69, 0.5) !important;
        }

        #todoModal .modal-close:active, #noteModal .modal-close:active {
            transform: scale(0.95) rotate(90deg) !important;
        }

        /* Enhanced Form Styling */
        #todoModal form, #noteModal form {
            padding: 35px !important;
            background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%) !important;
            flex: 1 !important;
            overflow-y: auto !important;
            position: relative !important;
        }

        #todoModal form::before, #noteModal form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.3), transparent);
        }

        #todoModal .form-group, #noteModal .form-group {
            margin-bottom: 28px !important;
            position: relative !important;
        }

        #todoModal .form-row, #noteModal .form-row {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 20px !important;
            margin-bottom: 24px !important;
        }

        #todoModal .form-label, #noteModal .form-label {
            display: block !important;
            margin-bottom: 8px !important;
            color: #2c3e50 !important;
            font-weight: 600 !important;
            font-size: 14px !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }

        #todoModal .form-input, #todoModal .form-select, #todoModal .form-textarea,
        #noteModal .form-input, #noteModal .form-select, #noteModal .form-textarea {
            width: 100% !important;
            padding: 16px 20px !important;
            border: 2px solid rgba(70, 130, 180, 0.15) !important;
            border-radius: 15px !important;
            font-size: 16px !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-sizing: border-box !important;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            font-family: inherit !important;
            box-shadow: inset 0 3px 6px rgba(70, 130, 180, 0.08) !important;
            position: relative !important;
        }

        #todoModal .form-input::placeholder, #todoModal .form-textarea::placeholder,
        #noteModal .form-input::placeholder, #noteModal .form-textarea::placeholder {
            color: rgba(108, 117, 125, 0.7) !important;
            font-style: italic !important;
        }

        #todoModal .form-input:focus, #todoModal .form-select:focus, #todoModal .form-textarea:focus,
        #noteModal .form-input:focus, #noteModal .form-select:focus, #noteModal .form-textarea:focus {
            outline: none !important;
            border-color: #87CEEB !important;
            box-shadow: 0 0 0 4px rgba(135, 206, 235, 0.25), inset 0 3px 6px rgba(70, 130, 180, 0.1) !important;
            transform: translateY(-3px) scale(1.01) !important;
            background: #ffffff !important;
        }

        #todoModal .form-input:hover, #todoModal .form-select:hover, #todoModal .form-textarea:hover,
        #noteModal .form-input:hover, #noteModal .form-select:hover, #noteModal .form-textarea:hover {
            border-color: rgba(135, 206, 235, 0.3) !important;
            transform: translateY(-1px) !important;
        }

        #todoModal .form-textarea, #noteModal .form-textarea {
            resize: vertical !important;
            min-height: 120px !important;
            font-family: inherit !important;
        }

        /* Enhanced Buttons */
        #todoModal .modal-actions, #noteModal .modal-actions {
            display: flex !important;
            gap: 15px !important;
            justify-content: flex-end !important;
            margin-top: 35px !important;
            padding: 30px 35px !important;
            border-top: 2px solid rgba(70, 130, 180, 0.1) !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-radius: 0 0 25px 25px !important;
            position: relative !important;
        }

        #todoModal .modal-actions::before, #noteModal .modal-actions::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 35px;
            width: 80px;
            height: 2px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 2px;
        }

        #todoModal .btn, #noteModal .btn {
            padding: 16px 32px !important;
            border: none !important;
            border-radius: 15px !important;
            cursor: pointer !important;
            font-weight: 700 !important;
            font-size: 15px !important;
            text-transform: uppercase !important;
            letter-spacing: 0.8px !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            overflow: hidden !important;
            min-width: 120px !important;
        }

        #todoModal .btn::before, #noteModal .btn::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
            transition: left 0.6s ease !important;
        }

        #todoModal .btn:hover::before, #noteModal .btn:hover::before {
            left: 100% !important;
        }

        #todoModal .btn-primary, #noteModal .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%) !important;
            color: white !important;
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4) !important;
            border: 2px solid rgba(135, 206, 235, 0.3) !important;
        }

        #todoModal .btn-primary:hover, #noteModal .btn-primary:hover {
            background: linear-gradient(135deg, #4682B4 0%, #2c5aa0 100%) !important;
            transform: translateY(-3px) scale(1.05) !important;
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.5) !important;
        }

        #todoModal .btn-secondary, #noteModal .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
            color: white !important;
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3) !important;
            border: 2px solid rgba(108, 117, 125, 0.2) !important;
        }

        #todoModal .btn-secondary:hover, #noteModal .btn-secondary:hover {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
            transform: translateY(-3px) scale(1.05) !important;
            box-shadow: 0 10px 30px rgba(108, 117, 125, 0.4) !important;
        }

        #todoModal .btn:active, #noteModal .btn:active {
            transform: translateY(-1px) scale(0.98) !important;
        }

        #todoModal .btn-primary:hover, #noteModal .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4) !important;
        }

        #todoModal .btn-secondary, #noteModal .btn-secondary {
            background: #6c757d !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
        }

        #todoModal .btn-secondary:hover, #noteModal .btn-secondary:hover {
            background: #5a6268 !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
        }

        /* Priority Indicators */
        .priority-indicator {
            display: inline-block !important;
            width: 12px !important;
            height: 12px !important;
            border-radius: 50% !important;
            margin-left: 8px !important;
        }

        .priority-low { background: #28a745 !important; }
        .priority-medium { background: #ffc107 !important; }
        .priority-high { background: #dc3545 !important; }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            #todoModal, #noteModal {
                padding: 10px !important;
            }

            #todoModal .modal-content, #noteModal .modal-content {
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 !important;
                max-height: 95vh !important;
                border-radius: 15px !important;
            }

            #todoModal .modal-header, #noteModal .modal-header {
                padding: 20px !important;
            }

            #todoModal .modal-header h3, #noteModal .modal-header h3 {
                font-size: 18px !important;
            }

            #todoModal form, #noteModal form {
                padding: 20px !important;
            }

            #todoModal .form-row, #noteModal .form-row {
                grid-template-columns: 1fr !important;
                gap: 15px !important;
            }

            #todoModal .form-group, #noteModal .form-group {
                margin-bottom: 20px !important;
            }

            #todoModal .form-input, #todoModal .form-select, #todoModal .form-textarea,
            #noteModal .form-input, #noteModal .form-select, #noteModal .form-textarea {
                padding: 12px 14px !important;
                font-size: 16px !important;
            }

            #todoModal .form-textarea, #noteModal .form-textarea {
                min-height: 100px !important;
            }

            #todoModal .modal-actions, #noteModal .modal-actions {
                flex-direction: column !important;
                gap: 10px !important;
                margin-top: 25px !important;
            }

            #todoModal .btn, #noteModal .btn {
                width: 100% !important;
                padding: 12px 20px !important;
                font-size: 16px !important;
            }

            #todoModal .modal-close, #noteModal .modal-close {
                top: 15px !important;
                left: 20px !important;
                width: 30px !important;
                height: 30px !important;
                font-size: 18px !important;
            }
        }

        /* Small Mobile Devices */
        @media (max-width: 480px) {
            #todoModal, #noteModal {
                padding: 5px !important;
            }

            #todoModal .modal-content, #noteModal .modal-content {
                border-radius: 15px !important;
                max-height: 98vh !important;
                margin: 5px !important;
            }

            #todoModal .modal-header, #noteModal .modal-header {
                padding: 20px 15px !important;
            }

            #todoModal .modal-header h3, #noteModal .modal-header h3 {
                font-size: 18px !important;
            }

            #todoModal form, #noteModal form {
                padding: 20px 15px !important;
            }

            #todoModal .modal-actions, #noteModal .modal-actions {
                padding: 20px 15px !important;
                flex-direction: column !important;
                gap: 12px !important;
            }

            #todoModal .form-input, #todoModal .form-select, #todoModal .form-textarea,
            #noteModal .form-input, #noteModal .form-select, #noteModal .form-textarea {
                padding: 12px 14px !important;
                font-size: 16px !important;
                border-radius: 10px !important;
            }

            #todoModal .btn, #noteModal .btn {
                padding: 12px 20px !important;
                font-size: 16px !important;
                border-radius: 10px !important;
                width: 100% !important;
            }

            #todoModal .modal-close, #noteModal .modal-close {
                top: 12px !important;
                left: 15px !important;
                width: 32px !important;
                height: 32px !important;
                font-size: 20px !important;
            }

            /* Improve touch targets on mobile */
            #todoModal .form-label, #noteModal .form-label {
                font-size: 15px !important;
                margin-bottom: 10px !important;
            }

            #todoModal .form-group, #noteModal .form-group {
                margin-bottom: 25px !important;
            }
        }

        /* Enhanced Dashboard Styles */
        .dashboard-main {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            min-height: 100vh;
        }

        .welcome-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 45px;
            margin-bottom: 45px;
            box-shadow: 0 20px 50px rgba(70, 130, 180, 0.2);
            border: 2px solid rgba(70, 130, 180, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .welcome-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 70px rgba(70, 130, 180, 0.25);
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 25px 25px 0 0;
        }

        .welcome-background-animation {
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(135, 206, 235, 0.05) 0%, transparent 70%);
            animation: welcomeRotate 20s linear infinite;
            pointer-events: none;
        }

        @keyframes welcomeRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .welcome-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 45px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .welcome-greeting h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 20px;
            line-height: 1.2;
            background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .greeting-emoji {
            display: inline-block;
            animation: wave 2s infinite;
            transform-origin: 70% 70%;
        }

        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20% { transform: rotate(12deg); }
            40%, 60% { transform: rotate(9deg); }
            50% { transform: rotate(-9deg); }
        }

        .welcome-subtitle {
            color: #6c757d;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .welcome-date {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #4682B4;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .date-icon {
            font-size: 18px;
        }

        .student-info {
            margin-bottom: 20px;
        }

        .grade-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 700;
            box-shadow: 0 6px 20px rgba(70, 130, 180, 0.3);
            transition: all 0.3s ease;
        }

        .grade-badge:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 10px 30px rgba(70, 130, 180, 0.4);
        }

        .badge-icon {
            font-size: 18px;
        }

        .motivational-message {
            display: flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            padding: 15px 20px;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            border: 2px solid rgba(255, 193, 7, 0.3);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
        }

        .motivation-icon {
            font-size: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .grade-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .welcome-stats {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-3px);
        }

        .stat-icon {
            font-size: 32px;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 50%;
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 4px;
        }

        /* Enhanced Widgets Grid */
        .widgets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        /* Enhanced Widget Styles */
        .todo-widget,
        .notes-widget,
        .messages-widget,
        .courses-widget-modern,
        .activity-log-widget {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            border-radius: 20px !important;
            padding: 30px !important;
            box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15) !important;
            border: 1px solid rgba(70, 130, 180, 0.1) !important;
            position: relative !important;
            overflow: hidden !important;
            margin-bottom: 30px !important;
            transition: all 0.3s ease !important;
        }

        .todo-widget::before,
        .notes-widget::before,
        .messages-widget::before,
        .courses-widget-modern::before,
        .activity-log-widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .todo-widget:hover,
        .notes-widget:hover,
        .messages-widget:hover,
        .courses-widget-modern:hover,
        .activity-log-widget:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 50px rgba(70, 130, 180, 0.2) !important;
        }

        /* Widget Specific Styles */
        .messages-widget {
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%) !important;
        }

        .courses-widget-modern {
            background: linear-gradient(135deg, #ffffff 0%, #f0fff0 100%) !important;
        }

        .activity-log-widget {
            background: linear-gradient(135deg, #ffffff 0%, #fff8f0 100%) !important;
        }

        /* Courses Widget Styles */
        .courses-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .course-stat-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: rgba(70, 130, 180, 0.05);
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid rgba(70, 130, 180, 0.1);
        }

        .course-stat-item:hover {
            background: rgba(70, 130, 180, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.15);
        }

        .course-stat-item .stat-icon {
            font-size: 1.2rem;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .course-stat-item.active .stat-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .course-stat-item.total .stat-icon {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }

        .course-stat-item.pending .stat-icon {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
        }

        .course-stat-item.progress .stat-icon {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }

        .course-stat-item .stat-content {
            flex: 1;
        }

        .course-stat-item .stat-number {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .course-stat-item .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .courses-progress h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 600;
        }

        .progress-chart {
            space-y: 12px;
        }

        .progress-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .progress-label {
            font-size: 0.85rem;
            color: #6c757d;
            min-width: 60px;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-fill.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .progress-fill.pending {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }

        .progress-value {
            font-size: 0.85rem;
            font-weight: 600;
            color: #2c3e50;
            min-width: 30px;
            text-align: center;
        }

        .no-courses {
            text-align: center;
            padding: 30px 15px;
            color: #6c757d;
        }

        .no-courses-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .no-courses h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .no-courses p {
            margin-bottom: 20px;
            line-height: 1.6;
        }

        /* Activity Log Widget Styles */
        .activities-list {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding-left: 5px;
        }

        .activities-list::-webkit-scrollbar {
            width: 4px;
        }

        .activities-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .activities-list::-webkit-scrollbar-thumb {
            background: #87CEEB;
            border-radius: 2px;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
            position: relative;
            transition: all 0.3s ease;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(70, 130, 180, 0.05);
            border-radius: 8px;
            padding: 12px 8px;
            margin: 0 -8px;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .activity-content {
            flex: 1;
            min-width: 0;
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .activity-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .activity-time {
            font-size: 0.75rem;
            color: #6c757d;
            white-space: nowrap;
        }

        .activity-description {
            font-size: 0.8rem;
            color: #495057;
            line-height: 1.4;
        }

        .activity-description strong {
            color: #2c3e50;
        }

        .activity-subject {
            color: #6c757d;
            font-style: italic;
        }

        .activity-indicator {
            width: 3px;
            height: 100%;
            border-radius: 2px;
            position: absolute;
            right: 0;
            top: 0;
            opacity: 0.7;
        }

        .activity-summary {
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
        }

        .summary-stats {
            display: flex;
            justify-content: space-around;
            gap: 10px;
        }

        .summary-item {
            text-align: center;
            flex: 1;
        }

        .summary-number {
            display: block;
            font-size: 1.2rem;
            font-weight: 700;
            color: #4682B4;
            margin-bottom: 2px;
        }

        .summary-label {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .no-activities {
            text-align: center;
            padding: 40px 15px;
            color: #6c757d;
        }

        .no-activities-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .no-activities h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .no-activities p {
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .messages-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .message-stat-card {
            background: white;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .message-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .message-stat-card.total { border-left-color: #4682B4; }
        .message-stat-card.replied { border-left-color: #28a745; }
        .message-stat-card.pending { border-left-color: #ffc107; }
        .message-stat-card.status { border-left-color: #17a2b8; }

        .message-stat-card .stat-icon {
            font-size: 20px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(70, 130, 180, 0.1);
        }

        .message-stat-card .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            line-height: 1;
        }

        .message-stat-card .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 3px;
        }

        .messages-breakdown h4,
        .recent-messages h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
        }

        .status-bar {
            margin-bottom: 10px;
        }

        .status-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 13px;
            color: #555;
            font-weight: 500;
        }

        .status-count {
            font-size: 13px;
            font-weight: 600;
            color: #333;
        }

        .status-progress {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-fill.replied { background: #28a745; }
        .progress-fill.pending { background: #ffc107; }
        .progress-fill.read { background: #17a2b8; }
        .progress-fill.closed { background: #6c757d; }

        .no-messages {
            text-align: center;
            padding: 30px 15px;
            color: #666;
        }

        .no-messages i {
            font-size: 40px;
            color: #ddd;
            margin-bottom: 12px;
        }

        .message-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .message-item:hover {
            transform: translateX(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .message-icon {
            font-size: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(70, 130, 180, 0.1);
        }

        .message-content {
            flex: 1;
        }

        .message-subject {
            font-weight: 600;
            color: #333;
            font-size: 13px;
            margin-bottom: 3px;
        }

        .message-meta {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .message-date {
            font-size: 11px;
            color: #666;
        }

        .message-status {
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 8px;
            font-weight: 600;
        }

        .message-status.status-pending { background: #fff3cd; color: #856404; }
        .message-status.status-read { background: #d1ecf1; color: #0c5460; }
        .message-status.status-replied { background: #d4edda; color: #155724; }
        .message-status.status-closed { background: #d6d8db; color: #383d41; }

        .widget-footer {
            display: flex;
            gap: 8px;
            justify-content: space-between;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
            margin-top: 15px;
        }

        .widget-footer .btn {
            flex: 1;
            text-align: center;
            padding: 8px 12px;
            font-size: 12px;
        }

        .todo-header h3,
        .notes-header h3 {
            color: #2c3e50 !important;
            font-size: 22px !important;
            font-weight: 600 !important;
        }

        .btn-add-todo,
        .btn-add-note {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%) !important;
            color: white !important;
            border: none !important;
            padding: 12px 20px !important;
            border-radius: 12px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3) !important;
        }

        .btn-add-todo:hover,
        .btn-add-note:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4) !important;
        }

        /* Enhanced Quick Actions */
        .quick-actions-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15);
            border: 1px solid rgba(70, 130, 180, 0.1);
            position: relative;
            overflow: hidden;
        }

        .quick-actions-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .quick-actions-section h2 {
            color: #2c3e50;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .quick-actions-section h2::before {
            content: '⚡';
            font-size: 24px;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: all 0.4s ease;
            border: 2px solid rgba(70, 130, 180, 0.1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.1);
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .action-card:hover::before {
            left: 100%;
        }

        .action-card:hover {
            transform: translateY(-8px);
            border-color: #87CEEB;
            box-shadow: 0 20px 45px rgba(70, 130, 180, 0.25);
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
        }

        .action-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .action-card h4 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .action-card p {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
            line-height: 1.4;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .dashboard-main {
                padding: 20px !important;
            }

            .welcome-section {
                padding: 25px !important;
                margin-bottom: 30px !important;
            }

            .welcome-content {
                grid-template-columns: 1fr;
                gap: 25px;
                text-align: center;
            }

            .welcome-text h1 {
                font-size: 24px !important;
            }

            .welcome-text p {
                font-size: 16px !important;
            }

            .welcome-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stat-item {
                flex-direction: column;
                text-align: center;
                padding: 15px;
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 24px;
            }

            .widgets-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .quick-actions-section {
                padding: 25px !important;
            }

            .quick-actions-section h2 {
                font-size: 20px !important;
            }

            .quick-actions-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .action-card {
                padding: 20px !important;
            }

            .action-card h4 {
                font-size: 16px !important;
            }

            .action-icon {
                font-size: 40px !important;
            }
        }

        @media (max-width: 480px) {
            .dashboard-main {
                padding: 15px !important;
            }

            .welcome-section {
                padding: 20px !important;
                margin-bottom: 25px !important;
            }

            .welcome-text h1 {
                font-size: 22px !important;
            }

            .welcome-text p {
                font-size: 14px !important;
            }

            .welcome-stats {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .stat-item {
                padding: 12px;
            }

            .quick-actions-section {
                padding: 20px !important;
            }

            .quick-actions-section h2 {
                font-size: 18px !important;
            }

            .quick-actions-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .action-card {
                padding: 18px !important;
            }

            .action-card h4 {
                font-size: 15px !important;
            }

            .action-card p {
                font-size: 13px !important;
            }

            .action-icon {
                font-size: 36px !important;
                margin-bottom: 12px !important;
            }

            .widgets-grid {
                gap: 15px;
            }

            .todo-widget,
            .notes-widget,
            .messages-widget,
            .courses-widget-modern,
            .activity-log-widget {
                padding: 20px !important;
            }

            .messages-stats-grid,
            .courses-stats-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .activities-list {
                max-height: 300px;
            }

            .activity-item {
                padding: 10px 0;
            }

            .activity-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .summary-stats {
                flex-direction: column;
                gap: 8px;
            }

            .course-stat-item {
                padding: 12px;
            }

            .course-stat-item .stat-icon {
                width: 28px;
                height: 28px;
                font-size: 1rem;
            }

            .course-stat-item .stat-number {
                font-size: 1.1rem;
            }

            .activity-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .activity-title {
                font-size: 0.8rem;
            }

            .activity-description {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .courses-stats-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .course-stat-item {
                padding: 10px;
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }

            .activities-list {
                max-height: 250px;
            }

            .activity-item {
                padding: 8px 0;
            }

            .activity-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 2px;
            }

            .activity-time {
                font-size: 0.7rem;
            }

            .summary-stats {
                gap: 6px;
            }

            .summary-number {
                font-size: 1rem;
            }

            .summary-label {
                font-size: 0.7rem;
            }

            .message-stat-card {
                padding: 12px;
                gap: 10px;
            }

            .message-stat-card .stat-number {
                font-size: 20px;
            }

            .widget-footer {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* User Info Card Styles */
        .user-info-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(70, 130, 180, 0.1);
            overflow: hidden;
        }

        .user-info-card .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #dee2e6;
            position: relative;
        }

        .user-info-card .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .user-info-card .card-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }

        .user-info-card .card-content {
            padding: 30px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 14px;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info-card .card-actions {
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .user-info-card .btn {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .user-info-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
    </style>

    <!-- Todo Modal -->
    <div id="todoModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="todoModalTitle">إضافة مهمة جديدة</h3>
                <button type="button" class="modal-close" onclick="closeTodoModal()">×</button>
            </div>
            <form id="todoForm">
                <input type="hidden" id="todoId" name="todo_id" value="">

                <div class="form-group">
                    <label for="todoTitle" class="form-label">عنوان المهمة</label>
                    <input type="text" id="todoTitle" name="title" class="form-input" placeholder="أدخل عنوان المهمة" required>
                </div>

                <div class="form-group">
                    <label for="todoDescription" class="form-label">وصف المهمة</label>
                    <textarea id="todoDescription" name="description" class="form-textarea" placeholder="أدخل وصف المهمة" rows="4"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="todoPriority" class="form-label">الأولوية</label>
                        <select id="todoPriority" name="priority" class="form-select">
                            <option value="low">منخفضة</option>
                            <option value="medium" selected>متوسطة</option>
                            <option value="high">عالية</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="todoDueDate" class="form-label">تاريخ الاستحقاق</label>
                        <input type="date" id="todoDueDate" name="due_date" class="form-input">
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeTodoModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ المهمة</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Note Modal -->
    <div id="noteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="noteModalTitle">إضافة ملاحظة جديدة</h3>
                <button type="button" class="modal-close" onclick="closeNoteModal()">×</button>
            </div>
            <form id="noteForm">
                <input type="hidden" id="noteId" name="note_id" value="">

                <div class="form-group">
                    <label for="noteTitle" class="form-label">عنوان الملاحظة</label>
                    <input type="text" id="noteTitle" name="title" class="form-input" placeholder="أدخل عنوان الملاحظة" required>
                </div>

                <div class="form-group">
                    <label for="noteContent" class="form-label">محتوى الملاحظة</label>
                    <textarea id="noteContent" name="content" class="form-textarea" placeholder="أدخل محتوى الملاحظة" rows="6" required></textarea>
                </div>

                <div class="form-group">
                    <label for="noteCategory" class="form-label">التصنيف</label>
                    <select id="noteCategory" name="category" class="form-select">
                        <option value="general">عام</option>
                        <option value="work">عمل</option>
                        <option value="personal">شخصي</option>
                        <option value="study">دراسة</option>
                        <option value="important">مهم</option>
                    </select>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeNoteModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الملاحظة</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Load Dashboard JavaScript -->
    <script src="../js/dashboard-fixed.js?v=<?php echo time(); ?>"></script>

    <!-- Loading Screen Script -->
    <script>
        // Loading Screen Functions
        function showLoadingScreen() {
            const loadingScreen = document.querySelector('.loading-screen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('fade-out');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }, 1500); // Shorter delay for dashboard
            }
        }

        // Initialize loading screen on page load
        document.addEventListener('DOMContentLoaded', function() {
            showLoadingScreen();
            updateCurrentTime();
        });

        // Update current time every minute
        function updateCurrentTime() {
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                const now = new Date();
                const hours = now.getHours().toString().padStart(2, '0');
                const minutes = now.getMinutes().toString().padStart(2, '0');
                timeElement.textContent = `${hours}:${minutes}`;
            }
        }

        // Update time every minute
        setInterval(updateCurrentTime, 60000);

        // Initialize Charts when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // Learning Progress Pie Chart
            const progressCtx = document.getElementById('learningProgressChart').getContext('2d');
            new Chart(progressCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مكتمل', 'قيد التقدم', 'لم يبدأ'],
                    datasets: [{
                        data: [<?php echo $courseStats['completed_courses'] ?? 0; ?>,
                               <?php echo $courseStats['in_progress_courses'] ?? 0; ?>,
                               <?php echo $courseStats['not_started_courses'] ?? 0; ?>],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Weekly Activity Bar Chart
            const activityCtx = document.getElementById('weeklyActivityChart').getContext('2d');
            new Chart(activityCtx, {
                type: 'bar',
                data: {
                    labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                    datasets: [{
                        label: 'ساعات التعلم',
                        data: [2, 4, 3, 5, 2, 6, 1],
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
    </script>

    <style>
        /* Analytics Section Styles */
        .analytics-section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-header h2 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-header p {
            color: #6c757d;
            font-size: 16px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .analytics-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .analytics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-container {
            position: relative;
            height: 250px;
            margin-bottom: 20px;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6c757d;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .metric-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: scale(1.05);
        }

        .metric-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
        }

        .achievements-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .achievement-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .achievement-item:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateX(-5px);
        }

        .achievement-icon {
            font-size: 30px;
            min-width: 50px;
            text-align: center;
        }

        .achievement-info h4 {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 5px 0;
        }

        .achievement-info p {
            font-size: 14px;
            color: #6c757d;
            margin: 0 0 5px 0;
        }

        .achievement-date {
            font-size: 12px;
            color: #28a745;
            font-weight: 500;
        }

        /* Subscription Section Styles */
        .subscription-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-top: 4px solid #4682B4;
        }

        .subscription-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #dee2e6;
        }

        .subscription-active .subscription-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .plan-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
        }

        .subscription-info h3 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }

        .subscription-status.active {
            color: #28a745;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }

        .days-remaining {
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .subscription-actions {
            text-align: center;
        }

        .subscription-inactive {
            text-align: center;
        }

        .no-subscription-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #dc3545, #c82333);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px auto;
            color: white;
            font-size: 2rem;
        }

        .subscription-inactive h3 {
            color: #dc3545;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }

        .subscription-inactive p {
            color: #666;
            margin: 0 0 20px 0;
        }

        .subscription-options {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #4682B4;
            color: #4682B4;
        }

        .btn-outline:hover {
            background: #4682B4;
            color: white;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .subscription-section {
                margin: 20px 0;
                padding: 20px;
            }

            .subscription-active .subscription-header {
                flex-direction: column;
                text-align: center;
            }

            .subscription-options {
                flex-direction: column;
                align-items: center;
            }

            .analytics-section {
                margin: 20px 0;
                padding: 20px;
            }

            .analytics-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .chart-container {
                height: 200px;
            }

            .chart-legend {
                gap: 10px;
            }

            .legend-item {
                font-size: 12px;
            }

            .curriculum-section {
                margin: 20px 0;
                padding: 20px;
            }

            .curriculum-stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .curriculum-stat-card {
                padding: 20px;
            }

            .curriculum-actions {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* Curriculum Statistics Styles */
        .curriculum-section {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 4px 20px rgba(70, 130, 180, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .curriculum-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(70, 130, 180, 0.15);
        }

        .curriculum-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .curriculum-stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .curriculum-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            border-color: #87CEEB;
        }

        .curriculum-stat-card .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .curriculum-stat-card .stat-content {
            flex: 1;
        }

        .curriculum-stat-card .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .curriculum-stat-card .stat-label {
            font-size: 1rem;
            font-weight: 600;
            color: #4682B4;
            margin-bottom: 4px;
        }

        .curriculum-stat-card .stat-description {
            font-size: 0.85rem;
            color: #6c757d;
            line-height: 1.4;
        }

        .curriculum-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
            flex-wrap: wrap;
        }

        .curriculum-actions .btn {
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .curriculum-actions .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
        }

        .curriculum-actions .btn-primary:hover {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(70, 130, 180, 0.4);
        }

        .curriculum-actions .btn-outline {
            background: transparent;
            color: #4682B4;
            border: 2px solid #4682B4;
        }

        .curriculum-actions .btn-outline:hover {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
        }

        /* Quick Access Subjects Styles */
        .quick-subjects-section {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 4px 20px rgba(70, 130, 180, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .quick-subjects-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(70, 130, 180, 0.15);
        }

        .quick-subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .quick-subject-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
            overflow: hidden;
        }

        .quick-subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .quick-subject-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            border-color: #87CEEB;
        }

        .quick-subject-card:hover::before {
            transform: scaleX(1);
        }

        .subject-icon-wrapper {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .subject-icon {
            font-size: 1.8rem;
        }

        .subject-content {
            flex: 1;
        }

        .subject-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 8px 0;
        }

        .subject-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.85rem;
            color: #6c757d;
        }

        .stat-icon {
            font-size: 0.9rem;
        }

        .progress-section {
            margin-top: 8px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .progress-label {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .progress-percentage {
            font-size: 0.85rem;
            font-weight: 600;
            color: #4682B4;
        }

        .progress-bar {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .subject-action {
            color: #87CEEB;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .quick-subject-card:hover .subject-action {
            color: #4682B4;
            transform: translateX(-3px);
        }

        .quick-subjects-footer {
            text-align: center;
            margin-top: 25px;
        }

        .no-subjects-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .no-subjects-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .no-subjects-message h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .no-subjects-message p {
            margin-bottom: 20px;
            line-height: 1.6;
        }

        /* Mobile Responsive for Quick Subjects */
        @media (max-width: 768px) {
            .quick-subjects-section {
                margin: 20px 0;
                padding: 20px;
            }

            .quick-subjects-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .quick-subject-card {
                padding: 16px;
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .subject-content {
                width: 100%;
            }

            .subject-stats {
                justify-content: center;
            }

            .subject-action {
                position: absolute;
                top: 16px;
                left: 16px;
            }

            .curriculum-section {
                margin: 20px 0;
                padding: 20px;
            }

            .curriculum-stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .curriculum-stat-card {
                flex-direction: column;
                text-align: center;
                gap: 12px;
                padding: 20px;
            }

            .curriculum-stat-card .stat-icon {
                width: 50px;
                height: 50px;
            }

            .curriculum-actions {
                flex-direction: column;
                gap: 12px;
            }
        }

        @media (max-width: 480px) {
            .curriculum-stats-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .quick-subjects-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .quick-subject-card {
                padding: 14px;
            }

            .subject-icon-wrapper {
                width: 50px;
                height: 50px;
            }

            .subject-icon {
                font-size: 1.5rem;
            }
        }

        /* Enhanced Touch Interactions for Mobile */
        @media (hover: none) and (pointer: coarse) {
            .quick-subject-card:active {
                transform: translateY(-1px) scale(0.98);
                transition: all 0.1s ease;
            }

            .curriculum-stat-card:active {
                transform: translateY(-1px) scale(0.98);
                transition: all 0.1s ease;
            }

            .btn:active {
                transform: translateY(0) scale(0.95);
                transition: all 0.1s ease;
            }
        }

        /* Modern Welcome Section Styles */
        .modern-welcome-section {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 24px;
            padding: 0;
            margin-bottom: 32px;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(70, 130, 180, 0.3);
            min-height: 200px;
            transition: all 0.3s ease;
        }

        .modern-welcome-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 16px 50px rgba(70, 130, 180, 0.4);
        }

        .welcome-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        .welcome-container {
            position: relative;
            z-index: 2;
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 32px;
        }

        .welcome-main {
            flex: 1;
        }

        .greeting-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .greeting-emoji {
            margin-right: 12px;
            font-size: 2.5rem;
            animation: wave 2s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(20deg); }
            75% { transform: rotate(-10deg); }
        }

        .greeting-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .student-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin-top: 12px;
            backdrop-filter: blur(10px);
        }

        .welcome-date-time {
            display: flex;
            gap: 20px;
            margin-top: 16px;
            flex-wrap: wrap;
        }

        .date-info, .time-info {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .quick-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            min-width: 280px;
        }

        .quick-stat-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            text-align: center;
        }

        .quick-stat-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .quick-stat-card .stat-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .quick-stat-card .stat-number {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .quick-stat-card .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .quick-stat-card .stat-trend {
            font-size: 0.8rem;
            margin-top: 4px;
            opacity: 0.8;
        }

        /* Modern Subscription Section */
        .modern-subscription-section {
            margin-bottom: 32px;
        }

        .subscription-active-card {
            background: white;
            border-radius: 20px;
            padding: 0;
            box-shadow: 0 8px 30px rgba(70, 130, 180, 0.15);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            position: relative;
        }

        .subscription-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .subscription-content {
            padding: 24px;
            position: relative;
            z-index: 2;
        }

        .subscription-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .plan-icon-modern {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .subscription-info {
            flex: 1;
            margin: 0 20px;
        }

        .plan-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 8px 0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #4682B4;
            color: white;
            border-color: #4682B4;
        }

        .subscription-progress {
            margin-bottom: 16px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .progress-label {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .progress-value {
            font-size: 0.9rem;
            font-weight: 600;
            color: #4682B4;
        }

        .progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .subscription-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .meta-label {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .meta-value {
            font-size: 0.85rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .subscription-inactive-card {
            background: white;
            border-radius: 20px;
            padding: 32px;
            text-align: center;
            box-shadow: 0 8px 30px rgba(70, 130, 180, 0.1);
            border: 2px dashed #87CEEB;
            position: relative;
            overflow: hidden;
        }

        .inactive-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            opacity: 0.5;
        }

        .inactive-content {
            position: relative;
            z-index: 2;
        }

        .inactive-icon {
            font-size: 3rem;
            color: #87CEEB;
            margin-bottom: 16px;
        }

        .inactive-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .inactive-description {
            color: #6c757d;
            margin-bottom: 24px;
            line-height: 1.6;
        }

        .subscription-options {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary-modern, .btn-secondary-modern {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
        }

        .btn-primary-modern:hover {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(70, 130, 180, 0.4);
        }

        .btn-secondary-modern {
            background: white;
            color: #4682B4;
            border: 2px solid #4682B4;
        }

        .btn-secondary-modern:hover {
            background: #4682B4;
            color: white;
            transform: translateY(-2px);
        }

        /* Modern Welcome Section Styles */
        .modern-welcome-section {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 24px;
            margin-bottom: 32px;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(70, 130, 180, 0.3);
            transition: all 0.3s ease;
        }

        .modern-welcome-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 16px 50px rgba(70, 130, 180, 0.4);
        }

        .welcome-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .welcome-container {
            position: relative;
            z-index: 2;
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 32px;
        }

        .greeting-title {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .greeting-emoji {
            margin-right: 12px;
            font-size: 2.5rem;
            animation: wave 2s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(20deg); }
            75% { transform: rotate(-10deg); }
        }

        .greeting-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .student-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin-top: 12px;
            backdrop-filter: blur(10px);
        }

        .welcome-date-time {
            display: flex;
            gap: 20px;
            margin-top: 16px;
            flex-wrap: wrap;
        }

        .date-info, .time-info {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .quick-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            min-width: 280px;
        }

        .quick-stat-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            text-align: center;
        }

        .quick-stat-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .quick-stat-card .stat-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .quick-stat-card .stat-number {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .quick-stat-card .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .quick-stat-card .stat-trend {
            font-size: 0.8rem;
            margin-top: 4px;
            opacity: 0.8;
        }

        /* Modern Subscription Section */
        .modern-subscription-section {
            margin-bottom: 32px;
        }

        .subscription-active-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(70, 130, 180, 0.15);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        .subscription-active-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(70, 130, 180, 0.2);
        }

        .subscription-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .subscription-content {
            padding: 24px;
            position: relative;
            z-index: 2;
        }

        .subscription-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .plan-icon-modern {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .subscription-info {
            flex: 1;
            margin: 0 20px;
        }

        .plan-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 8px 0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #4682B4;
            color: white;
            border-color: #4682B4;
        }

        /* Modern Analytics Section */
        .modern-analytics-section {
            margin-bottom: 32px;
        }

        .analytics-grid-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }

        .analytics-card-modern {
            background: white;
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 8px 30px rgba(70, 130, 180, 0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .analytics-card-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(70, 130, 180, 0.15);
        }

        .analytics-card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .card-header-modern {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .header-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-content h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 4px 0;
        }

        .header-content p {
            font-size: 0.85rem;
            color: #6c757d;
            margin: 0;
        }

        /* Progress Card Styles */
        .progress-stats {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .progress-circle {
            position: relative;
            width: 80px;
            height: 80px;
        }

        .progress-circle svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .circle-bg {
            fill: none;
            stroke: #e9ecef;
            stroke-width: 2;
        }

        .circle {
            fill: none;
            stroke: #4682B4;
            stroke-width: 2;
            stroke-linecap: round;
            transition: stroke-dasharray 0.3s ease;
        }

        .percentage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .progress-label {
            text-align: center;
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 8px;
        }

        .progress-details {
            flex: 1;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .detail-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .detail-dot.completed {
            background: #28a745;
        }

        .detail-dot.in-progress {
            background: #ffc107;
        }

        .detail-dot.pending {
            background: #dc3545;
        }

        .detail-text {
            font-size: 0.85rem;
            color: #6c757d;
        }

        /* Activity Card Styles */
        .activity-chart {
            padding: 16px 0;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            justify-content: space-between;
            height: 120px;
            margin-bottom: 16px;
            gap: 8px;
        }

        .bar-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .bar {
            width: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            border-radius: 4px 4px 0 0;
            transition: all 0.3s ease;
            position: relative;
            min-height: 4px;
        }

        .bar:hover {
            background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
            transform: scaleY(1.1);
        }

        .bar-label {
            font-size: 0.75rem;
            color: #6c757d;
            font-weight: 500;
        }

        .chart-summary {
            text-align: center;
            padding-top: 16px;
            border-top: 1px solid #e9ecef;
        }

        .summary-text {
            font-size: 0.85rem;
            color: #6c757d;
        }

        /* Metrics Card Styles */
        .metrics-grid-modern {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .metric-item-modern {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric-item-modern:hover {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            transform: translateY(-2px);
        }

        .metric-icon-modern {
            font-size: 1.2rem;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .metric-value-modern {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .metric-item-modern:hover .metric-value-modern {
            color: white;
        }

        .metric-label-modern {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .metric-item-modern:hover .metric-label-modern {
            color: rgba(255, 255, 255, 0.9);
        }

        /* Mobile Responsive for Modern Dashboard */
        @media (max-width: 768px) {
            .welcome-container {
                flex-direction: column;
                gap: 20px;
                padding: 24px;
            }

            .quick-stats-grid {
                grid-template-columns: repeat(2, 1fr);
                min-width: auto;
                width: 100%;
            }

            .analytics-grid-modern {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .progress-stats {
                flex-direction: column;
                text-align: center;
                gap: 16px;
            }

            .metrics-grid-modern {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .chart-bars {
                height: 100px;
            }

            .greeting-title {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .welcome-container {
                padding: 20px;
            }

            .quick-stats-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .quick-stat-card {
                padding: 12px;
            }

            .analytics-card-modern {
                padding: 20px;
            }

            .greeting-title {
                font-size: 1.6rem;
            }

            .greeting-emoji {
                font-size: 2rem;
            }
        }

        /* Enhanced Touch Interactions for Mobile */
        @media (hover: none) and (pointer: coarse) {
            .quick-stat-card:active {
                transform: translateY(-1px) scale(0.98);
                transition: all 0.1s ease;
            }

            .analytics-card-modern:active {
                transform: translateY(-1px) scale(0.98);
                transition: all 0.1s ease;
            }

            .metric-item-modern:active {
                transform: translateY(0) scale(0.95);
                transition: all 0.1s ease;
            }
        }

        /* Enhanced Todo Widget Styles */
        .todo-stats-modern {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .todo-stat-modern {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: rgba(70, 130, 180, 0.05);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .todo-stat-modern:hover {
            background: rgba(70, 130, 180, 0.1);
            transform: translateY(-2px);
        }

        .todo-stat-modern .stat-icon {
            font-size: 1rem;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .todo-stat-modern.pending .stat-icon {
            color: #ffc107;
        }

        .todo-stat-modern.completed .stat-icon {
            color: #28a745;
        }

        .todo-stat-modern.progress .stat-icon {
            color: #17a2b8;
        }

        .todo-stat-modern .stat-content {
            flex: 1;
        }

        .todo-stat-modern .stat-number {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2c3e50;
            display: block;
        }

        .todo-stat-modern .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .todo-item-modern {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .todo-item-modern:hover {
            border-color: #4682B4;
            box-shadow: 0 4px 12px rgba(70, 130, 180, 0.1);
            transform: translateY(-1px);
        }

        .todo-checkbox-modern {
            position: relative;
            flex-shrink: 0;
        }

        .enhanced-checkbox {
            opacity: 0;
            position: absolute;
        }

        .checkbox-label {
            width: 20px;
            height: 20px;
            border: 2px solid #4682B4;
            border-radius: 4px;
            display: block;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .enhanced-checkbox:checked + .checkbox-label {
            background: #4682B4;
            border-color: #4682B4;
        }

        .enhanced-checkbox:checked + .checkbox-label::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .todo-content-modern {
            flex: 1;
            min-width: 0;
        }

        .todo-title-modern {
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 4px 0;
            line-height: 1.3;
        }

        .todo-description-modern {
            font-size: 0.85rem;
            color: #6c757d;
            margin: 0 0 8px 0;
            line-height: 1.4;
        }

        .todo-meta-modern {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .todo-priority-modern {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .todo-priority-modern.priority-low {
            background: #d4edda;
            color: #155724;
        }

        .todo-priority-modern.priority-medium {
            background: #fff3cd;
            color: #856404;
        }

        .todo-priority-modern.priority-high {
            background: #f8d7da;
            color: #721c24;
        }

        .todo-due-modern, .todo-date-modern {
            font-size: 0.75rem;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .todo-actions-modern {
            display: flex;
            gap: 4px;
            flex-shrink: 0;
        }

        .btn-action {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .btn-action.edit {
            background: #e3f2fd;
            color: #1976d2;
        }

        .btn-action.edit:hover {
            background: #1976d2;
            color: white;
        }

        .btn-action.delete {
            background: #ffebee;
            color: #d32f2f;
        }

        .btn-action.delete:hover {
            background: #d32f2f;
            color: white;
        }

        /* Mobile responsive for todo widget */
        @media (max-width: 768px) {
            .todo-stats-modern {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .todo-item-modern {
                padding: 12px;
                gap: 8px;
            }

            .todo-meta-modern {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .todo-actions-modern {
                flex-direction: column;
                gap: 2px;
            }
        }
        }
    </style>

    <!-- Include Dashboard JavaScript -->
    <script src="../js/dashboard.js"></script>
    <script src="../js/enhanced-ui.js"></script>
    <script src="../js/loading-system.js"></script>

    <script>
        // Initialize dashboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard loaded');

            // Initialize charts
            initializeCharts();

            // Initialize enhanced dashboard
            initializeModernDashboard();
            initializeEnhancedWidgets();
            initializeEnhancedTodoSystem();
            initializeNotificationSystem();
            initializeInteractiveQuiz();
            initializeMotivationalSystem();

            // Update time every minute
            setInterval(updateCurrentTime, 60000);
        });

        // Update current time display
        function updateCurrentTime() {
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-EG', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                });
                timeElement.textContent = timeString;
            }
        }

        // Show activation modal
        function showActivationModal() {
            window.location.href = 'activate_code.php';
        }

        // Enhanced Todo System
        function initializeEnhancedTodoSystem() {
            let completedTasks = parseInt(localStorage.getItem('completedTasks') || '0');

            // Enhanced modal styles
            const modalStyles = `
                .enhanced-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    backdrop-filter: blur(5px);
                }

                .modal-content {
                    background: white;
                    border-radius: 20px;
                    padding: 30px;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(70, 130, 180, 0.3);
                    transform: scale(0.8);
                    animation: modalAppear 0.3s ease forwards;
                }

                @keyframes modalAppear {
                    to {
                        transform: scale(1);
                    }
                }

                .modal-icon {
                    font-size: 4rem;
                    margin-bottom: 20px;
                }

                .modal-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }

                .modal-message {
                    color: #6c757d;
                    margin-bottom: 25px;
                    line-height: 1.6;
                }

                .modal-buttons {
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                }

                .modal-btn {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 10px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .modal-btn-primary {
                    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
                    color: white;
                }

                .modal-btn-secondary {
                    background: #f8f9fa;
                    color: #6c757d;
                    border: 1px solid #e2e8f0;
                }

                .modal-btn:hover {
                    transform: translateY(-2px);
                }
            `;

            if (!document.getElementById('enhanced-modal-styles')) {
                const style = document.createElement('style');
                style.id = 'enhanced-modal-styles';
                style.textContent = modalStyles;
                document.head.appendChild(style);
            }

            // Show completion modal
            function showCompletionModal() {
                completedTasks++;
                localStorage.setItem('completedTasks', completedTasks.toString());

                let message, icon;
                if (completedTasks % 5 === 0) {
                    message = 'أحسنت! أنت رائع! 🌟';
                    icon = '🏆';
                } else {
                    message = 'أكمل يا بطل! 💪';
                    icon = '✅';
                }

                const modal = document.createElement('div');
                modal.className = 'enhanced-modal';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-icon">${icon}</div>
                        <h3 class="modal-title">مهمة مكتملة!</h3>
                        <p class="modal-message">${message}</p>
                        <div class="modal-buttons">
                            <button class="modal-btn modal-btn-primary" onclick="this.closest('.enhanced-modal').remove()">
                                رائع!
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.remove();
                    }
                }, 3000);
            }

            // Enhanced todo completion
            document.addEventListener('click', function(e) {
                if (e.target.matches('.todo-checkbox') || e.target.closest('.todo-checkbox')) {
                    setTimeout(() => {
                        if (e.target.checked || e.target.closest('.todo-checkbox').checked) {
                            showCompletionModal();
                            showNotification('تم إنهاء المهمة بنجاح!', 'success');
                        }
                    }, 100);
                }
            });
        }

        // Enhanced Notification System
        function initializeNotificationSystem() {
            // Create notification container
            if (!document.getElementById('notification-container')) {
                const container = document.createElement('div');
                container.id = 'notification-container';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 350px;
                `;
                document.body.appendChild(container);
            }
        }

        // Show notification function
        function showNotification(message, type = 'info', duration = 4000) {
            const container = document.getElementById('notification-container');
            if (!container) return;

            const notification = document.createElement('div');
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };

            notification.style.cssText = `
                background: white;
                border-radius: 12px;
                padding: 16px 20px;
                margin-bottom: 10px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                border-left: 4px solid ${colors[type]};
                transform: translateX(100%);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 12px;
                cursor: pointer;
            `;

            notification.innerHTML = `
                <span style="font-size: 1.2rem;">${icons[type]}</span>
                <span style="flex: 1; color: #2c3e50; font-weight: 500;">${message}</span>
                <button style="background: none; border: none; font-size: 1.2rem; color: #6c757d; cursor: pointer;">×</button>
            `;

            container.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Auto remove
            const autoRemove = setTimeout(() => {
                removeNotification(notification);
            }, duration);

            // Manual remove
            notification.addEventListener('click', () => {
                clearTimeout(autoRemove);
                removeNotification(notification);
            });
        }

        function removeNotification(notification) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }

        // Interactive Quiz System
        function initializeInteractiveQuiz() {
            const quizQuestions = [
                {
                    question: "ما هو أفضل وقت للمذاكرة؟",
                    type: "multiple",
                    options: ["الصباح الباكر", "بعد الظهر", "المساء", "منتصف الليل"],
                    correct: 0
                },
                {
                    question: "هل المراجعة اليومية مهمة؟",
                    type: "boolean",
                    correct: true
                },
                {
                    question: "كم ساعة يجب أن تدرس يومياً؟",
                    type: "multiple",
                    options: ["ساعة واحدة", "2-3 ساعات", "4-5 ساعات", "أكثر من 6 ساعات"],
                    correct: 1
                }
            ];

            function showQuiz() {
                const randomQuestion = quizQuestions[Math.floor(Math.random() * quizQuestions.length)];

                const quizModal = document.createElement('div');
                quizModal.className = 'enhanced-modal';

                let optionsHTML = '';
                if (randomQuestion.type === 'multiple') {
                    optionsHTML = randomQuestion.options.map((option, index) =>
                        `<label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px; transition: all 0.3s ease;">
                            <input type="radio" name="quiz-answer" value="${index}" style="margin-left: 8px;">
                            ${option}
                        </label>`
                    ).join('');
                } else {
                    optionsHTML = `
                        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                            <input type="radio" name="quiz-answer" value="true" style="margin-left: 8px;">
                            صحيح
                        </label>
                        <label style="display: block; margin: 10px 0; cursor: pointer; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                            <input type="radio" name="quiz-answer" value="false" style="margin-left: 8px;">
                            خطأ
                        </label>
                    `;
                }

                quizModal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-icon">🧠</div>
                        <h3 class="modal-title">سؤال سريع</h3>
                        <p class="modal-message">${randomQuestion.question}</p>
                        <div style="text-align: right; margin: 20px 0;">
                            ${optionsHTML}
                        </div>
                        <div class="modal-buttons">
                            <button class="modal-btn modal-btn-primary" onclick="checkQuizAnswer(this, ${JSON.stringify(randomQuestion).replace(/"/g, '&quot;')})">
                                تسليم
                            </button>
                            <button class="modal-btn modal-btn-secondary" onclick="this.closest('.enhanced-modal').remove()">
                                تخطي
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(quizModal);

                // Add hover effects to options
                quizModal.querySelectorAll('label').forEach(label => {
                    label.addEventListener('mouseenter', () => {
                        label.style.borderColor = '#4682B4';
                        label.style.background = '#f8f9fa';
                    });
                    label.addEventListener('mouseleave', () => {
                        if (!label.querySelector('input').checked) {
                            label.style.borderColor = '#e2e8f0';
                            label.style.background = 'white';
                        }
                    });
                    label.addEventListener('click', () => {
                        quizModal.querySelectorAll('label').forEach(l => {
                            l.style.borderColor = '#e2e8f0';
                            l.style.background = 'white';
                        });
                        label.style.borderColor = '#4682B4';
                        label.style.background = '#e3f2fd';
                    });
                });
            }

            // Show quiz randomly
            if (Math.random() < 0.3) { // 30% chance
                setTimeout(showQuiz, 5000);
            }

            // Global function for checking answers
            window.checkQuizAnswer = function(button, question) {
                const modal = button.closest('.enhanced-modal');
                const selectedAnswer = modal.querySelector('input[name="quiz-answer"]:checked');

                if (!selectedAnswer) {
                    showNotification('يرجى اختيار إجابة!', 'warning');
                    return;
                }

                let isCorrect = false;
                if (question.type === 'multiple') {
                    isCorrect = parseInt(selectedAnswer.value) === question.correct;
                } else {
                    isCorrect = (selectedAnswer.value === 'true') === question.correct;
                }

                modal.remove();

                if (isCorrect) {
                    showNotification('🎉 لقد نجحت! إجابة ممتازة!', 'success');
                } else {
                    showNotification('😔 لقد فشلت! حاول مرة أخرى!', 'error');
                }
            };
        }

        // Motivational System
        function initializeMotivationalSystem() {
            let startTime = Date.now();
            let breakTimer = null;

            // Check every minute
            setInterval(() => {
                const timeSpent = (Date.now() - startTime) / (1000 * 60); // minutes

                if (timeSpent >= 5 && timeSpent < 6) {
                    showMotivationalMessage();
                }
            }, 60000);

            function showMotivationalMessage() {
                const messages = [
                    'أكمل يا بطل! 💪',
                    'أنت تقوم بعمل رائع! 🌟',
                    'استمر في التقدم! 🚀',
                    'أنت على الطريق الصحيح! ✨'
                ];

                const randomMessage = messages[Math.floor(Math.random() * messages.length)];

                const modal = document.createElement('div');
                modal.className = 'enhanced-modal';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-icon">⏰</div>
                        <h3 class="modal-title">وقت للتحفيز!</h3>
                        <p class="modal-message">لقد قضيت 5 دقائق في التعلم</p>
                        <div class="modal-buttons">
                            <button class="modal-btn modal-btn-primary" onclick="continueStudying(this)">
                                ${randomMessage}
                            </button>
                            <button class="modal-btn modal-btn-secondary" onclick="takeBreak(this)">
                                خذ استراحة 5 دقائق
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            }

            window.continueStudying = function(button) {
                button.closest('.enhanced-modal').remove();
                showNotification('ممتاز! استمر في التعلم! 🎯', 'success');
                startTime = Date.now(); // Reset timer
            };

            window.takeBreak = function(button) {
                button.closest('.enhanced-modal').remove();
                showBreakTimer();
            };

            function showBreakTimer() {
                let breakTime = 5 * 60; // 5 minutes in seconds

                const breakModal = document.createElement('div');
                breakModal.className = 'enhanced-modal';
                breakModal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-icon">☕</div>
                        <h3 class="modal-title">وقت الاستراحة</h3>
                        <p class="modal-message">استرح قليلاً واشرب كوب ماء</p>
                        <div style="font-size: 2rem; font-weight: bold; color: #4682B4; margin: 20px 0;" id="break-timer">
                            05:00
                        </div>
                        <div id="motivational-messages" style="color: #6c757d; font-style: italic; min-height: 20px;">

                        </div>
                        <div class="modal-buttons">
                            <button class="modal-btn modal-btn-primary" onclick="endBreak(this)">
                                إنهاء الاستراحة
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(breakModal);

                const timerElement = breakModal.querySelector('#break-timer');
                const messagesElement = breakModal.querySelector('#motivational-messages');

                const motivationalMessages = [
                    'الراحة مهمة للعقل 🧠',
                    'اشرب الماء للحفاظ على التركيز 💧',
                    'تمدد قليلاً لتنشيط الدورة الدموية 🤸',
                    'تنفس بعمق واسترخ 🌸',
                    'أنت تستحق هذه الاستراحة 😌'
                ];

                let messageIndex = 0;

                breakTimer = setInterval(() => {
                    breakTime--;

                    const minutes = Math.floor(breakTime / 60);
                    const seconds = breakTime % 60;
                    timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                    // Show motivational message every 30 seconds
                    if (breakTime % 30 === 0 && breakTime > 0) {
                        messagesElement.textContent = motivationalMessages[messageIndex % motivationalMessages.length];
                        messageIndex++;
                    }

                    if (breakTime <= 0) {
                        clearInterval(breakTimer);
                        breakModal.remove();
                        showNotification('انتهت الاستراحة! وقت العودة للتعلم! 🎓', 'info');
                        startTime = Date.now(); // Reset timer
                    }
                }, 1000);
            }

            window.endBreak = function(button) {
                if (breakTimer) {
                    clearInterval(breakTimer);
                }
                button.closest('.enhanced-modal').remove();
                showNotification('عودة موفقة للتعلم! 💪', 'success');
                startTime = Date.now(); // Reset timer
            };
        }

        // Enhanced Confirmation System
        function showConfirmation(message, onConfirm, onCancel = null) {
            const modal = document.createElement('div');
            modal.className = 'enhanced-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-icon">❓</div>
                    <h3 class="modal-title">تأكيد العملية</h3>
                    <p class="modal-message">${message}</p>
                    <div class="modal-buttons">
                        <button class="modal-btn modal-btn-primary" onclick="confirmAction(this, true)">
                            نعم
                        </button>
                        <button class="modal-btn modal-btn-secondary" onclick="confirmAction(this, false)">
                            لا
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            window.confirmAction = function(button, confirmed) {
                modal.remove();
                if (confirmed && onConfirm) {
                    onConfirm();
                } else if (!confirmed && onCancel) {
                    onCancel();
                }
            };
        }

        // Override default confirm
        window.originalConfirm = window.confirm;
        window.confirm = function(message) {
            return new Promise((resolve) => {
                showConfirmation(message, () => resolve(true), () => resolve(false));
            });
        };

        // Enhanced form submissions with notifications
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.classList.contains('enhanced-form')) {
                e.preventDefault();

                const formData = new FormData(form);
                const action = form.action || window.location.href;

                fetch(action, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message || 'تم بنجاح!', 'success');
                        if (data.reload) {
                            setTimeout(() => location.reload(), 1500);
                        }
                    } else {
                        showNotification(data.message || 'حدث خطأ!', 'error');
                    }
                })
                .catch(error => {
                    showNotification('حدث خطأ في الاتصال!', 'error');
                });
            }
        });

        // Add interactive quiz to welcome section
        function addQuizToWelcome() {
            const welcomeSection = document.querySelector('.modern-welcome-section .welcome-container');
            if (welcomeSection && !document.querySelector('.daily-quiz')) {
                const quizHTML = `
                    <div class="daily-quiz" style="
                        background: rgba(255, 255, 255, 0.15);
                        border-radius: 16px;
                        padding: 20px;
                        margin-top: 20px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                    ">
                        <h4 style="color: white; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                            <span>🧠</span> سؤال اليوم
                        </h4>
                        <p style="color: rgba(255, 255, 255, 0.9); margin-bottom: 15px; font-size: 0.9rem;">
                            ما هو أفضل وقت للمراجعة؟
                        </p>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-bottom: 15px;">
                            <button class="quiz-option" data-answer="morning" style="
                                background: rgba(255, 255, 255, 0.2);
                                border: 1px solid rgba(255, 255, 255, 0.3);
                                color: white;
                                padding: 8px 12px;
                                border-radius: 8px;
                                font-size: 0.8rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">الصباح</button>
                            <button class="quiz-option" data-answer="evening" style="
                                background: rgba(255, 255, 255, 0.2);
                                border: 1px solid rgba(255, 255, 255, 0.3);
                                color: white;
                                padding: 8px 12px;
                                border-radius: 8px;
                                font-size: 0.8rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">المساء</button>
                            <button class="quiz-option" data-answer="afternoon" style="
                                background: rgba(255, 255, 255, 0.2);
                                border: 1px solid rgba(255, 255, 255, 0.3);
                                color: white;
                                padding: 8px 12px;
                                border-radius: 8px;
                                font-size: 0.8rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">بعد الظهر</button>
                            <button class="quiz-option" data-answer="night" style="
                                background: rgba(255, 255, 255, 0.2);
                                border: 1px solid rgba(255, 255, 255, 0.3);
                                color: white;
                                padding: 8px 12px;
                                border-radius: 8px;
                                font-size: 0.8rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">الليل</button>
                        </div>
                        <button id="submit-quiz" style="
                            background: rgba(255, 255, 255, 0.9);
                            color: #4682B4;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-weight: 600;
                            cursor: pointer;
                            width: 100%;
                            transition: all 0.3s ease;
                        ">تسليم</button>
                    </div>
                `;

                welcomeSection.insertAdjacentHTML('beforeend', quizHTML);

                // Add quiz functionality
                let selectedAnswer = null;

                document.querySelectorAll('.quiz-option').forEach(option => {
                    option.addEventListener('click', function() {
                        document.querySelectorAll('.quiz-option').forEach(opt => {
                            opt.style.background = 'rgba(255, 255, 255, 0.2)';
                        });
                        this.style.background = 'rgba(255, 255, 255, 0.4)';
                        selectedAnswer = this.dataset.answer;
                    });

                    option.addEventListener('mouseenter', function() {
                        if (selectedAnswer !== this.dataset.answer) {
                            this.style.background = 'rgba(255, 255, 255, 0.3)';
                        }
                    });

                    option.addEventListener('mouseleave', function() {
                        if (selectedAnswer !== this.dataset.answer) {
                            this.style.background = 'rgba(255, 255, 255, 0.2)';
                        }
                    });
                });

                document.getElementById('submit-quiz').addEventListener('click', function() {
                    if (!selectedAnswer) {
                        showNotification('يرجى اختيار إجابة!', 'warning');
                        return;
                    }

                    const correctAnswer = 'morning';
                    if (selectedAnswer === correctAnswer) {
                        showNotification('🎉 لقد نجحت! الصباح هو أفضل وقت للمراجعة!', 'success');
                    } else {
                        showNotification('😔 لقد فشلت! الإجابة الصحيحة هي الصباح!', 'error');
                    }

                    // Hide quiz after answering
                    document.querySelector('.daily-quiz').style.opacity = '0.5';
                    document.querySelector('.daily-quiz').style.pointerEvents = 'none';
                });
            }
        }

        // Initialize quiz in welcome section
        setTimeout(addQuizToWelcome, 2000);

        // Enhanced Todo Functions
        window.toggleTodoEnhanced = function(todoId) {
            // Show completion modal and notification
            setTimeout(() => {
                const checkbox = document.getElementById(`todo-${todoId}`);
                if (checkbox && checkbox.checked) {
                    showNotification('تم إنهاء المهمة بنجاح! 🎉', 'success');

                    // Show motivational modal
                    const completedTasks = parseInt(localStorage.getItem('completedTasks') || '0') + 1;
                    localStorage.setItem('completedTasks', completedTasks.toString());

                    let message, icon;
                    if (completedTasks % 5 === 0) {
                        message = 'أحسنت! أنت رائع! 🌟';
                        icon = '🏆';
                    } else {
                        message = 'أكمل يا بطل! 💪';
                        icon = '✅';
                    }

                    setTimeout(() => {
                        const modal = document.createElement('div');
                        modal.className = 'enhanced-modal';
                        modal.innerHTML = `
                            <div class="modal-content">
                                <div class="modal-icon">${icon}</div>
                                <h3 class="modal-title">مهمة مكتملة!</h3>
                                <p class="modal-message">${message}</p>
                                <div class="modal-buttons">
                                    <button class="modal-btn modal-btn-primary" onclick="this.closest('.enhanced-modal').remove()">
                                        رائع!
                                    </button>
                                </div>
                            </div>
                        `;
                        document.body.appendChild(modal);

                        setTimeout(() => {
                            if (modal.parentNode) {
                                modal.remove();
                            }
                        }, 3000);
                    }, 500);
                }

                // Call original toggle function
                if (window.toggleTodo) {
                    toggleTodo(todoId);
                }
            }, 100);
        };

        window.editTodoEnhanced = function(todoId) {
            showNotification('فتح نافذة التعديل...', 'info');
            if (window.editTodo) {
                editTodo(todoId);
            }
        };

        window.deleteTodoEnhanced = function(todoId) {
            showConfirmation(
                'هل أنت متأكد من حذف هذه المهمة؟',
                () => {
                    if (window.deleteTodo) {
                        deleteTodo(todoId);
                        showNotification('تم حذف المهمة بنجاح!', 'success');
                    }
                }
            );
        };

        window.showEnhancedTodoModal = function() {
            showNotification('فتح نافذة إضافة مهمة...', 'info');
            if (window.showAddTodoModal) {
                showAddTodoModal();
            }
        };

        // Initialize modern dashboard features
        function initializeModernDashboard() {
            // Animate welcome section
            const welcomeSection = document.querySelector('.modern-welcome-section');
            if (welcomeSection) {
                welcomeSection.style.opacity = '0';
                welcomeSection.style.transform = 'translateY(20px)';
                welcomeSection.style.transition = 'opacity 0.8s ease, transform 0.8s ease';

                setTimeout(() => {
                    welcomeSection.style.opacity = '1';
                    welcomeSection.style.transform = 'translateY(0)';
                }, 100);
            }

            // Animate analytics cards
            const analyticsCards = document.querySelectorAll('.analytics-card-modern');
            analyticsCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 + (index * 150));
            });

            // Animate quick stats
            const quickStats = document.querySelectorAll('.quick-stat-card');
            quickStats.forEach((stat, index) => {
                stat.style.opacity = '0';
                stat.style.transform = 'scale(0.8)';
                stat.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                setTimeout(() => {
                    stat.style.opacity = '1';
                    stat.style.transform = 'scale(1)';
                }, 300 + (index * 100));
            });

            // Animate progress circles
            const progressCircles = document.querySelectorAll('.progress-circle');
            progressCircles.forEach(circle => {
                const percentage = circle.getAttribute('data-percentage');
                const circleElement = circle.querySelector('.circle');
                if (circleElement && percentage) {
                    setTimeout(() => {
                        circleElement.style.strokeDasharray = `${percentage}, 100`;
                    }, 800);
                }
            });

            // Animate activity bars
            const bars = document.querySelectorAll('.bar');
            bars.forEach((bar, index) => {
                const originalHeight = bar.style.height;
                bar.style.height = '0%';
                bar.style.transition = 'height 0.8s ease';

                setTimeout(() => {
                    bar.style.height = originalHeight;
                }, 1000 + (index * 100));
            });

            // Add ripple effect to cards
            const interactiveCards = document.querySelectorAll('.quick-stat-card, .analytics-card-modern, .metric-item-modern');
            interactiveCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(70, 130, 180, 0.3);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add CSS for ripple animation
            if (!document.getElementById('modern-ripple-styles')) {
                const style = document.createElement('style');
                style.id = 'modern-ripple-styles';
                style.textContent = `
                    @keyframes ripple {
                        to {
                            transform: scale(4);
                            opacity: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }

            // Performance optimization for mobile
            if (window.innerWidth <= 768) {
                // Reduce animations on mobile for better performance
                const style = document.createElement('style');
                style.textContent = `
                    * {
                        animation-duration: 0.3s !important;
                        transition-duration: 0.3s !important;
                    }
                `;
                document.head.appendChild(style);
            }

            // Add intersection observer for lazy loading
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('visible');
                        }
                    });
                }, { threshold: 0.1 });

                document.querySelectorAll('.analytics-card-modern').forEach(card => {
                    observer.observe(card);
                });
            }
        }

        // Initialize enhanced widgets
        function initializeEnhancedWidgets() {
            // Animate courses stats
            const courseStats = document.querySelectorAll('.course-stat-item');
            courseStats.forEach((stat, index) => {
                stat.style.opacity = '0';
                stat.style.transform = 'translateX(-20px)';
                stat.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                setTimeout(() => {
                    stat.style.opacity = '1';
                    stat.style.transform = 'translateX(0)';
                }, 100 + (index * 100));
            });

            // Animate activity items
            const activityItems = document.querySelectorAll('.activity-item');
            activityItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(10px)';
                item.style.transition = 'opacity 0.4s ease, transform 0.4s ease';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 200 + (index * 50));
            });

            // Animate summary numbers
            const summaryNumbers = document.querySelectorAll('.summary-number');
            summaryNumbers.forEach(number => {
                const finalValue = parseInt(number.textContent);
                if (finalValue > 0) {
                    let currentValue = 0;
                    const increment = finalValue / 20;

                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        number.textContent = Math.floor(currentValue);
                    }, 50);
                }
            });

            // Add hover effects
            courseStats.forEach(stat => {
                stat.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(0) scale(1.02)';
                });

                stat.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0) scale(1)';
                });
            });

            // Add CSS for enhanced animations
            if (!document.getElementById('widget-animations')) {
                const style = document.createElement('style');
                style.id = 'widget-animations';
                style.textContent = `
                    .course-stat-item:hover .stat-number {
                        color: #4682B4;
                        transform: scale(1.1);
                        transition: all 0.3s ease;
                    }

                    .activity-item:hover .activity-time {
                        color: #4682B4;
                        font-weight: 600;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Initialize enhanced widgets
        function initializeEnhancedWidgets() {
            // Animate courses stats
            const courseStats = document.querySelectorAll('.course-stat-item');
            courseStats.forEach((stat, index) => {
                stat.style.opacity = '0';
                stat.style.transform = 'translateX(-20px)';
                stat.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                setTimeout(() => {
                    stat.style.opacity = '1';
                    stat.style.transform = 'translateX(0)';
                }, 100 + (index * 100));
            });

            // Animate activity items
            const activityItems = document.querySelectorAll('.activity-item');
            activityItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(10px)';
                item.style.transition = 'opacity 0.4s ease, transform 0.4s ease';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 200 + (index * 50));
            });

            // Animate summary numbers
            const summaryNumbers = document.querySelectorAll('.summary-number');
            summaryNumbers.forEach(number => {
                const finalValue = parseInt(number.textContent);
                if (finalValue > 0) {
                    let currentValue = 0;
                    const increment = finalValue / 20;

                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        number.textContent = Math.floor(currentValue);
                    }, 50);
                }
            });

            // Add hover effects
            courseStats.forEach(stat => {
                stat.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(0) scale(1.02)';
                });

                stat.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0) scale(1)';
                });
            });

            // Add CSS for enhanced animations
            if (!document.getElementById('widget-animations')) {
                const style = document.createElement('style');
                style.id = 'widget-animations';
                style.textContent = `
                    .course-stat-item:hover .stat-number {
                        color: #4682B4;
                        transform: scale(1.1);
                        transition: all 0.3s ease;
                    }

                    .activity-item:hover .activity-time {
                        color: #4682B4;
                        font-weight: 600;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Initialize quick subjects animations
        function initializeQuickSubjectsAnimations() {
            const quickSubjectCards = document.querySelectorAll('.quick-subject-card');

            // Animate cards on load
            quickSubjectCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100 + (index * 100));
            });

            // Add click ripple effect
            quickSubjectCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(70, 130, 180, 0.3);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add CSS for ripple animation
            if (!document.getElementById('ripple-styles')) {
                const style = document.createElement('style');
                style.id = 'ripple-styles';
                style.textContent = `
                    @keyframes ripple {
                        to {
                            transform: scale(4);
                            opacity: 0;
                        }
                    }

                    .quick-subject-card {
                        position: relative;
                        overflow: hidden;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Initialize charts
        function initializeCharts() {
            // Learning Progress Chart
            const progressCtx = document.getElementById('learningProgressChart');
            if (progressCtx) {
                new Chart(progressCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['مكتمل', 'قيد التقدم', 'لم يبدأ'],
                        datasets: [{
                            data: [<?php echo $videoStats['completed_videos'] ?? 0; ?>,
                                   <?php echo ($courseStats['active_courses'] ?? 0) * 2; ?>,
                                   <?php echo ($courseStats['total_courses'] ?? 0) - ($courseStats['active_courses'] ?? 0); ?>],
                            backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Weekly Activity Chart
            const activityCtx = document.getElementById('weeklyActivityChart');
            if (activityCtx) {
                new Chart(activityCtx, {
                    type: 'line',
                    data: {
                        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                        datasets: [{
                            label: 'النشاط اليومي',
                            data: [12, 19, 8, 15, 22, 18, 25],
                            borderColor: '#4682B4',
                            backgroundColor: 'rgba(70, 130, 180, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // Sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            if (sidebar && overlay) {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
            }
        }

        // Close sidebar when clicking overlay
        document.addEventListener('click', function(e) {
            if (e.target.id === 'sidebarOverlay') {
                toggleSidebar();
            }
        });

        // Auto-refresh page after successful operations
        function refreshPage() {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // Override the refresh functions to reload the page
        window.refreshTodoWidget = refreshPage;
        window.refreshNotesWidget = refreshPage;
    </script>

    <?php
    // Show subscription expiry notification if needed
    include __DIR__ . '/../includes/subscription_notification.php';
    ?>
</body>
</html>