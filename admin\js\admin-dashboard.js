/**
 * Modern Admin Dashboard JavaScript
 * Enhanced interactions, animations, and functionality
 */

class AdminDashboard {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.initAnimations();
        this.setupCharts();
        this.initNotifications();
    }

    init() {
        console.log('🚀 Admin Dashboard initialized');
        this.addLoadingStates();
        this.setupTheme();
        this.initCounters();
    }

    setupEventListeners() {
        // Button click effects
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', this.createRippleEffect.bind(this));
        });

        // Card hover effects
        document.querySelectorAll('.content-card, .stat-card').forEach(card => {
            card.addEventListener('mouseenter', this.cardHoverIn.bind(this));
            card.addEventListener('mouseleave', this.cardHoverOut.bind(this));
        });

        // Form enhancements
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', this.inputFocus.bind(this));
            input.addEventListener('blur', this.inputBlur.bind(this));
        });

        // Table row interactions
        document.querySelectorAll('.table tr').forEach(row => {
            row.addEventListener('click', this.tableRowClick.bind(this));
        });

        // Sidebar toggle for mobile
        this.setupSidebarToggle();
    }

    createRippleEffect(e) {
        const button = e.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    cardHoverIn(e) {
        const card = e.currentTarget;
        card.style.transform = 'translateY(-8px) scale(1.02)';
        card.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.2)';
    }

    cardHoverOut(e) {
        const card = e.currentTarget;
        card.style.transform = 'translateY(0) scale(1)';
        card.style.boxShadow = '';
    }

    inputFocus(e) {
        const input = e.currentTarget;
        const parent = input.closest('.form-group');
        if (parent) {
            parent.classList.add('focused');
        }
    }

    inputBlur(e) {
        const input = e.currentTarget;
        const parent = input.closest('.form-group');
        if (parent && !input.value) {
            parent.classList.remove('focused');
        }
    }

    tableRowClick(e) {
        const row = e.currentTarget;
        if (row.tagName === 'TR' && !row.querySelector('th')) {
            row.classList.toggle('selected');
        }
    }

    setupSidebarToggle() {
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'sidebar-toggle d-lg-none';
        toggleBtn.innerHTML = '☰';
        toggleBtn.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 18px;
            cursor: pointer;
        `;

        document.body.appendChild(toggleBtn);

        toggleBtn.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.classList.toggle('open');
            }
        });
    }

    initAnimations() {
        // Intersection Observer for scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.content-card, .stat-card').forEach(el => {
            observer.observe(el);
        });

        // Staggered animation for stats cards
        document.querySelectorAll('.stat-card').forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('bounce-in');
            }, index * 100);
        });
    }

    setupTheme() {
        // Dark mode toggle (future enhancement)
        const themeToggle = document.createElement('button');
        themeToggle.className = 'theme-toggle';
        themeToggle.innerHTML = '🌙';
        themeToggle.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            box-shadow: var(--shadow-medium);
            transition: all var(--transition-normal);
        `;

        document.body.appendChild(themeToggle);

        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            themeToggle.innerHTML = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
        });
    }

    initCounters() {
        // Animated counters for statistics
        document.querySelectorAll('.stat-number').forEach(counter => {
            const target = parseInt(counter.textContent);
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current);
            }, 16);
        });
    }

    setupCharts() {
        // Placeholder for chart initialization
        // This would integrate with Chart.js or similar library
        console.log('📊 Charts setup ready');
    }

    initNotifications() {
        // Real-time notifications system
        this.checkNotifications();
        setInterval(() => {
            this.checkNotifications();
        }, 30000); // Check every 30 seconds
    }

    checkNotifications() {
        // Placeholder for notification checking
        console.log('🔔 Checking notifications...');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1002;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-large);
            min-width: 300px;
            animation: slideInUp 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        });

        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    addLoadingStates() {
        // Add loading states to buttons and forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;
                }
            });
        });
    }
}

// CSS for additional effects
const additionalStyles = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .form-group.focused .form-label {
        color: var(--primary-color);
        transform: translateY(-2px);
    }

    .table tr.selected {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(159, 122, 234, 0.1) 100%);
        border-left: 4px solid var(--primary-color);
    }

    .sidebar-toggle:hover {
        background: var(--primary-dark) !important;
        transform: scale(1.1);
    }

    .theme-toggle:hover {
        transform: scale(1.1) rotate(10deg);
        box-shadow: var(--shadow-large);
    }

    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }

    .notification-info { border-left: 4px solid var(--accent-blue); }
    .notification-success { border-left: 4px solid var(--accent-green); }
    .notification-warning { border-left: 4px solid var(--accent-orange); }
    .notification-error { border-left: 4px solid var(--accent-red); }

    .notification-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: var(--spacing-md);
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        color: var(--text-muted);
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notification-close:hover {
        color: var(--text-primary);
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminDashboard;
}
