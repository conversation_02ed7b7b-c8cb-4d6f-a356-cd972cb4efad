<!-- Subscription Required Modal -->
<div id="subscriptionModal" class="subscription-modal">
    <div class="modal-overlay" onclick="closeSubscriptionModal()"></div>
    <div class="modal-container">
        <div class="modal-header">
            <div class="modal-icon">
                <i class="fas fa-crown"></i>
            </div>
            <button class="modal-close" onclick="closeSubscriptionModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-content">
            <h2>نعتذر، اشتراك مطلوب!</h2>
            <p class="modal-message">
                هذا المحتوى متاح للمشتركين فقط. يرجى الاشتراك في إحدى خططنا للوصول إلى جميع الدروس والمحتوى التعليمي المتميز.
            </p>
            
            <div class="subscription-benefits">
                <h3>ما ستحصل عليه مع الاشتراك:</h3>
                <ul class="benefits-list">
                    <li>
                        <i class="fas fa-check-circle"></i>
                        <span>الوصول لجميع الدروس والمحتوى التعليمي</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle"></i>
                        <span>فيديوهات تعليمية عالية الجودة</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle"></i>
                        <span>تدريبات وامتحانات تفاعلية</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle"></i>
                        <span>ملخصات PDF قابلة للتحميل</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle"></i>
                        <span>تتبع التقدم والإنجازات</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle"></i>
                        <span>دعم فني متواصل</span>
                    </li>
                </ul>
            </div>
            
            <div class="modal-actions">
                <a href="../page/subscriptions.php" class="btn btn-primary btn-large">
                    <i class="fas fa-crown"></i>
                    اشترك الآن
                </a>
                <button class="btn btn-secondary" onclick="closeSubscriptionModal()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
            
            <div class="modal-footer">
                <p class="footer-text">
                    <i class="fas fa-info-circle"></i>
                    يمكنك إلغاء الاشتراك في أي وقت
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.subscription-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-container {
    position: relative;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 25px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.4s ease-out;
    border: 1px solid rgba(135, 206, 235, 0.3);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    position: relative;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    padding: 25px;
    text-align: center;
    border-radius: 25px 25px 0 0;
}

.modal-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 2.5rem;
    color: #ffc107;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-content {
    padding: 30px;
    text-align: center;
}

.modal-content h2 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.modal-message {
    color: #6c757d;
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 25px 0;
}

.subscription-benefits {
    background: rgba(135, 206, 235, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin: 25px 0;
    border: 1px solid rgba(135, 206, 235, 0.2);
}

.subscription-benefits h3 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: right;
}

.benefits-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: #2c3e50;
    font-size: 14px;
    line-height: 1.4;
}

.benefits-list li i {
    color: #28a745;
    font-size: 16px;
    flex-shrink: 0;
}

.modal-actions {
    display: flex;
    gap: 15px;
    margin: 25px 0;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.modal-footer {
    border-top: 1px solid rgba(135, 206, 235, 0.2);
    padding: 20px 0 0 0;
    margin-top: 20px;
}

.footer-text {
    color: #6c757d;
    font-size: 12px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.footer-text i {
    color: #87CEEB;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subscription-modal {
        padding: 10px;
    }
    
    .modal-container {
        max-width: 100%;
        border-radius: 20px;
    }
    
    .modal-header {
        padding: 20px;
        border-radius: 20px 20px 0 0;
    }
    
    .modal-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .modal-content {
        padding: 20px;
    }
    
    .modal-content h2 {
        font-size: 1.5rem;
    }
    
    .modal-message {
        font-size: 14px;
    }
    
    .modal-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
    
    .benefits-list li {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .modal-container {
        border-radius: 15px;
    }
    
    .modal-header {
        border-radius: 15px 15px 0 0;
    }
    
    .modal-content h2 {
        font-size: 1.3rem;
    }
    
    .subscription-benefits {
        padding: 15px;
    }
    
    .benefits-list li {
        font-size: 12px;
        padding: 6px 0;
    }
}

/* Animation for showing modal */
.subscription-modal.show {
    display: flex;
}

/* Smooth transitions */
.subscription-modal * {
    box-sizing: border-box;
}

/* Focus styles for accessibility */
.modal-close:focus,
.btn:focus {
    outline: 2px solid #87CEEB;
    outline-offset: 2px;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}
</style>

<script>
function showSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    const body = document.body;
    
    if (modal) {
        modal.classList.add('show');
        body.classList.add('modal-open');
        
        // Focus management for accessibility
        const closeButton = modal.querySelector('.modal-close');
        if (closeButton) {
            closeButton.focus();
        }
        
        // Add escape key listener
        document.addEventListener('keydown', handleModalEscape);
    }
}

function closeSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    const body = document.body;
    
    if (modal) {
        modal.classList.remove('show');
        body.classList.remove('modal-open');
        
        // Remove escape key listener
        document.removeEventListener('keydown', handleModalEscape);
    }
}

function handleModalEscape(event) {
    if (event.key === 'Escape') {
        closeSubscriptionModal();
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('subscriptionModal');
    if (modal && event.target === modal.querySelector('.modal-overlay')) {
        closeSubscriptionModal();
    }
});

// Prevent modal from closing when clicking inside the modal content
document.addEventListener('click', function(event) {
    const modalContainer = document.querySelector('.modal-container');
    if (modalContainer && modalContainer.contains(event.target)) {
        event.stopPropagation();
    }
});
</script>
