<?php
header('Content-Type: application/json');

try {
    // Set the correct path
    $basePath = dirname(__DIR__);
    require_once $basePath . '/config/config.php';
    require_once $basePath . '/includes/database.php';
    
    // Get database connection
    $db = DatabaseConnection::getInstance()->getConnection();
    
    // Create course_exams table
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_exams (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            duration_minutes INT DEFAULT 60,
            total_marks DECIMAL(5,2) DEFAULT 0,
            passing_marks DECIMAL(5,2) DEFAULT 0,
            week_number INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_course_id (course_id),
            INDEX idx_week_number (week_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create course_exam_questions table
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_exam_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            exam_id INT NOT NULL,
            question_text TEXT NOT NULL,
            question_type ENUM('true_false', 'multiple_choice') NOT NULL,
            options JSON,
            correct_answer TEXT NOT NULL,
            explanation TEXT,
            points DECIMAL(5,2) DEFAULT 1.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_exam_id (exam_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create course_weekly_tests table
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_weekly_tests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            duration_minutes INT DEFAULT 30,
            total_marks DECIMAL(5,2) DEFAULT 0,
            passing_marks DECIMAL(5,2) DEFAULT 0,
            week_number INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_course_id (course_id),
            INDEX idx_week_number (week_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create course_weekly_test_questions table
    $db->exec("
        CREATE TABLE IF NOT EXISTS course_weekly_test_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            test_id INT NOT NULL,
            question_text TEXT NOT NULL,
            question_type ENUM('true_false', 'multiple_choice') NOT NULL,
            options JSON,
            correct_answer TEXT NOT NULL,
            explanation TEXT,
            points DECIMAL(5,2) DEFAULT 1.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_test_id (test_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create user_exam_attempts table
    $db->exec("
        CREATE TABLE IF NOT EXISTS user_exam_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            exam_id INT NOT NULL,
            total_score DECIMAL(5,2) DEFAULT 0.00,
            max_score DECIMAL(5,2) DEFAULT 0.00,
            percentage DECIMAL(5,2) DEFAULT 0.00,
            passed BOOLEAN DEFAULT FALSE,
            time_taken_minutes INT DEFAULT 0,
            attempt_number INT DEFAULT 1,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            INDEX idx_user_exam (user_id, exam_id),
            INDEX idx_passed (passed),
            INDEX idx_completed_at (completed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create user_weekly_test_attempts table
    $db->exec("
        CREATE TABLE IF NOT EXISTS user_weekly_test_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            test_id INT NOT NULL,
            total_score DECIMAL(5,2) DEFAULT 0.00,
            max_score DECIMAL(5,2) DEFAULT 0.00,
            percentage DECIMAL(5,2) DEFAULT 0.00,
            time_taken_minutes INT DEFAULT 0,
            attempt_number INT DEFAULT 1,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            INDEX idx_user_test (user_id, test_id),
            INDEX idx_completed_at (completed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Add some sample data
    $stmt = $db->query("SELECT COUNT(*) FROM course_exams");
    $examCount = $stmt->fetchColumn();
    
    if ($examCount == 0) {
        // Get first course
        $stmt = $db->query("SELECT id FROM courses LIMIT 1");
        $courseId = $stmt->fetchColumn();
        
        if ($courseId) {
            // Create sample exam
            $stmt = $db->prepare("
                INSERT INTO course_exams (course_id, title, description, duration_minutes, total_marks, passing_marks, week_number, is_active, created_by)
                VALUES (?, 'امتحان تجريبي', 'امتحان تجريبي للاختبار', 30, 10, 6, 1, 1, 1)
            ");
            $stmt->execute([$courseId]);
            $examId = $db->lastInsertId();
            
            // Add sample questions
            $questions = [
                ['هل PHP لغة برمجة؟', 'true_false', 'true', 2],
                ['هل HTML لغة برمجة؟', 'true_false', 'false', 2],
                ['هل CSS تستخدم للتنسيق؟', 'true_false', 'true', 3],
                ['هل JavaScript تعمل في المتصفح؟', 'true_false', 'true', 3]
            ];
            
            foreach ($questions as $q) {
                $stmt = $db->prepare("
                    INSERT INTO course_exam_questions (exam_id, question_text, question_type, correct_answer, points)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$examId, $q[0], $q[1], $q[2], $q[3]]);
            }
            
            // Create sample weekly test
            $stmt = $db->prepare("
                INSERT INTO course_weekly_tests (course_id, title, description, duration_minutes, total_marks, passing_marks, week_number, is_active, created_by)
                VALUES (?, 'اختبار أسبوعي تجريبي', 'اختبار أسبوعي تجريبي للاختبار', 20, 5, 3, 1, 1, 1)
            ");
            $stmt->execute([$courseId]);
            $testId = $db->lastInsertId();
            
            // Add sample questions for weekly test
            $testQuestions = [
                ['هل CSS تستخدم للتنسيق؟', 'true_false', 'true', 1],
                ['هل MySQL قاعدة بيانات؟', 'true_false', 'true', 2],
                ['هل JavaScript لغة برمجة؟', 'true_false', 'true', 2]
            ];
            
            foreach ($testQuestions as $q) {
                $stmt = $db->prepare("
                    INSERT INTO course_weekly_test_questions (test_id, question_text, question_type, correct_answer, points)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$testId, $q[0], $q[1], $q[2], $q[3]]);
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Tables created and sample data added successfully',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
