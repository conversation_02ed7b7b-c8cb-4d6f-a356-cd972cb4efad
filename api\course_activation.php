<?php
header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

if (!isset($_POST['action']) || !isset($_POST['course_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة']);
    exit;
}

try {
    $courseManager = new CourseManager();
    $db = Database::getInstance()->getConnection();
    $userId = $_SESSION['user_id'];
    $courseId = $_POST['course_id'];
    
    // Check if course exists
    $course = $courseManager->getCourseById($courseId);
    if (!$course) {
        echo json_encode(['success' => false, 'message' => 'الكورس غير موجود']);
        exit;
    }
    
    // Check if user already has access
    if ($courseManager->userHasAccess($userId, $courseId)) {
        echo json_encode(['success' => false, 'message' => 'لديك وصول للكورس بالفعل']);
        exit;
    }
    
    switch ($_POST['action']) {
        case 'activate_code':
            if (!isset($_POST['activation_code'])) {
                echo json_encode(['success' => false, 'message' => 'كود التفعيل مطلوب']);
                exit;
            }
            
            $activationCode = trim($_POST['activation_code']);
            
            // Check if activation code is valid
            $stmt = $db->prepare("
                SELECT * FROM activation_codes 
                WHERE code = ? AND course_id = ? AND is_active = 1 
                AND (expires_at IS NULL OR expires_at > NOW())
                AND used_count < user_limit
            ");
            $stmt->execute([$activationCode, $courseId]);
            $codeData = $stmt->fetch();
            
            if (!$codeData) {
                echo json_encode(['success' => false, 'message' => 'كود التفعيل غير صحيح أو منتهي الصلاحية']);
                exit;
            }
            
            $db->beginTransaction();
            
            try {
                // Create or update subscription
                $courseManager->createSubscription($userId, $courseId, 'code');
                
                // Activate subscription
                $courseManager->activateSubscription($userId, $courseId, 'code', [
                    'activation_code' => $activationCode
                ]);
                
                // Update code usage count
                $stmt = $db->prepare("UPDATE activation_codes SET used_count = used_count + 1 WHERE id = ?");
                $stmt->execute([$codeData['id']]);
                
                $db->commit();
                
                echo json_encode(['success' => true, 'message' => 'تم تفعيل الكورس بنجاح']);
                
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            break;
            
        case 'payment_request':
            if (!isset($_FILES['receipt_image']) || !isset($_POST['mobile_number']) || !isset($_POST['transfer_method'])) {
                echo json_encode(['success' => false, 'message' => 'جميع البيانات مطلوبة']);
                exit;
            }
            
            // Validate file upload
            $uploadDir = '../uploads/receipts/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $file = $_FILES['receipt_image'];
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            
            if (!in_array($file['type'], $allowedTypes)) {
                echo json_encode(['success' => false, 'message' => 'نوع الملف غير مدعوم']);
                exit;
            }
            
            if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جداً']);
                exit;
            }
            
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'receipt_' . $userId . '_' . $courseId . '_' . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;
            
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                echo json_encode(['success' => false, 'message' => 'فشل في رفع الملف']);
                exit;
            }
            
            $db->beginTransaction();
            
            try {
                // Create or update subscription
                $courseManager->createSubscription($userId, $courseId, 'payment');
                
                // Create payment request
                $stmt = $db->prepare("
                    INSERT INTO payment_requests (user_id, course_id, receipt_image, mobile_number, transfer_method)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        receipt_image = VALUES(receipt_image),
                        mobile_number = VALUES(mobile_number),
                        transfer_method = VALUES(transfer_method),
                        status = 'pending',
                        created_at = NOW()
                ");
                
                $stmt->execute([
                    $userId,
                    $courseId,
                    $filename,
                    $_POST['mobile_number'],
                    $_POST['transfer_method']
                ]);
                
                $db->commit();
                
                echo json_encode(['success' => true, 'message' => 'تم إرسال طلب التفعيل بنجاح']);
                
            } catch (Exception $e) {
                $db->rollBack();
                // Delete uploaded file if database operation failed
                if (file_exists($filepath)) {
                    unlink($filepath);
                }
                throw $e;
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Error in course activation: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
