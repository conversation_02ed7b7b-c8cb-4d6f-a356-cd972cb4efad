/**
 * Education Selector JavaScript
 * Handles dynamic grade and specialization selection based on education level and type
 */

// Grade options for different education levels (copied from signup.php)
const gradeOptions = {
    'primary': {
        'azhari': {
            '4': 'الرابع الابتدائي',
            '5': 'الخامس الابتدائي',
            '6': 'السادس الابتدائي'
        },
        'general': {
            '1': 'الأول الابتدائي',
            '2': 'الثاني الابتدائي',
            '3': 'الثالث الابتدائي',
            '4': 'الرابع الابتدائي',
            '5': 'الخامس الابتدائي',
            '6': 'السادس الابتدائي'
        }
    },
    'preparatory': {
        'azhari': {
            '1': 'الأول الإعدادي',
            '2': 'الثاني الإعدادي',
            '3': 'الثالث الإعدادي'
        },
        'general': {
            '1': 'الأول الإعدادي',
            '2': 'الثاني الإعدادي',
            '3': 'الثالث الإعدادي'
        }
    },
    'secondary': {
        'azhari': {
            '1': 'الأول الثانوي',
            '2': 'الثاني الثانوي',
            '3': 'الثالث الثانوي'
        },
        'general': {
            '1': 'الأول الثانوي',
            '2': 'الثاني الثانوي',
            '3': 'الثالث الثانوي'
        }
    }
};

// News grade options (includes 'all' option)
const newsGradeOptions = {
    'primary': {
        'azhari': {
            'all': 'جميع الصفوف',
            '4': 'الرابع الابتدائي',
            '5': 'الخامس الابتدائي',
            '6': 'السادس الابتدائي'
        },
        'general': {
            'all': 'جميع الصفوف',
            '1': 'الأول الابتدائي',
            '2': 'الثاني الابتدائي',
            '3': 'الثالث الابتدائي',
            '4': 'الرابع الابتدائي',
            '5': 'الخامس الابتدائي',
            '6': 'السادس الابتدائي'
        }
    },
    'preparatory': {
        'azhari': {
            'all': 'جميع الصفوف',
            '1': 'الأول الإعدادي',
            '2': 'الثاني الإعدادي',
            '3': 'الثالث الإعدادي'
        },
        'general': {
            'all': 'جميع الصفوف',
            '1': 'الأول الإعدادي',
            '2': 'الثاني الإعدادي',
            '3': 'الثالث الإعدادي'
        }
    },
    'secondary': {
        'azhari': {
            'all': 'جميع الصفوف',
            '1': 'الأول الثانوي',
            '2': 'الثاني الثانوي',
            '3': 'الثالث الثانوي'
        },
        'general': {
            'all': 'جميع الصفوف',
            '1': 'الأول الثانوي',
            '2': 'الثاني الثانوي',
            '3': 'الثالث الثانوي'
        }
    },
    'all': {
        'all': {
            'all': 'جميع الصفوف'
        }
    }
};

/**
 * Initialize education selectors
 */
function initializeEducationSelectors(prefix = '', isNewsForm = false) {
    const educationLevelSelect = document.getElementById(prefix + 'education_level');
    const educationTypeSelect = document.getElementById(prefix + 'education_type');
    const gradeSelect = document.getElementById(prefix + 'grade');
    const specializationContainer = document.getElementById(prefix + 'specialization_container');

    if (educationLevelSelect) {
        educationLevelSelect.addEventListener('change', function() {
            updateGradeOptions(prefix, isNewsForm);
            updateSpecializationVisibility(prefix);
        });
    }

    if (educationTypeSelect) {
        educationTypeSelect.addEventListener('change', function() {
            updateGradeOptions(prefix, isNewsForm);
        });
    }

    // Initialize on page load
    updateGradeOptions(prefix, isNewsForm);
    updateSpecializationVisibility(prefix);
}

/**
 * Update grade options based on education level and type
 */
function updateGradeOptions(prefix = '', isNewsForm = false) {
    const educationLevelSelect = document.getElementById(prefix + 'education_level');
    const educationTypeSelect = document.getElementById(prefix + 'education_type');
    const gradeSelect = document.getElementById(prefix + 'grade');

    if (!educationLevelSelect || !educationTypeSelect || !gradeSelect) {
        return;
    }

    const educationLevel = educationLevelSelect.value;
    const educationType = educationTypeSelect.value;

    // Choose the appropriate grade options
    const currentGradeOptions = isNewsForm ? newsGradeOptions : gradeOptions;

    // Clear current options
    if (isNewsForm) {
        gradeSelect.innerHTML = '<option value="all">جميع الصفوف</option>';
    } else {
        gradeSelect.innerHTML = '<option value="">اختر الصف</option>';
    }

    if (educationLevel && educationType && currentGradeOptions[educationLevel] && currentGradeOptions[educationLevel][educationType]) {
        const grades = currentGradeOptions[educationLevel][educationType];

        Object.entries(grades).forEach(([value, text]) => {
            if (isNewsForm && value === 'all') {
                return; // Skip 'all' as it's already added
            }
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            gradeSelect.appendChild(option);
        });
    }
}

/**
 * Update specialization visibility for secondary level
 */
function updateSpecializationVisibility(prefix = '') {
    const educationLevelSelect = document.getElementById(prefix + 'education_level');
    const specializationContainer = document.getElementById(prefix + 'specialization_container');

    if (!educationLevelSelect || !specializationContainer) {
        return;
    }

    const educationLevel = educationLevelSelect.value;
    const isNewsForm = document.querySelector('textarea[name="content"]') !== null;

    if (educationLevel === 'secondary') {
        specializationContainer.style.display = 'block';
        // Make specialization required for secondary (except for news)
        const specializationInputs = specializationContainer.querySelectorAll('input[type="radio"], select');
        specializationInputs.forEach(input => {
            if (input.type === 'radio') {
                if (!isNewsForm) {
                    input.setAttribute('required', 'required');
                }
            } else if (input.tagName === 'SELECT') {
                if (!isNewsForm) {
                    input.setAttribute('required', 'required');
                }
            }
        });
    } else {
        specializationContainer.style.display = 'none';
        // Clear specialization value and remove required
        const specializationInputs = specializationContainer.querySelectorAll('input[type="radio"], select');
        specializationInputs.forEach(input => {
            if (input.type === 'radio') {
                input.checked = false;
                input.removeAttribute('required');
            } else if (input.tagName === 'SELECT') {
                // For news forms, set to 'all', for others set to empty
                input.value = isNewsForm ? 'all' : '';
                input.removeAttribute('required');
            }
        });
    }
}

/**
 * Set education values (for editing)
 */
function setEducationValues(prefix = '', educationLevel, educationType, grade, specialization) {
    const educationLevelSelect = document.getElementById(prefix + 'education_level');
    const educationTypeSelect = document.getElementById(prefix + 'education_type');
    const gradeSelect = document.getElementById(prefix + 'grade');

    // Detect if this is a news form
    const isNewsForm = document.querySelector('textarea[name="content"]') !== null;

    if (educationLevelSelect) {
        educationLevelSelect.value = educationLevel || (isNewsForm ? 'all' : '');
    }

    if (educationTypeSelect) {
        educationTypeSelect.value = educationType || (isNewsForm ? 'all' : '');
    }

    // Update grade options first
    updateGradeOptions(prefix, isNewsForm);
    updateSpecializationVisibility(prefix);

    // Then set the grade value
    setTimeout(() => {
        if (gradeSelect && grade) {
            gradeSelect.value = grade;
        }

        // Set specialization if provided
        if (specialization) {
            const specializationRadio = document.getElementById(prefix + 'specialization_' + specialization);
            const specializationSelect = document.getElementById(prefix + 'specialization');

            if (specializationRadio) {
                specializationRadio.checked = true;
            } else if (specializationSelect) {
                specializationSelect.value = specialization;
            }
        }
    }, 100);
}

/**
 * Validate education form
 */
function validateEducationForm(prefix = '') {
    const educationLevelSelect = document.getElementById(prefix + 'education_level');
    const educationTypeSelect = document.getElementById(prefix + 'education_type');
    const gradeSelect = document.getElementById(prefix + 'grade');
    
    let isValid = true;
    let errors = [];

    if (!educationLevelSelect || !educationLevelSelect.value) {
        errors.push('يرجى اختيار المرحلة التعليمية');
        isValid = false;
    }

    if (!educationTypeSelect || !educationTypeSelect.value) {
        errors.push('يرجى اختيار نوع التعليم');
        isValid = false;
    }

    if (!gradeSelect || !gradeSelect.value) {
        errors.push('يرجى اختيار الصف');
        isValid = false;
    }

    // Check specialization for secondary level
    if (educationLevelSelect && educationLevelSelect.value === 'secondary') {
        const specializationRadios = document.querySelectorAll(`input[name="${prefix}specialization"]:checked`);
        const specializationSelect = document.getElementById(prefix + 'specialization');
        
        if (specializationRadios.length === 0 && (!specializationSelect || !specializationSelect.value)) {
            errors.push('يرجى اختيار التخصص للمرحلة الثانوية');
            isValid = false;
        }
    }

    return {
        isValid: isValid,
        errors: errors
    };
}

/**
 * Get education form data
 */
function getEducationFormData(prefix = '') {
    const educationLevelSelect = document.getElementById(prefix + 'education_level');
    const educationTypeSelect = document.getElementById(prefix + 'education_type');
    const gradeSelect = document.getElementById(prefix + 'grade');
    const specializationRadio = document.querySelector(`input[name="${prefix}specialization"]:checked`);
    const specializationSelect = document.getElementById(prefix + 'specialization');

    return {
        education_level: educationLevelSelect ? educationLevelSelect.value : '',
        education_type: educationTypeSelect ? educationTypeSelect.value : '',
        grade: gradeSelect ? gradeSelect.value : '',
        specialization: specializationRadio ? specializationRadio.value : (specializationSelect ? specializationSelect.value : '')
    };
}

/**
 * Initialize when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    // Detect if this is a news form by checking for news-specific elements
    const isNewsPage = document.querySelector('input[name="action"][value="add"]') &&
                       document.querySelector('textarea[name="content"]');

    // Initialize main form selectors
    initializeEducationSelectors('', isNewsPage);

    // Initialize edit form selectors (with edit_ prefix)
    initializeEducationSelectors('edit_', isNewsPage);

    // Initialize filter form selectors (with filter_ prefix)
    initializeEducationSelectors('filter_', isNewsPage);
});

/**
 * Global functions for external use
 */
window.EducationSelector = {
    initialize: initializeEducationSelectors,
    updateGrades: updateGradeOptions,
    updateSpecialization: updateSpecializationVisibility,
    setValues: setEducationValues,
    validate: validateEducationForm,
    getData: getEducationFormData
};
