/* ===== LOADING SYSTEM STYLES ===== */

/* Loading Screen Container */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #4682B4 0%, #87CEEB 50%, #5F9EA0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    transition: all 0.6s ease-out;
    opacity: 1;
    visibility: visible;
}

.loading-screen.fade-out {
    opacity: 0;
    visibility: hidden;
}

/* Simple Loading Spinner */

.loading-spinner {
    width: 80px;
    height: 80px;
    border: 6px solid rgba(255, 255, 255, 0.2);
    border-top: 6px solid white;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    box-shadow:
        0 0 30px rgba(255, 255, 255, 0.4),
        0 0 60px rgba(255, 255, 255, 0.2);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hide page content during loading */
body.loading {
    overflow: hidden;
}

body.loading > *:not(.loading-screen):not(.welcome-message) {
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Welcome Message Styles */
.welcome-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 15px;
    box-shadow: 
        0 10px 30px rgba(40, 167, 69, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    z-index: 9999;
    transform: translateX(100%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 350px;
    backdrop-filter: blur(10px);
}

.welcome-message.show {
    transform: translateX(0);
}

.welcome-message.hide {
    transform: translateX(100%);
    opacity: 0;
}

.welcome-message-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.welcome-message-icon {
    font-size: 24px;
    animation: bounceIcon 2s ease-in-out infinite;
}

@keyframes bounceIcon {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.welcome-message-title {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
}

.welcome-message-text {
    font-size: 14px;
    margin: 0;
    opacity: 0.95;
    line-height: 1.4;
}

.welcome-message-close {
    position: absolute;
    top: 8px;
    left: 8px;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.welcome-message-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

/* Page Load Success Message */
.page-load-message {
    background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
}

.page-load-message .welcome-message-icon {
    color: #FFD700;
}

/* Different message types */
.welcome-message.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.welcome-message.info {
    background: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
}

.welcome-message.warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #333;
}

.welcome-message.error {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Loading Dots Animation */
.loading-dots {
    display: inline-block;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.loading-dots::after {
    content: '';
    animation: dots 2s infinite;
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .loading-logo {
        width: 100px;
        height: 100px;
        margin-bottom: 25px;
    }
    
    .loading-text {
        font-size: 24px;
        margin-bottom: 12px;
    }
    
    .loading-subtitle {
        font-size: 14px;
        margin-bottom: 30px;
    }
    
    .loading-progress {
        width: 250px;
    }
    
    .welcome-message {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        padding: 15px 20px;
    }
    
    .welcome-message-title {
        font-size: 16px;
    }
    
    .welcome-message-text {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .loading-text {
        font-size: 20px;
    }
    
    .loading-subtitle {
        font-size: 12px;
    }
    
    .loading-progress {
        width: 200px;
    }
    
    .loading-spinner {
        width: 50px;
        height: 50px;
    }
}
