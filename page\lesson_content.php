<?php
require_once '../config/config.php';
require_once '../includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . SITE_URL . '/login.php');
    exit;
}

$db = Database::getInstance()->getConnection();

// Get lesson ID
$lesson_id = isset($_GET['lesson_id']) ? (int)$_GET['lesson_id'] : 0;

if (!$lesson_id) {
    header('Location: ' . SITE_URL . '/page/curriculum.php');
    exit;
}

// Get lesson info with subject
$stmt = $db->prepare("
    SELECT l.*, cs.name as subject_name, cs.color as subject_color, cs.id as subject_id
    FROM lessons l 
    JOIN curriculum_subjects cs ON l.subject_id = cs.id 
    WHERE l.id = ? AND l.is_active = 1
");
$stmt->execute([$lesson_id]);
$lesson = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$lesson) {
    header('Location: curriculum.php');
    exit;
}

// Get user's subscription information
$user_id = $_SESSION['user_id'];
$subscriptionQuery = "SELECT u.subscription_status, u.subscription_end_date, u.current_plan_id
                     FROM users u WHERE u.id = ?";
$stmt = $db->prepare($subscriptionQuery);
$stmt->execute([$user_id]);
$userSubscription = $stmt->fetch(PDO::FETCH_ASSOC);

$has_subscription = $userSubscription && $userSubscription['subscription_status'] === 'active' &&
                   $userSubscription['subscription_end_date'] &&
                   strtotime($userSubscription['subscription_end_date']) > time();

// Check if user can access this lesson
$can_access = $lesson['is_free'] || $has_subscription;

if (!$can_access) {
    header('Location: subject_lessons.php?subject_id=' . $lesson['subject_id']);
    exit;
}

// Get lesson content
try {
    $videos_stmt = $db->prepare("
        SELECT lv.*, COALESCE(uvp.is_completed, 0) as is_completed
        FROM lesson_videos lv
        LEFT JOIN user_video_progress uvp ON lv.id = uvp.video_id AND uvp.user_id = ?
        WHERE lv.lesson_id = ? AND lv.is_active = 1
        ORDER BY lv.video_order
    ");
    $videos_stmt->execute([$user_id, $lesson_id]);
    $videos = $videos_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug: Log video count
    error_log("Lesson ID: $lesson_id, User ID: $user_id, Videos found: " . count($videos));

} catch (Exception $e) {
    // Log the error for debugging
    error_log("Error fetching videos for lesson $lesson_id: " . $e->getMessage());
    // If tables don't exist, create empty arrays
    $videos = [];
}

try {
    $exercises_stmt = $db->prepare("
        SELECT le.*, COALESCE(uep.is_completed, 0) as is_completed, COALESCE(uep.score, 0) as score
        FROM lesson_exercises le
        LEFT JOIN user_exercise_progress uep ON le.id = uep.exercise_id AND uep.user_id = ?
        WHERE le.lesson_id = ? AND le.is_active = 1
        ORDER BY le.exercise_order
    ");
    $exercises_stmt->execute([$user_id, $lesson_id]);
    $exercises = $exercises_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $exercises = [];
}

try {
    $exams_stmt = $db->prepare("
        SELECT le.*, COALESCE(uep.is_completed, 0) as is_completed, COALESCE(uep.score, 0) as score, COALESCE(uep.is_passed, 0) as is_passed
        FROM lesson_exams le
        LEFT JOIN user_exam_progress uep ON le.id = uep.exam_id AND uep.user_id = ?
        WHERE le.lesson_id = ? AND le.is_active = 1
        ORDER BY le.exam_order
    ");
    $exams_stmt->execute([$user_id, $lesson_id]);
    $exams = $exams_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $exams = [];
}

try {
    $summaries_stmt = $db->prepare("
        SELECT ls.*, COALESCE(usp.is_completed, 0) as is_completed
        FROM lesson_summaries ls
        LEFT JOIN user_summary_progress usp ON ls.id = usp.summary_id AND usp.user_id = ?
        WHERE ls.lesson_id = ? AND ls.is_active = 1
        ORDER BY ls.summary_order
    ");
    $summaries_stmt->execute([$user_id, $lesson_id]);
    $summaries = $summaries_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $summaries = [];
}

$exams_stmt = $db->prepare("
    SELECT le.*, COALESCE(uep.is_completed, 0) as is_completed, COALESCE(uep.score, 0) as score, COALESCE(uep.is_passed, 0) as is_passed
    FROM lesson_exams le
    LEFT JOIN user_exam_progress uep ON le.id = uep.exam_id AND uep.user_id = ?
    WHERE le.lesson_id = ? AND le.is_active = 1
    ORDER BY le.exam_order
");
$exams_stmt->execute([$user_id, $lesson_id]);
$exams = $exams_stmt->fetchAll(PDO::FETCH_ASSOC);

$summaries_stmt = $db->prepare("
    SELECT ls.*, COALESCE(usp.is_completed, 0) as is_completed
    FROM lesson_summaries ls
    LEFT JOIN user_summary_progress usp ON ls.id = usp.summary_id AND usp.user_id = ?
    WHERE ls.lesson_id = ? AND ls.is_active = 1
    ORDER BY ls.summary_order
");
$summaries_stmt->execute([$user_id, $lesson_id]);
$summaries = $summaries_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate completion percentage
$total_items = count($videos) + count($exercises) + count($exams) + count($summaries);
$completed_items = 0;

foreach ($videos as $video) {
    if ($video['is_completed']) $completed_items++;
}
foreach ($exercises as $exercise) {
    if ($exercise['is_completed']) $completed_items++;
}
foreach ($exams as $exam) {
    if ($exam['is_completed']) $completed_items++;
}
foreach ($summaries as $summary) {
    if ($summary['is_completed']) $completed_items++;
}

$completion_percentage = $total_items > 0 ? round(($completed_items / $total_items) * 100, 2) : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($lesson['title']); ?> - محتوى الدرس - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Mobile Sidebar Toggle -->
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                ☰
            </button>

            <div class="page-container">
                <!-- Lesson Header -->
                <div class="lesson-header">
                    <div class="lesson-info">
                        <div class="lesson-number-large">
                            <span><?php echo $lesson['lesson_number']; ?></span>
                        </div>
                        <div class="lesson-details">
                            <h1><?php echo htmlspecialchars($lesson['title']); ?></h1>
                            <div class="lesson-meta">
                                <span class="subject-badge" style="background-color: <?php echo $lesson['subject_color']; ?>20; color: <?php echo $lesson['subject_color']; ?>;">
                                    <?php echo htmlspecialchars($lesson['subject_name']); ?>
                                </span>
                                <span class="lesson-type">
                                    <?php if ($lesson['is_free']): ?>
                                        <span class="badge badge-success">مجاني</span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">مدفوع</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <?php if ($lesson['description']): ?>
                                <p class="lesson-description"><?php echo htmlspecialchars($lesson['description']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="lesson-actions">
                        <div class="completion-status">
                            <div class="completion-circle-large" style="background: conic-gradient(<?php echo $lesson['subject_color']; ?> <?php echo $completion_percentage * 3.6; ?>deg, #e9ecef 0deg);">
                                <span><?php echo round($completion_percentage); ?>%</span>
                            </div>
                            <span class="completion-text">مكتمل</span>
                        </div>
                        <a href="subject_lessons.php?subject_id=<?php echo $lesson['subject_id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i>
                            العودة للدروس
                        </a>
                    </div>
                </div>

                <!-- Content Summary -->
                <div class="content-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-list-check"></i>
                        </div>
                        <div class="summary-info">
                            <h3>إجمالي المحتوى</h3>
                            <div class="content-breakdown">
                                <span class="content-item">
                                    <i class="fas fa-video"></i>
                                    <?php echo count($videos); ?> فيديو
                                </span>
                                <span class="content-item">
                                    <i class="fas fa-dumbbell"></i>
                                    <?php echo count($exercises); ?> تدريب
                                </span>
                                <span class="content-item">
                                    <i class="fas fa-clipboard-check"></i>
                                    <?php echo count($exams); ?> امتحان
                                </span>
                                <span class="content-item">
                                    <i class="fas fa-file-pdf"></i>
                                    <?php echo count($summaries); ?> ملخص
                                </span>
                            </div>
                            <div class="total-items">
                                <strong><?php echo $total_items; ?> عنصر إجمالي</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Unified Content Grid -->
                <div class="unified-content-grid">
                    <?php
                    // Combine all content into one array with type and order
                    $all_content = [];

                    // Add videos
                    foreach ($videos as $video) {
                        $all_content[] = [
                            'type' => 'video',
                            'data' => $video,
                            'order' => $video['video_order'] ?? 0
                        ];
                    }

                    // Add exercises
                    foreach ($exercises as $exercise) {
                        $all_content[] = [
                            'type' => 'exercise',
                            'data' => $exercise,
                            'order' => $exercise['exercise_order'] ?? 0
                        ];
                    }

                    // Add exams
                    foreach ($exams as $exam) {
                        $all_content[] = [
                            'type' => 'exam',
                            'data' => $exam,
                            'order' => $exam['exam_order'] ?? 0
                        ];
                    }

                    // Add summaries
                    foreach ($summaries as $summary) {
                        $all_content[] = [
                            'type' => 'summary',
                            'data' => $summary,
                            'order' => $summary['summary_order'] ?? 0
                        ];
                    }

                    // Sort by order
                    usort($all_content, function($a, $b) {
                        return $a['order'] - $b['order'];
                    });
                    ?>

                    <?php if (!empty($all_content)): ?>
                        <div class="unified-content-cards">
                            <?php foreach ($all_content as $content): ?>
                                <?php
                                $item = $content['data'];
                                $type = $content['type'];
                                $is_completed = $item['is_completed'] ?? false;
                                ?>

                                <div class="unified-content-card <?php echo $type; ?>-card <?php echo $is_completed ? 'completed' : ''; ?>">
                                    <div class="card-header">
                                        <div class="content-icon <?php echo $type; ?>-icon">
                                            <?php
                                            $icons = [
                                                'video' => 'fas fa-video',
                                                'exercise' => 'fas fa-dumbbell',
                                                'exam' => 'fas fa-clipboard-check',
                                                'summary' => 'fas fa-file-pdf'
                                            ];
                                            ?>
                                            <i class="<?php echo $icons[$type]; ?>"></i>
                                        </div>
                                        <div class="content-type-badge">
                                            <?php
                                            $type_names = [
                                                'video' => 'فيديو',
                                                'exercise' => 'تدريب',
                                                'exam' => 'امتحان',
                                                'summary' => 'ملخص'
                                            ];
                                            echo $type_names[$type];
                                            ?>
                                        </div>
                                        <div class="completion-indicator">
                                            <?php if ($is_completed): ?>
                                                <i class="fas fa-check-circle completed"></i>
                                            <?php else: ?>
                                                <i class="far fa-circle"></i>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="card-body">
                                        <h4><?php echo htmlspecialchars($item['title']); ?></h4>
                                        <?php if (!empty($item['description'])): ?>
                                            <p><?php echo htmlspecialchars(substr($item['description'], 0, 100)) . '...'; ?></p>
                                        <?php endif; ?>

                                        <?php if ($type === 'video' && !empty($item['youtube_id'])): ?>
                                            <div class="video-thumbnail">
                                                <div class="play-overlay">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            </div>
                                        <?php elseif ($type === 'exam'): ?>
                                            <div class="exam-details">
                                                <div class="detail-item">
                                                    <i class="fas fa-clock"></i>
                                                    <span><?php echo $item['duration_minutes']; ?> دقيقة</span>
                                                </div>
                                                <div class="detail-item">
                                                    <i class="fas fa-percentage"></i>
                                                    <span>60% للنجاح</span>
                                                </div>
                                            </div>
                                        <?php elseif ($type === 'summary'): ?>
                                            <div class="file-info">
                                                <div class="file-icon">
                                                    <i class="fas fa-file-pdf"></i>
                                                </div>
                                                <div class="file-details">
                                                    <span class="file-name"><?php echo htmlspecialchars($item['file_name']); ?></span>
                                                    <span class="file-size"><?php echo round($item['file_size'] / 1024, 2); ?> KB</span>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($is_completed && ($type === 'exercise' || $type === 'exam')): ?>
                                            <div class="score-display">
                                                <span class="score <?php echo ($type === 'exam' && isset($item['is_passed']) && $item['is_passed']) ? 'passed' : (($type === 'exam' && isset($item['is_passed']) && !$item['is_passed']) ? 'failed' : ''); ?>">
                                                    <?php echo round($item['score'], 1); ?>%
                                                </span>
                                                <span class="score-label">
                                                    <?php
                                                    if ($type === 'exam') {
                                                        echo isset($item['is_passed']) && $item['is_passed'] ? 'نجح' : 'راسب';
                                                    } else {
                                                        echo 'النتيجة';
                                                    }
                                                    ?>
                                                </span>
                                            </div>
                                        <?php elseif (!$is_completed && ($type === 'exercise' || $type === 'exam')): ?>
                                            <div class="not-attempted">
                                                <span class="not-attempted-label">لم يتم المحاولة بعد</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="card-footer">
                                        <?php
                                        $links = [
                                            'video' => "video_player.php?video_id={$item['id']}",
                                            'exercise' => "exercise.php?exercise_id={$item['id']}",
                                            'exam' => "exam.php?exam_id={$item['id']}",
                                            'summary' => "summary_viewer.php?summary_id={$item['id']}"
                                        ];

                                        $button_texts = [
                                            'video' => $is_completed ? 'مراجعة' : 'مشاهدة',
                                            'exercise' => $is_completed ? 'إعادة التدريب' : 'بدء التدريب',
                                            'exam' => $is_completed ? 'إعادة الامتحان' : 'بدء الامتحان',
                                            'summary' => $is_completed ? 'مراجعة' : 'عرض'
                                        ];
                                        ?>
                                        <a href="<?php echo $links[$type]; ?>" class="btn btn-primary">
                                            <i class="<?php echo $icons[$type]; ?>"></i>
                                            <?php echo $button_texts[$type]; ?>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                </div>

                <?php if (empty($videos) && empty($exercises) && empty($exams) && empty($summaries)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">📚</div>
                        <h2>لا يوجد محتوى متاح</h2>
                        <p>لم يتم إضافة محتوى لهذا الدرس بعد</p>
                        <a href="subject_lessons.php?subject_id=<?php echo $lesson['subject_id']; ?>" class="btn btn-primary">العودة للدروس</a>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Motivational Modal -->
    <div id="motivationalModal" class="motivational-modal-overlay" style="display: none;">
        <div class="motivational-modal">
            <div class="motivational-header">
                <div class="motivational-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h3>استمر يا بطل! 🌟</h3>
            </div>
            <div class="motivational-body">
                <p>لقد مضى 5 دقائق من عدم النشاط. يمكنك:</p>
                <div class="motivational-options">
                    <button class="btn btn-primary" onclick="continueStudying()">
                        <i class="fas fa-play"></i>
                        العودة للدرس والمتابعة
                    </button>
                    <button class="btn btn-secondary" onclick="takeBreak()">
                        <i class="fas fa-coffee"></i>
                        أخذ استراحة 5 دقائق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Break Timer Modal -->
    <div id="breakTimerModal" class="break-timer-overlay" style="display: none;">
        <div class="break-timer-modal">
            <div class="break-header">
                <div class="break-icon">
                    <i class="fas fa-coffee"></i>
                </div>
                <h3>وقت الاستراحة ☕</h3>
            </div>
            <div class="break-body">
                <div class="timer-display">
                    <div class="timer-circle">
                        <span id="breakTimer">05:00</span>
                    </div>
                </div>
                <p>استرح قليلاً واشرب كوب ماء 💧</p>
                <div class="break-actions">
                    <button class="btn btn-primary" onclick="endBreak()">
                        <i class="fas fa-play"></i>
                        إنهاء الاستراحة والعودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Completion Modal -->
    <div id="completionModal" class="completion-modal-overlay" style="display: none;">
        <div class="completion-modal">
            <div class="completion-header">
                <div class="completion-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3>أحسنت! 🎉</h3>
            </div>
            <div class="completion-body">
                <p id="completionMessage">لقد انتهيت من هذا المحتوى بنجاح!</p>
                <div class="completion-stats">
                    <div class="stat-item">
                        <i class="fas fa-check-circle"></i>
                        <span>مكتمل</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span>ممتاز</span>
                    </div>
                </div>
                <div class="completion-options">
                    <button class="btn btn-primary" onclick="reviewContent()">
                        <i class="fas fa-eye"></i>
                        مراجعة المحتوى
                    </button>
                    <button class="btn btn-success" onclick="continueToNext()">
                        <i class="fas fa-arrow-left"></i>
                        المتابعة للتالي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg,
                rgba(135, 206, 235, 0.02) 0%,
                rgba(70, 130, 180, 0.05) 50%,
                rgba(32, 178, 170, 0.02) 100%);
            min-height: 100vh;
        }

        .lesson-header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(135, 206, 235, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lesson-info {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .lesson-number-large {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            box-shadow: 0 8px 20px rgba(70, 130, 180, 0.3);
        }

        .lesson-details h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 2.2rem;
            font-weight: 700;
        }

        .lesson-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .subject-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .lesson-type .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .lesson-description {
            color: #6c757d;
            font-size: 14px;
            margin: 0;
            line-height: 1.5;
        }

        .lesson-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .completion-status {
            text-align: center;
        }

        .completion-circle-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            position: relative;
            margin-bottom: 8px;
        }

        .completion-circle-large::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            background: white;
            border-radius: 50%;
            z-index: 1;
        }

        .completion-circle-large span {
            position: relative;
            z-index: 2;
        }

        .completion-text {
            font-size: 12px;
            color: #6c757d;
            font-weight: 600;
        }

        .content-summary {
            margin-bottom: 40px;
        }

        .summary-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 25px;
            transition: all 0.4s ease;
            border: 1px solid rgba(135, 206, 235, 0.2);
        }

        .summary-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15);
        }

        .summary-icon {
            font-size: 2.5rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            box-shadow: 0 8px 20px rgba(70, 130, 180, 0.3);
        }

        .summary-info h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .content-breakdown {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .content-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(135, 206, 235, 0.1);
            padding: 8px 15px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            color: #4682B4;
        }

        .content-item i {
            font-size: 16px;
        }

        .total-items {
            color: #2c3e50;
            font-size: 16px;
        }

        .unified-content-grid {
            margin-top: 30px;
        }

        .unified-content-cards {
            display: flex;
            flex-direction: column;
            gap: 30px;
            max-width: 100%;
        }

        .unified-content-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            overflow: hidden;
            border: 1px solid rgba(135, 206, 235, 0.2);
            transition: all 0.4s ease;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            width: 100%;
            min-height: 200px;
        }

        .unified-content-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .unified-content-card.completed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 20px 20px 0 0;
        }

        .unified-content-card .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid rgba(135, 206, 235, 0.1);
            min-height: 80px;
        }

        .unified-content-card .content-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .video-icon {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .exercise-icon {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .exam-icon {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .summary-icon {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .content-type-badge {
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .video-card .content-type-badge {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
        }

        .exercise-card .content-type-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .exam-card .content-type-badge {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .summary-card .content-type-badge {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        /* Enhanced card borders for different content types */
        .video-card {
            border-left: 4px solid #4682B4;
        }

        .exercise-card {
            border-left: 4px solid #28a745;
        }

        .exam-card {
            border-left: 4px solid #dc3545;
        }

        .summary-card {
            border-left: 4px solid #ffc107;
        }

        /* Hover effects for different content types */
        .video-card:hover {
            box-shadow: 0 15px 35px rgba(70, 130, 180, 0.2);
        }

        .exercise-card:hover {
            box-shadow: 0 15px 35px rgba(40, 167, 69, 0.2);
        }

        .exam-card:hover {
            box-shadow: 0 15px 35px rgba(220, 53, 69, 0.2);
        }

        .summary-card:hover {
            box-shadow: 0 15px 35px rgba(255, 193, 7, 0.2);
        }

        .completion-indicator {
            font-size: 20px;
        }

        .completion-indicator .completed {
            color: #28a745;
        }

        .completion-indicator .far {
            color: #dee2e6;
        }

        .unified-content-card .card-body {
            padding: 30px;
            min-height: 120px;
        }

        .unified-content-card .card-body h4 {
            color: #2c3e50;
            margin: 0 0 15px 0;
            font-size: 22px;
            font-weight: 700;
            line-height: 1.3;
        }

        .unified-content-card .card-body p {
            color: #6c757d;
            font-size: 16px;
            line-height: 1.6;
            margin: 0 0 20px 0;
        }

        .video-thumbnail {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .video-thumbnail img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.7);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .exam-details {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #6c757d;
        }

        .detail-item i {
            color: #4682B4;
        }

        .score-display {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: rgba(135, 206, 235, 0.1);
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .not-attempted {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            background: rgba(108, 117, 125, 0.1);
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .not-attempted-label {
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            font-style: italic;
        }

        .score {
            font-size: 18px;
            font-weight: bold;
        }

        .score.passed {
            color: #28a745;
        }

        .score.failed {
            color: #dc3545;
        }

        .score-label {
            font-size: 12px;
            color: #6c757d;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 193, 7, 0.1);
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .file-icon {
            font-size: 24px;
            color: #ffc107;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .file-size {
            font-size: 12px;
            color: #6c757d;
        }

        .unified-content-card .card-footer {
            padding: 25px 30px;
            border-top: 2px solid rgba(135, 206, 235, 0.1);
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            min-height: 70px;
            display: flex;
            align-items: center;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
            min-height: 50px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(70, 130, 180, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .empty-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .empty-state h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 16px;
        }

        @media (max-width: 768px) {
            .lesson-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
                padding: 25px;
            }

            .lesson-info {
                flex-direction: column;
                text-align: center;
            }

            .lesson-actions {
                flex-direction: column;
                gap: 15px;
            }

            .summary-card {
                flex-direction: column;
                text-align: center;
                gap: 20px;
                padding: 25px;
            }

            .content-breakdown {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .unified-content-cards {
                gap: 25px;
            }

            .unified-content-card .card-header {
                padding: 20px;
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .unified-content-card .card-body {
                padding: 20px;
                text-align: center;
            }

            .unified-content-card .card-footer {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .summary-card {
                padding: 20px;
            }

            .summary-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .content-breakdown {
                flex-direction: column;
                align-items: center;
            }

            .content-item {
                width: 100%;
                justify-content: center;
            }
        }

        /* Motivational Modal Styles */
        .motivational-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .motivational-modal {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            text-align: center;
            animation: modalSlideIn 0.5s ease-out;
        }

        .motivational-header {
            margin-bottom: 30px;
        }

        .motivational-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
        }

        .motivational-header h3 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin: 0;
            font-weight: 700;
        }

        .motivational-body p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .motivational-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* Break Timer Modal Styles */
        .break-timer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .break-timer-modal {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 40px;
            max-width: 450px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            text-align: center;
            animation: modalSlideIn 0.5s ease-out;
        }

        .break-header {
            margin-bottom: 30px;
        }

        .break-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 10px 30px rgba(139, 69, 19, 0.4);
        }

        .break-header h3 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin: 0;
            font-weight: 700;
        }

        .timer-display {
            margin: 30px 0;
        }

        .timer-circle {
            width: 150px;
            height: 150px;
            border: 8px solid #e9ecef;
            border-top: 8px solid #8B4513;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            animation: timerRotate 60s linear infinite;
        }

        .timer-circle span {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        /* Completion Modal Styles */
        .completion-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .completion-modal {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            text-align: center;
            animation: modalSlideIn 0.5s ease-out;
        }

        .completion-header {
            margin-bottom: 30px;
        }

        .completion-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
        }

        .completion-header h3 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin: 0;
            font-weight: 700;
        }

        .completion-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 25px 0;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .stat-item i {
            font-size: 2rem;
            color: #28a745;
        }

        .stat-item span {
            font-weight: 600;
            color: #2c3e50;
        }

        .completion-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 25px;
        }

        /* Animations */
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes timerRotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* Modal responsive */
        @media (max-width: 480px) {
            .motivational-modal,
            .break-timer-modal,
            .completion-modal {
                padding: 30px 20px;
                margin: 20px;
            }

            .motivational-options,
            .completion-options {
                gap: 12px;
            }

            .completion-stats {
                gap: 20px;
            }

            .timer-circle {
                width: 120px;
                height: 120px;
            }

            .timer-circle span {
                font-size: 1.5rem;
            }
        }
    </style>

    <script>
        let inactivityTimer;
        let breakTimer;
        let breakInterval;
        let currentContentType = '';
        let currentContentId = 0;

        // Initialize inactivity tracking
        function initInactivityTracking() {
            resetInactivityTimer();

            // Track user activity
            document.addEventListener('mousemove', resetInactivityTimer);
            document.addEventListener('keypress', resetInactivityTimer);
            document.addEventListener('click', resetInactivityTimer);
            document.addEventListener('scroll', resetInactivityTimer);
        }

        function resetInactivityTimer() {
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(showMotivationalModal, 5 * 60 * 1000); // 5 minutes
        }

        function showMotivationalModal() {
            document.getElementById('motivationalModal').style.display = 'flex';
        }

        function continueStudying() {
            document.getElementById('motivationalModal').style.display = 'none';
            resetInactivityTimer();
        }

        function takeBreak() {
            document.getElementById('motivationalModal').style.display = 'none';
            document.getElementById('breakTimerModal').style.display = 'flex';
            startBreakTimer();
        }

        function startBreakTimer() {
            let timeLeft = 5 * 60; // 5 minutes in seconds
            const timerElement = document.getElementById('breakTimer');

            breakInterval = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    clearInterval(breakInterval);
                    endBreak();
                }
                timeLeft--;
            }, 1000);
        }

        function endBreak() {
            clearInterval(breakInterval);
            document.getElementById('breakTimerModal').style.display = 'none';
            resetInactivityTimer();
        }

        function showCompletionModal(contentType, contentId, isCompleted) {
            if (isCompleted) {
                currentContentType = contentType;
                currentContentId = contentId;

                const messages = {
                    'video': 'لقد انتهيت من مشاهدة هذا الفيديو بنجاح! 🎬',
                    'exercise': 'أحسنت! لقد أكملت هذا التدريب بنجاح! 💪',
                    'exam': 'ممتاز! لقد انتهيت من الامتحان! 📝',
                    'summary': 'رائع! لقد قرأت الملخص بالكامل! 📚'
                };

                document.getElementById('completionMessage').textContent = messages[contentType] || 'أحسنت! لقد أكملت هذا المحتوى!';
                document.getElementById('completionModal').style.display = 'flex';
            }
        }

        function reviewContent() {
            document.getElementById('completionModal').style.display = 'none';
            // Stay on current page for review
        }

        function continueToNext() {
            document.getElementById('completionModal').style.display = 'none';
            // Go back to lesson content
            window.location.href = window.location.href;
        }

        // Check if content is completed when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initInactivityTracking();

            // Check URL parameters for completion status
            const urlParams = new URLSearchParams(window.location.search);
            const completed = urlParams.get('completed');
            const contentType = urlParams.get('type');
            const contentId = urlParams.get('id');

            if (completed === 'true' && contentType && contentId) {
                setTimeout(() => {
                    showCompletionModal(contentType, contentId, true);
                }, 1000);
            }
        });

        // Add click handlers to content links to check completion
        document.querySelectorAll('.unified-content-card .btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const card = this.closest('.unified-content-card');
                const isCompleted = card.classList.contains('completed');

                if (isCompleted) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    const contentType = card.classList.contains('video-card') ? 'video' :
                                      card.classList.contains('exercise-card') ? 'exercise' :
                                      card.classList.contains('exam-card') ? 'exam' : 'summary';

                    showCompletionModal(contentType, 0, true);

                    // Store the original link for review
                    document.querySelector('#completionModal .btn-primary').onclick = function() {
                        document.getElementById('completionModal').style.display = 'none';
                        window.location.href = href;
                    };
                }
            });
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
