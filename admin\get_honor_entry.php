<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/HonorBoardManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// Check if honor entry ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الطالب مطلوب']);
    exit;
}

$honorBoardManager = new HonorBoardManager();
$entry = $honorBoardManager->getHonorEntryById($_GET['id']);

if ($entry) {
    echo json_encode(['success' => true, 'entry' => $entry]);
} else {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'الطالب غير موجود']);
}
?>
