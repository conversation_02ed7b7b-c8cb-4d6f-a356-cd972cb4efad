<?php
session_start();
require_once '../includes/database.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance()->getConnection();
    
    $tables = [
        'user_exam_attempts',
        'user_weekly_test_attempts'
    ];
    
    $tableStructures = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            $tableStructures[$table] = $columns;
        } catch (Exception $e) {
            $tableStructures[$table] = 'Error: ' . $e->getMessage();
        }
    }
    
    echo json_encode([
        'success' => true,
        'tables' => $tableStructures
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
