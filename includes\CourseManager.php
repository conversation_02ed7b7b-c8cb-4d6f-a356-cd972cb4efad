<?php
require_once 'database.php';

class CourseManager {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    // Create a new course
    public function createCourse($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO courses (
                    title, subject, description, features, price, discount_percentage,
                    main_image, modal_images, education_level, education_type, 
                    grade, specialization, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $data['title'],
                $data['subject'],
                $data['description'],
                $data['features'],
                $data['price'],
                $data['discount_percentage'] ?? 0,
                $data['main_image'] ?? null,
                $data['modal_images'] ?? null,
                $data['education_level'] ?? 'all',
                $data['education_type'] ?? 'all',
                $data['grade'] ?? 'all',
                $data['specialization'] ?? 'all',
                $data['created_by']
            ]);

            if ($result) {
                return $this->db->lastInsertId();
            }
            return false;
        } catch (Exception $e) {
            error_log("Error creating course: " . $e->getMessage());
            return false;
        }
    }

    // Get all courses
    public function getAllCourses($filters = []) {
        try {
            $sql = "SELECT * FROM courses WHERE is_active = 1";
            $params = [];

            if (!empty($filters['education_level']) && $filters['education_level'] !== 'all') {
                $sql .= " AND (education_level = ? OR education_level = 'all')";
                $params[] = $filters['education_level'];
            }

            if (!empty($filters['education_type']) && $filters['education_type'] !== 'all') {
                $sql .= " AND (education_type = ? OR education_type = 'all')";
                $params[] = $filters['education_type'];
            }

            if (!empty($filters['grade']) && $filters['grade'] !== 'all') {
                $sql .= " AND (grade = ? OR grade = 'all')";
                $params[] = $filters['grade'];
            }

            $sql .= " ORDER BY created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting courses: " . $e->getMessage());
            return [];
        }
    }

    // Get course by ID
    public function getCourseById($id) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM courses WHERE id = ? AND is_active = 1");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting course: " . $e->getMessage());
            return false;
        }
    }

    // Update course
    public function updateCourse($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE courses SET 
                    title = ?, subject = ?, description = ?, features = ?, 
                    price = ?, discount_percentage = ?, main_image = ?, modal_images = ?,
                    education_level = ?, education_type = ?, grade = ?, specialization = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $data['title'],
                $data['subject'],
                $data['description'],
                $data['features'],
                $data['price'],
                $data['discount_percentage'] ?? 0,
                $data['main_image'] ?? null,
                $data['modal_images'] ?? null,
                $data['education_level'] ?? 'all',
                $data['education_type'] ?? 'all',
                $data['grade'] ?? 'all',
                $data['specialization'] ?? 'all',
                $id
            ]);
        } catch (Exception $e) {
            error_log("Error updating course: " . $e->getMessage());
            return false;
        }
    }

    // Delete course (soft delete)
    public function deleteCourse($id) {
        try {
            $stmt = $this->db->prepare("UPDATE courses SET is_active = 0 WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (Exception $e) {
            error_log("Error deleting course: " . $e->getMessage());
            return false;
        }
    }

    // Check if user has access to course
    public function userHasAccess($userId, $courseId) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM course_subscriptions 
                WHERE user_id = ? AND course_id = ? AND activation_status = 'active'
            ");
            $stmt->execute([$userId, $courseId]);
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            error_log("Error checking course access: " . $e->getMessage());
            return false;
        }
    }

    // Get course subscription status for user
    public function getUserCourseStatus($userId, $courseId) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM course_subscriptions 
                WHERE user_id = ? AND course_id = ?
            ");
            $stmt->execute([$userId, $courseId]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting course status: " . $e->getMessage());
            return false;
        }
    }

    // Create course subscription
    public function createSubscription($userId, $courseId, $activationMethod) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO course_subscriptions (user_id, course_id, activation_method)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    activation_method = VALUES(activation_method),
                    updated_at = CURRENT_TIMESTAMP
            ");
            return $stmt->execute([$userId, $courseId, $activationMethod]);
        } catch (Exception $e) {
            error_log("Error creating subscription: " . $e->getMessage());
            return false;
        }
    }

    // Clean up old pending subscriptions (older than 24 hours)
    public function cleanupPendingSubscriptions() {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM course_subscriptions
                WHERE activation_status = 'pending'
                AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error cleaning up pending subscriptions: " . $e->getMessage());
            return false;
        }
    }

    // Remove pending subscription for a specific user and course
    public function removePendingSubscription($userId, $courseId) {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM course_subscriptions
                WHERE user_id = ? AND course_id = ? AND activation_status = 'pending'
            ");
            return $stmt->execute([$userId, $courseId]);
        } catch (Exception $e) {
            error_log("Error removing pending subscription: " . $e->getMessage());
            return false;
        }
    }

    // Activate course subscription
    public function activateSubscription($userId, $courseId, $method = 'code', $additionalData = [], $handleTransaction = true) {
        try {
            if ($handleTransaction) {
                $this->db->beginTransaction();
            }

            $updateData = [
                'activation_status' => 'active',
                'activated_at' => date('Y-m-d H:i:s')
            ];

            if ($method === 'code' && !empty($additionalData['activation_code'])) {
                $updateData['activation_code'] = $additionalData['activation_code'];
            }

            if ($method === 'payment' && !empty($additionalData['payment_request_id'])) {
                $updateData['payment_request_id'] = $additionalData['payment_request_id'];
            }

            $setClause = implode(', ', array_map(function($key) {
                return "$key = ?";
            }, array_keys($updateData)));

            $stmt = $this->db->prepare("
                UPDATE course_subscriptions
                SET $setClause, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ? AND course_id = ?
            ");

            $params = array_values($updateData);
            $params[] = $userId;
            $params[] = $courseId;

            $result = $stmt->execute($params);

            if ($result) {
                if ($handleTransaction) {
                    $this->db->commit();
                }
                return true;
            } else {
                if ($handleTransaction) {
                    $this->db->rollBack();
                }
                return false;
            }
        } catch (Exception $e) {
            if ($handleTransaction && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log("Error activating subscription: " . $e->getMessage());
            return false;
        }
    }

    // Get course statistics
    public function getCourseStatistics($courseId = null) {
        try {
            if ($courseId) {
                $stmt = $this->db->prepare("SELECT * FROM course_statistics WHERE course_id = ?");
                $stmt->execute([$courseId]);
                return $stmt->fetch();
            } else {
                $stmt = $this->db->prepare("SELECT * FROM course_statistics ORDER BY total_subscribers DESC");
                $stmt->execute();
                return $stmt->fetchAll();
            }
        } catch (Exception $e) {
            error_log("Error getting course statistics: " . $e->getMessage());
            return $courseId ? false : [];
        }
    }

    // Upload course image
    public function uploadCourseImage($file, $courseId = null) {
        try {
            $uploadDir = '../uploads/courses/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
            }

            $maxSize = 5 * 1024 * 1024; // 5MB
            if ($file['size'] > $maxSize) {
                return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
            }

            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = ($courseId ? "course_{$courseId}_" : "course_") . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;

            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                return ['success' => true, 'filename' => $filename, 'path' => $filepath];
            } else {
                return ['success' => false, 'message' => 'فشل في رفع الملف'];
            }
        } catch (Exception $e) {
            error_log("Error uploading course image: " . $e->getMessage());
            return ['success' => false, 'message' => 'خطأ في رفع الملف'];
        }
    }
}
?>
