/**
 * Admin Dashboard Charts
 * Interactive charts and data visualization
 */

class AdminCharts {
    constructor() {
        this.charts = {};
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#48bb78',
            warning: '#ed8936',
            danger: '#f56565',
            info: '#4299e1'
        };
        this.init();
    }

    init() {
        this.createUserStatsChart();
        this.createCourseProgressChart();
        this.createRevenueChart();
        this.createActivityChart();
    }

    createUserStatsChart() {
        const canvas = document.createElement('canvas');
        canvas.id = 'userStatsChart';
        canvas.width = 400;
        canvas.height = 200;

        const container = document.createElement('div');
        container.className = 'chart-container';
        container.style.cssText = `
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-medium);
        `;

        const title = document.createElement('h3');
        title.textContent = 'إحصائيات المستخدمين';
        title.style.cssText = `
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
            text-align: center;
        `;

        container.appendChild(title);
        container.appendChild(canvas);

        // Insert after stats grid
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            statsGrid.parentNode.insertBefore(container, statsGrid.nextSibling);
        }

        this.drawUserStatsChart(canvas);
    }

    drawUserStatsChart(canvas) {
        const ctx = canvas.getContext('2d');
        const data = [
            { label: 'مستخدمين جدد', value: 45, color: this.colors.primary },
            { label: 'مستخدمين نشطين', value: 120, color: this.colors.success },
            { label: 'مستخدمين غير نشطين', value: 30, color: this.colors.warning },
            { label: 'مستخدمين محظورين', value: 5, color: this.colors.danger }
        ];

        this.drawDoughnutChart(ctx, data, canvas.width, canvas.height);
    }

    createCourseProgressChart() {
        const canvas = document.createElement('canvas');
        canvas.id = 'courseProgressChart';
        canvas.width = 600;
        canvas.height = 300;

        const container = document.createElement('div');
        container.className = 'chart-container';
        container.style.cssText = `
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-medium);
        `;

        const title = document.createElement('h3');
        title.textContent = 'تقدم الكورسات الشهري';
        title.style.cssText = `
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
            text-align: center;
        `;

        container.appendChild(title);
        container.appendChild(canvas);

        // Insert in grid layout
        const gridContainer = document.querySelector('.grid-2');
        if (gridContainer) {
            const chartWrapper = document.createElement('div');
            chartWrapper.appendChild(container);
            gridContainer.appendChild(chartWrapper);
        }

        this.drawCourseProgressChart(canvas);
    }

    drawCourseProgressChart(canvas) {
        const ctx = canvas.getContext('2d');
        const data = [
            { month: 'يناير', completed: 25, enrolled: 40 },
            { month: 'فبراير', completed: 35, enrolled: 55 },
            { month: 'مارس', completed: 45, enrolled: 70 },
            { month: 'أبريل', completed: 55, enrolled: 85 },
            { month: 'مايو', completed: 65, enrolled: 95 },
            { month: 'يونيو', completed: 75, enrolled: 110 }
        ];

        this.drawBarChart(ctx, data, canvas.width, canvas.height);
    }

    createRevenueChart() {
        const canvas = document.createElement('canvas');
        canvas.id = 'revenueChart';
        canvas.width = 600;
        canvas.height = 300;

        const container = document.createElement('div');
        container.className = 'chart-container';
        container.style.cssText = `
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-medium);
        `;

        const title = document.createElement('h3');
        title.textContent = 'الإيرادات الشهرية';
        title.style.cssText = `
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
            text-align: center;
        `;

        container.appendChild(title);
        container.appendChild(canvas);

        // Insert in grid layout
        const gridContainer = document.querySelector('.grid-2');
        if (gridContainer) {
            const chartWrapper = document.createElement('div');
            chartWrapper.appendChild(container);
            gridContainer.appendChild(chartWrapper);
        }

        this.drawRevenueChart(canvas);
    }

    drawRevenueChart(canvas) {
        const ctx = canvas.getContext('2d');
        const data = [
            { month: 'يناير', revenue: 15000 },
            { month: 'فبراير', revenue: 18000 },
            { month: 'مارس', revenue: 22000 },
            { month: 'أبريل', revenue: 25000 },
            { month: 'مايو', revenue: 28000 },
            { month: 'يونيو', revenue: 32000 }
        ];

        this.drawLineChart(ctx, data, canvas.width, canvas.height);
    }

    createActivityChart() {
        const canvas = document.createElement('canvas');
        canvas.id = 'activityChart';
        canvas.width = 800;
        canvas.height = 200;

        const container = document.createElement('div');
        container.className = 'chart-container';
        container.style.cssText = `
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-medium);
            grid-column: 1 / -1;
        `;

        const title = document.createElement('h3');
        title.textContent = 'نشاط المستخدمين الأسبوعي';
        title.style.cssText = `
            margin-bottom: var(--spacing-lg);
            color: var(--text-primary);
            text-align: center;
        `;

        container.appendChild(title);
        container.appendChild(canvas);

        // Insert at the end
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.appendChild(container);
        }

        this.drawActivityChart(canvas);
    }

    drawActivityChart(canvas) {
        const ctx = canvas.getContext('2d');
        const data = [
            { day: 'السبت', activity: 85 },
            { day: 'الأحد', activity: 92 },
            { day: 'الاثنين', activity: 78 },
            { day: 'الثلاثاء', activity: 88 },
            { day: 'الأربعاء', activity: 95 },
            { day: 'الخميس', activity: 82 },
            { day: 'الجمعة', activity: 70 }
        ];

        this.drawAreaChart(ctx, data, canvas.width, canvas.height);
    }

    drawDoughnutChart(ctx, data, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 3;
        const innerRadius = radius * 0.6;

        let total = data.reduce((sum, item) => sum + item.value, 0);
        let currentAngle = -Math.PI / 2;

        // Draw segments
        data.forEach(item => {
            const sliceAngle = (item.value / total) * 2 * Math.PI;
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true);
            ctx.closePath();
            ctx.fillStyle = item.color;
            ctx.fill();

            currentAngle += sliceAngle;
        });

        // Draw legend
        this.drawLegend(ctx, data, width, height);
    }

    drawBarChart(ctx, data, width, height) {
        const padding = 60;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;
        const barWidth = chartWidth / (data.length * 2);

        const maxValue = Math.max(...data.map(d => Math.max(d.completed, d.enrolled)));

        // Draw bars
        data.forEach((item, index) => {
            const x = padding + index * barWidth * 2;
            const completedHeight = (item.completed / maxValue) * chartHeight;
            const enrolledHeight = (item.enrolled / maxValue) * chartHeight;

            // Completed bars
            ctx.fillStyle = this.colors.success;
            ctx.fillRect(x, height - padding - completedHeight, barWidth * 0.8, completedHeight);

            // Enrolled bars
            ctx.fillStyle = this.colors.primary;
            ctx.fillRect(x + barWidth * 0.8, height - padding - enrolledHeight, barWidth * 0.8, enrolledHeight);

            // Labels
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(item.month, x + barWidth, height - padding + 20);
        });
    }

    drawLineChart(ctx, data, width, height) {
        const padding = 60;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;

        const maxValue = Math.max(...data.map(d => d.revenue));
        const minValue = Math.min(...data.map(d => d.revenue));

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, this.colors.primary);
        gradient.addColorStop(1, this.colors.secondary);

        ctx.strokeStyle = gradient;
        ctx.lineWidth = 3;
        ctx.beginPath();

        data.forEach((item, index) => {
            const x = padding + (index / (data.length - 1)) * chartWidth;
            const y = height - padding - ((item.revenue - minValue) / (maxValue - minValue)) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }

            // Draw points
            ctx.save();
            ctx.fillStyle = this.colors.primary;
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
            ctx.restore();
        });

        ctx.stroke();
    }

    drawAreaChart(ctx, data, width, height) {
        const padding = 60;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;

        const maxValue = Math.max(...data.map(d => d.activity));

        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, this.colors.info + '80');
        gradient.addColorStop(1, this.colors.info + '20');

        ctx.fillStyle = gradient;
        ctx.beginPath();

        // Start from bottom left
        ctx.moveTo(padding, height - padding);

        data.forEach((item, index) => {
            const x = padding + (index / (data.length - 1)) * chartWidth;
            const y = height - padding - (item.activity / maxValue) * chartHeight;
            ctx.lineTo(x, y);
        });

        // Close path to bottom right
        ctx.lineTo(padding + chartWidth, height - padding);
        ctx.closePath();
        ctx.fill();

        // Draw line on top
        ctx.strokeStyle = this.colors.info;
        ctx.lineWidth = 2;
        ctx.beginPath();

        data.forEach((item, index) => {
            const x = padding + (index / (data.length - 1)) * chartWidth;
            const y = height - padding - (item.activity / maxValue) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();
    }

    drawLegend(ctx, data, width, height) {
        const legendX = width - 150;
        const legendY = 20;

        data.forEach((item, index) => {
            const y = legendY + index * 25;

            // Color box
            ctx.fillStyle = item.color;
            ctx.fillRect(legendX, y, 15, 15);

            // Label
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(item.label, legendX + 20, y + 12);
        });
    }
}

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for the main dashboard to load
    setTimeout(() => {
        window.adminCharts = new AdminCharts();
    }, 500);
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminCharts;
}
