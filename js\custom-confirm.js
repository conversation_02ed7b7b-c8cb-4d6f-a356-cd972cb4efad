/**
 * Custom Confirmation System
 * A modern replacement for the browser's default confirm() dialog
 */

class CustomConfirm {
    constructor() {
        this.currentModal = null;
        this.addStyles();
    }

    addStyles() {
        // Check if styles already added
        if (document.getElementById('custom-confirm-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'custom-confirm-styles';
        style.textContent = `
            .custom-confirm-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .custom-confirm-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .custom-confirm-modal {
                background: white;
                border-radius: 20px;
                padding: 30px;
                max-width: 500px;
                width: 90%;
                text-align: center;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                transform: scale(0.7);
                transition: transform 0.3s ease;
                font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            .custom-confirm-overlay.show .custom-confirm-modal {
                transform: scale(1);
            }

            .confirm-icon {
                font-size: 60px;
                margin-bottom: 20px;
                color: #ffc107;
            }

            .confirm-title {
                font-size: 24px;
                font-weight: 700;
                color: #333;
                margin-bottom: 15px;
            }

            .confirm-message {
                font-size: 16px;
                color: #666;
                margin-bottom: 30px;
                line-height: 1.6;
            }

            .confirm-buttons {
                display: flex;
                gap: 15px;
                justify-content: center;
            }

            .confirm-btn {
                padding: 12px 30px;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 120px;
            }

            .confirm-btn-yes {
                background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                color: white;
            }

            .confirm-btn-yes:hover {
                background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            }

            .confirm-btn-no {
                background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
                color: white;
            }

            .confirm-btn-no:hover {
                background: linear-gradient(135deg, #5a6268 0%, #545b62 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
            }

            @media (max-width: 768px) {
                .confirm-buttons {
                    flex-direction: column;
                }

                .confirm-btn {
                    width: 100%;
                    min-width: auto;
                }
            }
        `;
        document.head.appendChild(style);
    }

    show(title, message, options = {}) {
        return new Promise((resolve) => {
            // Remove existing modal if any
            this.hide();

            const {
                confirmText = 'نعم',
                cancelText = 'لا',
                icon = '⚠️',
                confirmClass = 'confirm-btn-yes',
                cancelClass = 'confirm-btn-no'
            } = options;

            // Create modal HTML
            const modalHTML = `
                <div class="custom-confirm-overlay" id="customConfirmModal">
                    <div class="custom-confirm-modal">
                        <div class="confirm-icon">${icon}</div>
                        <h3 class="confirm-title">${this.escapeHtml(title)}</h3>
                        <p class="confirm-message">${this.escapeHtml(message)}</p>
                        <div class="confirm-buttons">
                            <button class="confirm-btn ${confirmClass}" id="confirmYes">${confirmText}</button>
                            <button class="confirm-btn ${cancelClass}" id="confirmNo">${cancelText}</button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            this.currentModal = document.getElementById('customConfirmModal');
            const yesBtn = document.getElementById('confirmYes');
            const noBtn = document.getElementById('confirmNo');

            // Show modal
            setTimeout(() => {
                this.currentModal.classList.add('show');
            }, 10);

            // Handle Yes button
            yesBtn.addEventListener('click', () => {
                this.hide();
                resolve(true);
            });

            // Handle No button
            noBtn.addEventListener('click', () => {
                this.hide();
                resolve(false);
            });

            // Handle overlay click
            this.currentModal.addEventListener('click', (e) => {
                if (e.target === this.currentModal) {
                    this.hide();
                    resolve(false);
                }
            });

            // Handle Escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    this.hide();
                    resolve(false);
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        });
    }

    hide() {
        if (this.currentModal) {
            this.currentModal.classList.remove('show');
            setTimeout(() => {
                if (this.currentModal && this.currentModal.parentNode) {
                    this.currentModal.remove();
                }
                this.currentModal = null;
            }, 300);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Create global instance
window.customConfirm = new CustomConfirm();

// Override the default confirm function
window.originalConfirm = window.confirm;
window.confirm = function(message) {
    return window.customConfirm.show('تأكيد', message);
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomConfirm;
}
