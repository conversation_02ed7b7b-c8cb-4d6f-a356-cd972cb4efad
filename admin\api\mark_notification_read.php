<?php
session_start();
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['notification_id'])) {
        echo json_encode(['success' => false, 'message' => 'معرف الإشعار مطلوب']);
        exit;
    }
    
    $notificationId = (int)$input['notification_id'];
    $db = Database::getInstance()->getConnection();
    
    $stmt = $db->prepare("UPDATE notifications SET is_read = 1 WHERE id = :id");
    $stmt->bindParam(':id', $notificationId, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'تم تحديث الإشعار']);
    } else {
        echo json_encode(['success' => false, 'message' => 'خطأ في تحديث الإشعار']);
    }
    
} catch (Exception $e) {
    error_log("Error in mark_notification_read.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في تحديث الإشعار'
    ]);
}
?>
