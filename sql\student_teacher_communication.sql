-- Student-Teacher Communication System Tables
-- This file contains the database structure for the student-teacher communication system

-- Message categories table - for organizing messages by subject/topic (create first)
CREATE TABLE IF NOT EXISTS message_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(50) DEFAULT '📝',
    color VARCHAR(7) DEFAULT '#4682B4',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Student messages table - stores messages sent by students to teachers
CREATE TABLE IF NOT EXISTS student_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('question', 'help', 'complaint', 'suggestion', 'other') DEFAULT 'question',
    category_id INT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending', 'read', 'replied', 'closed') DEFAULT 'pending',
    is_anonymous BOOLEAN DEFAULT FALSE,
    student_name VARCHAR(100) NULL, -- For anonymous messages
    student_email VARCHAR(100) NULL, -- For anonymous messages
    student_phone VARCHAR(15) NULL, -- For anonymous messages
    attachment_path VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES message_categories(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_message_type (message_type),
    INDEX idx_category_id (category_id),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_read_at (read_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin replies table - stores replies from admins/teachers to student messages
CREATE TABLE IF NOT EXISTS admin_replies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    admin_id INT NOT NULL,
    reply_text TEXT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE, -- If true, reply can be seen by other students
    attachment_path VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES student_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Message templates table - for quick admin responses
CREATE TABLE IF NOT EXISTS message_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (category_id) REFERENCES message_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Message notifications table - tracks notifications for new messages and replies
CREATE TABLE IF NOT EXISTS message_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    recipient_type ENUM('admin', 'student') NOT NULL,
    recipient_id INT NOT NULL, -- admin_id or user_id based on recipient_type
    notification_type ENUM('new_message', 'new_reply', 'status_change') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    
    FOREIGN KEY (message_id) REFERENCES student_messages(id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id),
    INDEX idx_recipient (recipient_type, recipient_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- Message templates table - for quick admin responses
CREATE TABLE IF NOT EXISTS message_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES message_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default message categories
INSERT INTO message_categories (name, name_ar, description, icon, color, sort_order) VALUES
('General Question', 'سؤال عام', 'General questions about courses or platform', '❓', '#4682B4', 1),
('Technical Support', 'الدعم الفني', 'Technical issues and platform problems', '🔧', '#e74c3c', 2),
('Course Content', 'محتوى الكورس', 'Questions about specific course content', '📚', '#27ae60', 3),
('Payment Issues', 'مشاكل الدفع', 'Payment and subscription related issues', '💳', '#f39c12', 4),
('Account Issues', 'مشاكل الحساب', 'Account access and profile issues', '👤', '#9b59b6', 5),
('Suggestions', 'اقتراحات', 'Suggestions for platform improvement', '💡', '#1abc9c', 6),
('Complaints', 'شكاوى', 'Complaints and feedback', '⚠️', '#e67e22', 7),
('Other', 'أخرى', 'Other types of messages', '📝', '#95a5a6', 8);

-- Insert default message templates
INSERT INTO message_templates (title, content, category_id, created_by) VALUES
('شكر على التواصل', 'شكراً لك على تواصلك معنا. تم استلام رسالتك وسيتم الرد عليك في أقرب وقت ممكن.', 1, 1),
('طلب معلومات إضافية', 'نشكرك على رسالتك. نحتاج إلى معلومات إضافية لمساعدتك بشكل أفضل. يرجى تقديم المزيد من التفاصيل حول مشكلتك.', 1, 1),
('حل مشكلة فنية', 'تم حل المشكلة الفنية التي واجهتك. يرجى المحاولة مرة أخرى والتواصل معنا إذا استمرت المشكلة.', 2, 1),
('توضيح محتوى الكورس', 'بخصوص استفسارك حول محتوى الكورس، يمكنك مراجعة الدروس المتاحة في قسم المحتوى أو مشاهدة الفيديوهات التوضيحية.', 3, 1),
('مساعدة في الدفع', 'بخصوص مشكلة الدفع، يرجى التواصل معنا عبر الواتساب مع إرفاق صورة من إيصال التحويل للمساعدة السريعة.', 4, 1);
