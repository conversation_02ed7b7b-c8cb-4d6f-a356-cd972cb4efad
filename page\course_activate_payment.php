<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/page/courses.php');
    exit;
}

$courseManager = new CourseManager();
$db = Database::getInstance()->getConnection();
$courseId = $_GET['id'];
$userId = $_SESSION['user_id'];

// Get user information
$stmt = $db->prepare("SELECT username, email, full_name FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login.php');
    exit;
}

// Get course details
$course = $courseManager->getCourseById($courseId);
if (!$course) {
    header('Location: ' . SITE_URL . '/page/courses.php?error=course_not_found');
    exit;
}

// Check if user already has access
$userCourseStatus = $courseManager->getUserCourseStatus($userId, $courseId);
if ($userCourseStatus && $userCourseStatus['activation_status'] === 'active') {
    header('Location: ' . SITE_URL . '/page/course_content.php?id=' . $courseId);
    exit;
}

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_FILES['receipt_image']) && isset($_POST['mobile_number']) && isset($_POST['transfer_method'])) {
        
        // Validate file upload
        $uploadDir = '../uploads/receipts/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $file = $_FILES['receipt_image'];
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        
        if (!in_array($file['type'], $allowedTypes)) {
            $message = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WebP';
            $messageType = 'error';
        } elseif ($file['size'] > 5 * 1024 * 1024) { // 5MB
            $message = 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت';
            $messageType = 'error';
        } else {
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'receipt_' . $userId . '_' . $courseId . '_' . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;
            
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                try {
                    // Create or update subscription
                    $courseManager->createSubscription($userId, $courseId, 'payment');

                    // Create payment request
                    $stmt = $db->prepare("
                        INSERT INTO payment_requests (user_id, course_id, receipt_image, mobile_number, transfer_method)
                        VALUES (?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                            receipt_image = VALUES(receipt_image),
                            mobile_number = VALUES(mobile_number),
                            transfer_method = VALUES(transfer_method),
                            status = 'pending',
                            created_at = NOW()
                    ");

                    $result = $stmt->execute([
                        $userId,
                        $courseId,
                        $filename,
                        $_POST['mobile_number'],
                        $_POST['transfer_method']
                    ]);

                    if ($result) {
                        // Redirect to success page
                        header('Location: ' . SITE_URL . '/page/course_activation_success.php?id=' . $courseId . '&method=payment');
                        exit;
                    } else {
                        // Delete uploaded file if database operation failed
                        if (file_exists($filepath)) {
                            unlink($filepath);
                        }
                        $message = 'فشل في حفظ طلب التفعيل. يرجى المحاولة مرة أخرى';
                        $messageType = 'error';
                    }

                } catch (Exception $e) {
                    // Delete uploaded file if database operation failed
                    if (file_exists($filepath)) {
                        unlink($filepath);
                    }
                    error_log("Error creating payment request: " . $e->getMessage());
                    $message = 'حدث خطأ أثناء إرسال طلب التفعيل. يرجى المحاولة مرة أخرى';
                    $messageType = 'error';
                }
            } else {
                $message = 'فشل في رفع الملف. يرجى المحاولة مرة أخرى';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'يرجى ملء جميع الحقول المطلوبة';
        $messageType = 'error';
    }
}

// Calculate final price
$finalPrice = $course['discount_percentage'] > 0 ? $course['discounted_price'] : $course['price'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب تفعيل بالدفع - <?php echo htmlspecialchars($course['title']); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/global-themes.css">
    <link rel="stylesheet" href="../css/dashboard-responsive.css">

    <?php
    // Get user theme preference
    $userTheme = 'light'; // default
    if (isset($_SESSION['user_id'])) {
        try {
            require_once __DIR__ . '/../includes/UserSettingsManager.php';
            $settingsManager = new UserSettingsManager();
            $userSettings = $settingsManager->getUserSettings($_SESSION['user_id']);
            $userTheme = $userSettings['theme_preference'] ?? 'light';
        } catch (Exception $e) {
            // Use default theme if error
        }
    }
    ?>
    
    <script>
        // Set user theme preference for JavaScript
        window.userThemePreference = '<?php echo $userTheme; ?>';
        window.SITE_URL = window.location.origin + window.location.pathname.split('/').slice(0, -2).join('/');
    </script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Include Header -->
        <?php include __DIR__ . '/../includes/header.php'; ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <main class="dashboard-main">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            
            <div class="page-container">
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <a href="../page/dashboard.php">الرئيسية</a>
                    <span>/</span>
                    <a href="../page/courses.php">الكورسات</a>
                    <span>/</span>
                    <a href="../page/course_details.php?id=<?php echo $courseId; ?>"><?php echo htmlspecialchars($course['title']); ?></a>
                    <span>/</span>
                    <span>طلب تفعيل بالدفع</span>
                </div>

                <div class="payment-container">
                    <!-- Course Info Card -->
                    <div class="course-info-card">
                        <div class="course-image">
                            <?php if ($course['main_image']): ?>
                                <img src="../uploads/courses/<?php echo htmlspecialchars($course['main_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($course['title']); ?>">
                            <?php else: ?>
                                <div class="course-placeholder">
                                    <span class="course-icon">📚</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="course-details">
                            <h3><?php echo htmlspecialchars($course['title']); ?></h3>
                            <p><?php echo htmlspecialchars($course['subject']); ?></p>
                            <div class="course-price">
                                <?php if ($course['discount_percentage'] > 0): ?>
                                    <span class="original-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                    <span class="discounted-price"><?php echo number_format($course['discounted_price'], 0); ?> جنيه</span>
                                <?php else: ?>
                                    <span class="current-price"><?php echo number_format($course['price'], 0); ?> جنيه</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <div class="payment-form-card">
                        <div class="form-header">
                            <div class="payment-icon">💳</div>
                            <h1>طلب تفعيل بالدفع</h1>
                            <p>ارفع إيصال الدفع لمراجعته وتفعيل الكورس</p>
                        </div>

                        <!-- Payment Instructions -->
                        <div class="payment-instructions">
                            <h3>📋 تعليمات الدفع</h3>
                            <div class="instruction-steps">
                                <div class="step">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h4>قم بتحويل المبلغ</h4>
                                        <p>حول مبلغ <strong><?php echo number_format($finalPrice, 0); ?> جنيه</strong> إلى الرقم: <strong>01126130559</strong></p>
                                    </div>
                                </div>
                                <div class="step">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h4>احتفظ بالإيصال</h4>
                                        <p>احتفظ بصورة من إيصال التحويل أو لقطة شاشة</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h4>ارفع الإيصال</h4>
                                        <p>ارفع صورة الإيصال مع بيانات التحويل في النموذج أدناه</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" enctype="multipart/form-data" class="payment-form">
                            <div class="form-group">
                                <label for="receipt_image">صورة الإيصال *</label>
                                <div class="file-upload-area">
                                    <input type="file" id="receipt_image" name="receipt_image" accept="image/*" required>
                                    <div class="file-upload-text">
                                        <span class="upload-icon">📷</span>
                                        <span>اضغط لاختيار صورة الإيصال</span>
                                        <small>JPG, PNG, GIF أو WebP - حد أقصى 5 ميجابايت</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="mobile_number">رقم الهاتف المستخدم في التحويل *</label>
                                <input type="tel" 
                                       id="mobile_number" 
                                       name="mobile_number" 
                                       placeholder="01xxxxxxxxx"
                                       value="<?php echo isset($_POST['mobile_number']) ? htmlspecialchars($_POST['mobile_number']) : ''; ?>"
                                       required>
                                <small class="form-help">الرقم الذي تم التحويل منه</small>
                            </div>

                            <div class="form-group">
                                <label for="transfer_method">طريقة التحويل *</label>
                                <select id="transfer_method" name="transfer_method" required>
                                    <option value="">اختر طريقة التحويل</option>
                                    <option value="vodafone_cash" <?php echo (isset($_POST['transfer_method']) && $_POST['transfer_method'] === 'vodafone_cash') ? 'selected' : ''; ?>>فودافون كاش</option>
                                    <option value="etisalat_cash" <?php echo (isset($_POST['transfer_method']) && $_POST['transfer_method'] === 'etisalat_cash') ? 'selected' : ''; ?>>اتصالات كاش</option>
                                    <option value="we_cash" <?php echo (isset($_POST['transfer_method']) && $_POST['transfer_method'] === 'we_cash') ? 'selected' : ''; ?>>وي كاش</option>
                                    <option value="orange_cash" <?php echo (isset($_POST['transfer_method']) && $_POST['transfer_method'] === 'orange_cash') ? 'selected' : ''; ?>>أورانج كاش</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="username">اسم المستخدم</label>
                                <input type="text"
                                       id="username"
                                       name="username"
                                       value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>"
                                       readonly>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large">
                                    <span class="btn-icon">📤</span>
                                    إرسال طلب التفعيل
                                </button>
                                <a href="../page/course_register.php?id=<?php echo $courseId; ?>" class="btn btn-secondary btn-large">
                                    <span class="btn-icon">↩️</span>
                                    العودة لخيارات التفعيل
                                </a>
                            </div>
                        </form>

                        <!-- Contact Info -->
                        <div class="contact-section">
                            <h3>💬 تحتاج مساعدة؟</h3>
                            <p>تواصل معنا عبر الواتساب: <a href="https://wa.me/201128031228" target="_blank" class="whatsapp-link">01128031228</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>
    </div>

    <style>
        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 30px;
        }

        .course-info-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .course-image {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-icon {
            font-size: 40px;
            color: white;
        }

        .course-details h3 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .course-details p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .course-price {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .original-price {
            color: #6c757d;
            text-decoration: line-through;
            font-size: 14px;
        }

        .discounted-price {
            color: #dc3545;
            font-size: 18px;
            font-weight: 700;
        }

        .current-price {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 700;
        }

        .payment-form-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .payment-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #6c757d;
            font-size: 18px;
        }

        .payment-instructions {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .payment-instructions h3 {
            color: #1976d2;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .instruction-steps {
            display: grid;
            gap: 20px;
        }

        .step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .step-number {
            background: #2196f3;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 18px;
            flex-shrink: 0;
        }

        .step-content h4 {
            color: #1976d2;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .step-content p {
            color: #1976d2;
            line-height: 1.5;
            margin: 0;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4682B4;
            box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
        }

        .form-group input[readonly] {
            background: #f8f9fa;
            color: #6c757d;
        }

        .file-upload-area {
            position: relative;
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #4682B4;
            background: rgba(70, 130, 180, 0.05);
        }

        .file-upload-area input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            padding: 0;
            border: none;
        }

        .file-upload-text {
            pointer-events: none;
        }

        .upload-icon {
            font-size: 48px;
            display: block;
            margin-bottom: 15px;
        }

        .file-upload-text span {
            display: block;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .file-upload-text small {
            color: #6c757d;
            font-size: 14px;
        }

        .form-help {
            display: block;
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }

        .form-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .btn-large {
            padding: 18px 35px;
            font-size: 18px;
            font-weight: 700;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border: none;
            cursor: pointer;
        }

        .btn-large:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .contact-section {
            text-align: center;
            padding-top: 30px;
            border-top: 2px solid #f8f9fa;
        }

        .contact-section h3 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .whatsapp-link {
            color: #25d366;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
        }

        .whatsapp-link:hover {
            text-decoration: underline;
        }

        .breadcrumb {
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb a {
            color: #4682B4;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #6c757d;
            margin: 0 10px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .payment-container {
                padding: 15px;
            }

            .course-info-card {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .payment-form-card {
                padding: 25px;
            }

            .form-header h1 {
                font-size: 24px;
            }

            .payment-icon {
                font-size: 60px;
            }

            .instruction-steps {
                gap: 15px;
            }

            .step {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-large {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        // File upload preview
        document.getElementById('receipt_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const uploadText = document.querySelector('.file-upload-text span');
            
            if (file) {
                uploadText.textContent = `تم اختيار: ${file.name}`;
                document.querySelector('.file-upload-area').style.borderColor = '#28a745';
                document.querySelector('.file-upload-area').style.background = 'rgba(40, 167, 69, 0.05)';
            }
        });

        // Phone number formatting
        document.getElementById('mobile_number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            e.target.value = value;
        });
    </script>

    <!-- Global Theme Manager -->
    <script src="../js/global-theme-manager.js"></script>
</body>
</html>
