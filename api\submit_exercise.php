<?php
header('Content-Type: application/json');
// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/CourseManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

if (!isset($_POST['exercise_id']) || !isset($_POST['answer'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة']);
    exit;
}

try {
    $db = Database::getInstance()->getConnection();
    $courseManager = new CourseManager();
    $userId = $_SESSION['user_id'];
    $exerciseId = $_POST['exercise_id'];
    $userAnswer = trim($_POST['answer']);

    // Ensure the user_exercise_attempts table exists
    try {
        $db->query("SELECT 1 FROM user_exercise_attempts LIMIT 1");
    } catch (Exception $e) {
        // Create the table if it doesn't exist
        $db->exec("
            CREATE TABLE IF NOT EXISTS user_exercise_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                exercise_id INT NOT NULL,
                user_answer TEXT NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                score DECIMAL(5,2) DEFAULT 0,
                attempt_number INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_exercise (user_id, exercise_id),
                INDEX idx_attempt (user_id, exercise_id, attempt_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        error_log("Created user_exercise_attempts table");
    }

    // Get exercise details
    $stmt = $db->prepare("SELECT * FROM course_exercises WHERE id = ?");
    $stmt->execute([$exerciseId]);
    $exercise = $stmt->fetch();
    
    if (!$exercise) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'التمرين غير موجود']);
        exit;
    }
    
    // Check if user has access to this course
    if (!$courseManager->userHasAccess($userId, $exercise['course_id'])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول لهذا التمرين']);
        exit;
    }
    
    // Check if answer is correct
    $correctAnswer = trim($exercise['correct_answer']);

    // Normalize answers for true/false questions
    if ($exercise['question_type'] === 'true_false') {
        // Normalize user answer
        if (in_array(strtolower($userAnswer), ['true', 'صح', '1', 'نعم'])) {
            $userAnswer = 'true';
        } elseif (in_array(strtolower($userAnswer), ['false', 'خطأ', '0', 'لا'])) {
            $userAnswer = 'false';
        }

        // Normalize correct answer
        if (in_array(strtolower($correctAnswer), ['true', 'صح', '1', 'نعم'])) {
            $correctAnswer = 'true';
        } elseif (in_array(strtolower($correctAnswer), ['false', 'خطأ', '0', 'لا'])) {
            $correctAnswer = 'false';
        }
    }

    $isCorrect = (strcasecmp($userAnswer, $correctAnswer) === 0);
    
    // Get current attempt number
    $stmt = $db->prepare("SELECT MAX(attempt_number) as max_attempt FROM user_exercise_attempts WHERE user_id = ? AND exercise_id = ?");
    $stmt->execute([$userId, $exerciseId]);
    $result = $stmt->fetch();
    $attemptNumber = ($result['max_attempt'] ?? 0) + 1;
    
    // Save attempt
    $stmt = $db->prepare("
        INSERT INTO user_exercise_attempts (user_id, exercise_id, user_answer, is_correct, attempt_number)
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([$userId, $exerciseId, $userAnswer, $isCorrect, $attemptNumber]);
    
    // Format correct answer for display
    $displayCorrectAnswer = $exercise['correct_answer'] ?? 'غير محدد';

    // Ensure we have valid data
    if (empty($displayCorrectAnswer) || $displayCorrectAnswer === null) {
        $displayCorrectAnswer = 'غير محدد';
    }

    if ($exercise['question_type'] === 'true_false') {
        if ($correctAnswer === 'true' || strtolower($exercise['correct_answer']) === 'true' || $exercise['correct_answer'] === 'صح') {
            $displayCorrectAnswer = 'صح';
        } elseif ($correctAnswer === 'false' || strtolower($exercise['correct_answer']) === 'false' || $exercise['correct_answer'] === 'خطأ') {
            $displayCorrectAnswer = 'خطأ';
        }
    }

    echo json_encode([
        'success' => true,
        'is_correct' => $isCorrect,
        'correct_answer' => $displayCorrectAnswer,
        'explanation' => $exercise['explanation'] ?? '',
        'attempt_number' => $attemptNumber ?? 1,
        'question_type' => $exercise['question_type'] ?? 'true_false'
    ]);
    
} catch (Exception $e) {
    error_log("Error submitting exercise: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
