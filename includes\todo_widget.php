<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    return;
}

require_once 'database.php';
$userManager = new UserManager();

// Get user todos
$todos = $userManager->getUserTodos($_SESSION['user_id']);
$completedTodos = array_filter($todos, function($todo) { return $todo['is_completed']; });
$pendingTodos = array_filter($todos, function($todo) { return !$todo['is_completed']; });
?>

<div class="todo-widget enhanced-widget">
    <div class="widget-header">
        <div class="header-icon">📋</div>
        <div class="header-content">
            <h3>قائمة المهام</h3>
            <p>تنظيم وإدارة مهامك اليومية</p>
        </div>
        <button class="widget-action btn-add-todo" onclick="showEnhancedTodoModal()">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <div class="todo-stats-modern">
        <div class="todo-stat-modern pending">
            <div class="stat-icon">⏳</div>
            <div class="stat-content">
                <span class="stat-number"><?php echo count($pendingTodos); ?></span>
                <span class="stat-label">مهام معلقة</span>
            </div>
        </div>
        <div class="todo-stat-modern completed">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <span class="stat-number"><?php echo count($completedTodos); ?></span>
                <span class="stat-label">مهام مكتملة</span>
            </div>
        </div>

    </div>

    <div class="todo-tabs">
        <button class="todo-tab active" onclick="showTodoTab('pending')">المعلقة</button>
    </div>

    <div class="todo-content">
        <!-- Pending Todos -->
        <div id="pending-todos" class="todo-list active">
            <?php if (empty($pendingTodos)): ?>
                <div class="empty-state">
                    <span class="empty-icon">📝</span>
                    <p>لا توجد مهام معلقة</p>
                </div>
            <?php else: ?>
                <?php foreach ($pendingTodos as $todo): ?>
                    <div class="todo-item-modern" data-id="<?php echo $todo['id']; ?>">
                        <div class="todo-checkbox-modern">
                            <input type="checkbox" class="enhanced-checkbox"
                                   id="todo-<?php echo $todo['id']; ?>"
                                   onchange="toggleTodoEnhanced(<?php echo $todo['id']; ?>)">
                            <label for="todo-<?php echo $todo['id']; ?>" class="checkbox-label"></label>
                        </div>
                        <div class="todo-content-modern">
                            <h4 class="todo-title-modern"><?php echo htmlspecialchars($todo['title']); ?></h4>
                            <?php if ($todo['description']): ?>
                                <p class="todo-description-modern"><?php echo htmlspecialchars($todo['description']); ?></p>
                            <?php endif; ?>
                            <div class="todo-meta-modern">
                                <span class="todo-priority-modern priority-<?php echo $todo['priority']; ?>">
                                    <?php
                                    $priorities = ['low' => 'منخفض', 'medium' => 'متوسط', 'high' => 'عالي'];
                                    echo $priorities[$todo['priority']] ?? $todo['priority'];
                                    ?>
                                </span>
                                <?php if ($todo['due_date']): ?>
                                    <span class="todo-due-modern">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?php echo date('M d', strtotime($todo['due_date'])); ?>
                                    </span>
                                <?php endif; ?>
                                <span class="todo-date-modern">
                                    <i class="fas fa-clock"></i>
                                    <?php echo date('M d', strtotime($todo['created_at'])); ?>
                                </span>
                            </div>
                        </div>
                        <div class="todo-actions-modern">
                            <button onclick="editTodoEnhanced(<?php echo $todo['id']; ?>)" class="btn-action edit" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteTodoEnhanced(<?php echo $todo['id']; ?>)" class="btn-action delete" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Completed Todos -->
        <div id="completed-todos" class="todo-list">
            <?php if (empty($completedTodos)): ?>
                <div class="empty-state">
                    <span class="empty-icon">✅</span>
                    <p>لا توجد مهام مكتملة</p>
                </div>
            <?php else: ?>
                <?php foreach ($completedTodos as $todo): ?>
                    <div class="todo-item completed" data-id="<?php echo $todo['id']; ?>">
                        <div class="todo-checkbox">
                            <input type="checkbox" checked onchange="toggleTodo(<?php echo $todo['id']; ?>)">
                        </div>
                        <div class="todo-content-item">
                            <h4 class="todo-title"><?php echo htmlspecialchars($todo['title']); ?></h4>
                            <?php if ($todo['description']): ?>
                                <p class="todo-description"><?php echo htmlspecialchars($todo['description']); ?></p>
                            <?php endif; ?>
                            <div class="todo-meta">
                                <span class="todo-completed-date">
                                    ✅ مكتملة في <?php echo date('Y-m-d H:i', strtotime($todo['completed_at'])); ?>
                                </span>
                            </div>
                        </div>
                        <div class="todo-actions">
                            <button onclick="deleteTodo(<?php echo $todo['id']; ?>)" class="todo-action-btn delete">
                                🗑️
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add/Edit Todo Modal -->
<div id="todoModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="todoModalTitle">إضافة مهمة جديدة</h3>
            <button onclick="closeTodoModal()" class="modal-close">×</button>
        </div>
        <form id="todoForm">
            <input type="hidden" id="todoId" name="todo_id">
            <div class="form-group">
                <label for="todoTitle" class="form-label">عنوان المهمة *</label>
                <input type="text" id="todoTitle" name="title" class="form-input" required>
            </div>
            <div class="form-group">
                <label for="todoDescription" class="form-label">الوصف</label>
                <textarea id="todoDescription" name="description" class="form-textarea" rows="3"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="todoPriority" class="form-label">الأولوية</label>
                    <select id="todoPriority" name="priority" class="form-select">
                        <option value="low">منخفضة</option>
                        <option value="medium" selected>متوسطة</option>
                        <option value="high">عالية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="todoDueDate" class="form-label">تاريخ الاستحقاق</label>
                    <input type="date" id="todoDueDate" name="due_date" class="form-input">
                </div>
            </div>
            <div class="modal-actions">
                <button type="submit" class="btn btn-primary">حفظ</button>
                <button type="button" onclick="closeTodoModal()" class="btn btn-secondary">إلغاء</button>
            </div>
        </form>
    </div>
</div>

<style>
.todo-widget {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(70, 130, 180, 0.15);
    border: 2px solid rgba(70, 130, 180, 0.1);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.todo-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 20px 20px 0 0;
}

.todo-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 60px rgba(70, 130, 180, 0.25);
    border-color: rgba(135, 206, 235, 0.3);
}

.todo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(70, 130, 180, 0.1);
    position: relative;
}

.todo-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    border-radius: 2px;
}

.todo-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 22px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #2c3e50 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.todo-header h3::before {
    content: '✓';
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 0 6px 20px rgba(70, 130, 180, 0.3);
    transition: all 0.3s ease;
}

.todo-header h3:hover::before {
    transform: rotate(360deg) scale(1.1);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
}

.btn-add-todo {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 20px rgba(70, 130, 180, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-add-todo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-add-todo:hover::before {
    left: 100%;
}

.btn-add-todo:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.4);
}

.btn-add-todo:active {
    transform: translateY(-1px) scale(0.98);
}

.btn-add-todo span {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.btn-add-todo:hover span {
    transform: rotate(90deg) scale(1.1);
}

.todo-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.todo-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 15px;
    flex: 1;
    text-align: center;
    border: 2px solid rgba(70, 130, 180, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.todo-stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.todo-stat:hover::before {
    transform: scaleX(1);
}

.todo-stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(70, 130, 180, 0.2);
    border-color: rgba(135, 206, 235, 0.3);
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.todo-stat:hover .stat-number {
    transform: scale(1.1);
}

.stat-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.todo-tabs {
    display: flex;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 25px;
    box-shadow: inset 0 2px 4px rgba(70, 130, 180, 0.1);
}

.todo-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #6c757d;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    flex: 1;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.todo-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(135, 206, 235, 0.2), transparent);
    transition: left 0.5s ease;
}

.todo-tab:hover::before {
    left: 100%;
}

.todo-tab.active {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
    transform: translateY(-1px);
}

.todo-tab.active::before {
    display: none;
}

.todo-tab:hover:not(.active) {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    transform: translateY(-1px);
}

.todo-list {
    display: none;
    max-height: 400px;
    overflow-y: auto;
}

.todo-list.active {
    display: block;
}

.todo-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 18px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid rgba(70, 130, 180, 0.1);
    border-radius: 12px;
    margin-bottom: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.todo-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.todo-item:hover::before {
    transform: scaleY(1);
}

.todo-item:hover {
    transform: translateX(5px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.15);
    border-color: rgba(135, 206, 235, 0.3);
}

.todo-item.completed {
    opacity: 0.6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: rgba(108, 117, 125, 0.2);
}

.todo-item.completed::before {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.todo-item.completed .todo-title {
    text-decoration: line-through;
    color: #6c757d;
}

.todo-checkbox input {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.todo-content-item {
    flex: 1;
}

.todo-title {
    margin: 0 0 8px;
    color: #333;
    font-size: 16px;
}

.todo-description {
    margin: 0 0 10px;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.todo-meta {
    display: flex;
    gap: 15px;
    align-items: center;
}

.todo-priority {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.priority-low {
    background: #d4edda;
    color: #155724;
}

.priority-medium {
    background: #fff3cd;
    color: #856404;
}

.priority-high {
    background: #f8d7da;
    color: #721c24;
}

.todo-due-date,
.todo-completed-date {
    font-size: 12px;
    color: #666;
}

.todo-actions {
    display: flex;
    gap: 5px;
}

.todo-action-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.todo-action-btn:hover {
    background: #f8f9fa;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 15px;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal[style*="flex"] {
    display: flex !important;
}

.modal-content {
    background: white;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #4682B4;
}

.form-textarea {
    resize: vertical;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .todo-widget {
        padding: 20px !important;
        margin-bottom: 25px !important;
    }

    .todo-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .todo-header h3 {
        text-align: center;
        font-size: 18px;
    }

    .btn-add-todo {
        justify-content: center;
        padding: 12px 20px;
        font-size: 15px;
        min-height: 44px;
    }

    .todo-stats {
        flex-direction: column;
        gap: 12px;
    }

    .todo-stat {
        padding: 12px;
        min-height: 60px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 11px;
    }

    .todo-tabs {
        margin-bottom: 15px;
    }

    .todo-tab {
        padding: 10px 16px;
        font-size: 13px;
        min-height: 40px;
    }

    .todo-item {
        padding: 12px;
        gap: 12px;
    }

    .todo-title {
        font-size: 15px;
    }

    .todo-description {
        font-size: 13px;
    }

    .todo-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .todo-priority {
        font-size: 11px;
        padding: 3px 6px;
    }

    .todo-due-date,
    .todo-completed-date {
        font-size: 11px;
    }

    .todo-actions {
        flex-direction: column;
        gap: 4px;
    }

    .todo-action-btn {
        padding: 6px;
        min-width: 32px;
        min-height: 32px;
        font-size: 14px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .modal-content {
        width: 95%;
        margin: 15px;
        max-height: 95vh;
    }
}

@media (max-width: 480px) {
    .todo-widget {
        padding: 15px !important;
        margin-bottom: 20px !important;
        border-radius: 12px !important;
    }

    .todo-header h3 {
        font-size: 16px;
    }

    .btn-add-todo {
        padding: 10px 16px;
        font-size: 14px;
        min-height: 40px;
    }

    .todo-stats {
        gap: 10px;
    }

    .todo-stat {
        padding: 10px;
        min-height: 50px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 10px;
    }

    .todo-tab {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 36px;
    }

    .todo-item {
        padding: 10px;
        gap: 10px;
    }

    .todo-checkbox input {
        width: 16px;
        height: 16px;
    }

    .todo-title {
        font-size: 14px;
    }

    .todo-description {
        font-size: 12px;
    }

    .todo-priority {
        font-size: 10px;
        padding: 2px 5px;
    }

    .todo-due-date,
    .todo-completed-date {
        font-size: 10px;
    }

    .todo-action-btn {
        padding: 5px;
        min-width: 28px;
        min-height: 28px;
        font-size: 12px;
    }

    .empty-state {
        padding: 30px 15px;
    }

    .empty-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .empty-state p {
        font-size: 13px;
    }
}

@media (max-width: 320px) {
    .todo-widget {
        padding: 12px !important;
        margin-bottom: 15px !important;
    }

    .todo-header h3 {
        font-size: 15px;
    }

    .btn-add-todo {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 36px;
    }

    .todo-stat {
        padding: 8px;
        min-height: 45px;
    }

    .stat-number {
        font-size: 16px;
    }

    .stat-label {
        font-size: 9px;
    }

    .todo-tab {
        padding: 6px 10px;
        font-size: 11px;
        min-height: 32px;
    }

    .todo-item {
        padding: 8px;
        gap: 8px;
    }

    .todo-title {
        font-size: 13px;
    }

    .todo-description {
        font-size: 11px;
    }

    .todo-action-btn {
        padding: 4px;
        min-width: 24px;
        min-height: 24px;
        font-size: 11px;
    }

    .empty-state {
        padding: 25px 10px;
    }

    .empty-icon {
        font-size: 36px;
        margin-bottom: 10px;
    }

    .empty-state p {
        font-size: 12px;
    }
}
</style>


